package com.meta.supplychain.wds.web.controller;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.entity.dto.wds.MoveBillDTO;
import com.meta.supplychain.entity.dto.wds.MoveLocBatchDetailDTO;
import com.meta.supplychain.entity.dto.wds.req.MoveBillSaveReq;
import com.meta.supplychain.entity.dto.wds.req.QueryMoveBillReq;
import com.meta.supplychain.wds.SupplychainWDSiteApplication;
import com.metadata.idaas.client.model.LoginUserDTO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 */
@SpringBootTest(classes = SupplychainWDSiteApplication.class)
//指定启动环境
@ActiveProfiles(value = "local")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WdsMoveCtrTest {


    @Resource
    private MoveOrderController moveOrderController;

    @BeforeAll
    void setTenantId() {
        TenantContext.set(153658);
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setCode("210047");
        loginUserDTO.setUid(30002380L);
        loginUserDTO.setName("admin");
        ThreadLocals.setValue("_login_user_idaas", loginUserDTO);
    }

    @Test
    void pageQtyTest() {
        try {
            TenantContext.set(153658);
            QueryMoveBillReq params = new QueryMoveBillReq();
            params.setStartTime("2025-04-09 00:00:00");
            //params.setBillNo("PL250517000001");
           // params.setSkuSearchKey("541721");

            PageResult<MoveBillDTO> result = moveOrderController.queryMoveOrderListByPage(params);
            Logs.info("调用结果:{}",result);
            assertNotNull(result);
        } catch (Exception e){
            Logs.error("调用异常：{}",e);
        }
    }

    @Test
    void detailTest() {
        try {
            MoveBillDTO result = moveOrderController.detail("MB202505151550001");
            Logs.info("调用结果:{}",result);
            assertNotNull(result);
        } catch (Exception e){
            Logs.error("调用异常：{}",e);
        }
    }

    @Test
    void saveShipOrderSkusTest() {
        try {
            TenantContext.set(153658);
            MoveBillSaveReq params = new MoveBillSaveReq();
            params.setBillNo("MB202505301550003");
            params.setWhCode("0092");
            params.setWhName("test");
            params.setOptType(3);
            params.setMoveType(1);
            List<MoveLocBatchDetailDTO> detailList = new ArrayList<>();


            MoveLocBatchDetailDTO detail = new MoveLocBatchDetailDTO();
            detail.setSkuCode("542630");
            detail.setSkuName("6666");
            detail.setInLocationCode("L0006");
            detail.setOutLocationCode("L0003");
            detail.setMoveQty(new BigDecimal(2));
            detailList.add(detail);
            params.setDetailList(detailList);

            Result<Boolean> result = moveOrderController.saveMoveOrderSkus(params);
            Logs.info("调用结果:{}",result);
            assertNotNull(result);
        } catch (Exception e){

            Logs.error("调用异常：{}",e);
        }

    }


    @Test
    void cancelTest() {
        try {
            TenantContext.set(153658);
            Result<Boolean> result = moveOrderController.cancel("MB202505271550001");
            Logs.info("调用结果:{}",result);
            assertNotNull(result);
        } catch (Exception e){
            Logs.error("调用异常：{}",e);
        }
    }

    @Test
    void exportMoveOrderTest() {
        try {
            TenantContext.set(153658);
            QueryMoveBillReq params = new QueryMoveBillReq();
            params.setStartTime("2025-04-09 00:00:00");
            Result<String> result = moveOrderController.exportMoveOrder(params);
            Logs.info("调用结果:{}",result);
            assertNotNull(result);
        } catch (Exception e){
            Logs.error("调用异常：{}",e);
        }
    }





}