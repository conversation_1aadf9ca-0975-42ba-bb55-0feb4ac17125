package com.meta.supplychain.wds.web.controller;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillOptParams;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles(value = "local")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ShipDiffControllerTest {

    @Autowired
    private ShipDiffController shipDiffController;

    @BeforeAll
    public void setTenantId() {
        TenantContext.set(153658);
    }

    @Test
    void queryDeliveryOrderListByPage() {
        WdShipAccDiffBillQueryReq query = new WdShipAccDiffBillQueryReq();
//        query.setBillNo("SD202401080001");
        PageResult<WdShipAccDiffBillResp> response = shipDiffController.queryDeliveryOrderListByPage(query);
        assertNotNull(response);
        // 可根据实际情况断言数量
        // assertEquals(1, response.getRows().size());
    }

    @Test
    void exportTest() {
        WdShipAccDiffBillQueryReq query = new WdShipAccDiffBillQueryReq();
        query.setBillNo("SD202401080001");
        String exportCode = shipDiffController.export(query);
        assertNotNull(exportCode);
    }

    @Test
    void detail() {
        String billNo = "SD202401080001";
        List<WdShipAccDiffBatchDetailResp> response = shipDiffController.detail(billNo);
        assertNotNull(response);
        // 可根据实际情况断言数量
        // assertEquals(1, response.size());
    }

    @Test
    void audit() {
        WdShipAccDiffBillOptParams optParams = new WdShipAccDiffBillOptParams();
        optParams.setBillNo("SD202401080001");
        Result<Boolean> result = shipDiffController.audit(optParams);
        assertNotNull(result);
        // 可根据实际情况断言
        // assertTrue(result.getData());
    }
} 