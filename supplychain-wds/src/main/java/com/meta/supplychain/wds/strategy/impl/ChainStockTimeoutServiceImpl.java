package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchStockReportReq;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.wds.WDAcceptDiffOwnerModeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IShipBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAccDiffBatchDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAccDiffBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptBatchDetailRepository;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.wds.domain.delivery.IShipDiffDomainService;
import com.meta.supplychain.wds.util.ShipAcceptBillConvertUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配送验收差异处理
 */
@Service
@RequiredArgsConstructor
public class ChainStockTimeoutServiceImpl extends AbstractChainOrderEventServiceImpl {

    @Override
    public void executeEvent(String data) {
        EventMsgBaseDTO<BatchStockReportReq> billBaseDto = convertParam(data, BatchStockReportReq.class);
        Logs.info("库存超时入库 单号 {} ", billBaseDto.getBillNo());
        logPersistence(true, billBaseDto, SysConstants.SUCCESS);
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.STOCK_EXECUTE_TIMEOUT.getSign());
    }
}
