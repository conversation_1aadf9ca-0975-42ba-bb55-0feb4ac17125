package com.meta.supplychain.wds.domain.delivery;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillOptParams;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.entity.po.wds.WdShipAccDiffBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdShipAccDiffBillPO;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.wds.WDAcceptDiffOwnerModeEnum;

import java.util.List;

/**
 * 差异处理单领域服务接口
 */
public interface IShipDiffDomainService {

    /**
     * 分页查询差异处理单列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<WdShipAccDiffBillResp> queryShipDiffBillList(WdShipAccDiffBillQueryReq query);

    /**
     * 查询差异处理单批次明细详情
     *
     * @param optParams 操作参数
     * @return 批次明细详情
     */
    List<WdShipAccDiffBatchDetailResp> queryShipDiffBatchDetail(WdShipAccDiffBillOptParams optParams);

    /**
     * 审核差异处理单
     *
     * @param optParams 操作参数
     * @return 操作结果
     */
    Result<Boolean> audit(WdShipAccDiffBillOptParams optParams);


    void convertStockReq(WdShipAccDiffBillPO diffBillPO,
                                List<WdShipAccDiffBatchDetailPO> diffBatchDetailPOS,
                                CommonOperateEnum operateEnum, WDAcceptDiffOwnerModeEnum ownerMode);

}