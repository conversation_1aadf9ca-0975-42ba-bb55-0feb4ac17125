package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.trace.annotation.TraceId;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.wds.req.FulfillDeliveryInfoRequest;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.wds.domain.delivery.IDeliveryOrderDomainService;
import com.meta.supplychain.wds.domain.delivery.impl.DeliveryOrderAuditService;
import feign.codec.DecodeException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配送订单审核
 *
 */
@Service
public class ChainDeliveryAuditEventServiceImpl extends AbstractChainOrderEventServiceImpl {

    @Resource
    private IWdDeliveryBillRepository iWdDeliveryBillRepository;
    @Resource
    IWdDeliveryBillDetailRepository IWdDeliveryBillDetailRepository;


    @Resource
     ICommonStockService iCommonStockService;

    @Resource
    DeliveryOrderAuditService deliveryOrderAuditService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceId
    public void executeEvent(String data) {
        EventMsgBaseDTO<WdDeliveryBillPO> billBaseDto = convertParam(data, WdDeliveryBillPO.class);
        if (isIdempotent(billBaseDto)){
            Logs.info("配送订单审核【{}】已处理过", billBaseDto.getBillNo());
            return;
        }
        WdDeliveryBillPO wdDeliveryBillPO = billBaseDto.getData();
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = IWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
        WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                .billNo(wdDeliveryBillPO.getBillNo())
                .id(wdDeliveryBillPO.getId())
                .approveManCode(wdDeliveryBillPO.getApproveManCode())
                .approveManName(wdDeliveryBillPO.getApproveManName())
                .approveTime(LocalDateTime.now())
                .approveRemark(wdDeliveryBillPO.getApproveRemark())
                .status(wdDeliveryBillPO.getStatus())
                .build();
        BatchRecordReq batchRecordReq = null;
        try {
            // 同步成本、库存
            deliveryOrderAuditService.convertStockReq(wdDeliveryBillPO, deliveryBillDetailPOList, WDDeliveryOptTypeEnum.AUDIT);
            BatchRecordResp batchRecordResp = iCommonStockService.costStockExecute(batchRecordReq);
            //  回写 商品成本单价
            deliveryOrderAuditService.backWriteGoodsCostPrice(batchRecordResp, deliveryBillDetailPOList);
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (BizException | DecodeException e) {
            // 业务或者解析异常  停留在待审核
            wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_PENDING_APPROVAL.getCode());
            Logs.error("成本库存同步失败，{}", e.getMessage());
//            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_P010);
            iCommonStockService.costStockRollback(batchRecordReq);
        } catch (Exception exception) {
            Logs.error("配送订单发货履行，事件处理异常：{}", billBaseDto.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            // 其它异常 停留在处理中
            wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_WAIT_CONVERT.getCode());
            logPersistence(false, billBaseDto, fullStackTrace);
        }
        update.setStatus(wdDeliveryBillPO.getStatus());
        iWdDeliveryBillRepository.updateById(update);
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.DELIVERY_AUDIT.getSign());
    }

}
