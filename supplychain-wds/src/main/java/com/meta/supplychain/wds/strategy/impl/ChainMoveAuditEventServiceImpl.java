package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.trace.annotation.TraceId;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveLocBatchDetailRepository;
import com.meta.supplychain.wds.domain.delivery.impl.MoveOrderAggregationService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class ChainMoveAuditEventServiceImpl extends AbstractChainOrderEventServiceImpl {

    @Resource
    private IMoveBillRepository moveRepository;
    @Resource
    private IMoveLocBatchDetailRepository  moveLocBatchDetailRepository;
    @Resource
    private MoveOrderAggregationService moveOrderAggregationService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceId
    public void executeEvent(String data) {
        Logs.info("executeEvent移库单审核：{}", data);
        EventMsgBaseDTO<MoveBillPO> billBaseDto = convertParam(data, MoveBillPO.class);
        MoveBillPO billEvent = billBaseDto.getData();
        try {
            if (isIdempotent(billBaseDto)){
                Logs.info("移库单审核【{}】已处理过", billEvent.getBillNo());
                return;
            }
            //当前只有审核处理库异常时会推送审核事件，后续可补充其他逻辑
            MoveBillPO moveBill = moveRepository.queryMoveBillPOWithValid(billEvent.getBillNo());
            if (Objects.equals(moveBill.getStatus(), WDBillStatusEnum.MOVE_STATUS_PENDING.getStatus())) {
                //只有状态为过账中才重新触发库存处理
                //是否需要处理事件
                List<MoveLocBatchDetailPO> billDetails = moveLocBatchDetailRepository.lambdaQuery().eq(MoveLocBatchDetailPO::getBillNo, billEvent.getBillNo()).list();
                try {
                    moveOrderAggregationService.handleStock(moveBill,billDetails);
                } catch (BizException bizException){
                    //业务异常,重置单据待审核 用户可重新操作单据
                    moveRepository.updateStatus(moveBill.getBillNo(), WDBillStatusEnum.MOVE_STATUS_1.getStatus(), WDBillStatusEnum.MOVE_STATUS_PENDING.getStatus());
                    logPersistence(true, billBaseDto, SysConstants.SUCCESS);
                    return;
                }
            }
            //其他审核完成触发逻辑...
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("移库单审核，事件处理异常：{}", billBaseDto.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(),BillActionTypeEnum.SupportSign.MOVE_AUDIT.getSign());
    }


}
