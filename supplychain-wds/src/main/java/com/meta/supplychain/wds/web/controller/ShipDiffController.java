package com.meta.supplychain.wds.web.controller;


import cn.linkkids.ageiport.client.AgeiTaskClient;
import cn.linkkids.ageiport.params.ExportDataParams;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillOptParams;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillCreateReq;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.application.IWdsApplicationManager;
import com.meta.supplychain.wds.export.processor.ShipDiffExportProcessor;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * 差异处理单管理
 */
@Tag(name = "差异处理单管理")
@RestController
@RequestMapping("${unit-deploy.prefix-main:}/ship/diff")
@RequiredArgsConstructor
@Validated
public class ShipDiffController {

    @Resource
    private IWdsApplicationManager orderWDApplicationManager;

    @Resource
    private UserUtil userUtil;

    /**
     * 分页查询差异处理单列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页 查询差异处理单列表")
    @PostMapping("/page")
    public PageResult<WdShipAccDiffBillResp> queryDeliveryOrderListByPage(@RequestBody @Valid WdShipAccDiffBillQueryReq query) {
        PageResult<WdShipAccDiffBillResp> shipDiffBillResults = orderWDApplicationManager.shipDiffApplication()
                .queryShipDiffBillList(query);
        return shipDiffBillResults;
    }

    /**
     * 导出差异处理单列表
     *
     * @param query 查询参数
     * @return 导出任务编码
     */
    @Operation(summary = "差异处理单列表导出")
    @PostMapping("/export")
    public String export(@RequestBody @Valid WdShipAccDiffBillQueryReq query) {
        if (com.alibaba.ageiport.common.utils.CollectionUtils.isEmpty(query.getWhCodeList())) {
            query.setWhCodeList(userUtil.getDeptOpInfoWithThrow().getManageDeptCodeList());
        }
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        ExportSpecification specification = ShipDiffExportProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(query)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return specification.code();
    }

    /**
     * 查询差异处理单详情
     *
     * @param billNo 单据号
     * @return 差异处理单详情
     */
    @GetMapping("/detail")
    @Operation(summary = "查询差异处理单详情", description = "查询差异处理单详情")
    public List<WdShipAccDiffBatchDetailResp> detail(
            @RequestParam("billNo") @Parameter(description = "差异处理单单据号") 
            @NotBlank(message = "单据号不能为空") String billNo) {
        WdShipAccDiffBillOptParams build = WdShipAccDiffBillOptParams.builder()
                .billNo(billNo).build();
        return orderWDApplicationManager.shipDiffApplication().queryShipDiffBatchDetail(build);
    }

    /**
     * 审核差异处理单
     *
     * @param optParams 操作参数z
     * @return 操作结果
     */
    @PostMapping("/audit")
    @Operation(summary = "审核差异处理单", description = "审核差 异处理单")
    @MethodLog("审核差异处理单")
    public Result<Boolean> audit(@RequestBody @Valid WdShipAccDiffBillOptParams optParams) {
        return orderWDApplicationManager.shipDiffApplication().audit(optParams);
    }
    
    
    
}
