package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBatchDetailCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBillCreateReq;
import com.meta.supplychain.entity.po.wds.ShipBatchDetailPO;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptBillPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.CommonBillTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.wds.WDShipAcceptBillStatusEnum;
import com.meta.supplychain.enums.wds.WDShipAcceptOptTypeEnum;
import com.meta.supplychain.enums.wds.WDShipTypeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IShipBatchDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IShipBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptBatchDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptBillRepository;
import com.meta.supplychain.wds.domain.delivery.IShipAcceptDomainService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动配送验收 - 逆向
 */
@Service
public class ChainAutoDeliveryAcceptRefundServiceImpl extends AbstractChainOrderEventServiceImpl {
    @Resource
    private IShipBillRepository shipOrderRepository;

    @Resource
    IShipAcceptDomainService shipAcceptDomainService;

    @Resource
    ICommonStockService iCommonStockService;

    @Resource
    private BillEventServiceFactory billEventServiceFactory;

    @Resource
    private IWdShipAcceptBatchDetailRepository shipAcceptBatchDetailRepository;
    @Resource
    private IWdShipAcceptBillRepository shipAcceptBillRepository;

    @Override
    public void executeEvent(String data) {
        EventMsgBaseDTO<WdShipAcceptBillPO> billBaseDto = convertParam(data, WdShipAcceptBillPO.class);
        WdShipAcceptBillPO acceptBillPO = billBaseDto.getData();
        Logs.info("逆向配送验收 {} 开始同步库存 通知逆向配送", acceptBillPO.getBillNo());
        try {
            List<WdShipAcceptBatchDetailPO> wdShipAcceptBatchDetailPOS = shipAcceptBatchDetailRepository.queryByBillNo(acceptBillPO.getBillNo());
            if (CollectionUtils.isNotEmpty(wdShipAcceptBatchDetailPOS)) {
                String srcBillNo = wdShipAcceptBatchDetailPOS.get(0).getSrcBillNo();
                Integer billType = acceptBillPO.getBillType();
                WDShipTypeEnum byCode = WDShipTypeEnum.getByCode(billType);
                CommonBillTypeEnum billTypeEnum = WDShipTypeEnum.DEPT_RETURN.equals(byCode) ? CommonBillTypeEnum.DNR : CommonBillTypeEnum.DNR_DIFF;
                BatchRecordReq batchRecordReq = shipAcceptDomainService.convertStockReq(billTypeEnum, CommonOperateEnum.POST,srcBillNo, acceptBillPO, wdShipAcceptBatchDetailPOS);
                iCommonStockService.costStockExecute(batchRecordReq);
                ShipBillPO shipBillPO = shipOrderRepository.queryShipBillPO(acceptBillPO.getShipBillNo());
                billEventServiceFactory.publishEvent(shipBillPO.getBillNo(), BillActionTypeEnum.AUTO_SHIP_SEND, shipBillPO);
                // 变更逆向配送验收状态为已审核
                WdShipAcceptBillPO build = WdShipAcceptBillPO.builder()
                        .id(acceptBillPO.getId())
                        .status(WDShipAcceptBillStatusEnum.ALREADY_AUDIT.getCode())
                        .build();
                build.setUpdateCode(acceptBillPO.getApproveManCode());
                build.setUpdateName(acceptBillPO.getApproveManName());
                build.setUpdateTime(LocalDateTime.now());
                shipAcceptBillRepository.updateById(build);
                logPersistence(true, billBaseDto, SysConstants.SUCCESS);
            }
        } catch (Exception exception) {
            Logs.error("逆向配送验收 {} 开始同步库存 异常", acceptBillPO.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.SHIP_ACCEPT_REFUND.getSign());
    }
}
