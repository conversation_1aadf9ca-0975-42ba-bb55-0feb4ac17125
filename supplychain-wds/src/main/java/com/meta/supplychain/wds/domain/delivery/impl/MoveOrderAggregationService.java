package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.entity.dto.stock.LocStockDTO;
import com.meta.supplychain.entity.dto.stock.req.LocStockMoveReq;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveBillRepository;
import feign.codec.DecodeException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MoveOrderAggregationService {

    @Resource
    public IMoveBillRepository moveBillRepository;


    @Resource
    private ICommonStockService commonStockService;

    /**
     * 库存处理
     * @param bill
     * @param billDetails
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleStock(MoveBillPO bill, List<MoveLocBatchDetailPO> billDetails) {
        Logs.info("商品移位单【{}】，handleStock start", bill.getBillNo());
        try {
            doStockExecute(bill, billDetails);
            moveBillRepository.updateStatus(bill.getBillNo(), WDBillStatusEnum.MOVE_STATUS_2.getStatus(), WDBillStatusEnum.MOVE_STATUS_1.getStatus());
            Logs.info("商品移位单【{}】，handleStock end", bill.getBillNo());
        } catch (BizException | DecodeException bizException){
            //业务异常回滚逻辑
            BizExceptions.throwWithThrowable(bizException);
        } catch (Exception e){
            Logs.error("handleStock商品移位单【{}】库存处理失败:", bill.getBillNo(), e);
            throw e;
        }

    }

    private void doStockExecute(MoveBillPO bill, List<MoveLocBatchDetailPO> billDetails) {
        List<LocStockDTO>  locStockDTOList = billDetails.stream().map(billDetail -> LocStockDTO.builder()
                .skuCode(billDetail.getSkuCode())
                .insideId(billDetail.getInsideId())
                .qty(billDetail.getMoveQty())
                .locationCode(billDetail.getOutLocationCode())
                .targetLocationCode(billDetail.getInLocationCode())
                .periodBatchNo(billDetail.getPeriodBatchNo())
                .productDate(billDetail.getProductDate())
                .expireDate(billDetail.getExpireDate()).build()).collect(Collectors.toList());

        LocStockMoveReq lockMoveReq = LocStockMoveReq.builder()
                .deptCode(bill.getWhCode())
                .billNo(bill.getBillNo())
                .skuList(locStockDTOList).build();
        commonStockService.locStockMove(lockMoveReq);
    }

}
