package com.meta.supplychain.wds.application.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillOptParams;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.wds.application.intf.IShipDiffApplication;
import com.meta.supplychain.wds.domain.delivery.IShipDiffDomainService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 差异处理单应用服务实现类
 */
@Service
public class ShipDiffApplicationImpl implements IShipDiffApplication {

    @Resource
    private IShipDiffDomainService shipDiffDomainService;

    @Override
    public PageResult<WdShipAccDiffBillResp> queryShipDiffBillList(WdShipAccDiffBillQueryReq query) {
        return shipDiffDomainService.queryShipDiffBillList(query);
    }

    @Override
    public List<WdShipAccDiffBatchDetailResp> queryShipDiffBatchDetail(WdShipAccDiffBillOptParams optParams) {
        return shipDiffDomainService.queryShipDiffBatchDetail(optParams);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> audit(WdShipAccDiffBillOptParams optParams) {
        return shipDiffDomainService.audit(optParams);
    }

}