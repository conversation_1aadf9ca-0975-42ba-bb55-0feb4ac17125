package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBatchDetailCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBillCreateReq;
import com.meta.supplychain.entity.po.wds.ShipBatchDetailPO;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.wds.WDShipAcceptOptTypeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IShipBatchDetailRepository;
import com.meta.supplychain.wds.domain.delivery.IShipAcceptDomainService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动配送验收
 */
@Service
public class ChainAutoDeliveryAcceptServiceImpl extends AbstractChainOrderEventServiceImpl {
    @Resource
    private IShipAcceptDomainService iShipAcceptDomainService;

    @Resource
    private IShipBatchDetailRepository shipBatchDetailRepository;

    @Override
    public void executeEvent(String data) {
        EventMsgBaseDTO<ShipBillPO> billBaseDto = convertParam(data, ShipBillPO.class);
        ShipBillPO shipBillPO = billBaseDto.getData();
        Logs.info("配送单 {} 开始自动验收", shipBillPO.getBillNo());
        try {
            WdShipAcceptBillCreateReq wdShipAcceptBillCreateReq = new WdShipAcceptBillCreateReq();
            wdShipAcceptBillCreateReq.setShipBillNo(shipBillPO.getBillNo());
            wdShipAcceptBillCreateReq.setOptType(WDShipAcceptOptTypeEnum.AUDIT.getCode());
            wdShipAcceptBillCreateReq.setRemarkAccept("自动验收");
            List<ShipBatchDetailPO> shipBatchDetailPOS = shipBatchDetailRepository.queryShipBatchDetailList(shipBillPO.getBillNo());
            List<WdShipAcceptBatchDetailCreateReq> detailCreateReqs = shipBatchDetailPOS.stream().map(shipBatchDetailPO -> {
                WdShipAcceptBatchDetailCreateReq wdShipAcceptBatchDetailCreateReq = new WdShipAcceptBatchDetailCreateReq();
                wdShipAcceptBatchDetailCreateReq.setShipBillNo(shipBillPO.getBillNo());
                wdShipAcceptBatchDetailCreateReq.setShipInsideId(shipBatchDetailPO.getInsideId());
                wdShipAcceptBatchDetailCreateReq.setPeriodBatchNo(shipBatchDetailPO.getPeriodBatchNo());
                wdShipAcceptBatchDetailCreateReq.setPeriodBarcode(shipBatchDetailPO.getPeriodBarcode());
                wdShipAcceptBatchDetailCreateReq.setSkuCode(shipBatchDetailPO.getSkuCode());
                wdShipAcceptBatchDetailCreateReq.setSkuName(shipBatchDetailPO.getSkuName());
                wdShipAcceptBatchDetailCreateReq.setProductDate(shipBatchDetailPO.getProductDate());
                wdShipAcceptBatchDetailCreateReq.setExpireDate(shipBatchDetailPO.getExpireDate());
                wdShipAcceptBatchDetailCreateReq.setAcceptQty(shipBatchDetailPO.getShipQty());
                wdShipAcceptBatchDetailCreateReq.setAcceptPrice(shipBatchDetailPO.getShipPrice());
                wdShipAcceptBatchDetailCreateReq.setAcceptTaxMoney(shipBatchDetailPO.getShipTaxMoney());
                wdShipAcceptBatchDetailCreateReq.setAcceptTax(shipBatchDetailPO.getShipTax());
                wdShipAcceptBatchDetailCreateReq.setRemarkAccept("自动验收");
                return wdShipAcceptBatchDetailCreateReq;
            }).collect(Collectors.toList());
            wdShipAcceptBillCreateReq.setAcceptDetailList(detailCreateReqs);
            OpInfo opInfo = new OpInfo();
            wdShipAcceptBillCreateReq.setOpInfo(opInfo);
            iShipAcceptDomainService.createShipAcceptBill(wdShipAcceptBillCreateReq);
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("配送单 {} 开始自动验收 异常", shipBillPO.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.SHIP_ACCEPT.getSign());
    }
}
