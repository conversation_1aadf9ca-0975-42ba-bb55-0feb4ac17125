package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.google.common.util.concurrent.AtomicDouble;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.wds.SaveShipAcceptBillDTO;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.util.MoneyUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 配送验收完成
 */
@Service
@RequiredArgsConstructor
public class ChainShipAcceptCompleteServiceImpl extends AbstractChainOrderEventServiceImpl {

    private final IWdShipAcceptBatchDetailRepository shipAcceptBatchDetailRepository;
    private final BillEventServiceFactory billEventServiceFactory;
    private final IShipBillRepository shipOrderRepository;
    private final IWdDeliveryBillRepository deliveryBillRepository;
    private final IShipBatchDetailRepository shipBatchDetailRepository;
    private final IWdDeliveryFulfillDetailRepository iWdDeliveryFulfillDetailRepository;
    private final IWdDeliveryBillDetailRepository iWdDeliveryBillDetailRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeEvent(String data) {
        EventMsgBaseDTO<SaveShipAcceptBillDTO> billBaseDto = convertParam(data, SaveShipAcceptBillDTO.class);
        SaveShipAcceptBillDTO shipAcceptBillDTO = billBaseDto.getData();
        WdShipAcceptBillPO acceptBillPO = shipAcceptBillDTO.getAcceptBillPO();
        Logs.info("配送验收完成 单号 {} 开始更新数据：", acceptBillPO.getBillNo());
        try {
            List<Integer> oldStatusListForConfirm = Arrays.asList(WDBillStatusEnum.SHIP_STATUS_2.getStatus(),
                    WDBillStatusEnum.SHIP_STATUS_3.getStatus(), WDBillStatusEnum.SHIP_STATUS_4.getStatus()
            );
            List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOList = shipAcceptBillDTO.getShipAcceptBatchDetailPOS();
            // 更新配送单 已验收
            shipOrderRepository.updateStatus(acceptBillPO.getShipBillNo(), WDBillStatusEnum.SHIP_STATUS_5.getStatus(), new ArrayList<>(oldStatusListForConfirm));
            // 更新配送单商品 收货标识
            List<Long> collect = acceptBatchDetailPOList.stream().map(WdShipAcceptBatchDetailPO::getShipInsideId).collect(Collectors.toList());
            shipBatchDetailRepository.updateAcceptSign(acceptBillPO.getShipBillNo(), YesOrNoEnum.YES.getCode(), new ArrayList<>(collect));

            //  更新配送订单 入货方标识
            String deliveryBillNo = acceptBillPO.getDeliveryBillNo();
            if (StringUtils.isNotBlank(deliveryBillNo)) {
                String[] split = deliveryBillNo.split(SysConstants.COMMA_DELIMITER);
                List<String> deliveryBillNoList = Arrays.asList(split);
                deliveryBillNoList.forEach(billNo -> {
                    deliveryBillRepository.lambdaUpdate()
                            .eq(WdDeliveryBillPO::getBillNo, billNo)
                            .set(WdDeliveryBillPO::getAcceptSign, YesOrNoEnum.YES.getCode())
                            .update();
                });
                //  更新履行 收货数量
                fulfillAcceptQty(acceptBillPO, acceptBatchDetailPOList);

                OpInfo opInfo = new OpInfo();
                deliveryBillNoList.forEach(billNo -> {
                    calDeliveryOrderAcceptQty(billNo, opInfo);
                });
            }
            acceptBatchDetailPOList.forEach(e -> e.setDiffQty(e.getShipQty().subtract(e.getAcceptQty())));
            List<WdShipAcceptBatchDetailPO> diffGoodsList = acceptBatchDetailPOList.stream()
                    .filter(e -> (e.getShipQty().subtract(e.getAcceptQty())).compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            //存在差异时，推送生成差异单事件
            if (CollectionUtils.isNotEmpty(diffGoodsList)) {
                List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOListForCheck = shipAcceptBatchDetailRepository.queryByBillNo(acceptBillPO.getBillNo());
                if (CollectionUtils.isEmpty(acceptBatchDetailPOListForCheck)) {
                    BizExceptions.throwWithMsg("配送单事务未结束，未查询到商品信息");
                }
                billEventServiceFactory.publishEvent(acceptBillPO.getBillNo(), BillActionTypeEnum.ACCEPT_DIFF_BUILD, acceptBillPO);
            }
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("配送验收完成 {} 更新数据失败 ", acceptBillPO.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    public void fulfillAcceptQty(WdShipAcceptBillPO acceptBillPO, List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOList) {
        try {
            // 按 发货单号 查询履行明细  然后 根据发货行号匹配数量 并填充收货数量
            List<WdDeliveryFulfillDetailPO> fulfillDetailPOList = iWdDeliveryFulfillDetailRepository.queryFulfillDetailListByShipBillNo(acceptBillPO.getShipBillNo());
            Map<Long, Double> acceptQtyMap = acceptBatchDetailPOList.stream()
                    .collect(Collectors.groupingBy(WdShipAcceptBatchDetailPO::getShipInsideId,
                            Collectors.summingDouble(item -> item.getAcceptQty().doubleValue())));
            Map<Long, List<WdDeliveryFulfillDetailPO>> shipDetailMap = fulfillDetailPOList.stream().collect(Collectors.groupingBy(WdDeliveryFulfillDetailPO::getShipInsideId));
            List<WdDeliveryFulfillDetailPO> updateList = new ArrayList<>();

            shipDetailMap.keySet().forEach(shipInsideId -> {
                List<WdDeliveryFulfillDetailPO> detailPOList = shipDetailMap.get(shipInsideId);
                BigDecimal acceptQtySum = BigDecimal.valueOf(acceptQtyMap.getOrDefault(shipInsideId, 0D));
                if (acceptQtySum.compareTo(BigDecimal.ZERO) == 0) {
                    updateList.addAll(detailPOList.stream().map(
                            detailPO -> WdDeliveryFulfillDetailPO.builder()
                                    .id(detailPO.getId())
                                    .acceptQty(BigDecimal.ZERO)
                                    .build()
                    ).collect(Collectors.toList()));
                } else {
                    AtomicDouble acceptQty = new AtomicDouble(acceptQtySum.doubleValue());
                    detailPOList.forEach(detailPO -> {
                        BigDecimal shipQty = detailPO.getShipQty();
                        if (acceptQty.get() == 0) {
                            return;
                        }
                        BigDecimal retailAcceptQty = BigDecimal.valueOf(acceptQty.get());
                        if (shipQty.compareTo(retailAcceptQty) >= 0) {
                            detailPO.setAcceptQty(retailAcceptQty);
                            acceptQty.addAndGet(-retailAcceptQty.doubleValue());
                        } else {
                            detailPO.setAcceptQty(shipQty);
                            acceptQty.addAndGet(-shipQty.doubleValue());
                        }
                        updateList.add(WdDeliveryFulfillDetailPO.builder()
                                .id(detailPO.getId())
                                .acceptQty(detailPO.getAcceptQty())
                                .build());
                    });
                }
            });
            OpInfo opInfo = new OpInfo();
            updateList.forEach(item -> {
                item.setUpdateCode(opInfo.getOperatorCode());
                item.setUpdateName(opInfo.getOperatorName());
                item.setUpdateTime(opInfo.getOperateTime());
            });
            iWdDeliveryFulfillDetailRepository.updateBatchById(updateList);
        } catch (Exception e) {
            Logs.error("配送单 {} 收货数量履行计算报错", acceptBillPO.getBillNo(), e);
        }
    }

    public void calDeliveryOrderAcceptQty(String billNo, OpInfo opInfo) {
        try {
            List<WdDeliveryFulfillDetailPO> wdDeliveryFulfillDetailPOList = iWdDeliveryFulfillDetailRepository.queryFulfillDetailListByBillNo(billNo);
            List<WdDeliveryBillDetailPO> wdDeliveryBillDetailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(billNo);
            Map<Long, WdDeliveryBillDetailPO> detailMap = wdDeliveryBillDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));
            Map<Long, Double> acceptQtyMap = wdDeliveryFulfillDetailPOList.stream()
                    .collect(Collectors.groupingBy(WdDeliveryFulfillDetailPO::getInsideId, Collectors.summingDouble(item -> MoneyUtil.bigDecimal2Double(item.getAcceptQty()))));
            List<WdDeliveryBillDetailPO> updateList = new ArrayList<>();
            for (Long insideId : acceptQtyMap.keySet()) {
                WdDeliveryBillDetailPO wdDeliveryBillDetailPO = detailMap.get(insideId);
                Double acceptQtySum = acceptQtyMap.get(insideId);
                WdDeliveryBillDetailPO build = WdDeliveryBillDetailPO.builder()
                        .id(wdDeliveryBillDetailPO.getId())
                        .acceptQty(BigDecimal.valueOf(acceptQtySum))
                        .build();
                build.setUpdateCode(opInfo.getOperatorCode());
                build.setUpdateName(opInfo.getOperatorName());
                updateList.add(build);
            }
            iWdDeliveryBillDetailRepository.updateBatchById(updateList);
        } catch (Exception e) {
            Logs.error("配送单 {} 收货数量计算报错", billNo, e);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.SHIP_ACCEPT_COMPLETE.getSign());
    }
}
