package com.meta.supplychain.wds.export.processor;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.wds.ShiDiffConvert;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.resp.ShipDiffExcelView;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.domain.delivery.IShipDiffDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 差异处理单导出处理器
 */
@Slf4j
@Component
@ExportSpecification(code = "ShipDiffExportProcessor", name = "差异处理单导出", desc = "差异处理单导出")
public class ShipDiffExportProcessor implements ExportProcessor<WdShipAccDiffBillQueryReq,WdShipAccDiffBillResp, ShipDiffExcelView> {


    @Override
    public Integer totalCount(BizUser bizUser, WdShipAccDiffBillQueryReq wdShipAccDiffBillQueryReq) throws BizException {
        IShipDiffDomainService shipDiffDomainService = SpringContextUtil.getApplicationContext().getBean(IShipDiffDomainService.class);
        PageResult<WdShipAccDiffBillResp> pageResult = shipDiffDomainService.queryShipDiffBillList(wdShipAccDiffBillQueryReq);
        return (int) pageResult.getTotal();
    }

    @Override
    public List<WdShipAccDiffBillResp> queryData(BizUser user, WdShipAccDiffBillQueryReq wdShipAccDiffBillQueryReq, BizExportPage bizExportPage) throws BizException {
        IShipDiffDomainService shipDiffDomainService = SpringContextUtil.getApplicationContext().getBean(IShipDiffDomainService.class);
        wdShipAccDiffBillQueryReq.setPageSize(bizExportPage.getSize().longValue());
        wdShipAccDiffBillQueryReq.setCurrent(bizExportPage.getNo().longValue());
        PageResult<WdShipAccDiffBillResp> pageResult = shipDiffDomainService.queryShipDiffBillList(wdShipAccDiffBillQueryReq);
        return pageResult.getRows();
    }

    @Override
    public List<ShipDiffExcelView> convert(BizUser user, WdShipAccDiffBillQueryReq wdShipAccDiffBillQueryReq, List<WdShipAccDiffBillResp> wdShipAccDiffBillResps) throws BizException {
        List<ShipDiffExcelView> shipDiffExcelViews = ShiDiffConvert.INSTANCE.convertToExcelViewList(wdShipAccDiffBillResps);
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_WDS_SPPS_VIEW_PRICE_BUTTON),user.getBizUserId());
        Logs.info("用户{}是否拥有查看价格权限{}",user.getBizUserId(),showPriceFlag);
        if (Boolean.FALSE.equals(showPriceFlag)){
            shipDiffExcelViews.forEach(shipExcelView -> {
                shipExcelView.setTotalDiffTaxMoney(SysConstants.ENCRYPT);
            });
        }
        return shipDiffExcelViews;
    }
}