package com.meta.supplychain.wds.export.processor;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.wds.ShipOrderConvert;
import com.meta.supplychain.entity.dto.wds.req.QueryShipReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryShipBillResp;
import com.meta.supplychain.entity.dto.wds.resp.ShipBillExcelView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.application.intf.IShipOrderApplication;

import java.util.List;
import java.util.stream.Collectors;

@ExportSpecification(code = "ShipBillExportProcessor", name = "商品配送单列表导出", executeType = ExecuteType.CLUSTER)
public class ShipBillExportProcessor implements ExportProcessor<QueryShipReq, QueryShipBillResp, ShipBillExcelView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryShipReq shipBillReq) throws BizException {
        IShipOrderApplication shipOrderService = SpringContextUtil.getApplicationContext().getBean(IShipOrderApplication.class);
        PageResult<QueryShipBillResp> pageResult = shipOrderService.queryShipOrderListByPage(shipBillReq);
        return (int)pageResult.getTotal();
    }

    @Override
    public List<QueryShipBillResp> queryData(BizUser user, QueryShipReq shipBillReq, BizExportPage bizExportPage) throws BizException {
        IShipOrderApplication shipOrderService = SpringContextUtil.getApplicationContext().getBean(IShipOrderApplication.class);
        shipBillReq.setCurrent(Long.valueOf(bizExportPage.getNo()));
        shipBillReq.setPageSize(Long.valueOf(bizExportPage.getSize()));
        return shipOrderService.queryShipOrderListByPage(shipBillReq).getRows();
    }

    @Override
    public List<ShipBillExcelView> convert(BizUser user, QueryShipReq shipBillReq, List<QueryShipBillResp> shipBillDTOList) throws BizException {
        List<ShipBillExcelView> shipExcelViewList = shipBillDTOList.stream().map(ShipOrderConvert.INSTANCE::shipBillVO2View).collect(Collectors.toList());
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_WDS_SPPS_VIEW_PRICE_BUTTON),user.getBizUserId());
        Logs.info("用户{}是否拥有查看价格权限{}",user.getBizUserId(),showPriceFlag);
        if (Boolean.FALSE.equals(showPriceFlag)){
            shipExcelViewList.forEach(shipExcelView -> {
                shipExcelView.setTotalShipTaxMoney(SysConstants.ENCRYPT);
                shipExcelView.setTotalShipTax(SysConstants.ENCRYPT);
            });
        }
        return shipExcelViewList;
    }
}
