package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.wds.WDAcceptDiffOwnerModeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.wds.domain.delivery.IShipDiffDomainService;
import com.meta.supplychain.wds.util.ShipAcceptBillConvertUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配送验收差异处理
 */
@Service
@RequiredArgsConstructor
public class ChainAcceptDiffBuildServiceImpl extends AbstractChainOrderEventServiceImpl {

    private final IWdShipAcceptBatchDetailRepository shipAcceptBatchDetailRepository;
    private final IWdShipAccDiffBillRepository shipAccDiffBillRepository;
    private final IWdShipAccDiffBatchDetailRepository shipAccDiffBatchDetailRepository;
    private final IShipDiffDomainService diffDomainService;
    private final IShipBillRepository shipBillRepository;

    @Override
    public void executeEvent(String data) {
        EventMsgBaseDTO<WdShipAcceptBillPO> billBaseDto = convertParam(data, WdShipAcceptBillPO.class);
        WdShipAcceptBillPO acceptBillPO = billBaseDto.getData();
        Logs.info("配送验收 单号 {} 开始差异处理", acceptBillPO.getBillNo());
        try {
            List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOList = shipAcceptBatchDetailRepository.queryByBillNo(acceptBillPO.getBillNo());
            acceptBatchDetailPOList.forEach(e -> e.setDiffQty(e.getShipQty().subtract(e.getAcceptQty())));
            List<WdShipAcceptBatchDetailPO> diffGoodsList = acceptBatchDetailPOList.stream()
                    .filter(e -> (e.getShipQty().subtract(e.getAcceptQty())).compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            Logs.info("配送验收 单号 {} 差异行数量 {}", acceptBillPO.getBillNo(), diffGoodsList.size());
            if (CollectionUtils.isNotEmpty(diffGoodsList)) {

                WdShipAccDiffBillPO shipAccDiffBillPO = ShipAcceptBillConvertUtil.convertDiffFromAcceptBill(acceptBillPO);
                shipAccDiffBillPO.setUpdateCode(acceptBillPO.getApproveManCode());
                shipAccDiffBillPO.setUpdateName(acceptBillPO.getApproveManName());
                shipAccDiffBillPO.setUpdateTime(LocalDateTime.now());
                shipAccDiffBillPO.setCreateCode(acceptBillPO.getApproveManCode());
                shipAccDiffBillPO.setCreateName(acceptBillPO.getApproveManName());
                shipAccDiffBillPO.setCreateTime(LocalDateTime.now());
                shipAccDiffBillPO.setAccRemark(acceptBillPO.getRemarkAccept());
                String shipBillNo = acceptBillPO.getShipBillNo();
                if (StringUtils.isNotBlank(shipBillNo)) {
                    ShipBillPO shipBillPO = shipBillRepository.queryShipBillPO(shipBillNo);
                    shipAccDiffBillPO.setShipTime(shipBillPO.getShipTime());
                }
                List<WdShipAccDiffBatchDetailPO> wdRefundAcceptBatchDetailPOS = ShipAcceptBillConvertUtil.convertDiffDetailFromAcceptDetail(shipAccDiffBillPO, diffGoodsList);

                shipAccDiffBillPO.setTotalDiffQty(wdRefundAcceptBatchDetailPOS.stream().map(WdShipAccDiffBatchDetailPO::getDiffQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                BigDecimal totalDiffTaxMoney = wdRefundAcceptBatchDetailPOS.stream().map(WdShipAccDiffBatchDetailPO::getDiffTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                shipAccDiffBillPO.setTotalDiffTaxMoney(MoneyUtil.round2HalfUp(totalDiffTaxMoney));
                BigDecimal totalDiffTax = wdRefundAcceptBatchDetailPOS.stream().map(WdShipAccDiffBatchDetailPO::getDiffTax).reduce(BigDecimal.ZERO, BigDecimal::add);
                shipAccDiffBillPO.setTotalDiffTax(MoneyUtil.round2HalfUp(totalDiffTax));

                //  差异同步库存 POST
                diffDomainService.convertStockReq(shipAccDiffBillPO, wdRefundAcceptBatchDetailPOS, CommonOperateEnum.POST, WDAcceptDiffOwnerModeEnum.OWNER_MODE_RECEIVE_STORE);

                shipAccDiffBillRepository.save(shipAccDiffBillPO);
                shipAccDiffBatchDetailRepository.saveBatch(wdRefundAcceptBatchDetailPOS);
            }
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("验收单 {} 保存差异失败 ", acceptBillPO.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.DIFF_BUILD.getSign());
    }
}
