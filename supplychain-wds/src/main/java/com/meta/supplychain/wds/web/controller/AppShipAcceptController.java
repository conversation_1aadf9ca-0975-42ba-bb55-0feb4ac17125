package com.meta.supplychain.wds.web.controller;

import cn.linkkids.ageiport.client.AgeiTaskClient;
import cn.linkkids.ageiport.params.ExportDataParams;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.lock.annotation.RequestLock;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.meta.supplychain.entity.dto.wds.req.*;
import com.meta.supplychain.entity.dto.wds.resp.*;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.application.IWdsApplicationManager;
import com.meta.supplychain.wds.export.processor.ShipAcceptExportProcessor;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 配送验收管理
 */
@Tag(name = "配送验收管理 APP端")
@RestController
@RequestMapping("${unit-deploy.prefix-main:}/ship/accept/app/")
@RequiredArgsConstructor
@Validated
public class AppShipAcceptController {

    @Resource
    private IWdsApplicationManager orderWDApplicationManager;


    /**
     * 分页查询配送单列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询配送单列表 验收用")
    @PostMapping("/queryShip")
    public PageResult<QueryShipBillForAcceptResp> queryShipForAccept(@RequestBody @Valid QueryShipReq query) {
        return orderWDApplicationManager.shipAcceptApplication().queryShipForAccept(query);
    }

    @PostMapping("/acceptDetail")
    @Operation(summary = "查询配送验收明细详情 验收用", description = "查询配送验收明细详情 验收用 批量查询")
    public List<WdShipAcceptBatchDetailForAcceptResp> acceptDetail(@RequestBody @Valid AppQueryShipDetailForAcceptReq query) {
        return query.getShipBillNoList().stream()
                .map(shipBillNo -> orderWDApplicationManager.shipAcceptApplication().acceptDetail(shipBillNo))
                .collect(Collectors.toList());
    }

    @PostMapping("/createBatch")
    @Operation(summary = "合并验收配送单", description = "合并验收配送单")
    public WdShipBatchAcceptBillCreateResp acceptBatch(@NotNull(message = "参数不能为空！") @Valid @RequestBody WdShipBatchAcceptBillCreateReq modifyParams) {
        return orderWDApplicationManager.shipAcceptApplication().acceptBatch(modifyParams);
    }

}
