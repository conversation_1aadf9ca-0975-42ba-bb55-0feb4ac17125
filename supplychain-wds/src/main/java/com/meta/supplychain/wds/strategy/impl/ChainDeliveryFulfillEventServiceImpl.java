package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.trace.annotation.TraceId;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.wds.req.FulfillDeliveryInfoRequest;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.enums.wds.WDShipSourceEnum;
import com.meta.supplychain.enums.wds.WDShipTypeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.wds.domain.delivery.IDeliveryOrderDomainService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 执行配送发货履行配送订单
 */
@Service
public class ChainDeliveryFulfillEventServiceImpl extends AbstractChainOrderEventServiceImpl {

    @Resource
    private IShipBillRepository shipOrderRepository;

    @Resource
    private IShipBatchDetailRepository shipBatchDetailRepository;

    @Resource
    public IDeliveryOrderDomainService deliveryOrdeDomainService;

    @Resource
    IWdDeliveryBillDetailRepository IWdDeliveryBillDetailRepository;

    @Resource
    public IWdDeliveryFulfillDetailRepository dDeliveryFulfillDetailRepository;

    @Resource
    IWdDeliveryDetailPickRefRepository deliveryDetailPickRefRepository;

    @Resource
    private IWdDeliveryBillRepository iWdDeliveryBillRepository;

    @Resource
    private IRefundAcceptBillRepository iRefundAcceptBillRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceId
    public void executeEvent(String data) {
        EventMsgBaseDTO<ShipBillPO> billBaseDto = convertParam(data, ShipBillPO.class);
        try {
            ShipBillPO shipBill = billBaseDto.getData();
            if (!needExecuteEvent(billBaseDto)) {
                //是否需要处理事件
                return;
            }
            ShipBillPO shipBillEvent = shipOrderRepository.queryShipBillPOWithValid(shipBill.getBillNo());
            List<ShipBatchDetailPO> shipBatchDetailPOList = shipBatchDetailRepository.queryShipBatchDetailList(shipBillEvent.getBillNo());
            Logs.info("配送发货单【{}】，开始处理履行记录", shipBillEvent.getBillNo());
            //  配送订单履行记录
            String srcBillNo = shipBillEvent.getSrcBillNo();
            if (StringUtils.isBlank(srcBillNo)) {
                Logs.info("配送发货单【{}】，srcBillNo 为空 不履行", shipBillEvent.getBillNo());
                return;
            }
            // 配送单来源 1波次拣货 2商品配送 3差异处理 4退配收货
            Integer billSource = shipBillEvent.getBillSource();
            WDShipSourceEnum wdShipSourceEnum = StandardEnum.codeOf(WDShipSourceEnum.class, billSource);
            // 查询配送订单

            // 查询配送订单明细
            if (Objects.nonNull(wdShipSourceEnum) && WDShipSourceEnum.WAVE_PICK.getCode().equals(billSource)) {
                // 此时 srcBillNo 是 pickBillNo 可能涉及多个 配送订单
                // 此处仅做 是否终止的判断  仅拣货单需要处理终止逻辑
                List<WdDeliveryBillDetailPickRefPO> pickRefPOList = deliveryDetailPickRefRepository.queryByPickBillNo(srcBillNo);
                if (CollectionUtils.isNotEmpty(pickRefPOList)) {
                    // 按配送订单分组
                    Map<String, List<WdDeliveryBillDetailPickRefPO>> deliveryBillMap = pickRefPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPickRefPO::getBillNo));
                    // 配送订单明细
                    // 终止查询
                    List<WdDeliveryBillDetailPickRefPO> shipDeliveryDetail = deliveryDetailPickRefRepository.getShipDeliveryDetail(new ArrayList<>(deliveryBillMap.keySet()));
                    Map<String, List<WdDeliveryBillDetailPickRefPO>> shipDeliveryMap = shipDeliveryDetail.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPickRefPO::getBillNo));
                    List<FulfillDeliveryInfoRequest> requestList = new ArrayList<>();
                    deliveryBillMap.keySet().forEach(billNo -> {
                        List<WdDeliveryBillDetailPickRefPO> shipDeliveryDetails = shipDeliveryMap.get(billNo);
                        Optional<WdDeliveryBillDetailPickRefPO> any = shipDeliveryDetails.stream().filter(item -> !WDBillStatusEnum.PICK_STATUS_3.getStatus().equals(item.getStatus()))
                                .findAny();
                        // 判断是否终止 只要包含拣货单不是已发货的 则为发货中
                        boolean shipping = any.isPresent();
                        FulfillDeliveryInfoRequest deliveryInfoRequest = FulfillDeliveryInfoRequest.builder()
                                .endFlag(!shipping ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                                .deliveryFlag(shipping ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                                .billNo(billNo)
                                .build();
                        requestList.add(deliveryInfoRequest);
                    });
                    List<WdDeliveryFulfillDetailPO> preFulfillDetailList = dDeliveryFulfillDetailRepository.queryPreFulfillDetailList(shipBillEvent.getBillNo());
                    Map<String, List<FulfillDeliveryInfoRequest.GoodsLine>> reqGoodsMap = convertPickDeliveryGoods(preFulfillDetailList);
                    //  处理 履行填充
                    requestList.forEach(request -> {
                        // shipBatchDetailPOList 按 skuCode + skuType 分组 填充 涉及的 配送订单商品
                        List<FulfillDeliveryInfoRequest.GoodsLine> goodsLines = reqGoodsMap.get(request.getBillNo());
                        request.setItems(goodsLines);
                    });
                    Logs.info("FulfillDelivery:{}", requestList);
                    //触发履行保存
                    deliveryOrdeDomainService.saveFulfillDeliveryInfo(requestList);
                    List<WdDeliveryBillPO> wdDeliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(new ArrayList<>(deliveryBillMap.keySet()));
                    //触发终止 todo 走事件 触发终止逻辑
                    wdDeliveryBillPOS.forEach(deliveryOrdeDomainService::endDelivery);
                    // 更新发货单 配送订单号字段
                    updateShipDeliveryBillNo(shipBillEvent.getId(), String.join(",", deliveryBillMap.keySet()));
                }
            } else {
                Integer billType = shipBillEvent.getBillType();
                if (YesOrNoEnum.YES.getCode().equals(shipBillEvent.getReversalBillSign()) || WDShipTypeEnum.DIFF_AUDIT.getCode().equals(billType)) {
                    //  冲红不履行   差异不履行
                } else {
                    if (WDShipTypeEnum.WH_SHIP.getCode().equals(billType)) {
                        // 此时 srcBillNo 是 配送订单号
                        // 新增履行明细记录
                        FulfillDeliveryInfoRequest deliveryInfoRequest = FulfillDeliveryInfoRequest.builder()
                                .billNo(srcBillNo)
                                .items(convertDeliveryGoodsInfo(shipBatchDetailPOList, shipBillEvent))
                                .build();
                        // 触发履行逻辑
                        deliveryOrdeDomainService.fulfillDeliveryInfo(deliveryInfoRequest);
                        // 更新发货单 配送订单号字段
                        updateShipDeliveryBillNo(shipBillEvent.getId(), srcBillNo);
                    } else {
                        //  退货收货 找配送订单 没有不履行
                        // 此时 srcBillNo 是 退货收货单号
                        WdRefundAcceptBillPO byBillNo = iRefundAcceptBillRepository.getByBillNo(srcBillNo);
                        String deliveryBillNo = byBillNo.getDeliveryBillNo();
                        // 新增履行明细记录
                        FulfillDeliveryInfoRequest deliveryInfoRequest = FulfillDeliveryInfoRequest.builder()
                                .billNo(deliveryBillNo)
                                .items(convertDeliveryGoodsInfo(shipBatchDetailPOList, shipBillEvent))
                                .build();
                        // 触发履行逻辑
                        deliveryOrdeDomainService.fulfillDeliveryInfo(deliveryInfoRequest);
                        // 更新发货单 配送订单号字段
                        updateShipDeliveryBillNo(shipBillEvent.getId(), deliveryBillNo);
                    }
                }
            }
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("配送订单发货履行，事件处理异常：{}", billBaseDto.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    public void updateShipDeliveryBillNo(Long id, String deliveryBillNo) {
        boolean update = shipOrderRepository.lambdaUpdate()
                .eq(ShipBillPO::getId, id)
                .eq(ShipBillPO::getDeliveryBillNo, "")
                .set(ShipBillPO::getDeliveryBillNo, deliveryBillNo)
                .update();
        if (!update) {
            shipOrderRepository.lambdaUpdate()
                    .eq(ShipBillPO::getId, id)
                    .ne(ShipBillPO::getDeliveryBillNo, "")
                    .setSql(String.format(" `delivery_bill_no` = concat(`delivery_bill_no`,'" + SysConstants.COMMA_DELIMITER + "%s') ", deliveryBillNo))
                    .update();
        }
    }


    public Map<String, List<FulfillDeliveryInfoRequest.GoodsLine>> convertPickDeliveryGoods(List<WdDeliveryFulfillDetailPO> preFulfillDetailList) {
        // 发货明细待分摊数量
        Map<Long, BigDecimal> shipQtyRemainMap = preFulfillDetailList.stream().collect(Collectors.toMap(WdDeliveryFulfillDetailPO::getShipInsideId, WdDeliveryFulfillDetailPO::getShipQty, (v1, v2) -> v1));
        // 拣货明细待分摊数量 并且过滤掉拣货数量为空的
        Map<Long, BigDecimal> pickQtyRemainMap = preFulfillDetailList.stream().filter(detail -> detail.getPickQty() != null).collect(Collectors.toMap(WdDeliveryFulfillDetailPO::getId, WdDeliveryFulfillDetailPO::getPickQty, (v1, v2) -> v1));
        List<FulfillDeliveryInfoRequest.GoodsLine> goodsLineList = new ArrayList<>();
        for (WdDeliveryFulfillDetailPO preFulfillDetail : preFulfillDetailList) {
            //如果拣货行已分摊完 或者 发货行已分摊完都跳过
            BigDecimal remainShipQty = shipQtyRemainMap.get(preFulfillDetail.getShipInsideId());
            BigDecimal remainPickQty = pickQtyRemainMap.get(preFulfillDetail.getId());
            if (remainShipQty.compareTo(BigDecimal.ZERO) <= 0 || remainPickQty.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //如果pickrefid 不为空，则为订单价拆行逻辑，价格一致才执行分摊
            if (preFulfillDetail.getPickRefId() != null && preFulfillDetail.getDeliveryPrice().compareTo(preFulfillDetail.getShipPrice()) != 0) {
                continue;
            }
            //还有剩余的拣货数量和发货数量，则按剩余拣货/剩余发货数量取小履行
            BigDecimal fulfillQty = remainPickQty.min(remainShipQty);
            FulfillDeliveryInfoRequest.GoodsLine build = FulfillDeliveryInfoRequest.GoodsLine
                    .builder()
                    .billNo(preFulfillDetail.getBillNo())
                    .insideId(preFulfillDetail.getInsideId())
                    .shipQty(fulfillQty)
                    .shipBillNo(preFulfillDetail.getShipBillNo())
                    .shipInsideId(preFulfillDetail.getShipInsideId())
                    .shipCreateTime(preFulfillDetail.getShipCreateTime())
                    .shipCreateCode(preFulfillDetail.getShipCreateCode())
                    .shipCreateName(preFulfillDetail.getShipCreateName())
                    .shipCreateUid(preFulfillDetail.getShipCreateUid())
                    .shipManCode(preFulfillDetail.getShipManCode())
                    .shipManName(preFulfillDetail.getShipManName())
                    .shipTime(preFulfillDetail.getShipTime())
                    .build();
            goodsLineList.add(build);
            // 更新剩余发货数量
            shipQtyRemainMap.put(preFulfillDetail.getShipInsideId(), remainShipQty.subtract(fulfillQty));
            // 更新剩余拣货数量
            pickQtyRemainMap.put(preFulfillDetail.getId(), remainPickQty.subtract(fulfillQty));
        }
        return goodsLineList.stream().collect(Collectors.groupingBy(FulfillDeliveryInfoRequest.GoodsLine::getBillNo));
    }

    /**
     * 组装履行商品明细
     *
     * @param shipBatchDetailPOList
     * @return
     */
    public List<FulfillDeliveryInfoRequest.GoodsLine> convertDeliveryGoodsInfo(List<ShipBatchDetailPO> shipBatchDetailPOList, ShipBillPO shipBillEvent) {
        List<FulfillDeliveryInfoRequest.GoodsLine> result = new ArrayList<>();
        try {
            result = shipBatchDetailPOList.stream().map(item -> {
                return FulfillDeliveryInfoRequest.GoodsLine.builder()
                        .insideId(item.getSrcInsideId())
                        .shipQty(item.getShipQty())
                        .shipBillNo(item.getBillNo())
                        .shipInsideId(item.getInsideId())
                        .shipCreateTime(item.getCreateTime())
                        .shipCreateCode(item.getCreateCode())
                        .shipCreateName(item.getCreateName())
                        .shipCreateUid(item.getCreateUid())
                        .shipManCode(shipBillEvent.getShipManCode())
                        .shipManName(shipBillEvent.getShipManName())
                        .shipTime(shipBillEvent.getShipTime())
                        .build();
            }).collect(Collectors.toList());
        } catch (Exception e) {
            Logs.error("FulfillDeliveryInfoRequest convert error, data:{}", e);
        }
        return result;
    }


    private boolean needExecuteEvent(EventMsgBaseDTO<ShipBillPO> billBaseDto) {
        ShipBillPO shipBillEvent = billBaseDto.getData();
        if (isIdempotent(billBaseDto)) {
            Logs.info("配送单【{}】已处理过", shipBillEvent.getBillNo());
            return false;
        }
        //**只处理仓库配送的发货单
        if (!Objects.equals(shipBillEvent.getBillType(), WDShipTypeEnum.WH_SHIP.getCode())) {
            Logs.info("配送发货单【{}】，非仓库配送类型，不处理履行", shipBillEvent.getBillNo());
            return false;
        }
        //**只处理有来源单据的发货单
        if (Objects.isNull(shipBillEvent.getSrcBillNo())) {
            Logs.info("配送发货单【{}】，来源单据为空，不处理履行", shipBillEvent.getBillNo());
            return false;
        }
        return true;
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.DELIVERY_FULFILL.getSign());
    }

}
