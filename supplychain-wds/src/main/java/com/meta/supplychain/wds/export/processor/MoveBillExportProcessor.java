package com.meta.supplychain.wds.export.processor;

import cn.linkkids.framework.croods.common.PageResult;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.meta.supplychain.convert.wds.MoveOrderConvert;
import com.meta.supplychain.entity.dto.wds.MoveBillDTO;
import com.meta.supplychain.entity.dto.wds.req.QueryMoveBillReq;
import com.meta.supplychain.entity.dto.wds.resp.MoveBillExcelView;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.application.intf.IMoveOrderApplication;

import java.util.List;
import java.util.stream.Collectors;

@ExportSpecification(code = "MoveBillExportProcessor", name = "商品移位列表导出", executeType = ExecuteType.CLUSTER)
public class MoveBillExportProcessor implements ExportProcessor<QueryMoveBillReq, MoveBillDTO, MoveBillExcelView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryMoveBillReq moveBillReq) throws BizException {
        IMoveOrderApplication moveOrderService = SpringContextUtil.getApplicationContext().getBean(IMoveOrderApplication.class);
        PageResult<MoveBillDTO> pageResult = moveOrderService.queryMoveOrderListByPage(moveBillReq);
        return (int)pageResult.getTotal();
    }

    @Override
    public List<MoveBillDTO> queryData(BizUser user, QueryMoveBillReq moveBillReq, BizExportPage bizExportPage) throws BizException {
        IMoveOrderApplication moveOrderService = SpringContextUtil.getApplicationContext().getBean(IMoveOrderApplication.class);
        moveBillReq.setCurrent(Long.valueOf(bizExportPage.getNo()));
        moveBillReq.setPageSize(Long.valueOf(bizExportPage.getSize()));
        return moveOrderService.queryMoveOrderListByPage(moveBillReq).getRows();
    }

    @Override
    public List<MoveBillExcelView> convert(BizUser user, QueryMoveBillReq moveBillReq, List<MoveBillDTO> moveBillDTOList) throws BizException {
        return moveBillDTOList.stream().map(MoveOrderConvert.INSTANCE::moveBillVO2View).collect(Collectors.toList());
    }
}
