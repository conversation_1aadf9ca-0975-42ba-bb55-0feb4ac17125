package com.meta.supplychain.wds.strategy.impl;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.trace.annotation.TraceId;
import com.meta.supplychain.common.component.service.impl.AbstractChainOrderEventServiceImpl;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.wds.WdShipAcceptSumDTO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptSumPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOptTypeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderBillStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptSumRepository;
import com.meta.supplychain.wds.domain.delivery.impl.DeliveryOrderAuditService;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 配送批量验收事件
 *
 */
@Service
@RequiredArgsConstructor
public class ChainBatchAcceptEventServiceImpl extends AbstractChainOrderEventServiceImpl {

    private final IWdShipAcceptSumRepository shipAcceptSumRepository;


    @Resource
     ICommonStockService iCommonStockService;

    @Resource
    DeliveryOrderAuditService deliveryOrderAuditService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceId
    public void executeEvent(String data) {
        EventMsgBaseDTO<WdShipAcceptSumDTO> billBaseDto = convertParam(data, WdShipAcceptSumDTO.class);
        try {
            WdShipAcceptSumDTO sumDTO = billBaseDto.getData();
            if (sumDTO != null) {
                List<WdShipAcceptSumPO> wdShipAcceptSumPOList = sumDTO.getWdShipAcceptSumPOList();
                if (CollectionUtils.isNotEmpty(wdShipAcceptSumPOList)) {
                    WdShipAcceptSumPO wdShipAcceptSumPO = wdShipAcceptSumPOList.get(0);
                    List<WdShipAcceptSumPO> list = shipAcceptSumRepository.lambdaQuery()
                            .eq(WdShipAcceptSumPO::getUnionBillNo, wdShipAcceptSumPO.getUnionBillNo())
                            .list();
                    if (CollectionUtils.isEmpty(list)) {
                        shipAcceptSumRepository.saveBatch(wdShipAcceptSumPOList);
                    }
                }
            }
            logPersistence(true, billBaseDto, SysConstants.SUCCESS);
        } catch (Exception exception) {
            Logs.error("配送订单发货履行，事件处理异常：{}", billBaseDto.getBillNo(), exception);
            String fullStackTrace = ExceptionUtils.getStackTrace(exception);
            logPersistence(false, billBaseDto, fullStackTrace);
        }
    }

    @Override
    public String supportType() {
        return String.join(SysConstants.COLON_DELIMITER, channel().getCode(), BillActionTypeEnum.SupportSign.SHIP_BATCH_ACCEPT.getSign());
    }

}
