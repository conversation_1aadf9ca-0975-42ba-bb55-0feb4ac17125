package com.meta.supplychain.wds.export.processor;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.wds.RefundAcceptConvert;
import com.meta.supplychain.entity.dto.wds.req.QueryWdRefundAcceptReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryWdRefundAcceptBillResp;
import com.meta.supplychain.entity.dto.wds.resp.QueryWdRefundAcceptExcelView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.domain.delivery.IRefundAcceptDomainService;


import java.util.List;

@ExportSpecification(code = "RefundAcceptExportProcessor", name = "退库收货导出")
public class RefundAcceptExportProcessor implements ExportProcessor<QueryWdRefundAcceptReq, QueryWdRefundAcceptBillResp, QueryWdRefundAcceptExcelView> {

    @Override
    public Integer totalCount(BizUser bizUser, QueryWdRefundAcceptReq query) throws BizException {
        IRefundAcceptDomainService refundAcceptDomainService = SpringContextUtil.getApplicationContext().getBean(IRefundAcceptDomainService.class);
        PageResult<QueryWdRefundAcceptBillResp> result = refundAcceptDomainService.queryRefundAcceptList(query);
        return (int) result.getTotal();
    }

    @Override
    public List<QueryWdRefundAcceptBillResp> queryData(BizUser user, QueryWdRefundAcceptReq query, BizExportPage bizExportPage) throws BizException {
        IRefundAcceptDomainService refundAcceptDomainService = SpringContextUtil.getApplicationContext().getBean(IRefundAcceptDomainService.class);
        query.setPageSize(bizExportPage.getSize().longValue());
        query.setCurrent(bizExportPage.getNo().longValue());
        PageResult<QueryWdRefundAcceptBillResp> result = refundAcceptDomainService.queryRefundAcceptList(query);
        return result.getRows();
    }

    @Override
    public List<QueryWdRefundAcceptExcelView> convert(BizUser user, QueryWdRefundAcceptReq query, List<QueryWdRefundAcceptBillResp> data) throws BizException {
        List<QueryWdRefundAcceptExcelView> views = RefundAcceptConvert.INSTANCE.convertToQueryWdRefundAcceptExcelViewList(data);
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_WDS_SPPS_VIEW_PRICE_BUTTON),user.getBizUserId());
        Logs.info("用户{}是否拥有查看价格权限{}",user.getBizUserId(),showPriceFlag);
        if (Boolean.FALSE.equals(showPriceFlag)){
            views.forEach(shipExcelView -> {
                shipExcelView.setTotalAcceptTax(SysConstants.ENCRYPT);
                shipExcelView.setTotalAcceptTaxMoney(SysConstants.ENCRYPT);
            });
        }
        return views;
    }
}