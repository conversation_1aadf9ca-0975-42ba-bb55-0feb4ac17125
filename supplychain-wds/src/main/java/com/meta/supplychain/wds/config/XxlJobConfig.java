package com.meta.supplychain.wds.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @title XxlJobSpringExecutor
 * @description
 * @create 2024-09-10 10:54
 **/
@Configuration
public class XxlJobConfig {
    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Value("${xxl.job.executor.wds.appname}")
    private String appname;

    @Value("${xxl.job.executor.wds.address}")
    private String address;

    @Value("${xxl.job.executor.wds.ip}")
    private String ip;

    @Value("${xxl.job.executor.wds.port}")
    private int port;

    @Value("${xxl.job.executor.wds.logpath}")
    private String logPath;

    @Value("${xxl.job.executor.wds.logretentiondays}")
    private int logRetentionDays;

    // XxlJobSpringExecutor创建完之后会调用初始化方法，对Spring容器中Bean上方法是否添加@XxlJob注解
    // 将其封装为IJobHandler然后注册
    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }
}
