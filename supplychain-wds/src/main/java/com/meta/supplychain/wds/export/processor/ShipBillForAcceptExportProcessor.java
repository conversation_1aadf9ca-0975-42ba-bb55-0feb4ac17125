package com.meta.supplychain.wds.export.processor;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.wds.ShipAcceptConvert;
import com.meta.supplychain.convert.wds.ShipOrderConvert;
import com.meta.supplychain.entity.dto.wds.req.QueryShipReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryShipBillForAcceptResp;
import com.meta.supplychain.entity.dto.wds.resp.QueryShipBillResp;
import com.meta.supplychain.entity.dto.wds.resp.ShipBillExcelView;
import com.meta.supplychain.entity.dto.wds.resp.ShipBillForAcceptExcelView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.wds.application.intf.IShipAcceptApplication;
import com.meta.supplychain.wds.application.intf.IShipOrderApplication;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@ExportSpecification(code = "ShipBillForAcceptExportProcessor", name = "配送单验收列表导出", executeType = ExecuteType.CLUSTER)
public class ShipBillForAcceptExportProcessor implements ExportProcessor<QueryShipReq, QueryShipBillForAcceptResp, ShipBillForAcceptExcelView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryShipReq shipBillReq) throws BizException {
        IShipAcceptApplication shipOrderService = SpringContextUtil.getApplicationContext().getBean(IShipAcceptApplication.class);
        PageResult<QueryShipBillForAcceptResp> pageResult = shipOrderService.queryShipForAccept(shipBillReq);
        return (int)pageResult.getTotal();
    }

    @Override
    public List<QueryShipBillForAcceptResp> queryData(BizUser user, QueryShipReq shipBillReq, BizExportPage bizExportPage) throws BizException {
        IShipAcceptApplication shipOrderService = SpringContextUtil.getApplicationContext().getBean(IShipAcceptApplication.class);
        PageResult<QueryShipBillForAcceptResp> pageResult = shipOrderService.queryShipForAccept(shipBillReq);
        shipBillReq.setCurrent(Long.valueOf(bizExportPage.getNo()));
        shipBillReq.setPageSize(Long.valueOf(bizExportPage.getSize()));
        return pageResult.getRows();
    }

    @Override
    public List<ShipBillForAcceptExcelView> convert(BizUser user, QueryShipReq shipBillReq, List<QueryShipBillForAcceptResp> shipBillDTOList) throws BizException {
        List<ShipBillForAcceptExcelView> shipExcelViewList = shipBillDTOList.stream().map(ShipAcceptConvert.INSTANCE::shipBillVO2AcceptView).collect(Collectors.toList());
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_WDS_SPPS_VIEW_PRICE_BUTTON),user.getBizUserId());
        Logs.info("用户{}是否拥有查看价格权限{}",user.getBizUserId(),showPriceFlag);
        if (Boolean.FALSE.equals(showPriceFlag)){
            shipExcelViewList.forEach(shipExcelView -> {
                shipExcelView.setTotalShipTaxMoney(SysConstants.ENCRYPT);
            });
        }
        return shipExcelViewList;
    }
}
