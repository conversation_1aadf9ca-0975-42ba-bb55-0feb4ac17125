package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.exception.BizException;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.entity.po.wds.PickBatchDetailPO;
import com.meta.supplychain.entity.po.wds.PickBillPO;
import com.meta.supplychain.enums.goods.MeasurePropertyEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.enums.wds.WDPickQtyContrlEnum;
import com.meta.supplychain.enums.wds.WDSystemParamEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IPickBatchDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IPickOrderRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PickOrderAggregationService {

    @Resource
    private IPickOrderRepository pickOrderRepository;
    @Resource
    private IPickBatchDetailRepository pickBatchDetailRepository;
    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;
    /**
     * 保存拣货明细数据
     * @param pickBillPO 拣货单
     * @param updatePickBatchDetailList 拣货单更新明细
     * @param insertPickBatchDetailList 拣货单新增明细
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean savePickOrderSkus(PickBillPO pickBillPO, List<PickBatchDetailPO> updatePickBatchDetailList,List<PickBatchDetailPO> insertPickBatchDetailList){
        pickOrderRepository.lambdaUpdate().eq(PickBillPO::getBillNo, pickBillPO.getBillNo())
                .eq(PickBillPO::getStatus, WDBillStatusEnum.PICK_STATUS_1.getStatus())
                .set(StringUtils.isNotEmpty(pickBillPO.getRemark()),PickBillPO::getRemark,  pickBillPO.getRemark())
                .set(PickBillPO::getPickTime,  LocalDateTime.now())
                .set(PickBillPO::getPickManCode, pickBillPO.getPickManCode())
                .set(PickBillPO::getPickManName, pickBillPO.getPickManName())
                .set(PickBillPO::getSubmitManCode, pickBillPO.getSubmitManCode())
                .set(PickBillPO::getSubmitManName, pickBillPO.getSubmitManName())
                .set(PickBillPO::getStatus, WDBillStatusEnum.PICK_STATUS_2.getStatus())
                .update();
        pickBatchDetailRepository.batchUpdatePickDistInfo(updatePickBatchDetailList);
        return pickBatchDetailRepository.saveBatch(insertPickBatchDetailList);
    }

    /**
     * 校验拣货明细数据
     * @param pickBill 拣货单
     * @param srcPickBatchDetailList 原拣货单明细数据
     * @param updatePickBatchDetailList 更新拣货单明细数据
     * @param insertPickBatchDetailList 新增拣货单明细数据
     */
    public void checkPickOrderSkus(PickBillPO pickBill,List<PickBatchDetailPO> srcPickBatchDetailList, List<PickBatchDetailPO> updatePickBatchDetailList,List<PickBatchDetailPO> insertPickBatchDetailList){
        //拣货数量按参数控制 PICKING_QUANTITY_CONTRO
        Integer pickQtyContrl = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.PICKING_QUANTITY_CONTRO,pickBill.getWhCode());
        if (!WDPickQtyContrlEnum.ALLOW.getCode().equals(pickQtyContrl)){
            //通过stream 将updatePickBatchDetailList 按照insideId分组后再根据pickQty求和
            Map<Long,  BigDecimal> skuInsideIdPickQtyMap = updatePickBatchDetailList.stream().collect(Collectors.groupingBy(PickBatchDetailPO::getInsideId, Collectors.reducing(BigDecimal.ZERO, PickBatchDetailPO::getPickQty, BigDecimal::add)));
            Map<Long,  BigDecimal> insertSkuInsideIdPickQtyMap = insertPickBatchDetailList.stream().collect(Collectors.groupingBy(PickBatchDetailPO::getMainInsideId, Collectors.reducing(BigDecimal.ZERO, PickBatchDetailPO::getPickQty, BigDecimal::add)));
            srcPickBatchDetailList.forEach(sku -> {
                BigDecimal pickQty = skuInsideIdPickQtyMap.getOrDefault(sku.getInsideId(), BigDecimal.ZERO);
                BigDecimal insertPickQty = insertSkuInsideIdPickQtyMap.getOrDefault(sku.getInsideId(), BigDecimal.ZERO);
                if (WDPickQtyContrlEnum.ALLOW_WEIGHT.getCode().equals(pickQtyContrl) && MeasurePropertyEnum.WEIGH.getCode().equals(sku.getUomAttr())){
                    return;
                }
                if (sku.getDistQty().compareTo(pickQty.add(insertPickQty)) < 0){
                    throw new BizException(WDErrorCodeEnum.WD_BIZ_ERROR_003B007.getCode(),WDErrorCodeEnum.WD_BIZ_ERROR_003B007.getErrorMsgFormat(sku.getSkuCode()));
                }
            });

        }
    }
}
