package com.meta.supplychain.demand.purch;

import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.demand.purch.domain.impl.BatchAdjustBillService;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsDeliveryDockLimitQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.purch.UpdateAppointmentQtyDTO;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustGoodsReq;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.metadata.idaas.client.model.LoginUserDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("test")
@SpringBootTest(classes = SupplychainPmsApplication.class)
public class PmsAppointmentDomainServiceTest {

    @Resource
    private IPmsAppointmentDomainService pmsAppointmentDomainService;

    @Resource
    private PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;

    @Resource
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;

    @Resource
    BatchAdjustBillService batchAdjustBillService;

    @Resource
    IWdDeliveryBillRepository deliveryBillRepository;
    @Resource
    IWdDeliveryBillDetailRepository deliveryBillDetailRepository;
    @Before
    public void initTenantId() {
        TenantContext.set(153658);

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setCode("001");
        loginUserDTO.setUid(10149793L);
        loginUserDTO.setName("admin");

        ThreadLocals.setValue("_login_user_idaas", loginUserDTO);
    }

    @Test
    public void testAdjust() {
        try {
            WdDeliveryBillPO wdDeliveryBillPO = deliveryBillRepository.queryDeliveryOrderBillByBillNo("DO022783250626000047");
            List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = deliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
            WdsDeliveryOrderAdjustBaseReq build = WdsDeliveryOrderAdjustBaseReq.builder()
                    .billSource(1)
                    .billNo(wdDeliveryBillPO.getBillNo())
                    .deliverDate(wdDeliveryBillPO.getDeliveryDate().plusDays(1))
                    .detailList(deliveryBillDetailPOList.stream().map(e -> WdsDeliveryOrderAdjustGoodsReq.builder()
                            .insideId(e.getInsideId())
                            .adjustType(0)
                            .adjustQty(BigDecimal.valueOf(3))
                            .build()).collect(Collectors.toList()))
                    .build();
            batchAdjustBillService.adjustBill(Arrays.asList(build), null, new OpInfo());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试查询停靠点可预约信息方法 - 完整参数
     */
    @Test
    public void testListAvailableDockDetail() {
        // 构建包含所有查询条件的PmsDeliveryDockLimitQueryDTO实例
        PmsDeliveryDockLimitQueryDTO queryDto = new PmsDeliveryDockLimitQueryDTO();
        
//        // 设置部门编码
//        queryDto.setDeptCode("001426");
//
//        // 设置停靠点编码
//        queryDto.setDockCode("DOCK001");
        
        // 设置停靠点类型列表
        queryDto.setDockType(Arrays.asList(1, 2)); // 假设1-普通停靠点，2-特殊停靠点
        
        // 设置约束规则
        queryDto.setConstraintRule(1); // 假设1-时间约束
        
        // 设置查询日期范围
        queryDto.setStartDate(LocalDate.of(2024, 1, 1));
        queryDto.setEndDate(LocalDate.of(2024, 1, 31));

        System.out.println(pmsAppointmentDomainService.listAvailableDockDetail(queryDto));
    }

    @Test
    public void test01() {
        System.out.println(pmsAppointmentDomainService.queryAppointmentBillGoodsDetail("PO022814250623000005"));
    }

    @Test
    public void test02() {
        System.out.println(pmsPurchaseOrderRepositoryService.updateAppointmentMark(Arrays.asList("PO022783250607000010",
                "PO022783250617000024",
                "PO022783250617000022",
                "PO022783250611000027",
                "PO022783250610000003",
                "PO022783250610000004",
                "PO022783250617000010",
                "PO022783250617000021",
                "PO022814250623000003",
                "PO022815250623000007",
                "PO022814250623000018",
                "PO022814250623000005",
                "PO022814250623000020",
                "PO022815250624000013",
                "PO022814250623000019",
                "PO022814250624000023")));
    }

    @Test
    public void test03() {
        UpdateAppointmentQtyDTO build = UpdateAppointmentQtyDTO.builder()
                .id(737L)
                .appointmentQty(BigDecimal.ONE)
                .build();
        System.out.println(pmsPurchaseDetailRepositoryService.batchUpdateAppointmentQty(Arrays.asList(build)));
    }
} 