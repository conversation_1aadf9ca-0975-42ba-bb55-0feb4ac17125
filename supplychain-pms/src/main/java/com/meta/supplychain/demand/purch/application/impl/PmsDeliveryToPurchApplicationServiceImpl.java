package com.meta.supplychain.demand.purch.application.impl;

import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.demand.purch.application.intf.IPmsDeliveryToPurchApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.IPmsDeliveryToPurchDomainService;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryDetailReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchSubmitReq;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchResultResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.PurchaseOrderSummaryResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/20 17:11
 **/
@Service
public class PmsDeliveryToPurchApplicationServiceImpl implements IPmsDeliveryToPurchApplicationService {
    @Autowired
    private IPmsDeliveryToPurchDomainService pmsDeliveryToPurchDomainService;


    @Override
    public DeliveryToPurchResultResp generateDeliveryToPurch(DeliveryToPurchReq param) {
        return pmsDeliveryToPurchDomainService.generateDeliveryToPurch(param);
    }

    /**
     * 加载订单生成可转采数据
     *
     * @param param 配送订单
     * @return 转采信息
     */
    @Override
    public DeliveryToPurchResultResp loadingDeliveryGenerateToPurch(DeliveryDetailReq param) {
        return pmsDeliveryToPurchDomainService.loadingDeliveryGenerateToPurch(param);
    }

    /**
     * 保存订单生成可转采数据
     *
     * @param param
     */
    @Override
    public Result<List<String>> saveDeliveryToPurch(DeliveryToPurchSubmitReq param) {
        return pmsDeliveryToPurchDomainService.saveDeliveryToPurch(param);
    }

    /**
     * 预览
     *
     * @param param
     */
    @Override
    public PurchaseOrderSummaryResp preview(DeliveryToPurchSubmitReq param) {
        return pmsDeliveryToPurchDomainService.preview(param);
    }
}
