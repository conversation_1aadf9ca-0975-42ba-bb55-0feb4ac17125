package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import cn.linkkids.framework.croods.sharding.properties.CroodsSharingProperties;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import cn.linkkids.framework.croods.trace.util.TraceIds;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchPlanApplicationService;
import com.meta.supplychain.entity.dto.pms.req.demand.TaskApplyToDemandReq;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订货采购定时任务
 **/
@RestController
@RequestMapping("${unit-deploy.prefix-main:}/job/pmsJob")
public class PmsJobController {
    @Autowired
    private ApplicationContext context;

    @Autowired
    private IPmsApplicationManagerService pmsApplicationManagerService;

    @Autowired
    private PmsPurchPlanApplicationService pmsPurchPlanApplicationService;

    /**
     * 从配置信息中获取全部租户id
     */
    @SuppressWarnings("all")
    private List<String> getAllTenantIdFromConfig() {
        Collection<List<String>> tenantList = Optional.ofNullable(context.getBean(CroodsSharingProperties.class))
                .map(CroodsSharingProperties::getDataSourceRouter)
                .map(CroodsSharingProperties.DataSourceRouter::getConfig)
                .map(Map::values)
                .orElseThrow(() -> new RuntimeException("未查询到多租户配置信息"));

        return tenantList.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 过期采购订单任务
     */
    @PostMapping("/autoExpirePurchBill")
    @XxlJob("autoExpirePurchBill")
    @Operation(summary = "过期采购订单任务")
    @MethodLog("过期采购订单任务")
    public void autoExpirePurchBill() {
        getAllTenantIdFromConfig().forEach(tenantId -> {
            TraceIds.clear();
            TraceIds.random();
            Logs.info("租户 {} 定时过期采购订单任务开始", tenantId);
            expirePurchBill(tenantId);
        });
    }

    /**
     * 过期采购订单
     */
    @PostMapping("/expirePurchBill")
    @Operation(summary = "过期采购订单")
    @MethodLog("过期采购订单")
    public void expirePurchBill(@Validated @NotBlank(message = "_platform_num 不能为空") @RequestParam(value = "_platform_num") String tenantId) {
        TenantContext.clear();
        TenantContext.set(tenantId);
        pmsApplicationManagerService.getPmsPurchBillApplicationService().expirePurchBill();
    }

    /**
     * 过期采购订单任务
     */
    @PostMapping("/autoExpirePurchPlanBill")
    @XxlJob("autoExpirePurchPlanBill")
    @Operation(summary = "过期采购计划单任务")
    @MethodLog("过期采购计划单任务")
    public void autoExpirePurchPlanBill() {
        getAllTenantIdFromConfig().forEach(tenantId -> {
            TraceIds.clear();
            TraceIds.random();
            Logs.info("租户 {} 定时过期采购订单任务开始", tenantId);
            expirePurchPlanBill(tenantId);
        });
    }

    @PostMapping("/expirePurchPlanBill")
    @Operation(summary = "过期采购计划单")
    @MethodLog("过期采购计划单")
    public void expirePurchPlanBill(@Validated @NotBlank(message = "_platform_num 不能为空") @RequestParam(value = "_platform_num") String tenantId) {
        TenantContext.clear();
        TenantContext.set(tenantId);
        pmsPurchPlanApplicationService.expirePurchPlanBill();
    }


    @XxlJob("execDemandJob")
    @MethodLog(level = MethodLog.Level.INFO, value = "合同定时任务")
    public void execContractJob(@RequestParam("tenantId") String tenantId,@RequestParam("purchBatchNo") String purchBatchNo
            ,@RequestParam("deptCode") String deptCode){
        String param = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(param)){
            JSONObject json = JSONObject.parseObject(param);
            //传入的租户号
            tenantId = json.getString("tenantId");
            //传入的采购批次号
            purchBatchNo = json.getString("purchBatchNo");

            //传入的配送部门
            deptCode = json.getString("deptCode");
        }

        TenantContext.clear();
        TenantContext.set(tenantId);

        TaskApplyToDemandReq taskApplyToDemandReq = new TaskApplyToDemandReq();
        taskApplyToDemandReq.setDeptCode(deptCode);
        taskApplyToDemandReq.setPurchBatchNo(purchBatchNo);

        pmsApplicationManagerService.getPmsDemandApplicationService().taskApplyToDemand(taskApplyToDemandReq);
    }
}
