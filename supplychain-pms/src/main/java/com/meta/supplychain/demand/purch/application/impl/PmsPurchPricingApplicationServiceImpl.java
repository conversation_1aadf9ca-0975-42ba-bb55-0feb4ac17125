package com.meta.supplychain.demand.purch.application.impl;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.google.common.collect.Lists;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.PurchaseBillConvert;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchPricingApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchaseOrderDomainService;
import com.meta.supplychain.entity.dto.pms.req.purch.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.*;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.enums.pms.PmsPurchaseBillStatusEnum;
import com.meta.supplychain.util.RedisUtil;
import com.meta.supplychain.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:56
 **/
@Service
public class PmsPurchPricingApplicationServiceImpl implements PmsPurchPricingApplicationService {

    @Resource
    private UserUtil userUtil;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private PmsPurchaseOrderDomainService pmsPurchaseOrderDomainService;

    private static final Long EXPIRE_TIME = 5 * 60 * 1000L;

    @Override
    public Result<List<QueryPurchPricingResp>> queryPurchPricing(QueryPurchPricingReq queryPurchPricingReq) {
        // 获取详情数据
        QueryPurchaseBillReq queryPurchaseBillReq = new QueryPurchaseBillReq();
        queryPurchaseBillReq.setOperatorInfo(userUtil.getOpInfoWithThrow());
        queryPurchaseBillReq.setSupplierCode(Arrays.asList(queryPurchPricingReq.getSupplierCode()));
        queryPurchaseBillReq.setBillNo(queryPurchPricingReq.getBillNo());
        queryPurchaseBillReq.setBillDirection(queryPurchPricingReq.getBillDirection());
        queryPurchaseBillReq.setDirectSign(queryPurchPricingReq.getDirectSign());
        queryPurchaseBillReq.setDeptCode(queryPurchPricingReq.getDeptCodeList());
        queryPurchaseBillReq.setSkuCodeList(queryPurchPricingReq.getSkuCodeList());
        queryPurchaseBillReq.setValidityDateStart(queryPurchPricingReq.getValidityDateStart());
        queryPurchaseBillReq.setValidityDateEnd(queryPurchPricingReq.getValidityDateEnd());
        queryPurchaseBillReq.setDeliverDateStart(queryPurchPricingReq.getDeliverDateStart());
        queryPurchaseBillReq.setDeliverDateEnd(queryPurchPricingReq.getDeliverDateEnd());
        queryPurchaseBillReq.setStatusList(Arrays.asList(PmsPurchaseBillStatusEnum.PURCH_STATUS_AUDITED.getCode()));
        queryPurchaseBillReq.setAppointmentSign(YesOrNoEnum.NO.getCode());
        List<PurchaseBillDetailSumResp> queryDetailList = pmsPurchaseOrderDomainService.queryDetailList(queryPurchaseBillReq);

        // 组装数据
        Map<String, List<PurchaseBillDetailSumResp>> skuMap = queryDetailList.stream().collect(Collectors.groupingBy(PurchaseBillDetailSumResp::getSkuCode));
        List<QueryPurchPricingResp> result = Lists.newArrayList();
        skuMap.forEach((skuCode, list) -> {
            QueryPurchPricingResp queryPurchPricingResp = new QueryPurchPricingResp();
            queryPurchPricingResp.setSkuCode(skuCode);
            List<QueryPurchPricingResp.QueryPurchPricingDetailResp> detailResps = list.stream().map(PurchaseBillConvert.INSTANCE::convertRespToResp).collect(Collectors.toList());
            queryPurchPricingResp.setDetailRespList(detailResps);
            Set<String> billList = list.stream().map(PurchaseBillDetailSumResp::getBillNo).collect(Collectors.toSet());
            queryPurchPricingResp.setBillTotal(billList.size());
            result.add(queryPurchPricingResp);
        });

        return new Result<>(result);
    }

    @Override
    public Result<Void> savePurchPricing(SavePurchPricingReq queryPurchasePlanBillReq) {
        queryPurchasePlanBillReq.setOperatorInfo(userUtil.getOpInfoWithThrow());
        List<String> redisWords = Arrays.asList(TenantContext.get(), "savePlanBill", queryPurchasePlanBillReq.getOperatorInfo().getUserId());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, queryPurchasePlanBillReq.getOperatorInfo().getUserId(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            // 组装数据
            List<PurchaseBillAdjustReq> purchaseBillAdjustReqs = pmsPurchaseOrderDomainService.buildPurchBill4Pricing(queryPurchasePlanBillReq);
            StringBuffer stf = new StringBuffer();
            // 执行操作
            purchaseBillAdjustReqs.forEach(item -> {
                try {
                    pmsPurchaseOrderDomainService.adjustPurchBill4Pricing(item);
                } catch (Exception e) {
                    String errMsg = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_003_B023.getErrorMsg(),
                            new String[]{item.getBillNo(), e.getMessage()}).getMessage();
                    Logs.error("采购核价失败", e);
                    stf.append(errMsg).append(";");
                }
            });
            if (StringUtils.isNotEmpty(stf.toString())) {
                BizExceptions.throwWithMsg(stf.toString());
            }
            return new Result<>();
        } catch (BizException e) {
            Logs.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            Logs.error(e.getMessage(), e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0015);
        } finally {
            // 释放锁
            redisUtil.unlock(redisKey);
        }
        return new Result<>();
    }
}
