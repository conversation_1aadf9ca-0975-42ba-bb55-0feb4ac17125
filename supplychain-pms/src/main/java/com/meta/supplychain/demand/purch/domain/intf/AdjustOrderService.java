package com.meta.supplychain.demand.purch.domain.intf;

import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;

import java.util.List;

/**
 * 配送订单调整
 *
 */
public interface AdjustOrderService<T,C> {


    List<C> checkParam(List<T> reqList) throws Exception;

    List<FranLineAdjustReq> convertFranLineReq(List<C> contextList, OpInfo operatorInfo);

    List<BatchRecordReq> convertStockReq(List<C> contextList);

    void doAdjust(List<C> contextList);

}
