package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.lock.LockManager;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdDeliveryAppointmentStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.convert.pms.PmsAppointmentBillConvert;
import com.meta.supplychain.convert.pms.PmsAppointmentBillGoodsConvert;
import com.meta.supplychain.convert.pms.PmsAppointmentBillPurchConvert;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchaseOrderDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentCloseDateDTO;
import com.meta.supplychain.entity.dto.md.req.deliveryappointment.QueryDeliveryDockLimitReq;
import com.meta.supplychain.entity.dto.md.resp.deliveryappointment.MdDeliveryDockLimitVO;
import com.meta.supplychain.entity.dto.pms.req.appointment.*;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryAbleAppointReq;
import com.meta.supplychain.entity.dto.pms.req.purch.UpdateAppointmentQtyDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.DockTimeStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.PmsAppointmentBillGoodsQueryResultDTO;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailBasicDTO;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillGoodsPO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPurchPO;
import com.meta.supplychain.enums.AppTypeEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockConstraintRuleEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.enums.pms.PmsBookingCategoryEnum;
import com.meta.supplychain.enums.pms.PmsBookingDocumentSourceEnum;
import com.meta.supplychain.enums.pms.PmsBookingMethodEnum;
import com.meta.supplychain.enums.pms.PmsBookingStatusEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDeliveryDockStrategyRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.*;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预约单领域服务实现类
 *
 * <AUTHOR>
 **/
@SuppressWarnings("DuplicatedCode")
@Service
public class PmsAppointmentDomainServiceImpl implements IPmsAppointmentDomainService {
    @Resource
    private IPmsAppointmentBillRepositoryService billRepositoryService;

    @Resource
    private IPmsAppointmentBillPurchRepositoryService purchRepositoryService;

    @Resource
    private IPmsAppointmentBillGoodsRepositoryService goodsRepositoryService;

    @Resource
    private IMdDeliveryDockStrategyRepositoryService dockStrategyRepositoryService;

    @Resource
    private IMdDeliveryAppointmentStrategyDomainService appointmentStrategyDomainService;

    @Resource
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private UserResourceUtil userResourceUtil;

    @Resource
    private LockManager lockManager;

    @Resource
    private IPmsDemandPruchDeliveryRefRepositoryService pmsDemandPruchDeliveryRefRepositoryService;

    @Resource
    private PmsPurchaseOrderRepositoryService  pmsPurchaseOrderRepositoryService;

    /**
     * 供应商预约单单据查询
     */
    @SuppressWarnings("DuplicatedCode")
    @Override
    public PageResult<PmsAppointmentBillStatsDTO> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO) {
        OpInfo optInfo = userUtil.getDeptOpInfoWithThrow();
        if(AppTypeEnum.SCM.verifyByCode(optInfo.getAppType())){
            // scm限制供应商数据权限
            if (StringUtils.isBlank(optInfo.getBusinessCode())) {
                Logs.warn("SCM 员工 {} 未获取到对应供应商数据", optInfo.getUserId());
                return PageResult.ofEmpty();
            }
            queryDTO.setSupplierCodes(Collections.singletonList(optInfo.getBusinessCode()));
        }else if (!optInfo.getOriginDeptFlag()) {
            // rmc用户分管权限
            if (CollectionUtils.isEmpty(optInfo.getManageDeptCodeList())) {
                return PageResult.ofEmpty();
            }
            List<String> deptCodeList = Optional.ofNullable(queryDTO.getDeptCodes()).orElse(new ArrayList<>());
            deptCodeList.retainAll(optInfo.getManageDeptCodeList());
            queryDTO.setDeptCodes(CollectionUtils.isEmpty(deptCodeList) ? optInfo.getManageDeptCodeList() : deptCodeList);
        }

        IPage<PmsAppointmentBillStatsDTO> pageResult = billRepositoryService.selectAppointmentBillWithStats(queryDTO);
        return PageResult.of(pageResult.getTotal(), pageResult.getRecords());
    }

    /**
     * 供应商预约单明细查询
     */
    @SuppressWarnings("DuplicatedCode")
    @Override
    public PageResult<PmsAppointmentDetailDTO> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO) {
        OpInfo optInfo = userUtil.getDeptOpInfoWithThrow();
        if(AppTypeEnum.SCM.verifyByCode(optInfo.getAppType())){
            // scm限制供应商数据权限
            if (StringUtils.isBlank(optInfo.getBusinessCode())) {
                Logs.warn("SCM 员工 {} 未获取到对应供应商数据", optInfo.getUserId());
                return PageResult.ofEmpty();
            }
            queryDTO.setSupplierCodes(Collections.singletonList(optInfo.getBusinessCode()));
        }else if (!optInfo.getOriginDeptFlag()){
            // rmc用户分管权限
            if (CollectionUtils.isEmpty(optInfo.getManageDeptCodeList())) {
                return PageResult.ofEmpty();
            }
            List<String> deptCodeList = Optional.ofNullable(queryDTO.getDeptCodes()).orElse(new ArrayList<>());
            deptCodeList.retainAll(optInfo.getManageDeptCodeList());
            queryDTO.setDeptCodes(CollectionUtils.isEmpty(deptCodeList) ? optInfo.getManageDeptCodeList() : deptCodeList);
        }

        IPage<PmsAppointmentDetailDTO> pageResult = billRepositoryService.selectAppointmentBillDetails(queryDTO);
        return PageResult.of(pageResult.getTotal(), pageResult.getRecords());
    }

    /**
     * 查询可预约采购订单原始明细行数据
     */
    private List<PurchaseBillDetailSumResp> listAllOriginalAvailableBillDetail(QueryAbleAppointReq queryDto) {
        OpInfo optInfo = userUtil.getDeptOpInfoThreadLocal();
        if(AppTypeEnum.SCM.verifyByCode(optInfo.getAppType())){
            queryDto.setSupplierCodeList(Collections.singletonList(optInfo.getBusinessCode()));
        }else if (!optInfo.getOriginDeptFlag()){
            if (CollectionUtils.isEmpty(optInfo.getManageDeptCodeList())) {
                return Collections.emptyList();
            }
            List<String> deptCodeList = Optional.ofNullable(queryDto.getDeptCodeList()).orElse(new ArrayList<>());
            deptCodeList.retainAll(optInfo.getManageDeptCodeList());
            queryDto.setDeptCodeList(CollectionUtils.isEmpty(deptCodeList) ? optInfo.getManageDeptCodeList() : deptCodeList);
        }

        PmsPurchaseOrderDomainService targetService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseOrderDomainService.class);
        return targetService.getAbleAppointDetail(queryDto);
    }

    /**
     * 查询单据维度可预约采购订单商品信息
     */
    @Override
    public List<PmsAppointmentBillPurchDTO> listAvailableBillDetail(QueryAbleAppointReq queryDto) {
        List<PurchaseBillDetailSumResp> result = listAllOriginalAvailableBillDetail(queryDto);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }

        Map<String, List<PurchaseBillDetailSumResp>> groupPurchBill2List = result.stream()
                // 分组条件校验
                .filter(originalBill -> StringUtils.isNotBlank(originalBill.getBillNo()))
                .collect(Collectors.groupingBy(PurchaseBillDetailSumResp::getBillNo));
        // 转成采购订单维度标准格式
        return groupPurchBill2List.values().stream()
                .map(PmsAppointmentBillPurchConvert.INSTANCE::convertOriginalBill2Dto)
                .collect(Collectors.toList());
    }

    /**
     * 查询商品维度可预约采购订单商品信息
     */
    @Override
    public List<PmsAppointmentBillGoodsDTO> listAvailableGoodsDetail(QueryAbleAppointReq queryDto) {
        return PmsAppointmentBillPurchConvert.INSTANCE.convertBillItem2GoodsItem(listAvailableBillDetail(queryDto));
    }

    /**
     * 创建/更新 预约单
     * 包含预约单号即为更新操作
     */
    @Transactional
    @Override
    public String createOrUpdatePmsAppointmentBill(PmsAppointmentBillDTO dto) {
        // 操作来源 & 数据权限校验
        accessCheckWithThrow(dto.getDeptCode(), dto.getSupplierCode());
        if(AppTypeEnum.SCM.verifyByCode(userUtil.getLoginUserWithThrow().getAppType())){
            dto.setOpSource(PmsBookingDocumentSourceEnum.SCM.getCode());
        }else {
            dto.setOpSource(PmsBookingDocumentSourceEnum.RMC.getCode());
        }

        // 统一格式转换 按照订单维度维护
        if (CollectionUtils.isNotEmpty(dto.getGoodsList())) {
            dto.setPurchList(PmsAppointmentBillPurchConvert.INSTANCE.convertGoodsItem2PurchItem(dto.getGoodsList()));
        }

        // 入参数量校验
        String illegalQtyTips = dto.getPurchList().stream()
                .map(purch -> {
                    String errorTips = purch.getGoodsList().stream()
                            .map(goods -> {
                                switch (goods.validateQty()) {
                                    case -1:
                                        return String.format("商品编码%s非法的订货包装率%s", goods.getSkuCode(), goods.getUnitRate());
                                    case -2:
                                        return String.format("商品编码%s存在非法的数量信息", goods.getSkuCode());
                                    case -3:
                                        return String.format("商品编码%s 总数量%s != 整件数量%s * 商品包装率%s + 零头数量%s",
                                                goods.getSkuCode(), goods.getPurchQty().toPlainString(), goods.getWholeQty().toPlainString(), goods.getUnitRate().toPlainString(), goods.getOddQty().toPlainString());
                                    case -4:
                                        return String.format("商品编码%s 可预约数量%s != 可预约整件数量%s * 商品包装率%s + 可预约零头数量%s",
                                                goods.getSkuCode(), goods.getCanAppointmentQty().toPlainString(), goods.getCanAppointmentWholeQty().toPlainString(), goods.getUnitRate().toPlainString(), goods.getCanAppointmentOddQty().toPlainString());
                                    case -5:
                                        return String.format("商品编码%s 预约数量%s != 预约整件数量%s * 商品包装率%s + 预约零头数量%s",
                                                goods.getSkuCode(), goods.getAppointmentQty().toPlainString(), goods.getAppointmentWholeQty().toPlainString(), goods.getUnitRate().toPlainString(), goods.getAppointmentOddQty().toPlainString());
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining("、"));
                    return StringUtils.isBlank(errorTips) ? null : "采购订单" + purch.getPurchBillNo() + errorTips;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining("; "));
        if (StringUtils.isNotBlank(illegalQtyTips)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B023, new Object[]{illegalQtyTips});
        }

        // 商品明细行必要字段校验
        String illegalGoodsFieldErrorTips = dto.getPurchList().stream()
                .map(PmsAppointmentBillPurchDTO::getGoodsList)
                .flatMap(Collection::stream)
                .filter(goods -> goods.getPurchGoodsDetailId() == null || goods.getPurchGoodsDetailInsideId() == null)
                .map(goods -> String.format("%s-%s", goods.getPurchBillNo(), goods.getSkuCode()))
                .collect(Collectors.joining("; "));
        if (StringUtils.isNotBlank(illegalGoodsFieldErrorTips)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B019, new Object[]{illegalGoodsFieldErrorTips});
        }

        // 兜底汇总预约单中采购单数量信息
        dto.getPurchList().forEach(PmsAppointmentBillPurchDTO::summarize);

        // 避免采购单明细行或商品明细行重复
        String duplicatedPurchList = dto.getPurchList().stream()
                .map(PmsAppointmentBillPurchDTO::getPurchBillNo)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.joining("; "));
        if (StringUtils.isNotBlank(duplicatedPurchList)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B002, new Object[]{duplicatedPurchList});
        }
        // 避免采购单商品明细行重复
        String duplicatedGoodsList = dto.getPurchList().stream()
                .map(PmsAppointmentBillPurchDTO::getGoodsList)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(PmsAppointmentBillGoodsDTO::getPurchGoodsDetailId))
                .values().stream()
                .filter(goodsList -> goodsList.size() > 1)
                .map(pmsAppointmentBillGoodsDTOS -> String.format("采购单号 %s 商品编码 %s", pmsAppointmentBillGoodsDTOS.get(0).getPurchBillNo(), pmsAppointmentBillGoodsDTOS.get(0).getSkuCode()))
                .collect(Collectors.joining("; "));
        if (StringUtils.isNotBlank(duplicatedGoodsList)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B003, new Object[]{duplicatedGoodsList});
        }

        // 采购订单商品可预约数量校验
        String errorTip = dto.getPurchList().stream()
                .map(purch -> {
                    switch (purch.validateAppointmentQty()) {
                        case -1: return String.format("采购订单 %s 本次预约数量%s 超过 可预约数量%s", purch.getPurchBillNo(), purch.getTotalAppointmentQty().toPlainString(), purch.getTotalCanAppointmentQty().toPlainString());
                        case -2: return String.format("采购订单 %s 本次预约整件数量%s 超过 可预约整件数量%s", purch.getPurchBillNo(), purch.getTotalAppointmentWholeQty().toPlainString(), purch.getTotalCanAppointmentWholeQty().toPlainString());
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining(";"));
        if (StringUtils.isNotBlank(errorTip)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B023, new Object[]{errorTip});
        }

        String goodsErrorTips = dto.getPurchList().stream()
                .map(purch -> {
                    String errorTips = purch.getGoodsList().stream()
                            .map(goods -> {
                                switch (goods.validateAppointmentQty()) {
                                    case -1:
                                        return String.format("商品编码%s 预约数量%s 超过 可预约数量%s", goods.getSkuCode(), goods.getAppointmentQty().toPlainString(), goods.getCanAppointmentQty().toPlainString());
                                    case -2:
                                        return String.format("商品编码%s 预约整件数量%s 超过 可预约整件数量%s", goods.getSkuCode(), goods.getAppointmentWholeQty().toPlainString(), goods.getCanAppointmentWholeQty().toPlainString());
                                    case -3:
                                        return String.format("商品编码%s 预约零头数%s 超过 商品包装率%s", goods.getSkuCode(), goods.getAppointmentOddQty().toPlainString(), goods.getUnitRate().toPlainString());
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining("、"));
                    return StringUtils.isBlank(errorTips) ? null : "采购订单" + purch.getPurchBillNo() + errorTips;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining("; "));
        if (StringUtils.isNotBlank(goodsErrorTips)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B023, new Object[]{errorTip});
        }

        // 停靠点校验
        LocalDate targetDate = dto.getPlanArrivalTime().toLocalDate();
        LocalTime targetTime = dto.getPlanArrivalTime().toLocalTime();
        PmsDeliveryDockLimitQueryDTO dockQueryCondition = PmsDeliveryDockLimitQueryDTO.builder()
                .deptCodeList(Collections.singletonList(dto.getDeptCode()))
                .dockCode(dto.getDockCode())
                .startDate(targetDate)
                .endDate(targetDate)
                .build();
        List<PmsAvailableDockInfoDTO> targetDockList = listAvailableDockDetail(dockQueryCondition);
        if (CollectionUtils.isEmpty(targetDockList)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B006, new Object[]{dto.getDeptCode(), dto.getDockCode()});
        }
        // 确定归属的时段
        PmsAvailableDockInfoDTO targetDock = targetDockList.stream()
                .sorted(Comparator.comparing(PmsAvailableDockInfoDTO::getStartTime))
                .filter(dock -> !dock.getStartTime().isAfter(targetTime) && dock.getEndTime().isAfter(targetTime))
                .findFirst()
                .orElse(null);
        if (targetDock == null) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B007, new Object[]{dto.getDockCode()});
        }
        // 回写时段行号
        dto.setDockTimeInsideId(targetDock.getInsideId());
        // 停靠点名称
        dto.setDockName(targetDock.getDockName());

        // 停靠点类型
        MdDeliveryDockTypeEnum dockType = StandardEnum.codeOf(MdDeliveryDockTypeEnum.class, targetDock.getDockType());
        // 预约类别
        PmsBookingCategoryEnum bookingCategory = StandardEnum.codeOf(PmsBookingCategoryEnum.class, dto.getBillDirection());
        // 是否直流
        YesOrNoEnum isDirect = StandardEnum.codeOf(YesOrNoEnum.class, dto.getDirectSign());

        if (MdDeliveryDockTypeEnum.UNLIMITED != dockType) {
            String tip = String.format("当前停靠点 %s（类型 %s） 预约单（类型 %s，是否直流 %s）", dto.getDockCode(), dockType.getDesc(), bookingCategory.getDesc(), isDirect.getDesc());

            // 采购 & 直流
            if (PmsBookingCategoryEnum.PURCHASE_DISTRIBUTION == bookingCategory
                    && YesOrNoEnum.YES == isDirect
                    && MdDeliveryDockTypeEnum.DIRECT_FLOW != dockType) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B008, new Object[]{MdDeliveryDockTypeEnum.DIRECT_FLOW.getDesc()});
            }
            // 采购 & 非直流
            if (PmsBookingCategoryEnum.PURCHASE_DISTRIBUTION == bookingCategory
                    && YesOrNoEnum.NO == isDirect
                    && MdDeliveryDockTypeEnum.RECEIVING != dockType) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B008, new Object[]{MdDeliveryDockTypeEnum.RECEIVING.getDesc()});
            }
            // 采退
            if (PmsBookingCategoryEnum.PURCHASE_RETURN == bookingCategory
                    && MdDeliveryDockTypeEnum.SHIPPING != dockType) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B008, new Object[]{MdDeliveryDockTypeEnum.SHIPPING.getDesc()});
            }
        }

        // 可预约数量校验
        MdDeliveryDockConstraintRuleEnum constraintRule = StandardEnum.codeOf(MdDeliveryDockConstraintRuleEnum.class, targetDock.getConstraintRule());
        BigDecimal appointmentCount = dto.getPurchList().stream()
                .map(purch -> {
                    if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE) {
                        return purch.getTotalAppointmentWholeQty();
                    }
                    return BigDecimal.ZERO;
                })
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE) {
            if (targetDock.getAvailableCount().compareTo(appointmentCount) < 0) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B009, new Object[]{
                        appointmentCount.toPlainString(),
                        targetDock.getDockCode(),
                        targetDock.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                        targetDock.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                        targetDock.getAvailableCount().toString()});
            }
        }

        List<PmsAppointmentBillGoodsDTO> purchGoodsList = dto.getPurchList().stream()
                .map(PmsAppointmentBillPurchDTO::getGoodsList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        PmsAppointmentBillPO billPo = PmsAppointmentBillConvert.INSTANCE.convertDto2Po(dto);
        List<PmsAppointmentBillPurchPO> purchList = PmsAppointmentBillPurchConvert.INSTANCE.convertDto2PoList(dto.getPurchList());
        List<PmsAppointmentBillGoodsPO> goodsList = PmsAppointmentBillGoodsConvert.INSTANCE.convertDto2PoList(purchGoodsList);
        try {
            if (StringUtils.isBlank(billPo.getBillNo())) {
                ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
                String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.PMS_APPOINTMENT_ORDER, billPo.getDeptCode());
                billPo.setBillNo(billNo);
                // 默认状态
                billPo.setStatus(PmsBookingStatusEnum.DRAFT.getCode());
            }else {
                PmsAppointmentBillPO exist = billRepositoryService.lambdaQuery().eq(PmsAppointmentBillPO::getBillNo, billPo.getBillNo()).one();
                if (exist == null || !PmsBookingStatusEnum.DRAFT.verifyByCode(exist.getStatus())) {
                    throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
                }
                boolean isOwner = Objects.equals(exist.getCreateUid(), userUtil.getLoginUserWithThrow().getUid());
                if (!isOwner && PmsBookingDocumentSourceEnum.RMC.verifyByCode(exist.getOpSource())
                        && !userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_MODIFY_BUTTON))) {
                    throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
                }
                if (!isOwner && PmsBookingDocumentSourceEnum.SCM.verifyByCode(exist.getOpSource())
                        && !userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_MODIFY_BUTTON_SUP))) {
                    throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
                }
                billRepositoryService.purgeAppointmentBill(billPo.getBillNo());
                OperatorInfoHandler.useCreateUser(exist);
            }
            // 回写单号
            purchList.forEach(purch -> purch.setAppointmentBillNo(billPo.getBillNo()));
            goodsList.forEach(goods -> goods.setAppointmentBillNo(billPo.getBillNo()));

            billRepositoryService.save(billPo);
            purchRepositoryService.saveBatch(purchList);
            goodsRepositoryService.saveBatch(goodsList);
        }finally {
            OperatorInfoHandler.useDefaultLoginUser();
        }

        return billPo.getBillNo();
    }

    /**
     * 预约单详情（采购单维度的数据）
     */
    @Override
    public PmsAppointmentBillDTO commonDetail(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        PmsAppointmentBillPO exist = billRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPO::getBillNo, billNo)
                .one();
        if (exist == null) {
            return null;
        }
        if (!Objects.equals(exist.getCreateUid(), userUtil.getLoginUserWithThrow().getUid())) {
            accessCheckWithThrow(exist.getDeptCode(), exist.getSupplierCode());
        }

        PmsAppointmentBillDTO bill = PmsAppointmentBillConvert.INSTANCE.convertPo2Dto(exist);
        Map<String, List<PmsAppointmentBillGoodsDTO>> goodsDtoGroupingByPurchBillNo = goodsRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillGoodsPO::getAppointmentBillNo, bill.getBillNo())
                .list()
                .stream()
                .map(PmsAppointmentBillGoodsConvert.INSTANCE::convertPo2Dto)
                .collect(Collectors.groupingBy(PmsAppointmentBillGoodsDTO::getPurchBillNo));
        List<PmsAppointmentBillPurchDTO> purchList = purchRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPurchPO::getAppointmentBillNo, bill.getBillNo())
                .list()
                .stream()
                .map(PmsAppointmentBillPurchConvert.INSTANCE::convertPo2Dto)
                .peek(purch -> {
                    List<PmsAppointmentBillGoodsDTO> goodsList = goodsDtoGroupingByPurchBillNo.get(purch.getPurchBillNo());
                    if (CollectionUtils.isEmpty(goodsList)) {
                        return;
                    }
                    purch.setGoodsList(goodsList);
                    // 填充归属采购单信息
                    goodsList.forEach(goods -> goods.setRefPurchBill(PmsAppointmentBillPurchConvert.INSTANCE.copyPlainProperty(purch)));
                })
                .collect(Collectors.toList());
        bill.setPurchList(purchList);
        return bill;
    }

    /**
     * 预约单详情
     */
    @Override
    public PmsAppointmentBillDTO detail(String billNo) {
        // 获取预约单详情（采购订单展示维度）
        PmsAppointmentBillDTO bill = commonDetail(billNo);
        if (bill == null) {
            return null;
        }

        List<String> purchBillNoList = bill.getPurchList().stream()
                .map(PmsAppointmentBillPurchDTO::getPurchBillNo)
                .collect(Collectors.toList());
        QueryAbleAppointReq queryCondition = QueryAbleAppointReq.builder()
                .purchBillNoList(purchBillNoList)
                .build();

        // 商品维度
        if (PmsBookingMethodEnum.BY_PRODUCT.verifyByCode(bill.getAppointmentMode())) {
            // 数据维度转换
            List<PmsAppointmentBillGoodsDTO> goodsList = PmsAppointmentBillPurchConvert.INSTANCE.convertBillItem2GoodsItem(bill.getPurchList());
            bill.setGoodsList(goodsList);
            bill.setPurchList(null);
            // 非草稿状态不处理变更数据
            if (!PmsBookingStatusEnum.DRAFT.verifyByCode(bill.getStatus())) {
                return bill;
            }

            // 标记变更数据
            Map<String, PmsAppointmentBillGoodsDTO> mappingGoodsKey2Item = listAvailableGoodsDetail(queryCondition).stream()
                    .collect(Collectors.toMap(goodsItem -> String.format("%s-%s-%s",
                            goodsItem.getSkuType(),
                            goodsItem.getSkuCode(),
                            goodsItem.getUnitRate().toPlainString()), Function.identity()));
            goodsList.forEach(goods -> {
                String goodsKey = String.format("%s-%s-%s", goods.getSkuType(), goods.getSkuCode(), goods.getUnitRate().toPlainString());
                PmsAppointmentBillGoodsDTO latestGoodsInfo = mappingGoodsKey2Item.get(goodsKey);
                if (latestGoodsInfo == null) {
                    goods.setChanged(true);
                    goods.setExist(false);
                    return;
                }
                goods.markChanged(latestGoodsInfo);

                Map<String, PmsAppointmentBillPurchDTO> mappingPurchBillNo2PurchItem = latestGoodsInfo.getPurchList().stream()
                        .collect(Collectors.toMap(PmsAppointmentBillPurchDTO::getPurchBillNo, Function.identity()));
                goods.getPurchList().forEach(purch -> {
                    PmsAppointmentBillPurchDTO latestPurchInfo = mappingPurchBillNo2PurchItem.get(purch.getPurchBillNo());
                    if (latestPurchInfo == null) {
                        purch.setChanged(true);
                        purch.setExist(false);
                        return;
                    }
                    purch.markChanged(latestPurchInfo);
                });
            });

            return bill;
        }

        // 订单维度
        // 非草稿状态不处理变更数据
        if (!PmsBookingStatusEnum.DRAFT.verifyByCode(bill.getStatus())) {
            return bill;
        }
        Map<String, PmsAppointmentBillPurchDTO> mappingPurchBillNo2PurchItem = listAvailableBillDetail(queryCondition).stream()
                .collect(Collectors.toMap(PmsAppointmentBillPurchDTO::getPurchBillNo, Function.identity()));
        bill.getPurchList().forEach(purch -> {
            PmsAppointmentBillPurchDTO latestPurchInfo = mappingPurchBillNo2PurchItem.get(purch.getPurchBillNo());
            if (latestPurchInfo == null) {
                purch.setChanged(true);
                purch.setExist(false);
                return;
            }
            purch.markChanged(latestPurchInfo);

            Map<Long, PmsAppointmentBillGoodsDTO> mappingGoodsId2GoodsItem = latestPurchInfo.getGoodsList().stream()
                    .collect(Collectors.toMap(PmsAppointmentBillGoodsDTO::getPurchGoodsDetailId, Function.identity()));
            purch.getGoodsList().forEach(goods -> {
                PmsAppointmentBillGoodsDTO latestGoodsInfo = mappingGoodsId2GoodsItem.get(goods.getPurchGoodsDetailId());
                if (latestGoodsInfo == null) {
                    goods.setChanged(true);
                    goods.setExist(false);
                    return;
                }
                goods.markChanged(latestGoodsInfo);
            });
        });

        return bill;
    }

    /**
     * 访问权限校验
     */
    private void accessCheckWithThrow(String deptCode, String supplierCode) {
        if (StringUtils.isBlank(deptCode) || StringUtils.isBlank(supplierCode)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B011);
        }
        OpInfo optInfo = userUtil.getDeptOpInfoThreadLocal();
        if(AppTypeEnum.SCM.verifyByCode(optInfo.getAppType())){
            // scm限制供应商数据权限
            if (StringUtils.isBlank(optInfo.getBusinessCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B012, new Object[]{optInfo.getUserId()});
            }
            if (!Objects.equals(optInfo.getBusinessCode(), supplierCode)) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
            }
        }else if (!optInfo.getOriginDeptFlag()){
            // rmc用户分管权限
            if (CollectionUtils.isEmpty(optInfo.getManageDeptCodeList()) || !optInfo.getManageDeptCodeList().contains(deptCode)) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
            }
        }
    }

    /**
     * 供应商预约单提交统计
     */
    @Override
    public PmsAppointmentBillPreSubmitStatsDTO querySubmitStats(String billNo) {
        PmsAppointmentBillPO bill = billRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPO::getBillNo, billNo)
                .one();
        if (bill == null) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B013, new Object[]{billNo});
        }

        // 访问权限校验
        if (!Objects.equals(bill.getCreateUid(), userUtil.getLoginUserWithThrow().getUid())) {
            accessCheckWithThrow(bill.getDeptCode(), bill.getSupplierCode());
        }

        return billRepositoryService.selectAppointmentBillStats(billNo);
    }

    /**
     * 提交预约单
     */
    @Transactional
    @Override
    public void submit(String billNo) {
        // 获取预约单详情（采购订单展示维度）
        PmsAppointmentBillPO bill = billRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPO::getBillNo, billNo)
                .one();
        if (bill == null) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B013, new Object[]{billNo});
        }

        if (!PmsBookingStatusEnum.DRAFT.verifyByCode(bill.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
        }

        // 访问权限校验
        if (!Objects.equals(bill.getCreateUid(), userUtil.getLoginUserWithThrow().getUid())) {
            accessCheckWithThrow(bill.getDeptCode(), bill.getSupplierCode());
            if (!userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_MODIFY_BUTTON))) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
            }
        }

        // 采购订单状态校验
        List<String> purchBillNoList = purchRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPurchPO::getAppointmentBillNo, billNo)
                .select(PmsAppointmentBillPurchPO::getPurchBillNo)
                .list().stream()
                .map(PmsAppointmentBillPurchPO::getPurchBillNo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(purchBillNoList)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B020);
        }
        List<String> invalidBillNos = pmsPurchaseOrderRepositoryService.queryInvalidBillNos(purchBillNoList);
        if (!CollectionUtils.isEmpty(invalidBillNos)) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B021, new Object[]{String.join(";", invalidBillNos)});
        }

        // 锁定读（供应商-部门维度）
        String lockReadKey4SupplierAndDept = String.format("%s-%s", bill.getSupplierCode(), bill.getDeptCode());
        boolean lock4SupplierAndDept = lockManager.tryLock(lockReadKey4SupplierAndDept, 10, 3, TimeUnit.SECONDS);
        if (!lock4SupplierAndDept) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B014, new Object[]{bill.getSupplierCode(), bill.getDeptCode()});
        }
        // 停靠点锁定读（停靠点-时间维度）
        String lockReadKey4Dock = String.format("%s-%s", bill.getDockCode(), bill.getDockTimeInsideId());
        boolean lock4Dock = false;
        try {
            // 商品可预约数量校验
            List<PmsAppointmentBillGoodsPO> goodsList = goodsRepositoryService.lambdaQuery()
                    .eq(PmsAppointmentBillGoodsPO::getAppointmentBillNo, bill.getBillNo())
                    .list();
            if (CollectionUtils.isEmpty(goodsList)) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B015);
            }

            Map<Long, BigDecimal> mappingPurchGoodsDetailId2AppointmentQty = goodsList.stream()
                    .collect(Collectors.toMap(PmsAppointmentBillGoodsPO::getPurchGoodsDetailId, PmsAppointmentBillGoodsPO::getAppointmentQty));
            Map<Long, PurchaseBillDetailBasicDTO> mappingId2Detail = pmsPurchaseDetailRepositoryService.getDetailBasicInfoByIds(new ArrayList<>(mappingPurchGoodsDetailId2AppointmentQty.keySet())).stream()
                    .filter(purchaseBillDetailBasicDTO -> purchaseBillDetailBasicDTO.getUnitRate() != null
                            && purchaseBillDetailBasicDTO.getPurchQty() != null)
                    .peek(purchaseBillDetailBasicDTO -> {
                        if (purchaseBillDetailBasicDTO.getAppointmentQty() == null) {
                            purchaseBillDetailBasicDTO.setAppointmentQty(BigDecimal.ZERO);
                        }
                    })
                    .collect(Collectors.toMap(PurchaseBillDetailBasicDTO::getId, Function.identity()));
            String errorTips = goodsList.stream()
                    .collect(Collectors.groupingBy(PmsAppointmentBillGoodsPO::getPurchBillNo))
                    .values().stream()
                    .map(goodsPoList -> {
                        String goodsErrorTips = goodsPoList.stream()
                                .map(goods -> {
                                    PurchaseBillDetailBasicDTO detail = mappingId2Detail.get(goods.getPurchGoodsDetailId());
                                    if (detail == null) {
                                        return String.format("商品 %s 未找到对应合法的商品详情信息", goods.getSkuCode());
                                    } else if (detail.getPurchQty().subtract(detail.getAppointmentQty()).compareTo(goods.getAppointmentQty()) < 0) {
                                        return String.format("商品 %s 可预约数量不足（本次预约数量 %s 超过了剩余可约数量 %s）",
                                                goods.getSkuCode(),
                                                goods.getAppointmentQty().toPlainString(),
                                                detail.getPurchQty().subtract(detail.getAppointmentQty()).toPlainString());
                                    }
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining("、"));
                        return StringUtils.isBlank(goodsErrorTips) ? null : "采购订单" + goodsPoList.get(0).getPurchBillNo() + goodsErrorTips;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining("; "));
            if (StringUtils.isNotBlank(errorTips)) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B023, new Object[]{errorTips});
            }

            lock4Dock = lockManager.tryLock(lockReadKey4Dock, 10, 3, TimeUnit.SECONDS);
            if (!lock4Dock) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B016, new Object[]{bill.getDockCode(), bill.getDockTimeInsideId()});
            }

            // 停靠点可预约数量校验
            PmsDeliveryDockLimitQueryDTO dockQueryCondition = PmsDeliveryDockLimitQueryDTO.builder()
                    .dockCode(bill.getDockCode())
                    .startDate(bill.getPlanArrivalTime().toLocalDate())
                    .endDate(bill.getPlanArrivalTime().toLocalDate())
                    .timeInsideId(bill.getDockTimeInsideId())
                    .build();
            List<PmsAvailableDockInfoDTO> targetDockList = listAvailableDockDetail(dockQueryCondition);
            if (CollectionUtils.isEmpty(targetDockList)) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B007, new Object[]{bill.getDockCode()});
            }

            // 可预约数量校验
            MdDeliveryDockConstraintRuleEnum constraintRule = StandardEnum.codeOf(MdDeliveryDockConstraintRuleEnum.class, targetDockList.get(0).getConstraintRule());
            BigDecimal appointmentCount = goodsList.stream()
                    .map(goods -> {
                        if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE) {
                            return goods.getAppointmentWholeQty();
                        }
                        return BigDecimal.ZERO;
                    })
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE) {
                if (targetDockList.get(0).getAvailableCount().compareTo(appointmentCount) < 0) {
                    throw new ScBizException(MdErrorCodeEnum.SCPMS001B009, new Object[]{
                            appointmentCount.toPlainString(),
                            targetDockList.get(0).getDockCode(),
                            targetDockList.get(0).getStartTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                            targetDockList.get(0).getEndTime().format(DateTimeFormatter.ofPattern("HH:mm")),
                            targetDockList.get(0).getAvailableCount().toString()});
                }
            }

            // 更新单据状态
            boolean result = billRepositoryService.lambdaUpdate()
                    .eq(PmsAppointmentBillPO::getBillNo, bill.getBillNo())
                    .set(true, PmsAppointmentBillPO::getStatus, PmsBookingStatusEnum.SUBMITTED.getCode())
                    .set(true, PmsAppointmentBillPO::getSubmitManCode, userUtil.getLoginUserWithThrow().getCode())
                    .set(true, PmsAppointmentBillPO::getSubmitManName, userUtil.getLoginUserWithThrow().getName())
                    .set(true, PmsAppointmentBillPO::getSubmitTime, LocalDateTime.now())
                    .update();
            if (!result) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B017, new Object[]{bill.getBillNo()});
            }

            // 回写预约数量
            List<UpdateAppointmentQtyDTO> writeBackList = mappingId2Detail.values().stream()
                    .map(item -> {
                        BigDecimal appointmentQty = mappingPurchGoodsDetailId2AppointmentQty.get(item.getId());
                        if (appointmentQty == null || appointmentQty.compareTo(BigDecimal.ZERO) <= 0) {
                            return null;
                        }
                        return UpdateAppointmentQtyDTO.builder()
                                .id(item.getId())
                                .appointmentQty(appointmentQty)
                                .build();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            pmsPurchaseDetailRepositoryService.batchUpdateAppointmentQty(writeBackList);

            // 回写预约单已预约标记
            pmsPurchaseOrderRepositoryService.updateAppointmentMark(purchBillNoList);
        }finally {
            lockManager.unlock(lockReadKey4SupplierAndDept);
            if (lock4Dock) {
                lockManager.unlock(lockReadKey4Dock);
            }
        }
    }


    /**
     * 保存并提交
     */
    @Transactional
    @Override
    public String saveUpdateAndSubmit(PmsAppointmentBillDTO dto) {
        String billNo = createOrUpdatePmsAppointmentBill(dto);
        submit(billNo);
        return billNo;
    }

    /**
     * 预约单取消
     */
    @Override
    public void cancel(String billNo) {
        PmsAppointmentBillPO bill = billRepositoryService.lambdaQuery()
                .eq(PmsAppointmentBillPO::getBillNo, billNo)
                .one();
        if (bill == null) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B013, new Object[]{billNo});
        }

        if (!PmsBookingStatusEnum.DRAFT.verifyByCode(bill.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
        }

        // 访问权限校验
        if (!Objects.equals(bill.getCreateUid(), userUtil.getLoginUserWithThrow().getUid())) {
            accessCheckWithThrow(bill.getDeptCode(), bill.getSupplierCode());
            if (!userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_MODIFY_BUTTON))) {
                throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
            }
        }

        // 更新单据状态
        boolean result = billRepositoryService.lambdaUpdate()
                .eq(PmsAppointmentBillPO::getBillNo, bill.getBillNo())
                .set(true, PmsAppointmentBillPO::getStatus, PmsBookingStatusEnum.CANCELLED.getCode())
                .set(true, PmsAppointmentBillPO::getCancelManCode, userUtil.getLoginUserWithThrow().getCode())
                .set(true, PmsAppointmentBillPO::getCancelManName, userUtil.getLoginUserWithThrow().getName())
                .set(true, PmsAppointmentBillPO::getCancelTime, LocalDateTime.now())
                .update();
        if (!result) {
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B018, new Object[]{bill.getBillNo()});
        }
    }

    /**
     * 查询停靠点可预约信息
     */
    @Override
    public List<PmsAvailableDockInfoDTO> listAvailableDockDetail(PmsDeliveryDockLimitQueryDTO queryDto) {
        OpInfo optInfo = userUtil.getDeptOpInfoThreadLocal();
        // rmc用户分管权限
        if(!AppTypeEnum.SCM.verifyByCode(optInfo.getAppType()) && !optInfo.getOriginDeptFlag()){
            if (CollectionUtils.isEmpty(optInfo.getManageDeptCodeList())) {
                return Collections.emptyList();
            }
            List<String> deptCodeList = Optional.ofNullable(queryDto.getDeptCodeList()).orElse(new ArrayList<>());
            deptCodeList.retainAll(optInfo.getManageDeptCodeList());
            queryDto.setDeptCodeList(CollectionUtils.isEmpty(deptCodeList) ? optInfo.getManageDeptCodeList() : deptCodeList);
        }

        // 停靠点数据
        QueryDeliveryDockLimitReq queryCondition = QueryDeliveryDockLimitReq.builder()
                .deptCodeList(queryDto.getDeptCodeList())
                .dockCode(queryDto.getDockCode())
                .dockTypeList(queryDto.getDockType())
                .constraintRule(queryDto.getConstraintRule())
                .timeInsideId(queryDto.getTimeInsideId())
                .build();
        List<MdDeliveryDockLimitVO> dockList = dockStrategyRepositoryService.selectDeliveryDockLimitRules(queryCondition);
        if (CollectionUtils.isEmpty(dockList)) {
            return Collections.emptyList();
        }

        // 构建可预约停靠点数据
        ArrayList<PmsAvailableDockInfoDTO> availableDockInfoList = new ArrayList<>();

        // 如果没有指定开始或结束时间，则返回可预约的停靠点信息
        if (queryDto.getStartDate() == null || queryDto.getEndDate() == null) {
            dockList.stream()
                    .collect(Collectors.groupingBy(MdDeliveryDockLimitVO::getDockCode))
                    .values().stream()
                    .map(values -> values.get(0))
                    .forEach(dock -> {
                        PmsAvailableDockInfoDTO availableDockInfo = PmsAvailableDockInfoDTO.builder()
                                .deptCode(dock.getDeptCode())
                                .dockCode(dock.getDockCode())
                                .dockName(dock.getDockName())
                                .dockType(dock.getDockType())
                                .constraintRule(dock.getConstraintRule())
                                .build();
                        availableDockInfoList.add(availableDockInfo);
                    });
            return availableDockInfoList;
        }

        // 停止预约日期
        Set<String> dockBillNoList = dockList.stream().map(MdDeliveryDockLimitVO::getBillNo).collect(Collectors.toSet());
        Map<String, List<MdDeliveryAppointmentCloseDateDTO>> closeDateGroupingByBillNo = appointmentStrategyDomainService.getAppointmentCloseDateList(new ArrayList<>(dockBillNoList))
                .stream()
                .collect(Collectors.groupingBy(MdDeliveryAppointmentCloseDateDTO::getBillNo));

        // 扣减已预约的数量
        Set<String> dockCodeList = dockList.stream().map(MdDeliveryDockLimitVO::getDockCode).collect(Collectors.toSet());
        DockTimeStatsQueryDTO appointmentCountCondition = DockTimeStatsQueryDTO.builder()
                .dockCodes(new ArrayList<>(dockCodeList))
                .status(PmsBookingStatusEnum.SUBMITTED.getCode())
                .planArrivalTimeStart(queryDto.getStartDate().atTime(0, 0, 0))
                .planArrivalTimeEnd(queryDto.getEndDate().atTime(23, 59, 59))
                .build();
        Map<String, DockTimeStatsDTO> mappingMKey2UsedCount = billRepositoryService.selectDockTimeStats(appointmentCountCondition).stream()
                .collect(Collectors.toMap(dockTimeStatsDTO -> String.format("%s-%s-%s",
                        dockTimeStatsDTO.getDockCode(),
                        dockTimeStatsDTO.getPlanArrivalDate(),
                        dockTimeStatsDTO.getDockTimeInsideId()), Function.identity()));

        for (MdDeliveryDockLimitVO dock : dockList) {
            List<MdDeliveryAppointmentCloseDateDTO> closeDateList = closeDateGroupingByBillNo.get(dock.getBillNo());

            for (LocalDate start = queryDto.getStartDate(), end = queryDto.getEndDate();
                 !start.isAfter(end);
                 start = start.plusDays(1)) {

                LocalDate current = start;
                // 日期校验
                if (CollectionUtils.isNotEmpty(closeDateList)) {
                    MdDeliveryAppointmentCloseDateDTO inCloseDate = closeDateList.stream()
                            .filter(closeDate -> !current.isBefore(closeDate.getStartTime())
                                    && !current.isAfter(closeDate.getEndTime()))
                            .findFirst()
                            .orElse(null);
                    if (inCloseDate != null) {
                        continue;
                    }
                }

                // 预约数量校验
                Integer availableCount = dock.getAvailableCount(current);
                DockTimeStatsDTO usedStats = mappingMKey2UsedCount.getOrDefault(String.format("%s-%s-%s", dock.getDockCode(), current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), dock.getInsideId()), new DockTimeStatsDTO());
                BigDecimal result = BigDecimal.ZERO;

                MdDeliveryDockConstraintRuleEnum constraintRule = StandardEnum.codeOf(MdDeliveryDockConstraintRuleEnum.class, dock.getConstraintRule());
                if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE
                        && (result = BigDecimal.valueOf(availableCount).subtract(usedStats.getTotalAppointmentWholeQty())).compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                PmsAvailableDockInfoDTO availableDockInfo = PmsAvailableDockInfoDTO.builder()
                        .deptCode(dock.getDeptCode())
                        .dockCode(dock.getDockCode())
                        .dockName(dock.getDockName())
                        .dockType(dock.getDockType())
                        .constraintRule(dock.getConstraintRule())
                        .insideId(dock.getInsideId())
                        .startTime(dock.getStartTime())
                        .endTime(dock.getEndTime())
                        .date(current)
                        .build();

                if (constraintRule == MdDeliveryDockConstraintRuleEnum.BY_WHOLE_PIECE) {
                    availableDockInfo.setUsedCount(usedStats.getTotalAppointmentWholeQty());
                    availableDockInfo.setAvailableCount(result);
                }
                if (constraintRule == MdDeliveryDockConstraintRuleEnum.UNLIMITED) {
                    availableDockInfo.setUsedCount(usedStats.getTotalAppointmentWholeQty());
                    availableDockInfo.setAvailableCount(BigDecimal.valueOf(availableCount));
                }

                availableDockInfoList.add(availableDockInfo);
            }
        }

        // 按照日期排序
        availableDockInfoList.sort((d1, d2) -> {
            if (!d1.getDate().isEqual(d2.getDate())) {
                return d1.getDate().compareTo(d2.getDate());
            }
            return d1.getStartTime().compareTo(d2.getStartTime());
        });

        // 查询指定时段
        if (queryDto.getTargetTime() != null) {
            return availableDockInfoList.stream()
                    .filter(dock -> dock.getStartTime() != null
                            && dock.getEndTime() != null
                            && !dock.getStartTime().isAfter(queryDto.getTargetTime())
                            && !dock.getEndTime().isBefore(queryDto.getTargetTime()))
                    .collect(Collectors.toList());
        }

        return availableDockInfoList;
    }

    /**
     * 查询来源单据信息
     */
    @Override
    public PmsDemandSourceDetailDTO queryDemandSourceDetail(String refBillNo, Long refInsideId) {
        if (StringUtils.isBlank(refBillNo) || refInsideId == null) {
            return null;
        }
        return pmsDemandPruchDeliveryRefRepositoryService.getDemandSourceDetail(refBillNo, refInsideId);
    }

    /**
     * 查询采购订单对应预约单相关明细数据
     */
    @Override
    public List<PmsAppointmentBillGoodsQueryResultDTO> queryAppointmentBillGoodsDetail(String purchBillNo) {
        if (StringUtils.isBlank(purchBillNo)) {
            return Collections.emptyList();
        }
        List<PmsAppointmentBillGoodsQueryResultDTO> result = goodsRepositoryService.selectAppointmentBillGoodsByPurchBillNo(purchBillNo);
        result.forEach(item -> {
            if (item.getPlanArrivalTime() != null && item.getPlanStayMinute() != null) {
                item.setPlanCompleteTime(item.getPlanArrivalTime().plusMinutes(item.getPlanStayMinute()));
            }
        });
        return result;
    }
}
