package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdContractGoodsDefineDomainService;
import com.meta.supplychain.convert.md.MdContractGoodsDefineConvert;
import com.meta.supplychain.convert.pms.PurchasePlanBillConvert;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchasePlanDomainService;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractGoodsDefineDTO;
import com.meta.supplychain.entity.dto.md.view.MdContractGoodsDefineView;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanBillResp;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchasePlanBillView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 采购计划单导出
 *
 * <AUTHOR>
 * @date 2025/05/24 10:00
 **/
@ExportSpecification(code = "PmsPurchasePlanBillExportProcessor", name = "采购计划单导出", executeType = ExecuteType.CLUSTER)
public class PmsPurchasePlanBillExportProcessor implements ExportProcessor<QueryPurchasePlanBillReq, PmsPurchasePlanBillResp, PmsPurchasePlanBillView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryPurchasePlanBillReq params) throws BizException {
        Logs.info("PmsPurchasePlanBillExportProcessor.totalCount.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));
        PmsPurchasePlanDomainService pmsPurchasePlanDomainService =
                SpringContextUtil.getApplicationContext().getBean(PmsPurchasePlanDomainService.class);
        params.setPageSize(1L);
        params.setCurrent(1L);
        PageResult<PmsPurchasePlanBillResp> pagePlanList = pmsPurchasePlanDomainService.pagePlanList(params);

        return (int)pagePlanList.getTotal();
    }

    @Override
    public List<PmsPurchasePlanBillResp> queryData(BizUser bizUser, QueryPurchasePlanBillReq params, BizExportPage bizExportPage) throws BizException {
        Logs.info("PmsPurchasePlanBillExportProcessor.queryData.bizUser:{},params:{}.bizExportPage:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params),JSON.toJSONString(bizExportPage));
        params.setCurrent(bizExportPage.getNo().longValue());
        params.setPageSize(bizExportPage.getSize().longValue());
        PmsPurchasePlanDomainService pmsPurchasePlanDomainService =
                SpringContextUtil.getApplicationContext().getBean(PmsPurchasePlanDomainService.class);
        PageResult<PmsPurchasePlanBillResp> pagePlanList = pmsPurchasePlanDomainService.pagePlanList(params);
        return pagePlanList.getRows();
    }

    @Override
    public List<PmsPurchasePlanBillView> convert(BizUser bizUser, QueryPurchasePlanBillReq params, List<PmsPurchasePlanBillResp> list) throws BizException {
        Logs.info("PmsPurchasePlanBillExportProcessor.queryData.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));
        List<PmsPurchasePlanBillView> viewList = Lists.newArrayList();
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_PURCHASE_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        list.forEach(item -> {
            PmsPurchasePlanBillView view = PurchasePlanBillConvert.INSTANCE.convertRespToView(item);
            view.setValidityDate(null == item.getValidityDate() ? "" : DateUtil.localDateFormateYmd(item.getValidityDate()));
            view.setCreateTime(null == item.getCreateTime() ? "" : DateUtil.getDateTimeYmdHms(item.getCreateTime()));
            view.setAuditTime(null == item.getAuditTime() ? "" : DateUtil.getDateTimeYmdHms(item.getAuditTime()));
            view.setCancelTime(null == item.getCancelTime() ? "" : DateUtil.getDateTimeYmdHms(item.getCancelTime()));
            if (StringUtils.isEmpty(item.getCreateCode()) && StringUtils.isEmpty(item.getCreateName())) {
                view.setCreator("");
            } else {
                view.setCreator(item.getCreateCode() + "_" + item.getCreateName());
            }
            if  (StringUtils.isEmpty(item.getAuditCode()) && StringUtils.isEmpty(item.getAuditName())) {
                view.setAuditor("");
            } else {
                view.setAuditor(item.getAuditCode() + "_" + item.getAuditName());
            }
            if  (StringUtils.isEmpty(item.getCancelManCode()) && StringUtils.isEmpty(item.getCancelManName())) {
                view.setCancelMan("");
            } else {
                view.setCancelMan(item.getCancelManCode() + "_" + item.getCancelManName());
            }
            if (!showPriceFlag) {
                view.setTotalTaxMoney("****");
                view.setTotalTax("****");
            }
            viewList.add(view);
        });
        return viewList;
    }
}
