package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.alibaba.fastjson.JSON;
import com.meta.supplychain.convert.pms.PurchasePlanBillConvert;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchasePlanDomainService;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanDetailResp;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchasePlanDetailView;
import com.meta.supplychain.util.spring.SpringContextUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购计划单详情导出
 *
 * <AUTHOR>
 * @date 2025/05/24 10:00
 **/
@ExportSpecification(code = "PmsPurchasePlanDetailExportProcessor", name = "采购计划单详情导出", executeType = ExecuteType.CLUSTER)
public class PmsPurchasePlanDetailExportProcessor implements ExportProcessor<QueryPurchasePlanBillReq, PmsPurchasePlanDetailResp, PmsPurchasePlanDetailView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryPurchasePlanBillReq params) throws BizException {
        Logs.info("PmsPurchasePlanDetailExportProcessor.totalCount.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));
        PmsPurchasePlanDomainService pmsPurchasePlanDomainService =
                SpringContextUtil.getApplicationContext().getBean(PmsPurchasePlanDomainService.class);
        params.setPageSize(1L);
        params.setCurrent(1L);
        PageResult<PmsPurchasePlanDetailResp> pagePlanList = pmsPurchasePlanDomainService.pageDetailList(params);
        return (int)pagePlanList.getTotal();
    }

    @Override
    public List<PmsPurchasePlanDetailResp> queryData(BizUser bizUser, QueryPurchasePlanBillReq params, BizExportPage bizExportPage) throws BizException {
        Logs.info("PmsPurchasePlanDetailExportProcessor.queryData.bizUser:{},params:{}.bizExportPage:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params),JSON.toJSONString(bizExportPage));
        params.setCurrent(bizExportPage.getNo().longValue());
        params.setPageSize(bizExportPage.getSize().longValue());
        PmsPurchasePlanDomainService pmsPurchasePlanDomainService =
                SpringContextUtil.getApplicationContext().getBean(PmsPurchasePlanDomainService.class);
        PageResult<PmsPurchasePlanDetailResp> pagePlanList = pmsPurchasePlanDomainService.pageDetailList(params);
        return pagePlanList.getRows();
    }

    @Override
    public List<PmsPurchasePlanDetailView> convert(BizUser bizUser, QueryPurchasePlanBillReq params, List<PmsPurchasePlanDetailResp> list) throws BizException {
        Logs.info("PmsPurchasePlanDetailExportProcessor.queryData.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));
        List<PmsPurchasePlanDetailView> viewList = list.stream().map(PurchasePlanBillConvert.INSTANCE::convertDetailPoToResp).collect(Collectors.toList());
        viewList.forEach(item -> {
            if (!params.getViewFlag()) {
                item.setPromotePeriodPrice("****");
                item.setContractSpecialPrice("****");
                item.setContractPrice("****");
                item.setContractMaxPrice("****");
                item.setLastPurchPrice("****");
                item.setDeptGoodsPrice("****");
                item.setSkuPurchPrice("****");
                item.setPurchPrice("****");
            }
        });
        return viewList;
    }
}
