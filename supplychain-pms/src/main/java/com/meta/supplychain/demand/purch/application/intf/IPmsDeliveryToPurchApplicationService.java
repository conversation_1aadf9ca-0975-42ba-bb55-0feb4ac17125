package com.meta.supplychain.demand.purch.application.intf;


import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryDetailReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchSubmitReq;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchResultResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.PurchaseOrderSummaryResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;

import java.util.List;


public interface IPmsDeliveryToPurchApplicationService {
    DeliveryToPurchResultResp generateDeliveryToPurch(DeliveryToPurchReq param);

    /**
     * 加载订单生成可转采数据
     * @param param 配送订单
     * @return 转采信息
     */
    DeliveryToPurchResultResp loadingDeliveryGenerateToPurch(DeliveryDetailReq param);

    /**
     * 保存订单生成可转采数据
     * @param param
     */
    Result<List<String>> saveDeliveryToPurch(DeliveryToPurchSubmitReq param);

    /**
     * 预览
     * @param param
     */
    PurchaseOrderSummaryResp preview(DeliveryToPurchSubmitReq param);
}
