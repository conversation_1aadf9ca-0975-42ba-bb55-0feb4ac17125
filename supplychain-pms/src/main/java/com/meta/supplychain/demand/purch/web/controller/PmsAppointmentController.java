package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.ageiport.client.AgeiTaskClient;
import cn.linkkids.ageiport.params.ExportDataParams;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.google.common.collect.Sets;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.demand.purch.processor.export.PmsAppointmentBillStatsProcessor;
import com.meta.supplychain.demand.purch.processor.export.PmsAppointmentDetailProcessor;
import com.meta.supplychain.entity.dto.pms.req.appointment.*;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryAbleAppointReq;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.enums.AppTypeEnum;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.annotation.RequestJsonParam;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * 供应商预约单管理Controller
 *
 * <AUTHOR>
 * @date 2025/04/20 21:06
 **/
@Controller
@RequestMapping("${unit-deploy.prefix-main:}/pms/appointment")
@Tag(name = "供应商预约单管理", description = "供应商预约单相关接口")
@Validated
public class PmsAppointmentController {

    @Resource
    private IPmsApplicationManagerService pmsApplicationManagerService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private UserResourceUtil userResourceUtil;

    @PostMapping("/list")
    @ResponseBody
    @Operation(summary = "多条件查询预约单列表（包含统计信息）", description = "查询预约单列表，包含统计信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询预约单列表")
    public Result<PageResult<PmsAppointmentBillStatsDTO>> selectAppointmentBillWithStats(
            @Valid @RequestBody @Parameter(description = "预约单列表查询参数", required = true) PmsAppointmentBillQueryDTO queryDTO) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().selectAppointmentBillWithStats(queryDTO);
    }

    @PostMapping("/details")
    @ResponseBody
    @Operation(summary = "预约单详细查询", description = "查询预约单详细信息列表")
    @MethodLog(level = MethodLog.Level.INFO, value = "预约单详细查询")
    public Result<PageResult<PmsAppointmentDetailDTO>> selectAppointmentBillDetails(
            @Valid @RequestBody @Parameter(description = "预约单详细查询参数", required = true) PmsAppointmentDetailQueryDTO queryDTO) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().selectAppointmentBillDetails(queryDTO);
    }

    @PostMapping("/available/bill")
    @ResponseBody
    @Operation(summary = "查询单据维度可预约采购订单商品信息", description = "查询可预约的采购订单信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询可预约采购订单")
    public Result<List<PmsAppointmentBillPurchDTO>> listAvailableBillDetail(
            @Valid @RequestBody @Parameter(description = "可预约采购订单查询参数", required = true) QueryAbleAppointReq queryDto) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().listAvailableBillDetail(queryDto);
    }

    @PostMapping("/available/goods")
    @ResponseBody
    @Operation(summary = "查询商品维度可预约采购订单商品信息", description = "查询可预约的商品信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询可预约商品")
    public Result<List<PmsAppointmentBillGoodsDTO>> listAvailableGoodsDetail(
            @Valid @RequestBody @Parameter(description = "可预约商品查询参数", required = true) QueryAbleAppointReq queryDto) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().listAvailableGoodsDetail(queryDto);
    }

    @PostMapping("/create")
    @ResponseBody
    @Operation(summary = "创建预约单", description = "创建新的预约单")
    @MethodLog(level = MethodLog.Level.INFO, value = "创建预约单")
    public Result<String> createAppointmentBill(
            @Valid @RequestBody @Parameter(description = "预约单信息", required = true) PmsAppointmentBillDTO appointmentBillDTO) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().createOrUpdatePmsAppointmentBill(appointmentBillDTO);
    }

    @PostMapping("/update")
    @ResponseBody
    @Operation(summary = "更新预约单", description = "更新已有的预约单")
    @MethodLog(level = MethodLog.Level.INFO, value = "更新预约单")
    public Result<String> updateAppointmentBill(
            @Valid @RequestBody @Parameter(description = "预约单信息", required = true) PmsAppointmentBillDTO appointmentBillDTO) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().createOrUpdatePmsAppointmentBill(appointmentBillDTO);
    }

    @PostMapping("/detail")
    @ResponseBody
    @Operation(summary = "预约单详情", description = "获取预约单详情")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询预约单详情")
    public Result<PmsAppointmentBillDTO> detail(
            @Valid @RequestJsonParam(value = "billNo", required = true) @Parameter(description = "预约单号", required = true) String billNo) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().detail(billNo);
    }

    @PostMapping("/submit")
    @ResponseBody
    @Operation(summary = "提交预约单", description = "提交预约单进行审核")
    @MethodLog(level = MethodLog.Level.INFO, value = "提交预约单")
    public Result<Void> submit(
            @Valid @RequestJsonParam(value = "billNo", required = true) @Parameter(description = "预约单号", required = true) String billNo) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().submit(billNo);
    }

    @PostMapping("/cancel")
    @ResponseBody
    @Operation(summary = "取消预约单", description = "取消预约单")
    @MethodLog(level = MethodLog.Level.INFO, value = "取消预约单")
    public Result<Void> cancel(
            @Valid @RequestJsonParam(value = "billNo", required = true) @Parameter(description = "预约单号", required = true) String billNo) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().cancel(billNo);
    }

    @PostMapping("/dock/available")
    @ResponseBody
    @Operation(summary = "查询停靠点可预约信息", description = "查询可预约的停靠点信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询可预约停靠点")
    public Result<List<PmsAvailableDockInfoDTO>> listAvailableDockDetail(
            @Valid @RequestBody @Parameter(description = "停靠点查询参数", required = true) PmsDeliveryDockLimitQueryDTO queryDto) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().listAvailableDockDetail(queryDto);
    }

    @PostMapping("/submit-stats")
    @ResponseBody
    @Operation(summary = "供应商预约单提交统计", description = "获取预约单提交前的统计信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询预约单提交统计")
    public Result<PmsAppointmentBillPreSubmitStatsDTO> querySubmitStats(
            @Valid @RequestJsonParam(value = "billNo", required = true) @Parameter(description = "预约单号", required = true) String billNo) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().querySubmitStats(billNo);
    }

    @PostMapping("/save-and-submit")
    @ResponseBody
    @Operation(summary = "保存并提交预约单", description = "保存预约单并直接提交")
    @MethodLog(level = MethodLog.Level.INFO, value = "保存并提交预约单")
    public Result<String> saveUpdateAndSubmit(
            @Valid @RequestBody @Parameter(description = "预约单信息", required = true) PmsAppointmentBillDTO dto) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().saveUpdateAndSubmit(dto);
    }

    @PostMapping("/demand-source-detail")
    @ResponseBody
    @Operation(summary = "查询来源单据信息", description = "根据单据号和内部ID查询来源单据信息")
    @MethodLog(level = MethodLog.Level.INFO, value = "查询来源单据信息")
    public Result<PmsDemandSourceDetailDTO> queryDemandSourceDetail(
            @Valid @RequestJsonParam(value = "refBillNo", required = true) @Parameter(description = "来源单据号", required = true) String refBillNo,
            @Valid @RequestJsonParam(value = "refInsideId", required = true) @Parameter(description = "来源内部ID", required = true) Long refInsideId) {
        return pmsApplicationManagerService.getPmsAppointmentApplicationService().queryDemandSourceDetail(refBillNo, refInsideId);
    }

    /**
     * 导出预约单列表信息
     */
    @Operation(summary = "导出预约单列表信息")
    @PostMapping("/export-list")
    @ResponseBody
    @MethodLog(level = MethodLog.Level.INFO, value = "导出预约单列表信息")
    public Result<String> exportAppointmentBillStats(@RequestBody @Valid PmsAppointmentBillQueryDTO query) {
        if(AppTypeEnum.SCM.verifyByCode(userUtil.getLoginUserWithThrow().getAppType())
                && !userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_EXPORT_BUTTON_SUP))){
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
        }
        if(AppTypeEnum.RMC.verifyByCode(userUtil.getLoginUserWithThrow().getAppType())
                && !userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_APPOINTMENT_BILL_EXPORT_BUTTON))){
            throw new ScBizException(MdErrorCodeEnum.SCPMS001B010);
        }

        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        query.setLoginUser(loginUser);
        ExportSpecification specification = PmsAppointmentBillStatsProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(query)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return Results.ofSuccess(specification.code());
    }

    /**
     * 导出预约单详细信息
     */
    @Operation(summary = "导出预约单详细信息")
    @PostMapping("/export-details")
    @ResponseBody
    @MethodLog(level = MethodLog.Level.INFO, value = "导出预约单详细信息")
    public Result<String> exportAppointmentBillDetails(@RequestBody @Valid PmsAppointmentDetailQueryDTO query) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        query.setLoginUser(loginUser);
        ExportSpecification specification = PmsAppointmentDetailProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(query)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return Results.ofSuccess(specification.code());
    }
}
