package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.nacos.common.utils.StringUtils;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.convert.pms.AccountBillConvert;
import com.meta.supplychain.demand.purch.domain.intf.PmsAccountBillDomainService;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.po.pms.PmsAccountBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsAccountBillPO;
import com.meta.supplychain.enums.CommonBillTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountDetailRepositoryService;
import com.meta.supplychain.util.AlarmUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PmsAccountBillDomainServiceImpl implements PmsAccountBillDomainService {
    @Autowired
    private PmsAccountBillRepositoryService pmsAccountBillRepositoryService;

    @Autowired
    private PmsAccountDetailRepositoryService pmsAccountDetailRepositoryService;

    private final ISupplychainControlEngineService supplychainControlEngineService;

    @Autowired
    private ICommonStockService iCommonStockService;

    @Resource
    private AlarmUtil alarmUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PmsAccountBillPO saveAccountBill(AccountBillCreateReq req){
        //查询单号是否已生成过账单，存在直接返回
        if (!StringUtils.hasText(req.getSrcBillNo())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P001);
        }
        PmsAccountBillPO accountBill = pmsAccountBillRepositoryService.getAccountByBillNo(req.getSrcBillNo());
        if (Objects.nonNull(accountBill)) {
           return accountBill;
        }

        //生成过账单号
        String accBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ACCOUNT_BILL,req.getDcCode());
        req.setBillNo(accBillNo);
        PmsAccountBillPO accountPO = AccountBillConvert.INSTANCE.convertToAccountBillPO(req);
        doOptInfo(req,accountPO);

        //明细
        List<PmsAccountBillDetailPO> detailPOList = AccountBillConvert.INSTANCE.convertToAccountDetailPOList(req.getDetailList());
        rehandleDetail(accountPO,detailPOList,req.getIsInternal());

        //落DB
        pmsAccountBillRepositoryService.save(accountPO);
        pmsAccountDetailRepositoryService.saveBatch(detailPOList);

        //上报成本，处理进销存
//        doCost(accountPO,detailPOList);
        return accountPO;
    }

    /**
     * 重新处理过账单明细
     */
    public void rehandleDetail(PmsAccountBillPO accountBill, List<PmsAccountBillDetailPO> detailList,Boolean isInternal){
        AtomicLong insideId = new AtomicLong(1);
        detailList.forEach(item->{
            item.setBillNo(accountBill.getBillNo());
            item.setSrcBillNo(accountBill.getSrcBillNo());
            item.setDcCode(accountBill.getDcCode());
            item.setDcName(accountBill.getDcName());
            item.setInsideId(insideId.getAndAdd(1));
            if(Objects.nonNull(isInternal) && isInternal){
                item.setCreateCode(accountBill.getCreateCode());
                item.setCreateName(accountBill.getCreateName());
                item.setCreateTime(accountBill.getCreateTime());
                item.setUpdateCode(accountBill.getUpdateCode());
                item.setUpdateName(accountBill.getUpdateName());
                item.setUpdateTime(accountBill.getUpdateTime());
            }

            item.setUpdateTime(LocalDateTime.now());
        });
    }

    /**
     * 处理操作人信息
     */
    private void doOptInfo(AccountBillCreateReq req, PmsAccountBillPO accountPO){
        if(Objects.nonNull(req.getIsInternal()) && req.getIsInternal()){
            accountPO.setCreateCode(req.getCreateCode());
            accountPO.setCreateName(req.getCreateName());
            accountPO.setCreateTime(LocalDateTime.now());
            accountPO.setUpdateCode(req.getCreateCode());
            accountPO.setUpdateName(req.getCreateName());
            accountPO.setUpdateTime(LocalDateTime.now());
        }
    }

    /**
     * todo 处理进销存
     */
    private void doCost(PmsAccountBillPO accountPO,List<PmsAccountBillDetailPO> detailPOList){
        CommonBillTypeEnum srcBillType = null;
        CommonBillTypeEnum billType = null;
        CommonBillTypeEnum saleBillType = null;
        CommonOperateEnum operateCode = null;
        CommonOperateEnum saleOperateCode = null;
        //单据方向 0-采购验收 1-采购验收冲红 2-采购退货 3-采购退货冲红 4-门店调拨，5-门店调拨冲红，6-退补，7-退补冲红
        PmsAccountEnum billDirectionEnum =  PmsAccountEnum.getBillDirectionByCode(accountPO.getBillDirection());
        switch (billDirectionEnum) {
            case ACCOUNT_BILL_DIRECT_PA:
                //0-采购验收
                srcBillType = CommonBillTypeEnum.GR;
                billType = CommonBillTypeEnum.GR_DP;
                operateCode = CommonOperateEnum.GR_DP_POST;
                saleBillType = CommonBillTypeEnum.SO_DP;
                saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                break;
            case ACCOUNT_BILL_DIRECT_PAR:
                //1-采购验收冲红
                srcBillType = CommonBillTypeEnum.GR;
                billType = CommonBillTypeEnum.GR_DP;
                operateCode = CommonOperateEnum.GR_DP_RED;
                saleBillType = CommonBillTypeEnum.SOR_DP;
                saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                break;
            case ACCOUNT_BILL_DIRECT_PR:
                //2-采购退货
                srcBillType = CommonBillTypeEnum.RNS;
                billType = CommonBillTypeEnum.RNS_DP;
                operateCode = CommonOperateEnum.RNS_DP_POST;
                saleBillType = CommonBillTypeEnum.SOR_DP;
                saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                break;
            case ACCOUNT_BILL_DIRECT_PRR:
                //3-采购退货冲红
                srcBillType = CommonBillTypeEnum.RNS;
                billType = CommonBillTypeEnum.RNS_DP;
                operateCode = CommonOperateEnum.RNS_DP_RED;
                saleBillType = CommonBillTypeEnum.SO_DP;
                saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                break;
            case ACCOUNT_BILL_DIRECT_TO:
                //4-门店调拨
                srcBillType = CommonBillTypeEnum.TO;
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_OUT.getCode().equals(accountPO.getRedeployType())){
                    //0-拨入部门和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.TI_DP;
                    operateCode = CommonOperateEnum.TI_DP_POST;
                    saleBillType = CommonBillTypeEnum.SO_DP;
                    saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                }
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_IN.getCode().equals(accountPO.getRedeployType())){
                    //1-拨出部门和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.TO_DP;
                    operateCode = CommonOperateEnum.TO_DP_POST;
                    saleBillType = CommonBillTypeEnum.SOR_DP;
                    saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                }
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_IOD.getCode().equals(accountPO.getRedeployType())){
                    //2-拨出拨入部门都和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.SO_DP;
                    operateCode = CommonOperateEnum.SO_DP_DELIVERY;
                    saleBillType = CommonBillTypeEnum.SOR_DP;
                    saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                }
                break;
            case ACCOUNT_BILL_DIRECT_TOR:
                //5-门店调拨冲红
                srcBillType = CommonBillTypeEnum.TO;
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_OUT.getCode().equals(accountPO.getRedeployType())){
                    //0-拨入部门和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.TI_DP;
                    operateCode = CommonOperateEnum.TI_DP_RED;
                    saleBillType = CommonBillTypeEnum.SOR_DP;
                    saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                }
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_IN.getCode().equals(accountPO.getRedeployType())){
                    //1-拨出部门和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.TO_DP;
                    operateCode = CommonOperateEnum.TO_DP_RED;
                    saleBillType = CommonBillTypeEnum.SO_DP;
                    saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                }
                if(PmsAccountEnum.ACCOUNT_REDEPLOY_TYPE_IOD.getCode().equals(accountPO.getRedeployType())){
                    //2-拨出拨入部门都和过账配送中心核算单位不一致
                    billType = CommonBillTypeEnum.SOR_DP;
                    operateCode = CommonOperateEnum.SOR_DP_POST;
                    saleBillType = CommonBillTypeEnum.SO_DP;
                    saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                }
                break;
            case ACCOUNT_BILL_DIRECT_KTB:
                //6-库存退补
                srcBillType = CommonBillTypeEnum.REB_INV;
                billType = CommonBillTypeEnum.REB_INV_DP;
                operateCode = CommonOperateEnum.REB_INV_DP_POST;
                saleBillType = CommonBillTypeEnum.SOR_DP;
                saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                break;
            case ACCOUNT_BILL_DIRECT_XTB:
                //7-销售退补
                srcBillType = CommonBillTypeEnum.REB_SALE;
                billType = CommonBillTypeEnum.REB_SALE_DP;
                operateCode = CommonOperateEnum.REB_SALE_DP_POST;
                saleBillType = CommonBillTypeEnum.SO_DP;
                saleOperateCode = CommonOperateEnum.SO_DP_DELIVERY;
                break;
            case ACCOUNT_BILL_DIRECT_XTBR:
                //8-销售退补冲红
                srcBillType = CommonBillTypeEnum.REB_SALE;
                billType = CommonBillTypeEnum.REB_SALE_DP;
                operateCode = CommonOperateEnum.REB_SALE_DP_RED;
                saleBillType = CommonBillTypeEnum.SOR_DP;
                saleOperateCode = CommonOperateEnum.SOR_DP_POST;
                break;
            default:
        }
        final CommonBillTypeEnum srcBillTypeEnum = srcBillType;
        final CommonBillTypeEnum billTypeEnum = billType;
        final CommonBillTypeEnum saleBillTypeEnum = saleBillType;
        final CommonOperateEnum operateCodeEnum = operateCode;
        final CommonOperateEnum saleOperateCodeEnum = saleOperateCode;

        List<StkTaskItemExecuteDto> skuList = detailPOList.stream().map(item ->{
            StkTaskItemExecuteDto executeDto = StkTaskItemExecuteDto.builder()
                    .srcBillNo(item.getSrcBillNo())
                    .srcInsideId(item.getSrcInsideId().intValue())
                    .srcBillType(srcBillTypeEnum.getCode())
                    .billType(billTypeEnum.getCode())
                    .operateCode(operateCodeEnum.getCode())
                    .whCode(accountPO.getDcCode())
                    .whName(accountPO.getDcName())
                    .deptCode(accountPO.getDcCode())
                    .deptName(accountPO.getDcName())
                    .insideId(item.getInsideId())
                    .supplierCode(accountPO.getSupplierCode())
                    .supplierName(accountPO.getSupplierName())
                    .contractNo(accountPO.getContractNo())
                    .skuCode(item.getSkuCode())
                    .skuName(item.getSkuName())
                    .skuType(item.getSkuType())
                    .barcode(item.getBarcode())
                    .skuModel(item.getSkuModel())
                    .saleMode(item.getSaleMode())
                    .uomAttr(item.getUomAttr())
                    .periodFlag(item.getPeriodFlag())
                    .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                    .outTaxRate(item.getOutputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                    .realQty(item.getAccQty().abs())//发生数量
                    .costTaxPrice(item.getAccPrice())
                    .costTaxMoney(item.getAccMoney())
                    .costTax(item.getAccTax())
                    .salePrice(item.getSalePrice())
                    .saleTaxMoney(item.getSaleMoney())
                    .build();
            return executeDto;
        }).collect(Collectors.toList());

        List<StkTaskItemExecuteDto> saleSkuList = CglibCopier.copy(skuList,StkTaskItemExecuteDto.class);
        saleSkuList.forEach(ss ->{
            ss.setBillType(saleBillTypeEnum.getCode());
            ss.setOperateCode(saleOperateCodeEnum.getCode());
        });

        BatchRecordReq batchRecordReq = BatchRecordReq.builder()
                .tenantId(String.valueOf(accountPO.getTenantId()))
                .whCode(accountPO.getDcCode())
                .whName(accountPO.getDcName())
                .deptCode(accountPO.getDcCode())
                .deptName(accountPO.getDcName())
                .billNo(accountPO.getBillNo())
                .billType(billTypeEnum.getCode())
                .billTime(accountPO.getCreateTime())
                .skuList(skuList)
                .build();
        BatchRecordReq saleBatchRecordReq = BatchRecordReq.builder()
                .tenantId(String.valueOf(accountPO.getTenantId()))
                .whCode(accountPO.getDcCode())
                .whName(accountPO.getDcName())
                .deptCode(accountPO.getDcCode())
                .deptName(accountPO.getDcName())
                .billNo(accountPO.getBillNo())
                .billType(saleBillTypeEnum.getCode())
                .billTime(accountPO.getCreateTime())
                .skuList(saleSkuList)
                .build();

        //代配过账，要求上报两次，一次单据，一次销售
        try {
            iCommonStockService.costStockExecute(batchRecordReq);
        }catch (Exception e){
            Logs.error(e.getMessage());
            alarmUtil.bizWorkWeChatAlarm("单据维度 代配过账异常 过账单号:"+accountPO.getBillNo()+" 原单号："+accountPO.getSrcBillNo()+" error:"+e.getMessage());
        }
        try {
            iCommonStockService.costStockExecute(saleBatchRecordReq);
        }catch (Exception e){
            Logs.error(e.getMessage());
            alarmUtil.bizWorkWeChatAlarm("销售维度 代配过账异常 过账单号:"+accountPO.getBillNo()+" 原单号："+accountPO.getSrcBillNo()+" error:"+e.getMessage());
        }
    }
}
