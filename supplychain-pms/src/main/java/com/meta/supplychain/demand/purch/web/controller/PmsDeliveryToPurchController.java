package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import com.alibaba.fastjson.JSON;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryDetailReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchSubmitReq;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchResultResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.PurchaseOrderSummaryResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 需求单管理Controller
 *
 * <AUTHOR>
 * @date 2025/03/30 02:44
 **/
@Controller
@RequestMapping("${unit-deploy.prefix-main:}/pms/delivery/to/purch")
@Tag(name = "配转采管理", description = "配转采相关接口")
public class PmsDeliveryToPurchController {
    @Autowired
    private IPmsApplicationManagerService pmsApplicationManagerService;

    @PostMapping("/generateDeliveryToPurch")
    @ResponseBody
    @Operation(summary = "生成配转采数据", description = "生成配转采数据")
//    @MethodLog(level = MethodLog.Level.INFO, value = "生成配转采数据")
    public Result<DeliveryToPurchResultResp> generateDeliveryToPurch(@RequestBody @Parameter(description = "需求单列表查询参数", required = true) DeliveryToPurchReq param){
        Logs.info("生成配转采数据.req:" + JSON.toJSONString(param));
        try{
            DeliveryToPurchResultResp deliveryToPurchResultResp = pmsApplicationManagerService.getPmsDeliveryToPurchApplicationService().generateDeliveryToPurch(param);
            Logs.info("生成配转采数据.resp:" + JSON.toJSONString(deliveryToPurchResultResp));
            return Results.ofSuccess(deliveryToPurchResultResp);
        }catch (BizException e){
            e.printStackTrace();
            Logs.error("PmsDeliveryToPurchController.generateDeliveryToPurch.error.",e);
            return Results.of(e.getError().getErrorCode(),e.getMessage(),false);
        }catch (Exception e){
            e.printStackTrace();
            Logs.error("PmsDeliveryToPurchController.generateDeliveryToPurch.error.",e);
            return Results.of(PmsErrorCodeEnum.SC_PMS_001_U001,false);
        }

    }

    @ResponseBody
    @Operation(summary = "加载订单-生成配转采数据", description = "加载订单-生成配转采数据")
    @MethodLog(level = MethodLog.Level.INFO, value = "加载订单-生成配转采数据")
    @PostMapping("/loadingDeliveryGenerateToPurch")
    public Result<DeliveryToPurchResultResp> loadingDeliveryGenerateToPurch(@RequestBody @Parameter(description = "配送单转采查询参数", required = true) DeliveryDetailReq param){
        DeliveryToPurchResultResp deliveryToPurchResultResp = pmsApplicationManagerService.getPmsDeliveryToPurchApplicationService().loadingDeliveryGenerateToPurch(param);
        return Results.ofSuccess(deliveryToPurchResultResp);
    }

    @ResponseBody
    @Operation(summary = "保存配转采订单", description = "保存配转采订单")
    @MethodLog(level = MethodLog.Level.INFO, value = "保存配转采订单")
    @PostMapping("/saveDeliveryToPurch")
    public Result<List<String>> saveDeliveryToPurch(@RequestBody @Parameter(description = "需求单列表查询参数", required = true) DeliveryToPurchSubmitReq param){
       return pmsApplicationManagerService.getPmsDeliveryToPurchApplicationService().saveDeliveryToPurch(param);
    }

    @ResponseBody
    @Operation(summary = "预览配转采订单", description = "预览配转采订单")
    @MethodLog(level = MethodLog.Level.INFO, value = "预览配转采订单")
    @PostMapping("/preview")
    public Result<PurchaseOrderSummaryResp> preview(@RequestBody @Parameter(description = "需求单列表查询参数", required = true) DeliveryToPurchSubmitReq param){
        PurchaseOrderSummaryResp purchaseOrderSummaryResp = pmsApplicationManagerService.getPmsDeliveryToPurchApplicationService().preview(param);
        return Results.ofSuccess(purchaseOrderSummaryResp);
    }
}
