package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.impl.IPmsAdjustPurchaseCoreService;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillService;
import com.meta.supplychain.demand.purch.domain.intf.AdjustOrderService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.resp.SupplierByCodeResp;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjust4AsReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchasePlanBillNumChangeReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchaseAdjustDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.util.BaseStoreUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PmsAdjustPurchaseOrderServiceImpl implements AdjustOrderService<PurchaseBillAdjust4AsReq, PmsPurchaseAdjustDTO> {
    private final PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;
    private final PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;
    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IPmsAdjustPurchaseCoreService adjustPurchaseCoreService;
    private final IBillAdjustLogRepositoryService billAdjustLogRepositoryService;
    private final CommonFranchiseService commonFranchiseService;
    private final BaseStoreUtil baseStoreUtil;
    private final PmsPurchBillService pmsPurchBillService;


    @Override
    public List<PmsPurchaseAdjustDTO> checkParam(List<PurchaseBillAdjust4AsReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }
        List<String> billNoList = reqList.stream().map(PurchaseBillAdjust4AsReq::getBillNo).collect(Collectors.toList());
        List<PmsPurchaseOrderPO> purchaseOrderPOS = pmsPurchaseOrderRepositoryService.getPurchaseByBillNos(billNoList);
        Map<String, PmsPurchaseOrderPO> purchaseBillPOMap = purchaseOrderPOS.stream().collect(Collectors.toMap(PmsPurchaseOrderPO::getBillNo, Function.identity()));
        List<PmsPurchaseBillDetailPO> purchaseBillDetailPOList = pmsPurchaseDetailRepositoryService.getPurchDetailByBillNos(billNoList);
        Map<String, List<PmsPurchaseBillDetailPO>> detailMap = purchaseBillDetailPOList.stream().collect(Collectors.groupingBy(PmsPurchaseBillDetailPO::getBillNo));
        List<PmsPurchaseAdjustDTO> dtoList = new ArrayList<>();
        reqList.forEach(item -> {
            //检查单据
            PmsPurchaseOrderPO purchaseOrderPO = purchaseBillPOMap.get(item.getBillNo());
            if (purchaseOrderPO == null) {
                return;
            }
            //单据状态 已审核+未预约
            Boolean adjustCondition = PmsPurchaseBillStatusEnum.PURCH_STATUS_AUDITED.getCode().equals(purchaseOrderPO.getStatus())
                    && YesOrNoEnum.NO.getCode().equals(purchaseOrderPO.getAppointmentSign());
            if(!adjustCondition){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_003_B016);
            }
            //原商品明细
            List<PmsPurchaseBillDetailPO> billDetailPOList = detailMap.get(item.getBillNo());
            Map<Long, PmsPurchaseBillDetailPO> detailAlreadyMap = billDetailPOList.stream().collect(Collectors.toMap(PmsPurchaseBillDetailPO::getInsideId, Function.identity()));

            String adjustBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ADJUST_LOG, purchaseOrderPO.getDeptCode());

            PmsPurchaseAdjustDTO purchaseAdjustDTO = PmsPurchaseAdjustDTO.builder().build();
            purchaseAdjustDTO.setAdjustBillNo(adjustBillNo);
            purchaseAdjustDTO.setAdjustReq(adjustPurchaseCoreService.assembleAdjustReq(item, detailAlreadyMap));
            purchaseAdjustDTO.setOldBill(purchaseOrderPO);
            purchaseAdjustDTO.setNewBill(CglibCopier.copy(purchaseOrderPO, PmsPurchaseOrderPO.class));
            purchaseAdjustDTO.setExistDetailMap(detailAlreadyMap);
            List<BillAdjustLogPO> billAdjustLog = adjustPurchaseCoreService.assembleChangeInfo(purchaseAdjustDTO, ChangeTypeEnum.ADD_SUBTRACT_ADJUST);
            purchaseAdjustDTO.setBillAdjustLog(billAdjustLog);
            dtoList.add(purchaseAdjustDTO);
        });
        return dtoList;
    }

    @Override
    public void doAdjust(List<PmsPurchaseAdjustDTO> contextList) {
        contextList.forEach(adjustDTO -> {
            //调整信息落DB
            List<BillAdjustLogPO> billAdjustLog = adjustDTO.getBillAdjustLog();
            billAdjustLogRepositoryService.saveBatch(billAdjustLog);

            //更新原单调整信息
            pmsPurchaseOrderRepositoryService.updatePurchOrderBill(adjustDTO.getNewBill());
            if (CollectionUtils.isNotEmpty(adjustDTO.getAfterBillDetail())) {
                adjustDTO.getAfterBillDetail().forEach(p -> {
                    pmsPurchaseDetailRepositoryService.updateByBillSku(p);
                });
            }
        });

    }

    @Override
    public List<FranLineAdjustReq> convertFranLineReq(List<PmsPurchaseAdjustDTO> contextList, OpInfo operatorInfo) {
        List<FranLineAdjustReq> franLineAdjustReqList = new ArrayList<>();
        contextList.forEach(adjustDTO -> {
            PmsPurchaseOrderPO oldBill = adjustDTO.getOldBill();
            PmsPurchaseOrderPO newBill = adjustDTO.getNewBill();
            SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(oldBill.getSupplierCode());
            Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(oldBill.getDeptOperateMode())
                    && PmsBillDirectionEnum.NORMAL.getCode().equals(oldBill.getBillDirection())
                    && YesOrNoEnum.NO.getCode().equals(oldBill.getTransferPurchSign())
                    && DirectSignEnum.NOT_DIRECT.getCode().equals(oldBill.getDirectSign())
                    && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(oldBill.getBillSource())
                    && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());

            //加盟店 校验额度变化值
            if (isJM) {
                BigDecimal amount = newBill.getTotalTaxMoney().subtract(oldBill.getTotalTaxMoney());
                FranLineAdjustReq franLineAdjustReq = commonFranchiseService.buildReq(oldBill.getBillNo(),oldBill.getDeptCode(),
                        amount, FranLineTypeEnum.PURCH_MODIFY.getCode(),operatorInfo);
                franLineAdjustReq.setAmount(amount.doubleValue());
                Logs.info("采购订单 {} 调整加盟店额度：{}", oldBill.getBillNo(), amount);
                if (franLineAdjustReq.getAmount() != 0) {
                    franLineAdjustReqList.add(franLineAdjustReq);
                }
            }
        });
        return franLineAdjustReqList;
    }

    @Override
    public List<BatchRecordReq> convertStockReq(List<PmsPurchaseAdjustDTO> contextList) {
        List<BatchRecordReq> batchRecordReqList = new ArrayList<>();
        contextList.forEach(adjustDTO -> {
            PmsPurchaseOrderPO oldBill = adjustDTO.getOldBill();
            List<PmsPurchaseBillDetailPO> changeBillDetail = adjustDTO.getChangeBillDetail();
            if (CollectionUtils.isNotEmpty(changeBillDetail)) {
                BatchRecordReq batchRecordReq = pmsPurchBillService.convertStockReq4Adjust(adjustDTO.getAdjustBillNo(),oldBill,changeBillDetail);
                batchRecordReqList.add(batchRecordReq);
            }
        });
        return batchRecordReqList;
    }
}
