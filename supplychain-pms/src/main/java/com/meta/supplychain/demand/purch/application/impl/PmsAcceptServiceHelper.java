package com.meta.supplychain.demand.purch.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizBillRuleService;
import com.meta.supplychain.demand.purch.application.intf.PmsAcceptApplicationService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.pms.req.accept.CancelAcceptBillDTO;
import com.meta.supplychain.entity.dto.replenishment.req.TranscodingReq;
import com.meta.supplychain.entity.po.pms.PmsAcceptBillPO;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> cat
 * @date 2024/12/11 10:32
 */
@Service
@AllArgsConstructor
public class PmsAcceptServiceHelper {

    @Autowired
    private PmsAcceptApplicationService acceptApplicationService;

    @Autowired
    private ISupplychainBizBillRuleService supplychainBizBillRuleService;
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void cancelOldAcceptBill(PmsAcceptBillPO stAcceptBill) {
        final String amendAcceptBillNumber = stAcceptBill.getAmendAcceptBillNo();
        if (StringUtils.isNotBlank(amendAcceptBillNumber)) {
            CancelAcceptBillDTO acceptBillDTO = new CancelAcceptBillDTO();
            acceptBillDTO.setBillNo(amendAcceptBillNumber);
            // 被修正单号的部门和之前一致
            acceptBillDTO.setDeptCode(stAcceptBill.getDeptCode());
            acceptApplicationService.reversalAcceptBill(acceptBillDTO);
        }
    }
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void reverseTscBill(PmsAcceptBillPO acceptBill, OpInfo opInfo) {
        // 转码单单号
        final String tscBillNo = acceptBill.getTscBillNo();
        if (StringUtils.isNotBlank(tscBillNo)) {
            Logs.info("验收单号 {} 存在转码单 {} 需要冲红", acceptBill.getBillNo(), tscBillNo);
            final TranscodingReq transcodingReq = new TranscodingReq();
            transcodingReq.setBillNumber(tscBillNo);
            transcodingReq.setDeptCode(acceptBill.getDeptCode());
            Map<String, Object> map = BeanUtil.beanToMap(opInfo);
            map.put("operateTime",new Date());
            transcodingReq.setOperatorInfo(map);
            supplychainBizBillRuleService.reverseTscBill(transcodingReq);
        }
    }
}
