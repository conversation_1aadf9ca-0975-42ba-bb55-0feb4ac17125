package com.meta.supplychain.demand.purch.domain.intf;


import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.pms.req.purch.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.*;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;

import java.util.List;

public interface PmsPurchasePlanDomainService {

    /**
     * 保存采购计划单
     * @param purchasePlanBillReq
     * @return
     */
    PurchPlanBillOptResp savePlanBill(PurchasePlanBillReq purchasePlanBillReq);

    /**
     * 校验采购计划单
     * @param purchasePlanBillReq
     * @return
     */
    Boolean checkAndSet(PurchasePlanBillReq purchasePlanBillReq);

    /**
     * 审核采购计划单
     * @param batchPurchasePlanBillReq
     * @return
     */
    List<PurchPlanBillOptResp> batchAuditBill(BatchPurchasePlanBillReq batchPurchasePlanBillReq);

    /**
     * 审核采购计划单
     * @param purchasePlanMakeReq
     * @return
     */
    PurchPlanBillOptResp auditBill(PurchasePlanMakeReq purchasePlanMakeReq);

    /**
     * 取消采购计划单
     * @param purchasePlanMakeReq
     * @return
     */
    PurchPlanBillOptResp cancelBill(PurchasePlanMakeReq purchasePlanMakeReq);

    /**
     * 过期采购计划单
     * @param purchasePlanMakeReq
     * @return
     */
    PurchPlanBillOptResp expireBill(PurchasePlanMakeReq purchasePlanMakeReq);

    /**
     * 关闭采购计划单
     * @param purchasePlanMakeReq
     * @return
     */
    PurchPlanBillOptResp closeBill(PurchasePlanMakeReq purchasePlanMakeReq);

    /**
     * 查询采购计划单列表
     * @param queryPurchasePlanBillReq
     * @return
     */
    PageResult<PmsPurchasePlanBillResp> pagePlanList(QueryPurchasePlanBillReq queryPurchasePlanBillReq);

    /**
     * 查询采购计划单明细
     * @param queryPurchasePlanBillReq
     * @return
     */
    PageResult<PmsPurchasePlanDetailResp> pageDetailList(QueryPurchasePlanBillReq queryPurchasePlanBillReq);

    /**
     * 查询采购计划单数量
     * @param queryPurchasePlanBillReq
     * @return
     */
    PmsPurchasePlanNumResp queryListNum(QueryPurchasePlanBillReq queryPurchasePlanBillReq);

    /**
     * 查询采购计划单详情
     * @param queryPurchasePlanBillDetailReq
     * @return
     */
    PmsPurchasePlanBillResp queryPlanDetail(QueryPurchasePlanBillDetailReq queryPurchasePlanBillDetailReq);

    /**
     * 构建详情
     * @param resp
     * @param orderDetailList
     * @return
     */
    void buildPlanDetail(PmsPurchasePlanBillResp resp, List<PurchaseBillDetailResp> orderDetailList);

    /**
     * 采购计划单打印次数增加
     * @param queryPurchasePlanBillReq
     * @return
     */
    void addPrintCount(QueryPurchasePlanBillPrintReq queryPurchasePlanBillReq);

    /**
     * 修改采购计划数量
     * @param batchPurchasePlanBillReq
     * @return
     */
    PurchPlanBillOptResp updateNum(PurchasePlanBillNumChangeReq batchPurchasePlanBillReq);

    /**
     * 获取待过期的单据
     * @return
     */
    List<PmsPurchasePlanBillPO> getExpirePlanBill();

}