package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchasePlanDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjust4AsReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchasePlanBillNumChangeReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchaseAdjustDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.req.BatchStockReportReq;
import com.meta.supplychain.entity.dto.wds.WdsDeliveryAdjustDTO;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import feign.RetryableException;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BatchAdjustBillService {

    private final ICommonFranchiseService commonFranchiseService;
    final ICommonStockService iCommonStockService;
    final WdsAdjustDeliveryOrderServiceImpl wmsAdjustDeliveryOrderService;
    final PmsAdjustPurchaseOrderServiceImpl pmsAdjustPurchaseOrderService;
    final PmsPurchasePlanDomainService pmsPurchasePlanDomainService;

    public void adjustBill(List<WdsDeliveryOrderAdjustBaseReq> deliveryReq,
                                     List<PurchaseBillAdjust4AsReq> pmsReq,
                                     OpInfo operatorInfo) {
        String uniqueId = String.valueOf(IdWorker.getId());
        // 校验参数
        List<WdsDeliveryAdjustDTO> dtoList = wmsAdjustDeliveryOrderService.checkParam(deliveryReq);
        List<PmsPurchaseAdjustDTO> purchDtoList = pmsAdjustPurchaseOrderService.checkParam(pmsReq);

        //更新计划单剩余可采
        List<PurchasePlanBillNumChangeReq> planBillNumChangeReqList = convertPlanChangeReq(purchDtoList);
        if(CollectionUtils.isNotEmpty(planBillNumChangeReqList)){
            List<PurchasePlanBillNumChangeReq> successPlanReq = updatePlanBillNum(planBillNumChangeReqList);
            if (CollectionUtils.isEmpty(successPlanReq)) {
                rollbackPlanBillNum(planBillNumChangeReqList);
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_003_B025);
            }else {
                // 部分成功 需要回滚成功的请求体
                if (successPlanReq.size() != planBillNumChangeReqList.size()) {
                    rollbackPlanBillNum(successPlanReq);
                    BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_003_B025);
                }
            }
        }

        // 组装数据 batchRecordReqList  franLineAdjustReqList
        List<FranLineAdjustReq> deliveryFranLineAdjustReqs = wmsAdjustDeliveryOrderService.convertFranLineReq(dtoList, operatorInfo);
        List<FranLineAdjustReq> purchFranLineAdjustReqs = pmsAdjustPurchaseOrderService.convertFranLineReq(purchDtoList, operatorInfo);
        // 非加盟不需要处理的 不生成请求实体
        List<FranLineAdjustReq> franLineAdjustReqList = new ArrayList<>();
        franLineAdjustReqList.addAll(deliveryFranLineAdjustReqs);
        franLineAdjustReqList.addAll(purchFranLineAdjustReqs);
        if (CollectionUtils.isNotEmpty(franLineAdjustReqList)) {
            List<FranLineAdjustReq> successAdjustReq = adjustFranLine(franLineAdjustReqList);
            if (CollectionUtils.isEmpty(successAdjustReq)) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D008);
            }else {
                // 部分成功 需要回滚成功的请求体
                if (successAdjustReq.size() != franLineAdjustReqList.size()) {
                    franLineAdjustRollback(successAdjustReq);
                    BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D008);
                }
            }
        }

        // 加盟全部调整成功 franLineAdjustReqList
        List<BatchRecordReq> batchRecordReqList = new ArrayList<>();
        List<BatchRecordReq> deliveryReqList = wmsAdjustDeliveryOrderService.convertStockReq(dtoList);
        List<BatchRecordReq> purchReqList = pmsAdjustPurchaseOrderService.convertStockReq(purchDtoList);
        batchRecordReqList.addAll(deliveryReqList);
        batchRecordReqList.addAll(purchReqList);
        if (CollectionUtils.isNotEmpty(batchRecordReqList)) {
            try {
                syncStock(batchRecordReqList, uniqueId);
            } catch (BizException | DecodeException e) {
                // 业务或者解析异常  停留在待审核
                Logs.error("成本库存同步失败，{}", e.getMessage());
                franLineAdjustRollback(franLineAdjustReqList);
                throw e;
            } catch (Exception e) {
                // 其它异常
                franLineAdjustRollback(franLineAdjustReqList);
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D007);
            }
        }
        // 保存数据
        wmsAdjustDeliveryOrderService.doAdjust(dtoList);
        pmsAdjustPurchaseOrderService.doAdjust(purchDtoList);
    }



    /**
     * 调整加盟额度
     * @param franLineAdjustReqList
     * @return 成功回滚的请求体
     */
    List<FranLineAdjustReq> adjustFranLine(List<FranLineAdjustReq> franLineAdjustReqList){
        List<FranLineAdjustReq> successAdjustReq = new ArrayList<>();
        for (FranLineAdjustReq item : franLineAdjustReqList) {
            try {
                commonFranchiseService.adjustFranLine(item);
                successAdjustReq.add(item);
            } catch (Exception e) {
                Logs.info("单据号 {} 加盟店额度调整异常 {}", item.getBillNumber(), e);
                // 失败则后面的不再继续执行
                break;
            }
        }
        return successAdjustReq;
    }


    /**
     * 统一回滚加盟
     * @param franLineAdjustReqList
     */
    void franLineAdjustRollback(List<FranLineAdjustReq> franLineAdjustReqList){
        franLineAdjustReqList.forEach(commonFranchiseService::franLineAdjustRollback);
    }


    /**
     * 同步库存
     * @param batchRecordReqList
     */
    void syncStock(List<BatchRecordReq> batchRecordReqList,String uniqueId){
        iCommonStockService.costStockExecute(BatchStockReportReq.builder()
                .uniqueId(uniqueId)
                .executeDtoList(batchRecordReqList)
                .build());
    }

    public List<PurchasePlanBillNumChangeReq> convertPlanChangeReq(List<PmsPurchaseAdjustDTO> contextList) {
        List<PurchasePlanBillNumChangeReq> planChangeList = new ArrayList<>();
        contextList.forEach(adjustDTO -> {
            PmsPurchaseOrderPO oldBill = adjustDTO.getOldBill();
            List<PmsPurchasePlanDetailPO> planDetailList = adjustDTO.getPlanDetailList();
            List<PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq> goodsList = planDetailList.stream().map(item -> {
                Integer updateType = item.getPlanReqQty().compareTo(BigDecimal.ZERO) > 0 ? 2:1;

                PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq goods = PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq.builder()
                        .skuCode(item.getSkuCode())
                        .skuType(item.getSkuType())
                        .updateQty(item.getPlanReqQty().abs())
                        .updateType(updateType)
                        .build();
                return goods;
            }).collect(Collectors.toList());

            PurchasePlanBillNumChangeReq req = PurchasePlanBillNumChangeReq.builder()
                    .billNo(oldBill.getPlanBillNo())
                    .goodsList(goodsList)
                    .build();
            planChangeList.add(req);
        });
        return planChangeList;
    }
    /**
     * 更新计划单剩余可采
     * @param planList
     * @return
     */
    public List<PurchasePlanBillNumChangeReq> updatePlanBillNum(List<PurchasePlanBillNumChangeReq> planList){
        List<PurchasePlanBillNumChangeReq> successAdjustReq = new ArrayList<>();
        for (PurchasePlanBillNumChangeReq item : planList) {
            try {
                pmsPurchasePlanDomainService.updateNum(item);
                successAdjustReq.add(item);
            } catch (Exception e) {
                Logs.info("追加追减 采购计划单 {} 更新剩余可采失败 {}", item.getBillNo(), e);
                break;
            }
        }
        return successAdjustReq;
    }

    /**
     * 回滚计划单剩余可采 修改类型 1增加 2减少
     * @param
     */
    void rollbackPlanBillNum(List<PurchasePlanBillNumChangeReq> planList){
        planList.forEach(item ->{
            List<PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq> goodsList = item.getGoodsList().stream().map(goods -> {
                PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq backGoods = CglibCopier.copy(goods,PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq.class);
                backGoods.setUpdateType(backGoods.getUpdateType() == 1? 2:1);
                return backGoods;
            }).collect(Collectors.toList());
            item.setGoodsList(goodsList);
            try {
                pmsPurchasePlanDomainService.updateNum(item);
            } catch (Exception e) {
                Logs.info("追加追减 采购计划单 {} 回滚失败 {}", item.getBillNo(), e);
            }
        });
    }
}
