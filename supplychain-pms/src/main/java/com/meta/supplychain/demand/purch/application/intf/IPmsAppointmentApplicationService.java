package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.pms.req.appointment.*;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryAbleAppointReq;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPreSubmitStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;

import java.util.List;

/**
 * 预约单应用服务接口
 *
 * <AUTHOR>
 * @date 2025/04/20 21:06
 **/
public interface IPmsAppointmentApplicationService {

    /**
     * 多条件查询预约单列表（包含统计信息）
     * @param queryDTO 查询条件DTO
     * @return 预约单列表（包含统计信息）
     */
    Result<PageResult<PmsAppointmentBillStatsDTO>> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO);

    /**
     * 预约单详细查询
     * @param queryDTO 查询条件DTO
     * @return 预约单详细信息列表
     */
    Result<PageResult<PmsAppointmentDetailDTO>> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO);

    /**
     * 查询单据维度可预约采购订单商品信息
     * @param queryDto 查询条件
     * @return 可预约的采购订单信息
     */
    Result<List<PmsAppointmentBillPurchDTO>> listAvailableBillDetail(QueryAbleAppointReq queryDto);

    /**
     * 查询商品维度可预约采购订单商品信息
     * @param queryDto 查询条件
     * @return 可预约的商品信息
     */
    Result<List<PmsAppointmentBillGoodsDTO>> listAvailableGoodsDetail(QueryAbleAppointReq queryDto);

    /**
     * 创建/更新预约单
     * 包含预约单号即为更新操作
     * @param pmsAppointmentBillDTO 预约单DTO
     * @return 预约单号
     */
    Result<String> createOrUpdatePmsAppointmentBill(PmsAppointmentBillDTO pmsAppointmentBillDTO);

    /**
     * 预约单详情（采购单维度的数据）
     * @param billNo 预约单号
     * @return 预约单详情
     */
    Result<PmsAppointmentBillDTO> commonDetail(String billNo);

    /**
     * 预约单详情
     * @param billNo 预约单号
     * @return 预约单详情
     */
    Result<PmsAppointmentBillDTO> detail(String billNo);

    /**
     * 提交预约单
     * @param billNo 预约单号
     */
    Result<Void> submit(String billNo);

    /**
     * 取消预约单
     * @param billNo 预约单号
     */
    Result<Void> cancel(String billNo);

    /**
     * 查询停靠点可预约信息
     * @param queryDto 查询条件
     * @return 可预约的停靠点信息
     */
    Result<List<PmsAvailableDockInfoDTO>> listAvailableDockDetail(PmsDeliveryDockLimitQueryDTO queryDto);

    /**
     * 供应商预约单提交统计
     * @param billNo 预约单号
     * @return 预约单提交统计信息
     */
    Result<PmsAppointmentBillPreSubmitStatsDTO> querySubmitStats(String billNo);

    /**
     * 保存并提交预约单
     * @param dto 预约单DTO
     * @return 预约单号
     */
    Result<String> saveUpdateAndSubmit(PmsAppointmentBillDTO dto);

    /**
     * 查询来源单据信息
     * @param refBillNo 来源单据号
     * @param refInsideId 来源内部ID
     * @return 来源单据信息
     */
    Result<PmsDemandSourceDetailDTO> queryDemandSourceDetail(String refBillNo, Long refInsideId);
} 