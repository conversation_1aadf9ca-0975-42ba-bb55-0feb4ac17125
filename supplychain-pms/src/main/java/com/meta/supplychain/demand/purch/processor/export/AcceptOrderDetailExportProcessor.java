package com.meta.supplychain.demand.purch.processor.export;


import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.convert.pms.AcceptBillCopier;
import com.meta.supplychain.demand.purch.application.intf.PmsAcceptApplicationService;
import com.meta.supplychain.entity.dto.pms.req.accept.QueryAcceptDetailReq;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsAcceptBillStateEnum;
import com.meta.supplychain.enums.pms.PmsBillDirectionEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;

@ExportSpecification(code = "AcceptOrderDetailExportProcessor", name = "验收单明细列表导出", executeType = ExecuteType.CLUSTER)
public class AcceptOrderDetailExportProcessor implements ExportProcessor<QueryAcceptDetailReq, AcceptDetailResp, AcceptDetailView> {

    private PmsAcceptApplicationService pmsAcceptApplicationService(){
        return SpringContextUtil.getApplicationContext().getBean(PmsAcceptApplicationService.class);
    }

    @Override
    public Integer totalCount(BizUser bizUser, QueryAcceptDetailReq queryAcceptDTO) throws BizException {
        List<AcceptDetailResp> detailList = pmsAcceptApplicationService().queryAcceptDetailList(queryAcceptDTO);
        return detailList.size();
    }

    @Override
    public List<AcceptDetailResp> queryData(BizUser bizUser, QueryAcceptDetailReq queryAcceptDTO, BizExportPage bizExportPage) throws BizException {
        return pmsAcceptApplicationService().queryAcceptDetailList(queryAcceptDTO);
    }

    @Override
    public List<AcceptDetailView> convert(BizUser bizUser, QueryAcceptDetailReq queryAcceptDTO, List<AcceptDetailResp> list) throws BizException {
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Integer billDirection = list.stream().map(AcceptDetailResp::getBillDirection).findFirst().orElse(null);
        boolean showPriceFlag = false;
        if(PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)){
            showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_CGYS_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        }else {
            showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_CGTH_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        }
        Logs.info("用户{}是否拥有查看价格权限{}",bizUser.getBizUserId(),showPriceFlag);
        boolean finalShowPriceFlag = showPriceFlag;
        return Lists.transform(list, input -> {
            AcceptDetailView acceptDetailView = AcceptBillCopier.INSTANCE.convertToDetailView(input);
            acceptDetailView.setCreator(input.getCreateCode()+input.getCreateName());
            if(StringUtils.isNotBlank(input.getAuditManCode())){
                acceptDetailView.setAuditMan(input.getAuditManCode()+input.getAuditManName());
            }
            if(StringUtils.isNotBlank(input.getUpdateCode())){
                acceptDetailView.setUpdater(input.getUpdateCode()+input.getUpdateName());
            }
            if(StringUtils.isNotBlank(input.getSubmitManCode())){
                acceptDetailView.setSubmitMan(input.getSubmitManCode()+input.getSubmitManName());
            }
            //包装率
            BigDecimal unitRate = acceptDetailView.getUnitRate();
            //验收数量
            BigDecimal acceptQty = acceptDetailView.getAcceptQty();
            //整件数量 = 验收数量 / 包装率
            acceptDetailView.setWholeQty(acceptQty.divide(unitRate, 0, RoundingMode.FLOOR));
            //零头数量 = 验收数量 % 包装率
            acceptDetailView.setOddQty(acceptQty.divideAndRemainder(unitRate)[1]);
            acceptDetailView.setReversalBillSignDesc(YesOrNoEnum.getDescByCode(input.getReversalBillSign()));
            acceptDetailView.setStatusDesc(PmsAcceptBillStateEnum.getNameByCode(input.getStatus()));
            if(YesOrNoEnum.YES.getCode().equals(input.getReversalBillSign())){
                acceptDetailView.setPurchPrice(formatMoney(input.getPurchPrice().multiply(BigDecimal.valueOf(-1))));
                acceptDetailView.setSalePrice(formatMoney(input.getSalePrice().multiply(BigDecimal.valueOf(-1))));
                acceptDetailView.setSaleMoney(formatMoney(input.getSaleMoney().multiply(BigDecimal.valueOf(-1))));
                acceptDetailView.setPurchTax(formatMoney(input.getPurchTax().multiply(BigDecimal.valueOf(-1))));
            }else {
                acceptDetailView.setPurchPrice(formatMoney(input.getPurchPrice()));
                acceptDetailView.setSalePrice(formatMoney(input.getSalePrice()));
                acceptDetailView.setSaleMoney(formatMoney(input.getSaleMoney()));
                acceptDetailView.setPurchTax(formatMoney(input.getPurchTax()));
            }
            if(!finalShowPriceFlag){
                acceptDetailView.setPurchPrice("****");
                acceptDetailView.setPurchTax("****");
            }
            return acceptDetailView;
        });
    }

    public static String formatMoney(BigDecimal money) {
        if (money == null) {
            return "0";
        }
        // 去除末尾零
        BigDecimal stripped = money.stripTrailingZeros();
        // 如果是整数（scale == 0），直接返回整数形式
        if (stripped.scale() <= 0) {
            return stripped.toBigIntegerExact().toString();
        }
        // 设置 scale 至少为 2
        int scale = Math.max(stripped.scale(), 2);
        BigDecimal rounded = stripped.setScale(scale, RoundingMode.UNNECESSARY);
        // 构建格式化模式
        StringBuilder pattern = new StringBuilder("#.");
        for (int i = 0; i < scale; i++) {
            pattern.append('#');
        }
        DecimalFormat df = new DecimalFormat(pattern.toString());
        return df.format(rounded);
    }

    public static void main(String[] args) {
        BigDecimal money = new BigDecimal("0.0123000");
        System.out.println(formatMoney(money));
    }
}
