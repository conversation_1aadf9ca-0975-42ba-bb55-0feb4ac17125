package com.meta.supplychain.demand.purch.application.impl;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.demand.purch.application.intf.IPmsAccountApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.PmsAccountBillDomainService;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.dto.pms.resp.account.AccountBillResp;
import com.meta.supplychain.entity.po.pms.PmsAccountBillPO;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.util.RedisUtil;
import com.meta.supplychain.util.config.RedisCacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 这是是类描述
 *
 **/
@Service
public class PmsAccountBillApplicationServiceImpl implements IPmsAccountApplicationService {

    @Autowired
    private PmsAccountBillDomainService pmsAccountBillDomainService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Result<AccountBillResp> createAccountBill(AccountBillCreateReq req) {
        String key = TenantContext.get() +RedisCacheConstant.ACCOUNT_BILL_PREFIX + req.getSrcBillNo();
        if (!redisUtil.tryLock(key, req.getSrcBillNo(), RedisCacheConstant.EXPIRE_1_MINUTES)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            PmsAccountBillPO accountBill = pmsAccountBillDomainService.saveAccountBill(req);
            return new Result<>(new AccountBillResp(accountBill.getBillNo(),accountBill.getSrcBillNo(),""));
        } catch (BizException e) {
            Logs.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            Logs.error(e.getMessage(), e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_006_B0001);
        } finally {
            redisUtil.unlock(key);
        }
        return new Result<>(new AccountBillResp("",req.getSrcBillNo(),""));
    }

}
