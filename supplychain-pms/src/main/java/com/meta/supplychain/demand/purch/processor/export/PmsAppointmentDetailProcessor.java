package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.context.ThreadLocals;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.meta.supplychain.convert.pms.PmsAppointmentDetailConvert;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.view.PmsAppointmentDetailView;
import com.meta.supplychain.util.spring.SpringContextUtil;
import cn.linkkids.framework.croods.common.PageResult;
import com.metadata.idaas.client.cons.ClientIdentCons;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约单详细信息导出处理器
 * <AUTHOR>
 */
@ExportSpecification(code = "PmsAppointmentDetailProcessor", name = "预约单详细信息导出", executeType = ExecuteType.CLUSTER)
public class PmsAppointmentDetailProcessor implements ExportProcessor<PmsAppointmentDetailQueryDTO, PmsAppointmentDetailDTO, PmsAppointmentDetailView> {

    @Override
    public Integer totalCount(BizUser bizUser, PmsAppointmentDetailQueryDTO queryDTO) throws BizException {
        IPmsAppointmentDomainService appointmentDomainService = SpringContextUtil.getApplicationContext().getBean(IPmsAppointmentDomainService.class);

        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(1L);
        ThreadLocals.setValue(ClientIdentCons.KEY_LOGIN_USER, queryDTO.getLoginUser());
        PageResult<PmsAppointmentDetailDTO> pageResult = appointmentDomainService.selectAppointmentBillDetails(queryDTO);
        return Long.valueOf(pageResult.getTotal()).intValue();
    }

    @Override
    public List<PmsAppointmentDetailDTO> queryData(BizUser bizUser, PmsAppointmentDetailQueryDTO queryDTO, BizExportPage bizExportPage) throws BizException {
        IPmsAppointmentDomainService appointmentDomainService = SpringContextUtil.getApplicationContext().getBean(IPmsAppointmentDomainService.class);

        queryDTO.setCurrent(Long.valueOf(bizExportPage.getNo()));
        queryDTO.setPageSize(Long.valueOf(bizExportPage.getSize()));
        ThreadLocals.setValue(ClientIdentCons.KEY_LOGIN_USER, queryDTO.getLoginUser());
        PageResult<PmsAppointmentDetailDTO> pageResult = appointmentDomainService.selectAppointmentBillDetails(queryDTO);
        return pageResult.getRows();
    }

    @Override
    public List<PmsAppointmentDetailView> convert(BizUser bizUser, PmsAppointmentDetailQueryDTO queryDTO, List<PmsAppointmentDetailDTO> appointmentDetailDTOList) throws BizException {
        return appointmentDetailDTOList.stream()
                .map(PmsAppointmentDetailConvert.INSTANCE::dto2view)
                .collect(Collectors.toList());
    }
} 