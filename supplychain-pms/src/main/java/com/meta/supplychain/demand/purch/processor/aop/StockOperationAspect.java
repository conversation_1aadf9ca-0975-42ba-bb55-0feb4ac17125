package com.meta.supplychain.demand.purch.processor.aop;

import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.demand.purch.enums.BillOperateTypeEnum;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDTO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import com.meta.supplychain.enums.DeptOperateModeEnum;
import com.meta.supplychain.enums.FranLineTypeEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.util.List;

import static io.vertx.sqlclient.data.NullValue.BigDecimal;

@Aspect
@Component
@Slf4j
public class StockOperationAspect {

    // 定义切入点：拦截PmsOrderApplyDomainService的costStockExecute方法
    @Pointcut("execution(* com.meta.supplychain.demand.purch.domain.intf.PmsOrderApplyDomainService.costStockExecute(..))")
    public void costStockExecuteMethod() {
    }

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private CommonFranchiseService commonFranchiseService;

    @Value("${franLineAdjust_token:''}")
    String token;

    // 环绕通知：记录方法执行时间和参数
    @Around("execution(* com.meta.supplychain.demand.purch.domain.intf.PmsOrderApplyDomainService.costStockExecute(..))")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object[] args = joinPoint.getArgs();
        ApplyBillDTO applyBillDTO = (ApplyBillDTO) args[0];
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = (List<PmsApplyBillDetailPO>) args[1];
        BillOperateTypeEnum operateType = (BillOperateTypeEnum) args[2];
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        boolean isJM = DeptOperateModeEnum.JM.getCode().equals(applyBillDTO.getOperateMode());
        FranLineAdjustReq adjustReq = null;
        //加盟额度扣减
        if (isJM) {
            BigDecimal totalDetailMoney = pmsApplyBillDetailPOS.stream().map(PmsApplyBillDetailPO::getPurchTaxMoney).reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            boolean isAdjust = totalDetailMoney.compareTo(applyBillDTO.getTotalPurchTaxMoney()) != 0;
            BigDecimal amount = isAdjust ? totalDetailMoney.subtract(applyBillDTO.getTotalPurchTaxMoney()) : applyBillDTO.getTotalPurchTaxMoney();
            Integer type = isAdjust ? FranLineTypeEnum.APPLY_MODIFY.getCode() : BillOperateTypeEnum.CANCEL == operateType ? FranLineTypeEnum.APPLY_CANCEL.getCode() : FranLineTypeEnum.APPLY.getCode();
            adjustReq = buildReq(applyBillDTO.getBillNo(), applyBillDTO.getDeptCode(), amount, type, opInfo);
            try {
                commonFranchiseService.adjustFranLine(adjustReq);
            } catch (Exception e) {
                Logs.error("加盟额度扣减失败，{}", e.getMessage());
                return Results.of(PmsErrorCodeEnum.SC_PMS_003_B021.getErrorCode(), PmsErrorCodeEnum.SC_PMS_003_B021.getErrorMsg() + "," + e.getMessage(), false);
            }
        }

        try {
            // 打印入参日志
            log.info("costStockExecute invoked with params: applyBillDTO={}, detailListSize={}, operateType={}",
                    args[0], ((List<?>) args[1]).size(), args[2]);

            // 执行原方法
            Object result = joinPoint.proceed();

            // 记录耗时
            long duration = System.currentTimeMillis() - startTime;
            log.info("costStockExecute executed in {} ms", duration);

            return result;
        } catch (Exception ex) {
            //todo 回滚加盟
            if(isJM) {
                commonFranchiseService.franLineAdjustRollback(adjustReq);
            }
            log.error("costStockExecute failed: {}", ex.getMessage());
            throw ex;
        }
    }

    /**
     * 组装调整加盟额度入参
     */
    public FranLineAdjustReq buildReq(String billNo, String deptCode, BigDecimal amount, Integer type, OpInfo operatorInfo) {
        long timeMillis = System.currentTimeMillis();
        return FranLineAdjustReq.builder()
                .appCode("supplychain")
                .timeStamp(timeMillis)
                .sign(getSign(timeMillis))
                .createCode(operatorInfo.getOperatorCode())
                .createName(operatorInfo.getOperatorName())
                .type(type)
                .billNumber(billNo)
                .amount(amount.doubleValue())
                .storeCode(deptCode)
                .build();
    }

    /**
     * 验签规则
     */
    private String getSign(long timeStamp) {
        //测试环境 token
        return DigestUtils.md5DigestAsHex(("key=" + token + "&_platform_num=" + TenantContext.get() + "&timeStamp=" + timeStamp).toUpperCase().getBytes());
    }

}
