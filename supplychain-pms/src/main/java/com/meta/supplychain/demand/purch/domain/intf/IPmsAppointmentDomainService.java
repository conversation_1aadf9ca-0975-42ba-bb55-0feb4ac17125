package com.meta.supplychain.demand.purch.domain.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.pms.req.appointment.*;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryAbleAppointReq;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.PmsAppointmentBillGoodsQueryResultDTO;

import java.util.List;

public interface IPmsAppointmentDomainService {
    
    /**
     * 多条件查询预约单列表（包含统计信息）
     * @param queryDTO 查询条件DTO
     * @return 预约单列表（包含统计信息）
     */
    PageResult<PmsAppointmentBillStatsDTO> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO);

    /**
     * 预约单详细查询
     * @param queryDTO 查询条件DTO
     * @return 预约单详细信息列表
     */
    PageResult<PmsAppointmentDetailDTO> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO);

    List<PmsAppointmentBillPurchDTO> listAvailableBillDetail(QueryAbleAppointReq queryDto);

    List<PmsAppointmentBillGoodsDTO> listAvailableGoodsDetail(QueryAbleAppointReq queryDto);

    String createOrUpdatePmsAppointmentBill(PmsAppointmentBillDTO pmsAppointmentBillDTO);

    PmsAppointmentBillDTO commonDetail(String billNo);

    PmsAppointmentBillDTO detail(String billNo);

    PmsAppointmentBillPreSubmitStatsDTO querySubmitStats(String billNo);

    void submit(String billNo);

    String saveUpdateAndSubmit(PmsAppointmentBillDTO dto);

    void cancel(String billNo);

    List<PmsAvailableDockInfoDTO> listAvailableDockDetail(PmsDeliveryDockLimitQueryDTO queryDto);

    PmsDemandSourceDetailDTO queryDemandSourceDetail(String refBillNo, Long refInsideId);

    List<PmsAppointmentBillGoodsQueryResultDTO> queryAppointmentBillGoodsDetail(String purchBillNo);
}