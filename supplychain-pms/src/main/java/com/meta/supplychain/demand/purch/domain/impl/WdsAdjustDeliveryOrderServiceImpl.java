package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.impl.IWdsAdjustDeliveryOrderCoreService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.demand.purch.domain.intf.AdjustOrderService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.wds.WdsDeliveryAdjustDTO;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustReq;
import com.meta.supplychain.entity.dto.wds.resp.WdsDeliveryOrderAdjustResp;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderBillStatusEnum;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.infrastructure.repository.service.impl.wds.WdDeliveryBillDetailRepositoryImpl;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.BaseStoreUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WdsAdjustDeliveryOrderServiceImpl implements AdjustOrderService<WdsDeliveryOrderAdjustBaseReq, WdsDeliveryAdjustDTO> {

    private final IWdDeliveryBillRepository wdDeliveryBillRepositoryService;
    private final IBillAdjustLogRepositoryService adjustLogRepositoryService;
    private final WdDeliveryBillDetailRepositoryImpl wdsDeliveryBillDetailRepositoryService;
    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IWdsAdjustDeliveryOrderCoreService adjustDeliveryOrderCoreService;
    private final BaseStoreUtil baseStoreUtil;



    public List<WdsDeliveryOrderAdjustResp> adjustDeliveryOrder(List<WdsDeliveryOrderAdjustBaseReq> req, OpInfo operatorInfo) {

        List<String> billNoList = req.stream().map(WdsDeliveryOrderAdjustBaseReq::getBillNo).collect(Collectors.toList());
        List<WdDeliveryBillPO> wdDeliveryBillPOS = wdDeliveryBillRepositoryService.queryDeliveryOrderBillByBillNo(billNoList);
        Map<String, WdDeliveryBillPO> deliveryBillPOMap = wdDeliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = wdsDeliveryBillDetailRepositoryService.queryDetailListByBillNo(billNoList);
        Map<String, List<WdDeliveryBillDetailPO>> detailMap = deliveryBillDetailPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
        List<WdsDeliveryOrderAdjustResp> billResps = new ArrayList<>();
        req.forEach(item -> {
            try {
                //检查单据
                WdDeliveryBillPO wdDeliveryBillPO = deliveryBillPOMap.get(item.getBillNo());
                if (wdDeliveryBillPO == null) {
                    return;
                }
                //单据状态 已审核+未预约
                Boolean adjustCondition = WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode().equals(wdDeliveryBillPO.getStatus());
                if (!adjustCondition) {
                    billResps.add(new WdsDeliveryOrderAdjustResp(item.getBillNo(), WDErrorCodeEnum.SC_WDS_001_P012.getDesc()));
                }

                //原商品明细
                List<WdDeliveryBillDetailPO> billDetailPOList = detailMap.get(item.getBillNo());
                Map<Long, WdDeliveryBillDetailPO> detailAlreadyMap = billDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));


                String adjustBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ADJUST_LOG, wdDeliveryBillPO.getInDeptCode());

                WdsDeliveryAdjustDTO wdsDeliveryAdjustDTO = adjustDeliveryOrderCoreService.assembleAdjustReq(CglibCopier.copy(item, WdsDeliveryOrderAdjustReq.class), detailAlreadyMap);
                wdsDeliveryAdjustDTO.setAdjustBillNo(adjustBillNo);
                wdsDeliveryAdjustDTO.setOldBill(wdDeliveryBillPO);
                wdsDeliveryAdjustDTO.setNewBill(CglibCopier.copy(wdDeliveryBillPO, WdDeliveryBillPO.class));
                List<BillAdjustLogPO> billAdjustLog = adjustDeliveryOrderCoreService.assembleChangeInfo(wdsDeliveryAdjustDTO);
                adjustDeliveryOrderCoreService.doAdjust(wdsDeliveryAdjustDTO, operatorInfo, billAdjustLog);
            } catch (Exception e) {
                billResps.add(new WdsDeliveryOrderAdjustResp(item.getBillNo(), e.getMessage()));
            }
        });

        return billResps;
    }

    @Override
    public List<WdsDeliveryAdjustDTO> checkParam(List<WdsDeliveryOrderAdjustBaseReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Collections.emptyList();
        }
        List<String> billNoList = reqList.stream().map(WdsDeliveryOrderAdjustBaseReq::getBillNo).collect(Collectors.toList());
        List<WdDeliveryBillPO> wdDeliveryBillPOS = wdDeliveryBillRepositoryService.queryDeliveryOrderBillByBillNo(billNoList);
        Map<String, WdDeliveryBillPO> deliveryBillPOMap = wdDeliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = wdsDeliveryBillDetailRepositoryService.queryDetailListByBillNo(billNoList);
        Map<String, List<WdDeliveryBillDetailPO>> detailMap = deliveryBillDetailPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
        List<WdsDeliveryAdjustDTO> dtoList = new ArrayList<>();
        reqList.forEach(item -> {
            //检查单据
            WdDeliveryBillPO wdDeliveryBillPO = deliveryBillPOMap.get(item.getBillNo());
            if (wdDeliveryBillPO == null) {
                return;
            }
            //单据状态 已审核+未预约
            Boolean adjustCondition = WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode().equals(wdDeliveryBillPO.getStatus());
            if (!adjustCondition) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_P012);
            }
            //原商品明细
            List<WdDeliveryBillDetailPO> billDetailPOList = detailMap.get(item.getBillNo());
            Map<Long, WdDeliveryBillDetailPO> detailAlreadyMap = billDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));


            String adjustBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ADJUST_LOG, wdDeliveryBillPO.getInDeptCode());

            WdsDeliveryAdjustDTO wdsDeliveryAdjustDTO = adjustDeliveryOrderCoreService.assembleAdjustReq(CglibCopier.copy(item, WdsDeliveryOrderAdjustReq.class), detailAlreadyMap);
            wdsDeliveryAdjustDTO.setAdjustBillNo(adjustBillNo);
            wdsDeliveryAdjustDTO.setOldBill(wdDeliveryBillPO);
            wdsDeliveryAdjustDTO.setNewBill(CglibCopier.copy(wdDeliveryBillPO, WdDeliveryBillPO.class));
            List<BillAdjustLogPO> billAdjustLog = adjustDeliveryOrderCoreService.assembleChangeInfo(wdsDeliveryAdjustDTO);
            wdsDeliveryAdjustDTO.setBillAdjustLog(billAdjustLog);
            dtoList.add(wdsDeliveryAdjustDTO);
        });
        return dtoList;
    }

    @Override
    public List<FranLineAdjustReq> convertFranLineReq(List<WdsDeliveryAdjustDTO> contextList, OpInfo operatorInfo) {
        List<FranLineAdjustReq> franLineAdjustReqList = new ArrayList<>();
        contextList.forEach(deliveryAdjustDTO -> {
            WdDeliveryBillPO newBill = deliveryAdjustDTO.getNewBill();
            WdDeliveryBillPO oldBill = deliveryAdjustDTO.getOldBill();
            //  加盟调整
            FranLineAdjustReq franLineAdjustReq = adjustDeliveryOrderCoreService.buildFranLineAdjustReq(oldBill, operatorInfo);
            //加盟店 校验额度变化值
            if (oldBill.handleJiaMeng()) {
                BigDecimal amount = newBill.getTotalTaxMoney().subtract(oldBill.getTotalTaxMoney());
                franLineAdjustReq.setAmount(amount.doubleValue());
                Logs.info("配送订单 {} 调整加盟店额度：{}", oldBill.getBillNo(), amount);
                if (franLineAdjustReq.getAmount() != 0) {
                    franLineAdjustReqList.add(franLineAdjustReq);
                }
            }
        });
        return franLineAdjustReqList;
    }

    @Override
    public List<BatchRecordReq> convertStockReq(List<WdsDeliveryAdjustDTO> contextList) {
        List<BatchRecordReq> batchRecordReqList = new ArrayList<>();
        contextList.forEach(deliveryAdjustDTO -> {
            WdDeliveryBillPO newBill = deliveryAdjustDTO.getNewBill();
            WdDeliveryBillPO oldBill = deliveryAdjustDTO.getOldBill();
            List<WdDeliveryBillDetailPO> detailPOListForUpdate = deliveryAdjustDTO.getDetailPOListForUpdate();
            Map<Long, WdDeliveryBillDetailPO> existDetailMap = deliveryAdjustDTO.getExistDetailMap();
            List<WdDeliveryBillDetailPO> changeBillDetailList = deliveryAdjustDTO.getChangeBillDetailList();
            List<WdDeliveryBillDetailPO> changeQtyList = changeBillDetailList.stream().filter(item -> Objects.nonNull(item.getDiffQty()))
                    .collect(Collectors.toList());
            //商品行信息变更，处理相关数据
            if (CollectionUtils.isNotEmpty(changeQtyList)) {
                BatchRecordReq batchRecordReq = adjustDeliveryOrderCoreService.convertStockReq4Adjust(deliveryAdjustDTO.getAdjustBillNo(), newBill, changeQtyList);
                batchRecordReqList.add(batchRecordReq);
            }
        });
        return batchRecordReqList;
    }

    @Override
    public void doAdjust(List<WdsDeliveryAdjustDTO> contextList) {
        contextList.forEach(deliveryAdjustDTO -> {
            WdDeliveryBillPO newBill = deliveryAdjustDTO.getNewBill();
            List<BillAdjustLogPO> billAdjustLog = deliveryAdjustDTO.getBillAdjustLog();
            List<WdDeliveryBillDetailPO> detailPOListForUpdate = deliveryAdjustDTO.getDetailPOListForUpdate();
            //调整信息落DB
            adjustLogRepositoryService.saveBatch(billAdjustLog);

            //更新原单调整信息
            wdDeliveryBillRepositoryService.updateById(newBill);
            if (CollectionUtils.isNotEmpty(detailPOListForUpdate)) {
                wdsDeliveryBillDetailRepositoryService.updateBatchById(detailPOListForUpdate);
            }
        });

    }
}
