package com.meta.supplychain.demand.purch.application.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import com.meta.supplychain.demand.purch.application.intf.IPmsAppointmentApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.entity.dto.pms.req.appointment.*;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryAbleAppointReq;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPreSubmitStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 预约单应用服务实现类
 *
 * <AUTHOR>
 * @date 2025/04/20 21:06
 **/
@Service
public class PmsAppointmentApplicationServiceImpl implements IPmsAppointmentApplicationService {

    @Resource
    private IPmsAppointmentDomainService pmsAppointmentDomainService;

    /**
     * 多条件查询预约单列表（包含统计信息）
     */
    @Override
    public Result<PageResult<PmsAppointmentBillStatsDTO>> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO) {
        PageResult<PmsAppointmentBillStatsDTO> result = pmsAppointmentDomainService.selectAppointmentBillWithStats(queryDTO);
        return Results.ofSuccess(result);
    }

    /**
     * 预约单详细查询
     */
    @Override
    public Result<PageResult<PmsAppointmentDetailDTO>> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO) {
        PageResult<PmsAppointmentDetailDTO> result = pmsAppointmentDomainService.selectAppointmentBillDetails(queryDTO);
        return Results.ofSuccess(result);
    }

    /**
     * 查询单据维度可预约采购订单商品信息
     */
    @Override
    public Result<List<PmsAppointmentBillPurchDTO>> listAvailableBillDetail(QueryAbleAppointReq queryDto) {
        List<PmsAppointmentBillPurchDTO> result = pmsAppointmentDomainService.listAvailableBillDetail(queryDto);
        return Results.ofSuccess(result);
    }

    /**
     * 查询商品维度可预约采购订单商品信息
     */
    @Override
    public Result<List<PmsAppointmentBillGoodsDTO>> listAvailableGoodsDetail(QueryAbleAppointReq queryDto) {
        List<PmsAppointmentBillGoodsDTO> result = pmsAppointmentDomainService.listAvailableGoodsDetail(queryDto);
        return Results.ofSuccess(result);
    }

    /**
     * 创建/更新预约单
     * 包含预约单号即为更新操作
     */
    @Override
    public Result<String> createOrUpdatePmsAppointmentBill(PmsAppointmentBillDTO pmsAppointmentBillDTO) {
        String billNo = pmsAppointmentDomainService.createOrUpdatePmsAppointmentBill(pmsAppointmentBillDTO);
        return Results.ofSuccess(billNo);
    }

    /**
     * 预约单详情（采购单维度的数据）
     */
    @Override
    public Result<PmsAppointmentBillDTO> commonDetail(String billNo) {
        PmsAppointmentBillDTO result = pmsAppointmentDomainService.commonDetail(billNo);
        return Results.ofSuccess(result);
    }

    /**
     * 预约单详情
     */
    @Override
    public Result<PmsAppointmentBillDTO> detail(String billNo) {
        PmsAppointmentBillDTO result = pmsAppointmentDomainService.detail(billNo);
        return Results.ofSuccess(result);
    }

    /**
     * 提交预约单
     */
    @Override
    public Result<Void> submit(String billNo) {
        pmsAppointmentDomainService.submit(billNo);
        return Results.ofSuccess();
    }

    /**
     * 取消预约单
     */
    @Override
    public Result<Void> cancel(String billNo) {
        pmsAppointmentDomainService.cancel(billNo);
        return Results.ofSuccess();
    }

    /**
     * 查询停靠点可预约信息
     */
    @Override
    public Result<List<PmsAvailableDockInfoDTO>> listAvailableDockDetail(PmsDeliveryDockLimitQueryDTO queryDto) {
        List<PmsAvailableDockInfoDTO> result = pmsAppointmentDomainService.listAvailableDockDetail(queryDto);
        return Results.ofSuccess(result);
    }

    /**
     * 供应商预约单提交统计
     */
    @Override
    public Result<PmsAppointmentBillPreSubmitStatsDTO> querySubmitStats(String billNo) {
        PmsAppointmentBillPreSubmitStatsDTO result = pmsAppointmentDomainService.querySubmitStats(billNo);
        return Results.ofSuccess(result);
    }

    /**
     * 保存并提交预约单
     */
    @Override
    public Result<String> saveUpdateAndSubmit(PmsAppointmentBillDTO dto) {
        String billNo = pmsAppointmentDomainService.saveUpdateAndSubmit(dto);
        return Results.ofSuccess(billNo);
    }

    /**
     * 查询来源单据信息
     */
    @Override
    public Result<PmsDemandSourceDetailDTO> queryDemandSourceDetail(String refBillNo, Long refInsideId) {
        PmsDemandSourceDetailDTO result = pmsAppointmentDomainService.queryDemandSourceDetail(refBillNo, refInsideId);
        return Results.ofSuccess(result);
    }
} 