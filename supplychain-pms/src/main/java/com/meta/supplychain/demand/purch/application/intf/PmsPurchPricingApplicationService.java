package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchPricingReq;
import com.meta.supplychain.entity.dto.pms.req.purch.SavePurchPricingReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.QueryPurchPricingResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.SavePurchPricingResp;

import java.util.List;

public interface PmsPurchPricingApplicationService {

    Result<List<QueryPurchPricingResp>> queryPurchPricing(QueryPurchPricingReq queryPurchPricingReq);

    Result<Void> savePurchPricing(SavePurchPricingReq queryPurchasePlanBillReq);

}