package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.dto.pms.resp.account.AccountBillResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "PMS内部接口")
@RequestMapping("${unit-deploy.prefix-inner:}/pms/api")
@RestController
@Validated
@RequiredArgsConstructor
public class PmsInnerController {
    @Autowired
    private IPmsApplicationManagerService pmsApplicationManagerService;

    @PostMapping("/saveAccount")
    @Operation(summary = "记录过账单", description = "记录过账单")
    @MethodLog("记录过账单")
    public Result<AccountBillResp> saveAccountBill(@RequestBody @Valid AccountBillCreateReq createReq) {
        OpInfo operatorInfo = new OpInfo();
        operatorInfo.setOperatorCode(createReq.getCreateCode());
        operatorInfo.setOperatorName(createReq.getCreateName());
        createReq.setOperatorInfo(operatorInfo);
        createReq.setIsInternal(true);
        return pmsApplicationManagerService.getPmsAccountBillApplicationService().createAccountBill(createReq);
    }


}
