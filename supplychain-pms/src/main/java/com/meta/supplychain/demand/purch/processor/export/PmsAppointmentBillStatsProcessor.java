package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.context.ThreadLocals;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.meta.supplychain.convert.pms.PmsAppointmentBillStatsConvert;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.view.PmsAppointmentBillStatsView;
import com.meta.supplychain.util.spring.SpringContextUtil;
import cn.linkkids.framework.croods.common.PageResult;
import com.metadata.idaas.client.cons.ClientIdentCons;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约单列表信息导出处理器
 * <AUTHOR>
 */
@ExportSpecification(code = "PmsAppointmentBillStatsProcessor", name = "预约单列表信息导出", executeType = ExecuteType.CLUSTER)
public class PmsAppointmentBillStatsProcessor implements ExportProcessor<PmsAppointmentBillQueryDTO, PmsAppointmentBillStatsDTO, PmsAppointmentBillStatsView> {

    @Override
    public Integer totalCount(BizUser bizUser, PmsAppointmentBillQueryDTO queryDTO) throws BizException {
        IPmsAppointmentDomainService appointmentDomainService = SpringContextUtil.getApplicationContext().getBean(IPmsAppointmentDomainService.class);

        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(1L);
        ThreadLocals.setValue(ClientIdentCons.KEY_LOGIN_USER, queryDTO.getLoginUser());
        PageResult<PmsAppointmentBillStatsDTO> pageResult = appointmentDomainService.selectAppointmentBillWithStats(queryDTO);
        return Long.valueOf(pageResult.getTotal()).intValue();
    }

    @Override
    public List<PmsAppointmentBillStatsDTO> queryData(BizUser bizUser, PmsAppointmentBillQueryDTO queryDTO, BizExportPage bizExportPage) throws BizException {
        IPmsAppointmentDomainService appointmentDomainService = SpringContextUtil.getApplicationContext().getBean(IPmsAppointmentDomainService.class);

        queryDTO.setCurrent(Long.valueOf(bizExportPage.getNo()));
        queryDTO.setPageSize(Long.valueOf(bizExportPage.getSize()));
        ThreadLocals.setValue(ClientIdentCons.KEY_LOGIN_USER, queryDTO.getLoginUser());
        PageResult<PmsAppointmentBillStatsDTO> pageResult = appointmentDomainService.selectAppointmentBillWithStats(queryDTO);
        return pageResult.getRows();
    }

    @Override
    public List<PmsAppointmentBillStatsView> convert(BizUser bizUser, PmsAppointmentBillQueryDTO queryDTO, List<PmsAppointmentBillStatsDTO> appointmentBillStatsDTOList) throws BizException {
        return appointmentBillStatsDTOList.stream()
                .map(PmsAppointmentBillStatsConvert.INSTANCE::dto2view)
                .collect(Collectors.toList());
    }
} 