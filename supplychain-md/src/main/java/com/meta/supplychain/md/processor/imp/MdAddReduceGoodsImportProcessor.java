package com.meta.supplychain.md.processor.imp;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ImportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.importer.ImportProcessor;
import com.alibaba.ageiport.processor.core.task.importer.model.BizImportResult;
import com.alibaba.ageiport.processor.core.task.importer.model.BizImportResultImpl;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdAddReduceGoodsDomainService;
import com.meta.supplychain.convert.md.MdAddReduceGoodsConvert;
import com.meta.supplychain.entity.dto.bds.req.QueryDeptGroupBatchReq;
import com.meta.supplychain.entity.dto.bds.req.QueryDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryDeptGroupBatchResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailListResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailResp;
import com.meta.supplychain.entity.dto.goods.req.SimpleGoodsListQueryReq;
import com.meta.supplychain.entity.dto.goods.resp.SimpleGoodsResultResp;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptImportDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsExcelDataDTO;
import com.meta.supplychain.entity.dto.md.goodsstrategy.ImportGoodsStrategyParams;
import com.meta.supplychain.entity.dto.md.view.MdAddReduceGoodsExcelDataView;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.GoodsFeignClient;
import com.meta.supplychain.util.spring.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@ImportSpecification(code = "MdAddReduceGoodsImportProcessor", name = "追加追减设置导入", executeType = ExecuteType.CLUSTER)
@Slf4j
public class MdAddReduceGoodsImportProcessor implements ImportProcessor<ImportGoodsStrategyParams, MdAddReduceGoodsExcelDataDTO, MdAddReduceGoodsExcelDataView> {

    @Override
    public BizImportResult<MdAddReduceGoodsExcelDataView, MdAddReduceGoodsExcelDataDTO> convertAndCheck(BizUser user, ImportGoodsStrategyParams importGoodsStrategyParams, List<MdAddReduceGoodsExcelDataView> views) {
        Logs.info("MdAddReduceGoodsImportProcessor convertAndCheck start");
        BaseDataSystemFeignClient baseDataSystemFeignClient = SpringContextUtil.getApplicationContext().getBean(BaseDataSystemFeignClient.class);
        GoodsFeignClient goodsFeignClient = SpringContextUtil.getApplicationContext().getBean(GoodsFeignClient.class);
        IMdAddReduceGoodsDomainService mdAddReduceGoodsDomainService = SpringContextUtil.getApplicationContext().getBean(IMdAddReduceGoodsDomainService.class);

        BizImportResultImpl<MdAddReduceGoodsExcelDataView, MdAddReduceGoodsExcelDataDTO> result = new BizImportResultImpl<>();
        List<MdAddReduceGoodsExcelDataView> errorList = new ArrayList<>();
        List<MdAddReduceGoodsExcelDataDTO> successList = new ArrayList<>();
        List<MdAddReduceGoodsExcelDataView> itemList = new ArrayList<>();
        result.setView(errorList);
        result.setData(successList);
        // 必填校验
        for (MdAddReduceGoodsExcelDataView item : views) {
            if (StringUtils.isEmpty(item.getSkuCode()) && StringUtils.isEmpty(item.getBarCode())) {
                item.addErrorMsg(MdErrorCodeEnum.SCWDS011P012.getErrorMsg());
                errorList.add(item);
                continue;
            }
            if (null == item.getType()) {
                item.addErrorMsg(MdErrorCodeEnum.SCWDS011P019.getErrorMsg());
                errorList.add(item);
                continue;
            }
            if (null == item.getDeptType()) {
                item.addErrorMsg(MdErrorCodeEnum.SCWDS011P020.getErrorMsg());
                errorList.add(item);
                continue;
            }
            if (!item.getDeptType().equals(0) && StringUtils.isEmpty(item.getDeptCodes())) {
                item.addErrorMsg(MdErrorCodeEnum.SCWDS011P014.getErrorMsg());
                errorList.add(item);
                continue;
            }
            if (item.getDeptType().equals(0) && StringUtils.isNotEmpty(item.getDeptCodes())) {
                item.addErrorMsg(MdErrorCodeEnum.SCWDS011P013.getErrorMsg());
                errorList.add(item);
                continue;
            }
            itemList.add(item);
        }

        if (CollectionUtils.isEmpty(itemList)) {
            return result;
        }

        // 获取商品
        Set<String> skuCodes = itemList.stream()
                .map(MdAddReduceGoodsExcelDataView::getSkuCode)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Set<String> barCodes = itemList.stream()
                .map(MdAddReduceGoodsExcelDataView::getBarCode)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> groupList = itemList.stream()
                .filter(item -> item.getDeptType().equals(1))
                .map(MdAddReduceGoodsExcelDataView::getDeptCodes)
                .collect(Collectors.toSet());
        Set<String> deptList = itemList.stream()
                .filter(item -> item.getDeptType().equals(2))
                .map(MdAddReduceGoodsExcelDataView::getDeptCodes)
                .collect(Collectors.toSet());
        Set<String> groupCodeList = Sets.newHashSet();
        Set<String> deptCodeList = Sets.newHashSet();

        // 获取商品数据
        Map<String, SimpleGoodsResultResp.SimpleGoodsResult> skuMap = Maps.newHashMap();
        Map<String, SimpleGoodsResultResp.SimpleGoodsResult> barMap = Maps.newHashMap();
        SimpleGoodsListQueryReq simpleGoodsListQueryReq = new SimpleGoodsListQueryReq();
        simpleGoodsListQueryReq.setSkuCodeList(new ArrayList<>(skuCodes));
        simpleGoodsListQueryReq.setBarCodeList(new ArrayList<>(barCodes));
        List<SimpleGoodsResultResp.SimpleGoodsResult> simpleGoodsResultResps = goodsFeignClient.simpleGoodsListAll(simpleGoodsListQueryReq);
        if (CollectionUtils.isNotEmpty(simpleGoodsResultResps)) {
            skuMap.putAll(simpleGoodsResultResps.stream().collect(Collectors.toMap(SimpleGoodsResultResp.SimpleGoodsResult::getSkuCode, obj -> obj, (obj1, obj2) -> obj1)));
            barMap.putAll(simpleGoodsResultResps.stream().collect(Collectors.toMap(SimpleGoodsResultResp.SimpleGoodsResult::getSkuBarCode, obj -> obj, (obj1, obj2) -> obj1)));
        }

        // 获取部门数据
        Map<String, String> storeMap = Maps.newHashMap();
        Map<String, String> groupMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupList)) {
            groupList.forEach(item -> {
                String[] split = item.split(",");
                for (String s : split) {
                    groupCodeList.add(s);
                }
            });
            QueryDeptGroupBatchReq deptListReq = QueryDeptGroupBatchReq.builder()
                    .codeList(new ArrayList<>(groupCodeList))
                    .classCode(GroupDeptEnum.FIX_PRICE_GROUP.getCode())
                    .build();
            QueryDeptGroupBatchResp queryGroupDeptListResp = baseDataSystemFeignClient.queryDeptGroupBatch(deptListReq);
            if (queryGroupDeptListResp != null && CollectionUtils.isNotEmpty(queryGroupDeptListResp.getDeptGroupList())) {
                groupMap.putAll(queryGroupDeptListResp.getDeptGroupList().stream().collect(Collectors.toMap(QueryDeptGroupBatchResp.DeptGroup::getCode, QueryDeptGroupBatchResp.DeptGroup::getName)));
            }
        }
        if (CollectionUtils.isNotEmpty(deptList)) {
            deptList.forEach(item -> {
                String[] split = item.split(",");
                for (String s : split) {
                    deptCodeList.add(s);
                }
            });
            QueryDeptListReq queryDeptListReq = QueryDeptListReq.builder()
                    .deptCodeList(new ArrayList<>(deptCodeList))
                    .pageSize(500)
                    .build();
            StoreDetailListResp storeDetailListResp = baseDataSystemFeignClient.queryDeptList(queryDeptListReq);
            if (storeDetailListResp != null && CollectionUtils.isNotEmpty(storeDetailListResp.getRows())) {
                storeMap.putAll(storeDetailListResp.getRows().stream().collect(Collectors.toMap(StoreDetailResp::getCode, StoreDetailResp::getName)));
            }
        }

        // 获取追加追减设置
        List<MdAddReduceGoodsDTO> mdAddReduceGoodsDTOS = mdAddReduceGoodsDomainService.queryList(new ArrayList<>(skuCodes), new ArrayList<>(barCodes));
        Map<String, MdAddReduceGoodsDTO> skuDTOMap = mdAddReduceGoodsDTOS.stream().collect(Collectors.toMap(MdAddReduceGoodsDTO::getSkuCode, obj -> obj));

        // 校验数据
        Set<String> skuCodeSet = Sets.newHashSet();
        Set<String> barCodeSet = Sets.newHashSet();
        for (MdAddReduceGoodsExcelDataView item : itemList) {
            String skuCode = null;
            String barCode = null;
            String skuName = null;
            if (StringUtils.isNotEmpty(item.getSkuCode())) {
                item.setBarCode(null);
                if (skuCodeSet.contains(item.getSkuCode())) {
                    String errorMsg = String.format(MdErrorCodeEnum.SCWDS011P021.getErrorMsg(), item.getSkuCode());
                    item.addErrorMsg(errorMsg);
                    errorList.add(item);
                    continue;
                } else {
                    skuCodeSet.add(item.getSkuCode());
                }
                if (!skuMap.containsKey(item.getSkuCode())) {
                    item.addErrorMsg(MdErrorCodeEnum.SCWDS011P008.getErrorMsg());
                    errorList.add(item);
                    continue;
                } else {
                    SimpleGoodsResultResp.SimpleGoodsResult goodsSimpleInfo = skuMap.get(item.getSkuCode());
                    skuCode = goodsSimpleInfo.getSkuCode();
                    barCode = goodsSimpleInfo.getSkuBarCode();
                    skuName = goodsSimpleInfo.getSkuName();
                }
            }
            if (StringUtils.isNotEmpty(item.getBarCode())) {
                if (barCodeSet.contains(item.getBarCode())) {
                    String errorMsg = String.format(MdErrorCodeEnum.SCWDS011P021.getErrorMsg(), item.getBarCode());
                    item.addErrorMsg(errorMsg);
                    errorList.add(item);
                    continue;
                } else {
                    barCodeSet.add(item.getBarCode());
                }
                if (!barMap.containsKey(item.getBarCode())) {
                    item.addErrorMsg(MdErrorCodeEnum.SCWDS011P008.getErrorMsg());
                    errorList.add(item);
                    continue;
                } else {
                    SimpleGoodsResultResp.SimpleGoodsResult goodsSimpleInfo = barMap.get(item.getBarCode());
                    skuCode = goodsSimpleInfo.getSkuCode();
                    barCode = goodsSimpleInfo.getSkuBarCode();
                    skuName = goodsSimpleInfo.getSkuName();
                }
            }
            item.setSkuCode(skuCode);
            item.setBarCode(barCode);
            item.setSkuName(skuName);

            if (item.getDeptType().equals(0)) {
                if (skuDTOMap.containsKey(item.getSkuCode())) {
                    item.addErrorMsg(MdErrorCodeEnum.SCWDS011P017.getErrorMsg());
                    errorList.add(item);
                    continue;
                }
            }
            if (item.getDeptType().equals(1)) {
                String deptCodes = item.getDeptCodes();
                String[] split = deptCodes.split(",");
                String errorMsg = null;
                for (String s : split) {
                    if (!groupMap.containsKey(s)) {
                        errorMsg = String.format(MdErrorCodeEnum.SCWDS011P015.getErrorMsg(), s);
                    } else {
                        item.setDeptName(groupMap.get(s));
                    }
                }
                if (skuDTOMap.containsKey(item.getSkuCode())) {
                    MdAddReduceGoodsDTO mdAddReduceGoodsDTO = skuDTOMap.get(item.getSkuCode());
                    if (CollectionUtils.isNotEmpty(mdAddReduceGoodsDTO.getDetailList())) {
                        for (String s : split) {
                            Optional<MdAddReduceGoodsDeptDTO> first = mdAddReduceGoodsDTO.getDetailList().stream()
                                    .filter(obj -> obj.getDeptType().equals(2) && obj.getCode().equals(s))
                                    .findFirst();
                            if (first.isPresent()) {
                                errorMsg = String.format(MdErrorCodeEnum.SCWDS011P018.getErrorMsg(), s);
                            }
                        }
                    }
                }
                if (null != errorMsg) {
                    item.addErrorMsg(errorMsg);
                    errorList.add(item);
                    continue;
                }
            }
            if (item.getDeptType().equals(2)) {
                String deptCodes = item.getDeptCodes();
                String[] split = deptCodes.split(",");
                String errorMsg = null;
                for (String s : split) {
                    if (!storeMap.containsKey(s)) {
                        errorMsg = String.format(MdErrorCodeEnum.SCWDS011P016.getErrorMsg(), s);
                    } else {
                        item.setDeptName(storeMap.get(s));
                    }
                }
                if (skuDTOMap.containsKey(item.getSkuCode())) {
                    MdAddReduceGoodsDTO mdAddReduceGoodsDTO = skuDTOMap.get(item.getSkuCode());
                    if (CollectionUtils.isNotEmpty(mdAddReduceGoodsDTO.getDetailList())) {
                        for (String s : split) {
                            Optional<MdAddReduceGoodsDeptDTO> first = mdAddReduceGoodsDTO.getDetailList().stream()
                                    .filter(obj -> obj.getDeptType().equals(1) && obj.getCode().equals(s))
                                    .findFirst();
                            if (first.isPresent()) {
                                errorMsg = String.format(MdErrorCodeEnum.SCWDS011P018.getErrorMsg(), s);
                            }
                        }
                    }
                }
                if (null != errorMsg) {
                    item.addErrorMsg(errorMsg);
                    errorList.add(item);
                    continue;
                }
            }
            successList.add(MdAddReduceGoodsConvert.INSTANCE.convertView2Dto(item));
        }
        return result;
    }

    @Override
    public BizImportResult<MdAddReduceGoodsExcelDataView, MdAddReduceGoodsExcelDataDTO> write(BizUser bizUser, ImportGoodsStrategyParams importGoodsStrategyParams, List<MdAddReduceGoodsExcelDataDTO> list) {
        Logs.info("MdAddReduceGoodsImportProcessor write start");
        BizImportResultImpl<MdAddReduceGoodsExcelDataView, MdAddReduceGoodsExcelDataDTO> result = new BizImportResultImpl<>();
        result.setData(new ArrayList<>());
        result.setView(new ArrayList<>());
        List<MdAddReduceGoodsDeptImportDTO> saveList = Lists.newArrayList();

        // 组装数据
        for (MdAddReduceGoodsExcelDataDTO item : list) {
            if (StringUtils.isNotEmpty(item.getDeptCodes())) {
                String deptCodes = item.getDeptCodes();
                String[] split = deptCodes.split(",");
                for (String s : split) {
                    MdAddReduceGoodsDeptImportDTO dto = new MdAddReduceGoodsDeptImportDTO();
                    saveList.add(dto);
                    dto.setSkuCode(item.getSkuCode());
                    dto.setSkuName(item.getSkuName());
                    dto.setBarcode(item.getBarCode());
                    dto.setName(item.getDeptName());
                    if (item.getType().equals(0)) {
                        dto.setIsAllowMultiBill(1);
                        dto.setIsAllowReduceBill(1);
                    } else if (item.getType().equals(1)) {
                        dto.setIsAllowMultiBill(1);
                        dto.setIsAllowReduceBill(0);
                    } else if (item.getType().equals(2)) {
                        dto.setIsAllowMultiBill(0);
                        dto.setIsAllowReduceBill(1);
                    }
                    if (item.getDeptType().equals(0)) {
                        dto.setDeptScope(1);
                    } else if (item.getDeptType().equals(1)) {
                        dto.setDeptScope(2);
                        dto.setDeptType(2);
                        dto.setCode(s);
                    } else if (item.getDeptType().equals(2)) {
                        dto.setDeptScope(2);
                        dto.setDeptType(1);
                        dto.setCode(s);
                    }
                }
            } else {
                MdAddReduceGoodsDeptImportDTO dto = new MdAddReduceGoodsDeptImportDTO();
                saveList.add(dto);
                dto.setSkuCode(item.getSkuCode());
                dto.setSkuName(item.getSkuName());
                dto.setBarcode(item.getBarCode());
                dto.setName(item.getDeptName());
                if (item.getType().equals(0)) {
                    dto.setIsAllowMultiBill(1);
                    dto.setIsAllowReduceBill(1);
                } else if (item.getType().equals(1)) {
                    dto.setIsAllowMultiBill(1);
                    dto.setIsAllowReduceBill(0);
                } else if (item.getType().equals(2)) {
                    dto.setIsAllowMultiBill(0);
                    dto.setIsAllowReduceBill(1);
                }
                dto.setDeptScope(1);
            }
        }

        try {
            IMdAddReduceGoodsDomainService mdAddReduceGoodsDomainService = SpringContextUtil.getApplicationContext().getBean(IMdAddReduceGoodsDomainService.class);
            mdAddReduceGoodsDomainService.batchSave(saveList, importGoodsStrategyParams.getOperatorInfo());
        } catch (Exception e) {
            log.error("MdAddReduceGoodsImportProcessor write error:{}", e);
            List<MdAddReduceGoodsExcelDataView> errorList = new ArrayList<>();
            list.forEach(it -> {
                MdAddReduceGoodsExcelDataView view = new MdAddReduceGoodsExcelDataView();
                view.setSkuCode(it.getSkuCode());
                view.setErrorMsg(e.getMessage());
                errorList.add(view);
            });
            result.setView(errorList);
        }

        return result;
    }

}
