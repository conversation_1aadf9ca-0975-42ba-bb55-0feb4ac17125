package com.meta.supplychain.md.processor.export;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.alibaba.fastjson.JSON;
import com.meta.supplychain.common.component.domain.md.intf.IMdAddReduceGoodsDomainService;
import com.meta.supplychain.convert.md.MdAddReduceGoodsConvert;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDTO;
import com.meta.supplychain.entity.dto.md.req.addreducegoods.QueryMdAddReduceGoodsReq;
import com.meta.supplychain.entity.dto.md.view.MdAddReduceGoodsView;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 追加追减商品设置导出
 *
 * <AUTHOR>
 * @date 2025/05/12 10:00
 **/
@ExportSpecification(code = "MdAddReduceGoodsExportProcessor", name = "追加追减设置导出", executeType = ExecuteType.CLUSTER)
public class MdAddReduceGoodsExportProcessor implements ExportProcessor<QueryMdAddReduceGoodsReq, MdAddReduceGoodsDTO, MdAddReduceGoodsView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryMdAddReduceGoodsReq params) throws BizException {
        Logs.info("MdAddReduceGoodsExportProcessor.totalCount.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));
        IMdAddReduceGoodsDomainService mdAddReduceGoodsDomainService =
                SpringContextUtil.getApplicationContext().getBean(IMdAddReduceGoodsDomainService.class);
        params.setPageSize(1L);
        params.setCurrent(1L);
        PageResult<MdAddReduceGoodsDTO> mdAddReduceGoodsDTOPageResult = mdAddReduceGoodsDomainService.pageList(params);

        return (int)mdAddReduceGoodsDTOPageResult.getTotal();
    }

    @Override
    public List<MdAddReduceGoodsDTO> queryData(BizUser bizUser, QueryMdAddReduceGoodsReq params, BizExportPage bizExportPage) throws BizException {
        Logs.info("MdAddReduceGoodsExportProcessor.queryData.bizUser:{},params:{}.bizExportPage:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params),JSON.toJSONString(bizExportPage));
        params.setCurrent(bizExportPage.getNo().longValue());
        params.setPageSize(bizExportPage.getSize().longValue());
        IMdAddReduceGoodsDomainService mdAddReduceGoodsDomainService =
                SpringContextUtil.getApplicationContext().getBean(IMdAddReduceGoodsDomainService.class);
        PageResult<MdAddReduceGoodsDTO> mdAddReduceGoodsDTOPageResult = mdAddReduceGoodsDomainService.pageList(params);
        return mdAddReduceGoodsDTOPageResult.getRows();
    }

    @Override
    public List<MdAddReduceGoodsView> convert(BizUser bizUser, QueryMdAddReduceGoodsReq params, List<MdAddReduceGoodsDTO> list) throws BizException {
        Logs.info("MdAddReduceGoodsExportProcessor.queryData.bizUser:{},params:{}",
                JSON.toJSONString(bizUser), JSON.toJSONString(params));

        List<MdAddReduceGoodsView> viewList = Lists.newArrayList();
        list.forEach(item -> {
            MdAddReduceGoodsView view = MdAddReduceGoodsConvert.INSTANCE.convertDto2View(item);
            view.setCreateTime(null == item.getCreateTime() ? "" : DateUtil.getDateTimeYmdHms(item.getCreateTime()));
            view.setUpdateTime(null == item.getUpdateTime() ? "" : DateUtil.getDateTimeYmdHms(item.getUpdateTime()));
            if (StringUtils.isEmpty(item.getCreateCode()) && StringUtils.isEmpty(item.getCreateName())) {
                view.setCreator("");
            } else {
                view.setCreator(item.getCreateCode() + "_" + item.getCreateName());
            }
            if (StringUtils.isEmpty(item.getUpdateCode()) && StringUtils.isEmpty(item.getUpdateName())) {
                view.setUpdater("");
            } else {
                view.setUpdater(item.getUpdateCode() + "_" + item.getUpdateName());
            }
            viewList.add(view);
        });

        return viewList;
    }
}
