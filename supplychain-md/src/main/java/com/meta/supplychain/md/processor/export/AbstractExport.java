package com.meta.supplychain.md.processor.export;

import cn.hutool.core.lang.TypeReference;
import cn.linkkids.framework.croods.common.json.Jsons;
import com.alibaba.ageiport.processor.core.model.api.BizColumnHeader;
import com.alibaba.ageiport.processor.core.model.api.impl.BizColumnHeaderImpl;
import com.google.common.collect.Lists;
import com.meta.supplychain.entity.dto.md.req.export.ExportQuery;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractExport {

    public <T> T getQuery(ExportQuery exportQuery, TypeReference<T> typeReference) {
        return Jsons.toBean(Jsons.toJson(exportQuery.getQueryParams()), typeReference);
    }

    public List<BizColumnHeader> setHeaders(List<LinkedHashMap<String, String>> headers) {
        List<BizColumnHeader> headersList = Lists.newArrayList();
        for (LinkedHashMap<String, String> headerMap : headers) {
            if (headerMap.isEmpty()) {
                continue;
            }
            Map.Entry<String, String> entry = headerMap.entrySet().iterator().next();
            BizColumnHeaderImpl bizColumnHeader = new BizColumnHeaderImpl();
            bizColumnHeader.setDynamicColumn(false);
            // 设置字段名
            bizColumnHeader.setFieldName(entry.getKey());
            // 设置列头名称
            bizColumnHeader.setHeaderName(entry.getValue());
            headersList.add(bizColumnHeader);
        }
        return headersList;
    }
}
