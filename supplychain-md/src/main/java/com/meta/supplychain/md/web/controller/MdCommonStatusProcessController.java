package com.meta.supplychain.md.web.controller;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import com.meta.supplychain.common.component.service.intf.commonbiz.IMdCommonStatusProcessService;
import com.meta.supplychain.entity.dto.md.commonstatus.MdCommonStatusProcessDTO;
import com.meta.supplychain.enums.md.MdCommonStatusBizTypeEnum;
import com.meta.supplychain.enums.md.MdCommonStatusModuleEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应链主数据通用数据接口
 * <AUTHOR>
 */
@Tag(name = "公共审批")
@RestController
@RequestMapping("${unit-deploy.prefix-main:}/md/common/status/process")
@Validated
@RequiredArgsConstructor
public class MdCommonStatusProcessController {

    @Autowired
    private IMdCommonStatusProcessService mdCommonStatusProcessService;

    @Operation(summary = "审批记录查询")
    @GetMapping("/list")
    @MethodLog(level = MethodLog.Level.INFO, value = "审批记录查询")
    public Result<List<MdCommonStatusProcessDTO>> listCommonStatusProcess(@Parameter(description = "业务单据-合同号") @RequestParam("bizCode") @NotNull String bizCode,
                                                                   @Parameter(description = "业务单据-id") @RequestParam(value = "bizId", required = false) Long bizId,
                                                                   @Parameter(description = "模块编码mdCommonStatusModuleEnum") @RequestParam("moduleCode") @NotNull String moduleCode,
                                                                   @Parameter(description = "子模块编码mdCommonStatusBizTypeEnum") @RequestParam("bizTypeCode") @NotNull String bizTypeCode) {

        MdCommonStatusProcessDTO commonStatusProcessDTO = new MdCommonStatusProcessDTO();
        commonStatusProcessDTO.setBizCode(bizCode);
        commonStatusProcessDTO.setModuleCode(MdCommonStatusModuleEnum.ofCode(moduleCode));
        commonStatusProcessDTO.setBizType(MdCommonStatusBizTypeEnum.ofCode(bizTypeCode));
        commonStatusProcessDTO.setBizId(bizId);

        List<MdCommonStatusProcessDTO> mdCommonStatusProcessDTOS = mdCommonStatusProcessService.listCommonStatusProcess(commonStatusProcessDTO);

        return Results.ofSuccess(mdCommonStatusProcessDTOS);
    }
}
