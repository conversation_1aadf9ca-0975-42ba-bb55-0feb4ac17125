package com.meta.supplychain;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.impl.MdDemandStrategyDomainServiceImpl;
import com.meta.supplychain.common.component.domain.md.intf.IMdDeliveryAppointmentStrategyDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdDistributionPriceDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.convert.md.MdExpenseItemConvert;
import com.meta.supplychain.entity.dto.bds.req.*;
import com.meta.supplychain.entity.dto.md.component.bizrule.BatchGenerateBillNoDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.ListDeliveryAppointmentStrategyDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.ExistGoodsInfoQueryDTO;
import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceBillQueryDTO;
import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceQueryDTO;
import com.meta.supplychain.entity.dto.md.req.contractdef.MdContractDefGoodsDeptReq;
import com.meta.supplychain.entity.dto.md.req.contractdef.MdContractDefGoodsReq;
import com.meta.supplychain.entity.dto.md.req.contractdef.MdContractDefSaveReq;
import com.meta.supplychain.entity.dto.md.req.deliveryappointment.QueryDeliveryDockLimitReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeCreateReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludePageQueryReq;
import com.meta.supplychain.entity.dto.md.req.distprice.ExistDistPriceDataReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceComplexReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceReq;
import com.meta.supplychain.entity.dto.md.resp.MdDistPriceResponseDTO;
import com.meta.supplychain.entity.dto.md.resp.demandstrategyexclude.MdDemandStrategyExcludeResponseDTO;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemResponseDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.DockTimeStatsQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.po.md.*;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.enums.pms.PmsBookingCategoryEnum;
import com.meta.supplychain.enums.pms.PmsBookingStatusEnum;
import com.meta.supplychain.enums.pms.PmsCarrierMethodEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.IdaasFeignClient;
import com.meta.supplychain.infrastructure.repository.mapper.md.*;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAppointmentBillMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsAppointmentBillRepositoryService;
import com.meta.supplychain.md.SupplychainMdApplication;
import com.meta.supplychain.md.application.intf.IMdContractGoodsDefineApplicationService;
import com.meta.supplychain.util.UserResourceUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import org.assertj.core.util.Sets;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.locks.LockSupport;

@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("test")
@SpringBootTest(classes = SupplychainMdApplication.class)
public class TestMain {

    @Resource
    private IMdExpenseItemCategoryRepositoryService mdExpenseItemCategoryRepositoryService;

    @Resource
    private MdExpenseItemCategoryMapper mdExpenseItemCategoryMapper;

    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Resource
    private MdDistPriceBillMapper mdDistPriceBillMapper;
    
    @Resource
    private IMdDistPriceBillRepositoryService mdDistPriceBillRepositoryService;
    
    @Resource
    private IMdDistPriceRepositoryService mdDistPriceRepositoryService;

    @Resource
    private IdaasFeignClient idaasFeignClient;

    @Resource
    private UserResourceUtil userResourceUtil;

    @Resource
    private IMdDistributionPriceDomainService distributionPriceDomainService;

    @Resource
    private IMdContractGoodsDefineApplicationService contractGoodsDefineApplicationService;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Resource
    private MdDistPriceMapper mdDistPriceMapper;

    @InjectMocks
    private MdDemandStrategyDomainServiceImpl mdDemandStrategyDomainService;

    @Mock
    private IMdDemandStrategyExcludeRepositoryService mdDemandStrategyExcludeRepositoryService;

    @Resource
    private MdDemandBatchCateGoodsMapper mdDemandBatchCateGoodsMapper;

    @Resource
    private IMdDeliveryAppointmentStrategyDomainService mdDeliveryAppointmentStrategyDomainService;

    @Autowired
    private IMdContractGoodsDeptRepositoryService mdContractGoodsDeptRepositoryService;

    @Resource
    private PmsAppointmentBillMapper pmsAppointmentBillMapper;

    @Resource
    private IPmsAppointmentBillRepositoryService pmsAppointmentBillRepositoryService;

    @Resource
    private IMdDeliveryDockStrategyRepositoryService mdDeliveryDockStrategyRepositoryService;

    @Resource
    private IMdDistributionPriceDomainService mdDistributionPriceDomainService;

    @Resource
    private IMstSupplierRepositoryService mstSupplierRepositoryService;

    @Before
    public void initTenantId() {
        TenantContext.set(153658);

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setCode("001");
        loginUserDTO.setUid(10149793L);
        loginUserDTO.setName("admin");

        ThreadLocals.setValue("_login_user_idaas", loginUserDTO);
    }

    /**
     * 测试预约单查询方法，包含所有查询条件
     */
    @Test
    public void testSelectAppointmentBillWithStats() {
        // 构建包含所有查询条件的PmsAppointmentBillQueryDTO实例
        PmsAppointmentBillQueryDTO queryDTO = new PmsAppointmentBillQueryDTO();
        
        // 设置预约单据号（模糊匹配）
        queryDTO.setBillNo("**********");
        
        // 设置供应商编码列表
        List<String> supplierCodes = Arrays.asList("SUP001", "SUP002", "SUP003");
        queryDTO.setSupplierCodes(supplierCodes);
        
        // 设置停靠点编码列表
        List<String> dockCodes = Arrays.asList("DOCK001", "DOCK002");
        queryDTO.setDockCodes(dockCodes);
        
        // 设置状态（使用枚举值）
        queryDTO.setStatus(PmsBookingStatusEnum.SUBMITTED.getCode()); // 已提交
        
        // 设置部门编码列表
        List<String> deptCodes = Arrays.asList("DEPT001", "DEPT002", "DEPT003");
        queryDTO.setDeptCodes(deptCodes);
        
        // 设置承运方式（使用枚举值）
        queryDTO.setTransportMode(PmsCarrierMethodEnum.SELF_TRANSPORT.getCode()); // 自运
        
        // 设置承运备注（模糊匹配）
        queryDTO.setTransportRemark("快速配送");
        
        // 设置创建时间范围
        queryDTO.setCreateTimeStart(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        queryDTO.setCreateTimeEnd(LocalDateTime.of(2024, 12, 31, 23, 59, 59));
        
        // 设置提交时间范围
        queryDTO.setSubmitTimeStart(LocalDateTime.of(2024, 1, 1, 9, 0, 0));
        queryDTO.setSubmitTimeEnd(LocalDateTime.of(2024, 12, 31, 18, 0, 0));
        
        // 设置作废时间范围
        queryDTO.setCancelTimeStart(LocalDateTime.of(2024, 6, 1, 0, 0, 0));
        queryDTO.setCancelTimeEnd(LocalDateTime.of(2024, 6, 30, 23, 59, 59));
        
        // 设置计划到达时间范围
        queryDTO.setPlanArrivalTimeStart(LocalDateTime.of(2024, 1, 2, 8, 0, 0));
        queryDTO.setPlanArrivalTimeEnd(LocalDateTime.of(2024, 12, 31, 20, 0, 0));
        
        // 设置预约类别（使用枚举值）
        queryDTO.setBillDirection(PmsBookingCategoryEnum.PURCHASE_DISTRIBUTION.getCode()); // 采购配送
        
        // 设置承运人（模糊匹配）
        queryDTO.setTransportMan("张三");
        
        // 设置预约备注（模糊匹配）
        queryDTO.setAppointmentRemark("紧急处理");
        
        // 设置是否直流（使用枚举值）
        queryDTO.setDirectSign(YesOrNoEnum.YES.getCode()); // 是
        
        // 设置承运联系手机（模糊匹配）
        queryDTO.setTransportMobile("138");
        
        // 设置采购订单号（模糊匹配）
        queryDTO.setPurchBillNo("PO20240101");
        
        // 设置分页参数
        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(20L);

        // 创建分页对象
        Page<PmsAppointmentBillStatsDTO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());

        System.out.println(pmsAppointmentBillMapper.selectAppointmentBillWithStats(page, queryDTO));
    }

    /**
     * 测试预约单详细查询方法（三表关联UNION查询）
     */
    @Test
    public void testSelectAppointmentBillDetails() {
        // 构建包含所有查询条件的PmsAppointmentDetailQueryDTO实例
        PmsAppointmentDetailQueryDTO queryDTO = new PmsAppointmentDetailQueryDTO();
        
        // 设置预约单据号（模糊匹配）
        queryDTO.setBillNo("**********");
        
        // 设置采购订单号（模糊匹配）
        queryDTO.setPurchBillNo("PO20240101");
        
        // 设置创建时间范围
        queryDTO.setCreateTimeStart(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        queryDTO.setCreateTimeEnd(LocalDateTime.of(2024, 12, 31, 23, 59, 59));
        
        // 设置提交时间范围
        queryDTO.setSubmitTimeStart(LocalDateTime.of(2024, 1, 1, 9, 0, 0));
        queryDTO.setSubmitTimeEnd(LocalDateTime.of(2024, 12, 31, 18, 0, 0));
        
        // 设置作废时间范围
        queryDTO.setCancelTimeStart(LocalDateTime.of(2024, 6, 1, 0, 0, 0));
        queryDTO.setCancelTimeEnd(LocalDateTime.of(2024, 6, 30, 23, 59, 59));
        
        // 设置计划到达时间范围
        queryDTO.setPlanArrivalTimeStart(LocalDateTime.of(2024, 1, 2, 8, 0, 0));
        queryDTO.setPlanArrivalTimeEnd(LocalDateTime.of(2024, 12, 31, 20, 0, 0));
        
        // 设置部门编码列表
        List<String> deptCodes = Arrays.asList("DEPT001", "DEPT002", "DEPT003");
        queryDTO.setDeptCodes(deptCodes);
        
        // 设置供应商编码列表
        List<String> supplierCodes = Arrays.asList("SUP001", "SUP002", "SUP003");
        queryDTO.setSupplierCodes(supplierCodes);
        
        // 设置预约类别（使用枚举值）
        queryDTO.setBillDirection(PmsBookingCategoryEnum.PURCHASE_DISTRIBUTION.getCode());
        
        // 设置是否直流（使用枚举值）
        queryDTO.setDirectSign(YesOrNoEnum.YES.getCode());
        
        // 设置停靠点编码列表
        List<String> dockCodes = Arrays.asList("DOCK001", "DOCK002");
        queryDTO.setDockCodes(dockCodes);
        
        // 设置承运方式（使用枚举值）
        queryDTO.setTransportMode(PmsCarrierMethodEnum.SELF_TRANSPORT.getCode());
        
        // 设置承运人（模糊匹配）
        queryDTO.setTransportMan("张三");
        
        // 设置承运联系手机（模糊匹配）
        queryDTO.setTransportMobile("138");
        
        // 设置商品关键字（商品编码/名称/条码OR查询）
        queryDTO.setSkuKeyword("牛奶");
        
        // 设置状态（使用枚举值）
        queryDTO.setStatus(PmsBookingStatusEnum.SUBMITTED.getCode());
        
        // 设置预约备注（模糊匹配）
        queryDTO.setAppointmentRemark("紧急处理");
        
        // 设置承运备注（模糊匹配）
        queryDTO.setTransportRemark("快速配送");
        
        // 设置品类编码列表（前缀模糊查询）
        List<String> categoryCodes = Arrays.asList("01", "02", "03");
        queryDTO.setCategoryCodes(categoryCodes);
        
        // 设置品牌编码列表
        List<String> brandCodes = Arrays.asList("BRAND001", "BRAND002");
        queryDTO.setBrandCodes(brandCodes);
        
        // 设置分页参数
        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(20L);

        // 创建分页对象
        Page<PmsAppointmentDetailDTO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        
        System.out.println("=== 开始执行预约单详细查询（UNION查询） ===");
        System.out.println("查询条件：" + Jsons.toJson(queryDTO));
        System.out.println();
        
        System.out.println(pmsAppointmentBillMapper.selectAppointmentBillDetails(page, queryDTO));
    }

    @Test
    public void test05() {
        System.out.println(ValidStatusEnum.VALID.verifyByCode(1));
        System.out.println(StandardEnum.codeOf(ValidStatusEnum.class, 1));
        System.out.println(StandardEnum.descOf(ValidStatusEnum.class, "失效"));

        System.out.println(ValidStatusEnum.VALID.verifyByType(1, true));
        System.out.println(ValidStatusEnum.VALID.verifyByType(Arrays.asList(0, 1), true));
    }

    @Test
    public void test06() {
        System.out.println(EnumContainer.getEnumsByName("生效"));
    }


    @Test
    public void test07() {
        System.out.println(mdExpenseItemCategoryMapper.getFirstGapNumber());
    }

    @Test
    public void test08() {
        MdExpenseItemCategoryPO po = MdExpenseItemCategoryPO.builder()
                .expenseItemCategoryCode(1001)
                .expenseItemCategoryName("test")
                .build();
        po.setTenantId(TenantContext.getLong());
        System.out.println(mdExpenseItemCategoryMapper.insertIfNotExists(po));
    }

    @Test
    public void test09() {
        System.out.println(supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(MdSystemParamEnum.GOODS_CATE_CODE_LENGTH_CONFIG));

        LockSupport.unpark(Thread.currentThread());
    }

    @Test
    public void test10() throws NoSuchFieldException {
        MdExpenseItemResponseDTO mdExpenseItemResponseDTO = new MdExpenseItemResponseDTO();
        mdExpenseItemResponseDTO.setExpenseType(1);
        mdExpenseItemResponseDTO.setStatus(1);

        System.out.println(MdExpenseItemConvert.INSTANCE.dto2view(mdExpenseItemResponseDTO));
    }

    @Test
    public void test11() {
        // 构建包含所有查询条件的MdDistPriceBillQueryDTO实例
        MdDistPriceBillQueryDTO queryDTO = new MdDistPriceBillQueryDTO();
        
        // 设置配送中心编码
        // queryDTO.setWhCode("WH001");
        
        // 设置接收方部门编码列表
        List<String> deptCodes = Arrays.asList("DEPT001", "DEPT002");
        queryDTO.setDeptCodes(deptCodes);
        
        // 设置时间区间（String类型）
        queryDTO.setStartTime("2023-01-01 00:00:00");
        queryDTO.setEndTime("2023-12-31 23:59:59");
        
        // 设置状态列表
        List<Integer> statusList = Arrays.asList(1, 2);  // 1-有效，2-待审核
        queryDTO.setStatusList(statusList);
        
        // 设置商品编码或条码列表
        List<String> skuOrBarCodes = Arrays.asList("SKU001", "6901234567890");
        queryDTO.setSkuOrBarCodes(skuOrBarCodes);
        
        // 设置是否精确匹配
        queryDTO.setIsExactMatch(true);
        
        // 设置商品分类编码列表
        List<String> skuClassCodes = Arrays.asList("CLASS001", "CLASS002");
        queryDTO.setSkuClassCodes(skuClassCodes);
        
        // 设置分页参数
        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(10L);

        // 使用RepositoryService调用
        System.out.println(mdDistPriceBillRepositoryService.selectBillJoinDetailPage(
            new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize()), 
            queryDTO
        ));
    }

    @Test
    public void test12() {
        // 构建配送价格查询DTO
        MdDistPriceQueryDTO queryDTO = new MdDistPriceQueryDTO();
        
        // 设置配送中心编码列表
        List<String> whCodes = Arrays.asList("WH001", "WH002");
        queryDTO.setWhCodes(whCodes);
        
        // 设置部门编码列表
        List<String> deptCodes = Arrays.asList("DEPT001", "DEPT002");
        queryDTO.setDeptCodes(deptCodes);
        
        // 设置店组群编码列表
        List<String> deptGroupCodes = Arrays.asList("GROUP001", "GROUP002");
        queryDTO.setDeptGroupCodes(deptGroupCodes);
        
        // 设置商品/条码混合查询列表
        List<String> skuOrBarCodes = Arrays.asList("SKU001", "6901234567890");
        queryDTO.setSkuOrBarCodes(skuOrBarCodes);
        
        // 设置商品分类编码列表
        List<String> skuClassCodes = Arrays.asList("CLASS001", "CLASS002");
        queryDTO.setSkuClassCodes(skuClassCodes);
        
        // 设置是否精确匹配
        queryDTO.setIsExactMatch(true);
        
        // 设置时间区间（必须同时设置开始和结束时间）
        queryDTO.setStartTime("2023-01-01 00:00:00");
        queryDTO.setEndTime("2023-12-31 23:59:59");
        
        // 设置有效状态列表
        List<Integer> validStatusList = Arrays.asList(1);  // 假设1-有效
        queryDTO.setValidStatusList(validStatusList);
        
        // 设置分页参数
        queryDTO.setCurrent(1L);
        queryDTO.setPageSize(10L);

        // 使用RepositoryService调用
        Page<MdDistPriceResponseDTO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        System.out.println(Jsons.toJson(mdDistPriceRepositoryService.selectDistPricePage(page, queryDTO)));
    }

    @Test
    public void test13() {
        QueryUserResourceReq build = QueryUserResourceReq.builder()
                .userId("10149793")
                .type(3)
                .build();

        System.out.println(idaasFeignClient.queryUserResource(build));
    }

    @Test
    public void test14() {
        Set<UserResourceCodeEnum> userResourceCodeEnums = userResourceUtil.resourceEnumsGroupOf(UserResourceGroupEnum.SCM_PMS_APPOINTMENT);
        System.out.println(userResourceUtil.haveAllTheButtonResources(userResourceCodeEnums));
        System.out.println();
    }

    @Test
    public void test15() {
        distributionPriceDomainService.createNewDistPrice("153658", "dp20230001");
    }

    @Test
    public void test16() {
        QueryDeptListReq req = QueryDeptListReq.builder()
                .type(2)
                .deptCodeList(Arrays.asList("016568", "016552"))
                .pageSize(Integer.MAX_VALUE)
                .build();
        System.out.println(baseDataSystemFeignClient.queryDeptList(req));
    }

    @Test
    public void test17() {
        QueryDeptGroupBatchReq req = QueryDeptGroupBatchReq.builder()
                .classCode("0001")
                .codeList(Arrays.asList("1101", "10111001231"))
                .build();
        System.out.println(baseDataSystemFeignClient.queryDeptGroupBatch(req));
    }

    @Test
    public void test18() {
        QueryAllDeptDataReq req = QueryAllDeptDataReq.builder()
                .groupCodeList(Sets.set("0000901"))
                .classCode("0000")
                .deepSearch(true)
                .build();
        System.out.println(baseDataSystemFeignClient.queryAllDeptData(req));
    }

    @Test
    public void test19() {
        QueryGroupDeptListReq queryGroupDeptListReq = QueryGroupDeptListReq.builder()
                .classCode("0000")
                .codeList(Arrays.asList("0000901", "000090100002"))
                .build();
        System.out.println(baseDataSystemFeignClient.queryGroupDeptList(queryGroupDeptListReq));
    }

    @Test
    public void test20() {
        ExistDistPriceDataReq.DeptParam deptParam = ExistDistPriceDataReq.DeptParam.builder()
                .deptType(1)
                .deptCode("d001")
                .skuOrSkuClassCode("g001")
                .build();
        ExistDistPriceDataReq.DeptParam deptParam2 = ExistDistPriceDataReq.DeptParam.builder()
                .deptType(1)
                .deptCode("d002")
                .skuOrSkuClassCode("g001")
                .build();

        ExistDistPriceDataReq existDistPriceDataReq = ExistDistPriceDataReq.builder()
                .whCode("wh001")
                .deptParamList(new HashSet<>(Arrays.asList(deptParam, deptParam2)))
                .build();
//        System.out.println(mdDistPriceMapper.listValidDistPrice(existDistPriceDataReq));
    }

    @Test
    public void test21() {
        System.out.println(mdDistPriceBillRepositoryService.listPendingTakeEffectBillNo(LocalDateTime.now()).get(0).getTenantId());
    }

    @Test
    public void test22() {
        QueryDeptTreeReq.QueryFilter build = QueryDeptTreeReq.QueryFilter.builder().classCode("0000").codeList(Arrays.asList("00009")).build();
        QueryDeptTreeReq req = QueryDeptTreeReq.builder().queryFilter(build).build();

        System.out.println(baseDataSystemFeignClient.queryDeepDeptGroupList(req));
    }

    private MdDemandStrategyExcludePO getMockPo() {
        MdDemandStrategyExcludePO mockPo = new MdDemandStrategyExcludePO();
        mockPo.setId(1L);
        mockPo.setType(MdDemandStrategyExcludeTypeEnum.BY_CATEGORY.getCode());
        mockPo.setCode("C001");
        mockPo.setName("测试品类");
        mockPo.setDelFlag(0);
        return mockPo;
    }

    @Test
    public void testPageQueryExclude() {
        MdDemandStrategyExcludePageQueryReq request = new MdDemandStrategyExcludePageQueryReq();
//        request.setType(MdDemandStrategyExcludeTypeEnum.BY_CATEGORY.getCode());
        request.setCodeOrName("测试");
        request.setCurrent(1L);
        request.setPageSize(10L);

        MdDemandStrategyExcludePO mockPo = getMockPo();
        List<MdDemandStrategyExcludePO> records = Collections.singletonList(mockPo);
        Page<MdDemandStrategyExcludePO> page = new Page<>(1, 10);
        page.setRecords(records);
        page.setTotal(1);

        Mockito.when(mdDemandStrategyExcludeRepositoryService.page(Mockito.any(), Mockito.any())).thenReturn(page);

        PageResult<MdDemandStrategyExcludeResponseDTO> result = mdDemandStrategyDomainService.pageQueryExclude(request);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getTotal());
        Assertions.assertEquals(1, result.getRows().size());
        Assertions.assertEquals(mockPo.getCode(), result.getRows().get(0).getCode());
    }

    @Test
    public void testCreateExclude() {
        MdDemandStrategyExcludeCreateReq request = new MdDemandStrategyExcludeCreateReq();
        request.setType(MdDemandStrategyExcludeTypeEnum.BY_CATEGORY.getCode());
        request.setCode("C002");
        request.setName("新增测试品类");

        Mockito.when(mdDemandStrategyExcludeRepositoryService.save(Mockito.any(MdDemandStrategyExcludePO.class))).thenReturn(true);

//        Boolean result = mdDemandStrategyDomainService.createExclude(request);
//        Assertions.assertTrue(result);
    }

    @Test
    public void test101() {
        MdContractDefGoodsDeptReq dept = MdContractDefGoodsDeptReq.builder()
                .deptType(1)
                .code("123")
                .name("123")
//                .lastPurchPrice(BigDecimal.valueOf(1))
                .purchPriceMethod(1)
                .deductionRate(BigDecimal.valueOf(1))
                .purchTaxPrice(BigDecimal.valueOf(1))
                .maxPurchTaxPrice(BigDecimal.valueOf(1))
                .mainSupplierMode(1)
                .specialTaxPrice(BigDecimal.valueOf(1))
                .specialStartTime(LocalDateTime.now())
                .specialEndTime(LocalDateTime.now())
                .status(1)
                .processingMethod(1)
                .build();

        MdContractDefGoodsReq goods = MdContractDefGoodsReq.builder()
                .skuCode("123")
                .barcode("123")
                .skuName("123")
                .skuModel("123")
                .producingArea("123")
                .purchPrice(BigDecimal.valueOf(1))
                .salePrice(BigDecimal.valueOf(1))
                .inputTaxRate(BigDecimal.valueOf(1))
                .goodsDeptList(Arrays.asList(dept))
                .build();

        MdContractDefSaveReq mdCgoodsDefinition = MdContractDefSaveReq.builder()
                .opType(1)
                .contractNo("2222222")
                .billNo("1111111")
                .supplierCode("3333333")
                .type(1)
                .execTimeType(1)
                .remark("remark")
                .goodsList(Arrays.asList(goods))
                .build();
        contractGoodsDefineApplicationService.save(mdCgoodsDefinition);
    }

    @Test
    public void test23() {
        QueryDeptListReq req = QueryDeptListReq.builder()
                .deptCodeList(Arrays.asList("2024122421"))
                .pageSize(100)
                .build();
        System.out.println(baseDataSystemFeignClient.queryDeptList(req));

        MdContractDefGoodsReq goods = MdContractDefGoodsReq.builder()
                .skuCode("123")
                .barcode("123")
                .skuName("123")
                .skuModel("123")
                .producingArea("123")
                .purchPrice(BigDecimal.valueOf(1))
                .salePrice(BigDecimal.valueOf(1))
                .inputTaxRate(BigDecimal.valueOf(1))
//                .goodsDeptList(Arrays.asList(dept))
                .build();

        MdContractDefSaveReq mdCgoodsDefinition = MdContractDefSaveReq.builder()
                .opType(1)
                .contractNo("2222222")
                .billNo("1111111")
                .supplierCode("3333333")
                .type(1)
                .execTimeType(1)
                .remark("remark")
                .goodsList(Arrays.asList(goods))
                .build();
        contractGoodsDefineApplicationService.save(mdCgoodsDefinition);
    }

    @Test
    public void test24() {
        System.out.println(supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.DIST_PRICE_ORDER, "0001"));
    }

    @Test
    public void test25() {
        List<BatchGenerateBillNoDTO> list = new ArrayList<>();
        BatchGenerateBillNoDTO batchGenerateBillNoDTO = new BatchGenerateBillNoDTO();
        batchGenerateBillNoDTO.setDeptCode("10101");
        batchGenerateBillNoDTO.setMdBillNoBillTypeEnum(MdBillNoBillTypeEnum.DIST_PRICE_ORDER);
        batchGenerateBillNoDTO.setUnique("111");
        list.add(batchGenerateBillNoDTO);

        batchGenerateBillNoDTO = new BatchGenerateBillNoDTO();
        batchGenerateBillNoDTO.setDeptCode("201");
        batchGenerateBillNoDTO.setMdBillNoBillTypeEnum(MdBillNoBillTypeEnum.PMS_PURCHASE_ORDER);
        batchGenerateBillNoDTO.setUnique("13212");
        list.add(batchGenerateBillNoDTO);

        batchGenerateBillNoDTO = new BatchGenerateBillNoDTO();
        batchGenerateBillNoDTO.setDeptCode("201");
        batchGenerateBillNoDTO.setMdBillNoBillTypeEnum(MdBillNoBillTypeEnum.PMS_PURCHASE_ORDER);
        batchGenerateBillNoDTO.setUnique("1321211");
        list.add(batchGenerateBillNoDTO);


        System.out.println(supplychainControlEngineService.getSupplychainBizBillRuleService().getBatchBillNo(list));
    }

    @Autowired
    private IMdCommonStatusProcessRepositoryService mdCommonStatusProcessRepositoryService;

    @Test
    public void test26() {
        TenantContext.set("153658");

        List<MdCommonStatusProcessPO> list = new ArrayList<>();
        for(int i=0;i < 10;i++){
            MdCommonStatusProcessPO mdCommonStatusProcessPO = new MdCommonStatusProcessPO();
            mdCommonStatusProcessPO.setCreateUid(0L);
            mdCommonStatusProcessPO.setUpdateUid(0L);

            mdCommonStatusProcessPO.setModuleCode(MdCommonStatusModuleEnum.MD);
            mdCommonStatusProcessPO.setModuleName(MdCommonStatusModuleEnum.MD.getDesc());

            mdCommonStatusProcessPO.setBizId(1L);
            mdCommonStatusProcessPO.setBizCode("1");
            mdCommonStatusProcessPO.setBizType(MdCommonStatusBizTypeEnum.CONTRACT);

            mdCommonStatusProcessPO.setStatusCode("");
            mdCommonStatusProcessPO.setStatusName("");
            mdCommonStatusProcessPO.setRemark("re");
            list.add(mdCommonStatusProcessPO);
        }

        mdCommonStatusProcessRepositoryService.saveBatch(list);
        System.out.println("test26:" + JSON.toJSONString(list));
    }

    @Test
    public void test27() {
        ExistGoodsInfoQueryDTO build = ExistGoodsInfoQueryDTO.builder()
                .whCode("001426")
                .startTime("14:57")
                .endTime("14:57")
                .type(2)
                .code("542084")
                .build();
        System.out.println(mdDemandBatchCateGoodsMapper.listExistGoodsInfoByTimeSegmentAndWhCode(Arrays.asList(build)));
    }

    @Test
    public void test28() {
//        MdDemandStrategyAutoMappingGroupDTO build = MdDemandStrategyAutoMappingGroupDTO.builder()
//                .operationStatusList(Arrays.asList("1", "2"))
//                .build();
//        MdDemandStrategyAutoMappingGroupDTO build2 = MdDemandStrategyAutoMappingGroupDTO.builder()
//                .operationStatusList(Arrays.asList("2", "1"))
//                .build();
//
//        Stream.of(build, build2).distinct().forEach(System.out::println);
    }

    @Test
    public void test29(){
        TenantContext.set("153658");
        ListDeliveryAppointmentStrategyDTO param = new ListDeliveryAppointmentStrategyDTO();
        List<String> deliveryDeptCode = new ArrayList<>();
        deliveryDeptCode.add("1001");
        deliveryDeptCode.add("1002");
        param.setDeliveryDeptCode(deliveryDeptCode);

        //部门编码列表
        List<ListDeliveryAppointmentStrategyDTO.DeptInfo> deptList = new ArrayList<>();
        ListDeliveryAppointmentStrategyDTO.DeptInfo dept = new ListDeliveryAppointmentStrategyDTO.DeptInfo();
        dept.setDeptType(1);
        dept.setDeptCode("5050");
        deptList.add(dept);
        param.setDeptList(deptList);

        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add("2001");
        skuCodeList.add("2002");
        param.setSkuCodeList(skuCodeList);

        List<String> categoryCodeList = new ArrayList<>();
        categoryCodeList.add("3001");
        categoryCodeList.add("3002");
        param.setCategoryCodeList(categoryCodeList);

        mdDeliveryAppointmentStrategyDomainService.listDeliveryAppointmentStrategy(param);
    }

    @Test
    public void tet30(){
        MdErrorCodeEnum[] values = MdErrorCodeEnum.values();
        for (MdErrorCodeEnum value : values) {
            System.out.println(value.getCode() + "," + value.getDesc());
        }
    }

    @Test
    public void tet31(){
        System.out.println(baseDataSystemFeignClient.getSingleDeptInfo("001426"));
    }

    @Test
    public void tet32(){
        MdContractGoodsDeptPO save = new MdContractGoodsDeptPO();
        save.setContractNo("123");
        save.setSkuCode("321");
        save.setCreateCode("sys");
        save.setCreateName("sys");
        save.setUpdateCode("sys");
        save.setUpdateName("sys");
        save.setTenantId(123321L);
        mdContractGoodsDeptRepositoryService.getBaseMapper().insert(save);
        mdContractGoodsDeptRepositoryService.getBaseMapper().deleteById(save.getId());
    }

    /**
     * 测试停靠点时间统计查询方法
     */
    @Test
    public void testSelectDockTimeStats() {
        // 构建包含所有查询条件的DockTimeStatsQueryDTO实例
        DockTimeStatsQueryDTO queryDTO = DockTimeStatsQueryDTO.builder()
                // 设置计划到货开始时间
                .planArrivalTimeStart(LocalDateTime.of(2024, 1, 1, 0, 0, 0))
                // 设置计划到货结束时间
                .planArrivalTimeEnd(LocalDateTime.of(2024, 12, 31, 23, 59, 59))
                // 设置预约单状态（使用枚举值）
                .status(PmsBookingStatusEnum.SUBMITTED.getCode()) // 已提交
                // 设置停靠点编码列表
                .dockCodes(Arrays.asList("DOCK001", "DOCK002", "DOCK003"))
                .build();

        System.out.println(pmsAppointmentBillRepositoryService.selectDockTimeStats(queryDTO));
    }

    /**
     * 测试查询停靠点限制规则方法
     */
    @Test
    public void testSelectDeliveryDockLimitRules() {
        // 构建包含所有查询条件的QueryDeliveryDockLimitReq实例
        QueryDeliveryDockLimitReq queryReq = QueryDeliveryDockLimitReq.builder()
//                // 设置部门编码
//                .deptCode("DEPT001")
//                // 设置停靠点编码
//                .dockCode("DOCK001")
//                // 设置停靠点类型列表
//                .dockTypeList(Arrays.asList(1, 2, 3)) // 假设1-普通停靠点，2-特殊停靠点，3-临时停靠点
//                // 设置约束规则
//                .constraintRule(1) // 假设1-时间约束，2-数量约束
                .build();

        System.out.println(mdDeliveryDockStrategyRepositoryService.selectDeliveryDockLimitRules(queryReq));
    }

    @Test
    public void test33() {
        QueryDistPriceComplexReq.SkuCodeAndWhCode skuCodeAndWhCode = QueryDistPriceComplexReq.SkuCodeAndWhCode.builder()
                .skuCode("539128")
                .skuClassCode("004001001")
                .whCode("015929")
                .build();
        QueryDistPriceComplexReq.SkuCodeAndWhCode skuCodeAndWhCode2 = QueryDistPriceComplexReq.SkuCodeAndWhCode.builder()
                .skuCode("542761")
                .skuClassCode("004001001")
                .whCode("015929")
                .build();
        QueryDistPriceReq condition = QueryDistPriceReq.builder()
                .deptCode("202408301")
                .skuList(new HashSet<>(Arrays.asList(skuCodeAndWhCode, skuCodeAndWhCode2)))
                .build();
        System.out.println(mdDistributionPriceDomainService.queryValidDistPrice(condition));
    }

    @Test
    public void test331() {
        System.out.println(mstSupplierRepositoryService.lambdaQuery()
                .eq(MstSupplierPO::getId, 1741659816903L)
                .list());
    }
}
