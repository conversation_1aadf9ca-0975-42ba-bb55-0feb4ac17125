package com.meta.supplychain.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2024/11/25 17:39
 **/
public class NumberUtil {
    public static Integer getInteger(String str){
        try{
            if(StringUtils.isEmpty(str)){
                return 0;
            }
            Integer num = Integer.valueOf(str);
            return num;
        }
        catch (Exception e){
            return 0;
        }
    }

    public static Integer getInteger(String str,Integer defaultInt){
        try{
            if(StringUtils.isEmpty(str)){
                return defaultInt;
            }

            Integer num = Integer.valueOf(str);
            return num;
        }
        catch (Exception e){
            e.printStackTrace();
            return defaultInt;
        }
    }
}
