package com.meta.supplychain.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 获取Servlet相关组件
 */
@Slf4j
public class ServletContextHolder {

    /**
     * 从应用上下文获取HttpServletRequest组件
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest();
    }

    /**
     * 从应用上下文获取HttpServletResponse组件
     *
     * @return
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getResponse();
    }

    /**
     * 从cookie中获取数据
     * @return
     */
    public static String getValueByCookieName(String name) {
        if (name == null || "".equals(name)) {
            return null;
        }
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == attributes) {
            return null;
        }
        Cookie[] cookies = attributes.getRequest().getCookies();
        if (null == cookies) {
            return null;
        }
        for (Cookie c : cookies) {
            if (Objects.equals(c.getName(), name)) {
                return c.getValue();
            }
        }
        return null;
    }

}