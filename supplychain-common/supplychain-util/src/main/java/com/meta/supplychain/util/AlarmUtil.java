package com.meta.supplychain.util;

import cn.linkkids.framework.croods.alarm.api.WorkWeChatAlarm;
import cn.linkkids.framework.croods.alarm.enums.MsgTypeEnum;
import cn.linkkids.framework.croods.alarm.params.WorkWeChatAlarmParams;
import cn.linkkids.framework.croods.alarm.result.WorkWeChatAlarmResult;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2023/03/23 18:09
 **/
@Component
public class AlarmUtil {

    @Value("${env}")
    private String env;

    @Autowired
    private WorkWeChatAlarm workWeChatAlarm;


    public void bizWorkWeChatAlarm(String content) {
        workWeChatAlarm("SYS_ALARM",content);
    }

    /**
     * 发企业微信告警
     */
    public void workWeChatAlarm(String robotCode, String content) {
        WorkWeChatAlarmParams params = new WorkWeChatAlarmParams();
        try {
            String tenantId = TenantContext.get();
            params.setRobotCode(robotCode);
            params.setMsgType(MsgTypeEnum.TEXT);
            WorkWeChatAlarmParams.Text text = new WorkWeChatAlarmParams.Text();
            text.setContent( "【" + env + "】【" + tenantId + "】" + content);
            text.setMentionedList(Lists.newArrayList("@all"));
            params.setText(text);
            Logs.info("SysAlarmManager -> workWeChatAlarm, params: {}", params);
            WorkWeChatAlarmResult result = workWeChatAlarm.execute(params);
            if (0 != result.getErrorCode()) {
                Logs.error("SysAlarmManager -> workWeChatAlarm, error, result: {}", result);
            }
        } catch (Exception e) {
            Logs.error("SysAlarmManager -> workWeChatAlarm, error, params: {}", params, e);
        }
    }


}
