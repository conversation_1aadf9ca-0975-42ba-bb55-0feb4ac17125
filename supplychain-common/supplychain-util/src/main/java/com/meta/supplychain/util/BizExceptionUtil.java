package com.meta.supplychain.util;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.exception.ErrorCode;

public class BizExceptionUtil {

    public static void throwWithErrorCodeAndMsg(ErrorCode errorCode, String errorMsg) {
        BizExceptions.throwWithCodeAndMsg(errorCode.getErrorCode(), errorCode.getErrorMsg() + "," + errorMsg);
    }
}
