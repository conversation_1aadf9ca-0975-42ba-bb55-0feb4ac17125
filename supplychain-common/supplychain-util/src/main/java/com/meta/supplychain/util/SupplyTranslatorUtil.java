package com.meta.supplychain.util;


import cn.linkkids.framework.business.gull.language.utils.TranslatorUtils;
import cn.linkkids.framework.croods.common.exception.ErrorCode;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.meta.supplychain.util.spring.SpringContextUtil;

public class SupplyTranslatorUtil {

    public static String translate(ErrorCode errorCode) {
        return TranslatorUtils.translate(SpringContextUtil.getApplicationName(), "", errorCode.getErrorCode(), errorCode.getErrorMsg());
    }

    public static String translate(ErrorCode errorCode,String suffixMsg) {
        if (StringUtils.isBlank(suffixMsg)) {
            return translate(errorCode);
        }
        return TranslatorUtils.translate(SpringContextUtil.getApplicationName(), "", errorCode.getErrorCode(), errorCode.getErrorMsg() + "," + suffixMsg);
    }
}
