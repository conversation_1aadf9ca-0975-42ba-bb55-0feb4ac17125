package com.meta.supplychain.util;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.meta.supplychain.entity.dto.promotion.req.QueryAcceptanceRefundReq;
import com.meta.supplychain.entity.dto.promotion.req.QueryDeliveryPriceReq;
import com.meta.supplychain.entity.dto.promotion.resp.PromotionPriceInfo;
import com.meta.supplychain.entity.dto.promotion.resp.QueryAcceptanceRefundResp;
import com.meta.supplychain.entity.dto.promotion.resp.QueryDeliveryPriceResp;
import com.meta.supplychain.infrastructure.feign.PromotionFeignClient;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批次返回处理工具
 *
 * <AUTHOR> cat
 * @date 2024/8/1 17:06
 */
@Service
@AllArgsConstructor
@NoArgsConstructor
public class PromotionHandleUtil {

    @Autowired
    private PromotionFeignClient promotionFeignClient;

    @NacosValue(autoRefreshed = true, value = "${limiting.promotion.acceptanceRefund:49}")
    private Integer acceptanceRefund;

    /**
     * 查询验收退补信息
     *
     * @param req
     * @return
     */
    public Map<String, PromotionPriceInfo> queryAcceptanceRefund(QueryAcceptanceRefundReq req) {
        Map<String, PromotionPriceInfo> refundPrice = new HashMap<>();
        List<List<QueryAcceptanceRefundReq.GoodsPriceInfo>> partition = Lists.partition(req.getSkuList(), acceptanceRefund);
        List<QueryAcceptanceRefundResp.CompensationInfo> compensationInfoList = new ArrayList<>();
        for (List<QueryAcceptanceRefundReq.GoodsPriceInfo> goodsPriceInfos : partition) {
            QueryAcceptanceRefundReq partitionReq = QueryAcceptanceRefundReq.builder()
                    .storeCode(req.getStoreCode())
                    .skuList(goodsPriceInfos)
                    .build();
            QueryAcceptanceRefundResp queryAcceptanceRefundResp = promotionFeignClient.queryAcceptanceRefund(partitionReq);
            if (queryAcceptanceRefundResp != null && CollectionUtils.isNotEmpty(queryAcceptanceRefundResp.getCompensationInfoList())) {
                compensationInfoList.addAll(queryAcceptanceRefundResp.getCompensationInfoList());
                for (QueryAcceptanceRefundResp.CompensationInfo compensationInfo : queryAcceptanceRefundResp.getCompensationInfoList()) {
                    refundPrice.put(compensationInfo.getSkuCode(), PromotionPriceInfo.builder()
                            .skuCode(compensationInfo.getSkuCode())
                            .promotionId(compensationInfo.getPromotionId())
                            .promotionName(compensationInfo.getPromotionTheme())
                            .promotionPrice(BigDecimal.valueOf(compensationInfo.getCompensateValue() / 100d))
                            .build());
                }
            }
        }
        return refundPrice;
    }

    /**
     * 查询验收退补信息
     *
     * @param req
     * @return
     */
    public Map<String, PromotionPriceInfo> queryDeliveryPrice(QueryDeliveryPriceReq req) {
        Map<String, PromotionPriceInfo> refundPrice = new HashMap<>();
        List<List<String>> partition = Lists.partition(req.getSkuCodeList(), acceptanceRefund);
        for (List<String> skuCodes : partition) {
            QueryDeliveryPriceReq partitionReq = QueryDeliveryPriceReq.builder()
                    .storeCode(req.getStoreCode())
                    .skuCodeList(skuCodes)
                    .build();
            QueryDeliveryPriceResp queryAcceptanceRefundResp = promotionFeignClient.queryDeliveryPrice(partitionReq);
            if (queryAcceptanceRefundResp != null && CollectionUtils.isNotEmpty(queryAcceptanceRefundResp.getDeliveryPriceInfoList())) {
                for (QueryDeliveryPriceResp.DeliveryPriceInfo deliveryPriceInfo : queryAcceptanceRefundResp.getDeliveryPriceInfoList()) {
                    refundPrice.put(deliveryPriceInfo.getSkuCode(), PromotionPriceInfo.builder()
                            .skuCode(deliveryPriceInfo.getSkuCode())
                            .promotionPrice(BigDecimal.valueOf(deliveryPriceInfo.getPmDeliveryPrice() / 100d))
                            .promotionId(deliveryPriceInfo.getPromotionId())
                            .promotionName(deliveryPriceInfo.getPromotionTheme())
                            .build());
                }
            }
        }
        return refundPrice;
    }

}
