package com.meta.supplychain.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/09 13:48
 **/
public class CronGenerator {
    public static String cronStr(LocalDateTime baseTime, long m) {
        // 计算计划时间h小时m分钟后的时间点
        LocalDateTime delayTime = baseTime.plusMinutes(m);
        // 创建Cron表达式
        String cronExpression = generateCronExpression(delayTime);
        return cronExpression;
    }

    public static String generateCronExpression(LocalDateTime dateTime) {
        // 将LocalDateTime转换为Cron表达式的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ss mm HH dd MM ? yyyy");
        // 生成Cron表达式
        return dateTime.format(formatter);
    }
}
