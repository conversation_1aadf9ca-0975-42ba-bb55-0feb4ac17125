package com.meta.supplychain.entity.dto.pms.resp.appointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.DecimalScale;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 预约单商品查询结果DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "预约单商品查询结果DTO")
public class PmsAppointmentBillGoodsQueryResultDTO {

    @Schema(description = "预约单据号")
    private String billNo;

    @Schema(description = "创建人编码")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @Schema(description = "计划到达时间")
    private LocalDateTime planArrivalTime;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @Schema(description = "计划完成时间")
    private LocalDateTime planCompleteTime;

    @Schema(description = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @DecimalScale(value = 4)
    @Schema(description = "本次预约数量")
    private BigDecimal appointmentQty;

    @Schema(description = "采购订单号")
    private String purchBillNo;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;
}
