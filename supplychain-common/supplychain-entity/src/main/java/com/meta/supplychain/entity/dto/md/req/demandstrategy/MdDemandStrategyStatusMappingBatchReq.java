package com.meta.supplychain.entity.dto.md.req.demandstrategy;

import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 需求策略状态流转规则批量创建请求
 * <AUTHOR>
 */
@Data
public class MdDemandStrategyStatusMappingBatchReq {
    
    @Schema(description = "需求策略状态流转规则列表", required = true)
    @NotEmpty(message = "需求策略状态流转规则列表不能为空")
    @Valid
    private List<MdDemandStrategyStatusMapping4DeptDTO> items;
}
