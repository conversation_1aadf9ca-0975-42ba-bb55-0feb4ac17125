package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购订单明细汇总列表导出实体")
public class PmsPurchaseDetailExportView {
    @ViewField(headerName = "采购订单号")
    private String billNo;

    @ViewField(headerName = "采购订单行号")
    private String insideId;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    /**商品类型 0商品1附赠商品2附赠赠品*/
    @ViewField(headerName = "商品类型")
    private String skuTypeDesc;

    @ViewField(headerName = "商品编码")
    private String skuCode;

    @ViewField(headerName = "商品名称")
    private String skuName;

    @ViewField(headerName = "商品条码")
    private String barcode;

    @ViewField(headerName = "商品货号")
    private String goodsNo;

    @ViewField(headerName = "品类编码")
    private String categoryCode;

    @ViewField(headerName = "品类名称")
    private String categoryName;

    @ViewField(headerName = "单位")
    private String basicUnit;

    @ViewField(headerName = "整件单位")
    private String packageUnit;

    @ViewField(headerName = "规格")
    private String skuModel;

    @ViewField(headerName = "进项税率")
    private BigDecimal inputTaxRate;

    @ViewField(headerName = "销项税率")
    private BigDecimal outputTaxRate;

    @ViewField(headerName = "商品包装率")
    private BigDecimal unitRate;

    @ViewField(headerName = "订货包装率")
    private BigDecimal purchUnitRate;

    /**是否直流订单 0-非直流 1-直流*/
    @ViewField(headerName = "是否直流")
    private String directSignDesc;

    @ViewField(headerName = "促销期进价")
    private String promotePeriodPrice;

    @ViewField(headerName = "促销期活动编码")
    private String promoteActivityCode;

    @ViewField(headerName = "促销活动名称")
    private String promoteActivityName;

    @ViewField(headerName = "合同特供价")
    private String contractSpecialPrice;

    @ViewField(headerName = "合同进价")
    private String contractPrice;

    @ViewField(headerName = "合同最高进价")
    private String contractMaxPrice;

    @ViewField(headerName = "最后进价")
    private String lastPurchPrice;

    @ViewField(headerName = "订单行采购价格")
    private String purchPrice;

    @ViewField(headerName = "订单行采购数量")
    private BigDecimal purchQty;

    @ViewField(headerName = "订单行采购金额")
    private String purchMoney;

    @ViewField(headerName = "订单行采购税金")
    private String purchTax;

    @ViewField(headerName = "预约数量")
    private BigDecimal appointmentQty;

    @ViewField(headerName = "预约率（%）")
    private BigDecimal appointmentRate;

    @ViewField(headerName = "履行数量")
    private BigDecimal fulfilQty;

    @ViewField(headerName = "履行金额")
    private String fulfilMoney;

    @ViewField(headerName = "数量履行率（%）")
    private BigDecimal fulfilRate;

    @ViewField(headerName = "最后履行时间")
    private String fulfilTime;

    /**确认标记  0-无需确认，1-未确认，2-已确认*/
    @ViewField(headerName = "订单行确认标记")
    private String confirmSignDesc;

    @ViewField(headerName = "订单行确认数量")
    private BigDecimal confirmQty;

    /**单据类别 -1:采退，1:采购*/
    @ViewField(headerName = "单据类别")
    private String billDirectionDesc;

    /**采购类型 0-门店采购，1-配送采购*/
    @ViewField(headerName = "采购类型")
    private String billTypeDesc;

    @ViewField(headerName = "供应商合同号")
    private String contractNo;

    @ViewField(headerName = "商品品项数")
    private Integer totalSkuCount;

    @ViewField(headerName = "订单采购数量")
    private BigDecimal totalQty;

    @ViewField(headerName = "订单采购金额")
    private String totalTaxMoney;

    @ViewField(headerName = "订单采购税金")
    private String totalTax;

    @ViewField(headerName = "送货日期")
    private String deliverDate;

    /**送货方式 0-到店，1-到客户*/
    @ViewField(headerName = "送货方式")
    private String sendModeDesc;

    @ViewField(headerName = "有效日期")
    private String validityDate;

    @ViewField(headerName = "停靠点")
    private String dockName;

    /**来源 0-手工单，1-需求单，2-配转采*/
    @ViewField(headerName = "来源")
    private String billSourceDesc;

    @ViewField(headerName = "来源单号")
    private String srcBillNo;

    @ViewField(headerName = "来源单据备注")
    private String srcRemark;

    @ViewField(headerName = "采购计划单号")
    private String planBillNo;

    @ViewField(headerName = "退货原因")
    private String refundReasonDesc;

    @ViewField(headerName = "创建人")
    private String createName;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "采购订单备注")
    private String purchRemark;

    @ViewField(headerName = "审核人")
    private String auditName;

    @ViewField(headerName = "审核时间")
    private String auditTime;

    @ViewField(headerName = "审核备注")
    private String auditRemark;

    @ViewField(headerName = "作废人")
    private String cancelManName;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    @ViewField(headerName = "作废备注")
    private String cancelRemark;

    @ViewField(headerName = "订货属性")
    private String orderAttributeName;

    @ViewField(headerName = "需求批次")
    private String purchBatchNo;

    /**租户打印次数   供应商打印次数*/
    @ViewField(headerName = "打印次数")
    private Integer printCount;

    /**已读标记 0-否，未读  1-是，已读*/
    @ViewField(headerName = "已读标志")
    private String readSignDesc;

    @ViewField(headerName = "已读时间")
    private String readTime;

    /**状态 99-待转单， 0-草稿，1-待审核，2-已审核，3-收货中，4-已完成，5-已过期，6-已作废*/
    @ViewField(headerName = "状态")
    private String statusDesc;

    @ViewField(headerName = "确认时间")
    private String confirmDeliverTime;

    @ViewField(headerName = "发货标记")
    private String shipSignDesc;

    @ViewField(headerName = "发货时间")
    private String shipTime;

}
