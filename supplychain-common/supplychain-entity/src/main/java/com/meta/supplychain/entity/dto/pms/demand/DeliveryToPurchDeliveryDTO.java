package com.meta.supplychain.entity.dto.pms.demand;

import com.meta.supplychain.entity.po.pms.PmsDemandPruchDeliveryRefPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/28 19:31
 **/
@Data
public class DeliveryToPurchDeliveryDTO {

    @Schema(description = "配送订单主表")
    private WdDeliveryBillPO deliveryBill;

    @Schema(description = "配送订单明细表")
    private List<WdDeliveryBillDetailPO> deliveryBillDetailList = new ArrayList<>();

    @Schema(description = "需求单出货方与采购配送订单关联关系表")
    private List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = new ArrayList<>();
}
