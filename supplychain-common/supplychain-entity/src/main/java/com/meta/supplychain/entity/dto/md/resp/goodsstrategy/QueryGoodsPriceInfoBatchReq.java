package com.meta.supplychain.entity.dto.md.resp.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单")
@Getter
@Setter
@ToString
public class QueryGoodsPriceInfoBatchReq {
    /**
     * 部门编码列表
     *
     */
    @Schema(description = "仓库编码 - 部门编码映射")
    @NotEmpty(message = "部门编码映射列表不能为空")
    private List<DeptInfo> whDeptCodeMapList;


    @Schema(description = "商品信息")
    @NotEmpty(message = "商品信息不能为空")
    @Valid
    private List<QueryGoodsPriceInfoReq.GoodsInfo> goodsInfoList;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptInfo{
        @Schema(description = "仓库编码")
        @NotBlank(message = "仓库编码不能为空")
        private String whCode;

        @Schema(description = "入货部门编码")
        @NotBlank(message = "入货部门编码不能为空")
        private String inDeptCode;
    }



}
