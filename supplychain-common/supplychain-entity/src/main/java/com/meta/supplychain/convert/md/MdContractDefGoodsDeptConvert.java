package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.contractdef.MdContractDefGoodsDTO;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractDefGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.req.contractdef.MdContractDefGoodsDeptReq;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefineGoodsDeptPO;
import com.meta.supplychain.enums.md.MdContractGoodsDeptTypeEnum;
import com.meta.supplychain.enums.md.MdPriceCompensateTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdContractDefGoodsDeptConvert {
    MdContractDefGoodsDeptConvert INSTANCE = Mappers.getMapper(MdContractDefGoodsDeptConvert.class);

    MdContractGoodsDefineGoodsDeptPO convertReq2po(MdContractDefGoodsDeptReq model);

    List<MdContractGoodsDefineGoodsDeptPO> convertReq2poList(List<MdContractDefGoodsDeptReq> list);

    @Mapping(source = "deptType", target = "deptType")
    @Mapping(source = "deptType", target = "deptTypeDesc", qualifiedByName = "deptTypeToDesc")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethod")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "mainSupplierMode", target = "mainSupplierMode")
    @Mapping(source = "mainSupplierMode", target = "mainSupplierModeDesc", qualifiedByName = "mainSupplierModeToDesc")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    @Mapping(source = "processingMethod", target = "processingMethod")
    @Mapping(source = "processingMethod", target = "processingMethodDesc", qualifiedByName = "processingMethodToDesc")
    @Mapping(source = "oldPurchPriceMethod", target = "oldPurchPriceMethod")
    @Mapping(source = "oldPurchPriceMethod", target = "oldPurchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "banReturn", target = "banReturn")
    @Mapping(source = "priceCompensate", target = "priceCompensate")
    @Mapping(source = "priceCompensate", target = "priceCompensateDesc", qualifiedByName = "priceCompensateToDesc")
    @Mapping(source = "priceCompensateType", target = "priceCompensateType")
    @Mapping(source = "priceCompensateType", target = "priceCompensateTypeDesc", qualifiedByName = "priceCompensateTypeToDesc")
    MdContractDefGoodsDeptDTO convertPo2DTO(MdContractGoodsDefineGoodsDeptPO model);

    @Mapping(source = "deptType", target = "deptType")
    @Mapping(source = "deptType", target = "deptTypeDesc", qualifiedByName = "deptTypeToDesc")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethod")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "mainSupplierMode", target = "mainSupplierMode")
    @Mapping(source = "mainSupplierMode", target = "mainSupplierModeDesc", qualifiedByName = "mainSupplierModeToDesc")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    @Mapping(source = "processingMethod", target = "processingMethod")
    @Mapping(source = "processingMethod", target = "processingMethodDesc", qualifiedByName = "processingMethodToDesc")
    @Mapping(source = "oldPurchPriceMethod", target = "oldPurchPriceMethod")
    @Mapping(source = "oldPurchPriceMethod", target = "oldPurchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "banReturn", target = "banReturn")
    @Mapping(source = "priceCompensate", target = "priceCompensate")
    @Mapping(source = "priceCompensate", target = "priceCompensateDesc", qualifiedByName = "priceCompensateToDesc")
    @Mapping(source = "priceCompensateType", target = "priceCompensateType")
    @Mapping(source = "priceCompensateType", target = "priceCompensateTypeDesc", qualifiedByName = "priceCompensateTypeToDesc")
    List<MdContractDefGoodsDeptDTO> convertPo2DTOList(List<MdContractGoodsDefineGoodsDeptPO> list);

    MdContractDefGoodsDTO convertPo2GoodsDTO(MdContractGoodsDefineGoodsDeptPO model);

    @Named("deptTypeToDesc")
    default String deptTypeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return MdContractGoodsDeptTypeEnum.ofCodeToDesc(code) != null ?
                MdContractGoodsDeptTypeEnum.ofCodeToDesc(code) : String.valueOf(code);
    }

    @Named("purchPriceMethodToDesc")
    default String purchPriceMethodToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "定价" : "倒扣率";
    }

    @Named("mainSupplierModeToDesc")
    default String mainSupplierModeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "是" : "否";
    }

    @Named("statusToDesc")
    default String statusToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "生效中" : "待生效";
    }

    @Named("processingMethodToDesc")
    default String processingMethodToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "取消" : "不处理";
    }

    @Named("priceCompensateToDesc")
    default String priceCompensateToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "是" : "否";
    }

    @Named("priceCompensateTypeToDesc")
    default String priceCompensateTypeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return MdPriceCompensateTypeEnum.ofCodeToDesc(code) != null ?
                MdPriceCompensateTypeEnum.ofCodeToDesc(code) : String.valueOf(code);
    }
}
