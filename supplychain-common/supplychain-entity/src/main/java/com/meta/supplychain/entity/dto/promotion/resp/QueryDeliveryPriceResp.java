package com.meta.supplychain.entity.dto.promotion.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2023/5/5 17:36
 */
@Data
public class QueryDeliveryPriceResp implements Serializable {

    private List<DeliveryPriceInfo> deliveryPriceInfoList;

    @Data
    public static class DeliveryPriceInfo{

        /**
         * 商品编码
         */
        private String skuCode;

        /**
         * 配送价
         */
        private Long pmDeliveryPrice;

        /**
         * 开始时间
         */
        private String queryStartDate;

        /**
         * 结束时间
         */
        private String queryEndDate;

        /**
         * 活动编码
         */
        private Long promotionId;

        /**
         * 活动名称
         */
        private String promotionTheme;
    }
}
