package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceDTO;
import com.meta.supplychain.entity.po.md.MdDistPricePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 配送价格对象转换器
 */
@Mapper
public interface MdDistPriceDtoConvert {

    MdDistPriceDtoConvert INSTANCE = Mappers.getMapper(MdDistPriceDtoConvert.class);

    /**
     * PO转DTO
     */
    MdDistPriceDTO po2dto(MdDistPricePO po);


    List<MdDistPriceDTO> poList2dto(List<MdDistPricePO> po);

    /**
     * DTO转PO
     */
    MdDistPricePO dto2po(MdDistPriceDTO dto);
} 