package com.meta.supplychain.entity.dto.bds.req;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/5/16 18:06
 */
@Data
@Builder
public class QuerySupplierListReq {

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 供应商编码集合
     */
    private List<String> codeList;

    /**
     * 供应商外部编码
     */
    private String outCode;

    /**
     * 供应商外部编码集合
     */
    private List<String> outCodeList;

    /**
     * 供应商编码或名称 模糊匹配
     */
    private String keyword;

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 页面大小
     */
    private Integer pageSize;



}
