package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "供应链模块名称枚举", code = "mdModuleCodeEnum")
public enum MdModuleCodeEnum implements VerifiableEnum<String> {
    MD("SCMD", "供应链主数据"),
    WDS("SCWDS", "供应链仓库配送系统"),
    PMS("SCPMS", "供应链采购管理系统");

    private final String code;
    private final String desc;
}
