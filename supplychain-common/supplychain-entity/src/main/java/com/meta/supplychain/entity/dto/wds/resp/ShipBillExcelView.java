package com.meta.supplychain.entity.dto.wds.resp;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Builder
@Data
public class ShipBillExcelView {

    /**
     * 配送发货单号
     */
    @ViewField(headerName = "配送单号")
    private String billNo;

    @ViewField(headerName = "配送中心编码")
    private String whCode;

    @ViewField(headerName = "配送中心名称")
    private String whName;
    /**
     * 状态[  拣货状态 -1处理中  1待发货 2已发货 3验收中 4验收待审核 5已验收  9已作废]
     */
    @ViewField(headerName = "状态")
    private String status;

    @ViewField(headerName = "入货部门编码")
    private String inDeptCode;

    @ViewField(headerName = "入货部门名称")
    private String inDeptName;

    @ViewField(headerName = "配送单来源")
    private String billSource;

    @ViewField(headerName = "配送单据类型")
    private String billType;

    @ViewField(headerName = "配送数量")
    private BigDecimal totalShipQty;
    @ViewField(headerName = "配送金额")
    private String totalShipTaxMoney;
    @ViewField(headerName = "配送税金")
    private String totalShipTax;

    @ViewField(headerName = "是否冲红")
    private String reversalBillSign;

    @ViewField(headerName = "制单时间")
    private String createTime;
    @ViewField(headerName = "制单人编码")
    private String createCode;
    @ViewField(headerName = "制单人名称")
    private String createName;
    @ViewField(headerName = "发货时间")
    private String shipTime;
    @ViewField(headerName = "发货人编码")
    private String shipManCode;
    @ViewField(headerName = "发货人名称")
    private String shipManName;

    /**
     * 原单号
     * 仅是否冲红=是 或者 单据来源=差异处理时展示
     */
    @ViewField(headerName = "原单号")
    private String sourceBillNo;

    /**
     * 退货收货单号
     */
    @ViewField(headerName = "退货收货单号")
    private String refundAcceptBillNo;

    /**
     * 备注
     */
    @ViewField(headerName = "备注")
    private String remark;

}
