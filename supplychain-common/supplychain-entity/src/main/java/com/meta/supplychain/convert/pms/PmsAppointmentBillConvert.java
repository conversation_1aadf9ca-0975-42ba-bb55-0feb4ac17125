package com.meta.supplychain.convert.pms;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商预约单主表转换器
 * <AUTHOR>
 */
@Mapper
public interface PmsAppointmentBillConvert extends StandardEnumConvert {
    
    PmsAppointmentBillConvert INSTANCE = Mappers.getMapper(PmsAppointmentBillConvert.class);

    /**
     * PO转DTO
     */
    PmsAppointmentBillDTO convertPo2Dto(PmsAppointmentBillPO po);

    /**
     * PO列表转DTO列表
     */
    List<PmsAppointmentBillDTO> convertPo2DtoList(List<PmsAppointmentBillPO> poList);

    /**
     * DTO转PO - 忽略BaseDTO字段、提交操作字段、作废操作字段和id字段
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createCode", ignore = true),
            @Mapping(target = "createUid", ignore = true),
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateUid", ignore = true),
            @Mapping(target = "updateCode", ignore = true),
            @Mapping(target = "updateName", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "submitManCode", ignore = true),
            @Mapping(target = "submitManName", ignore = true),
            @Mapping(target = "submitTime", ignore = true),
            @Mapping(target = "cancelManCode", ignore = true),
            @Mapping(target = "cancelManName", ignore = true),
            @Mapping(target = "cancelTime", ignore = true),
            @Mapping(target = "delFlag", ignore = true),
            @Mapping(target = "tenantId", ignore = true)
    })
    PmsAppointmentBillPO convertDto2Po(PmsAppointmentBillDTO dto);

    /**
     * DTO列表转PO列表 - 忽略BaseDTO字段、提交操作字段、作废操作字段和id字段
     */
    List<PmsAppointmentBillPO> convertDto2PoList(List<PmsAppointmentBillDTO> dtoList);
} 