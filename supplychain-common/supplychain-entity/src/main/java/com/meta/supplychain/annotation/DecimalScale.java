package com.meta.supplychain.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.serializes.BigDecimalSerializer;

import java.lang.annotation.*;
import java.math.RoundingMode;

/**
 * BigDecimal小数位数控制注解
 * 标注在字段上，指定JSON序列化时的小数保留位数
 * 
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@JacksonAnnotationsInside
@JsonSerialize(using = BigDecimalSerializer.class)
public @interface DecimalScale {
    
    /**
     * 小数保留位数
     * @return 小数位数
     */
    int value() default 2;
    
    /**
     * 舍入模式
     * @return 舍入模式，默认为四舍五入
     */
    RoundingMode roundingMode() default RoundingMode.HALF_UP;
} 