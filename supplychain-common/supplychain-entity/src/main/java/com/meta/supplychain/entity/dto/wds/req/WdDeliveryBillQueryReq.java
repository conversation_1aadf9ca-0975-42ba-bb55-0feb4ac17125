package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.PageParams;
import com.meta.supplychain.entity.base.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单查询")
@Getter
@Setter
@ToString
public class WdDeliveryBillQueryReq extends PageParams implements BaseReq {
    @Schema(description = "配送单据号 模糊匹配")
    private String billNo;
    @Schema(description = "配送部门名称")
    private String whName;
    @Schema(description = "配送部门编码")
    private String whCode;

    @Schema(description = "配送部门编码列表")
    private List<String> whCodeList;

    /**
     *  日期查询  创建  审核 作废
     */
    @Schema(description = "时间类型 0 创建时间 1 审核时间 2 作废时间")
    private Integer timeType;

    @Schema(description = "开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @Schema(description = "结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;


    @Schema(description = "入货部门编码")
    private String inDeptCode;

    @Schema(description = "入货部门编码列表")
    private List<String> inDeptCodeList;

    @Schema(description = "入货部门名称")
    private String inDeptName;

    @Schema(description = "单据类别 -1退配 1 配送 WDDeliveryOrderDirectionEnum")
    private Integer billDirection;

    @Schema(description = "是否直流[0 否 1 是  ]  YesOrNoEnum")
    private Integer directSign;

    /**
     *  订货属性
     */
    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "订货属性编码列表")
    private List<String> orderAttributeCodeList;

    /** 配送单据类型 1.仓库配送、2.仓间调拨 */
    @Schema(description = "配送单据类型 0.仓库配送、1.仓间调拨 WDDeliveryOrderTypeEnum")
    private Integer billType;

    @Schema(description = "配送单来源 0 手工 1 需求单 WDDeliveryOrderSourceEnum")
    private Integer billSource;

    /**
     *  来源单号
     */
    @Schema(description = "来源单号 模糊匹配")
    private String srcBillNo;

    @Schema(description = "创建人编码")
    private String createCode;

    /** 审批人编码 */
    @Schema(description = "审核人编码")
    private String approveManCode;

    /** 打印次数 */
    @Schema(description = "是否打印 0 否 1 是 YesOrNoEnum")
    private Integer printCount;

    @Schema(description = "原因编码")
    private String causeCode;

    @Schema(description = "原因编码列表")
    private List<String> causeCodeList;

    @Schema(description = "需求批次 模糊匹配")
    private String requireBatch;

    @Schema(description = "需求批次列表")
    private List<String> requireBatchList;

    @Schema(description = "客户编码")
    private String customerCode;

    @Schema(description = "配送订单单据状态 WDDeliveryOrderBillStatusEnum")
    private List<Integer> statusList;

    /** 备注 */
    @Schema(description = "备注 模糊匹配")
    private String remark;

    /** 备注 */
    @Schema(description = "需求单备注 模糊匹配")
    private String srcBillRemark;


    @Max(value = 1000, message = "每页最大支持1000条")
    private Long pageSize = 10L;

    /**
     * 不显示待配单
     */
    @Schema(description = "不显示待配单 默认 true")
    private boolean notShowWaitBillFromDemand = true;

    @Override
    public List<String> getReqDeptCodeList() {
        return whCodeList;
    }

    @Override
    public String getReqDeptCode() {
        return whCode;
    }

    @Override
    public void setReqDeptCodeList(List<String> deptCodeList) {
        whCodeList = deptCodeList;
    }
}
