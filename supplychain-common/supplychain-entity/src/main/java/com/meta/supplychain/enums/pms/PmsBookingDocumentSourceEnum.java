package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约单据来源枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "预约单据来源", code = "pmsBookingDocumentSourceEnum")
public enum PmsBookingDocumentSourceEnum implements VerifiableEnum<Integer> {

    RMC(1, "rmc"),
    SCM(2, "scm");

    private final Integer code;
    private final String desc;
} 