package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "单据流转状态", code = "mdBillTransferStatusEnum")
public enum MdBillTransferStatusEnum implements VerifiableEnum<Integer> {
    TRANSFERRED(1, "已转单"),
    WAITING_TRANSFER(2, "待转单");

    private final Integer code;
    private final String desc;
} 