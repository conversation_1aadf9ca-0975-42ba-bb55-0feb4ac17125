package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单查询")
@Getter
@Setter
@ToString
public class WdDeliveryBillQueryReqInner {

    @Schema(description = "入货部门编码")
    private String inDeptCode;

    @Schema(description = "配送部门编码")
    private String whCode;

    @Schema(description = "需求批次")
    private String requireBatch;

    @Schema(description = "配送订单单据状态 WDDeliveryOrderBillStatusEnum")
    private Integer status;

    @Schema(description = "订货属性编码列表 not in")
    private List<String> orderAttributeCodeListNotIn;

    @Schema(description = "商品编码")
    private String skuCode;

}
