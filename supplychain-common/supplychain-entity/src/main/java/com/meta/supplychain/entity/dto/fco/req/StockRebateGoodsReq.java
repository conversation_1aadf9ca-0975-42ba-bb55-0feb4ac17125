package com.meta.supplychain.entity.dto.fco.req;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StockRebateGoodsReq implements Serializable {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 门店编码
     */
    private String deptCode;

    /**
     * 门店名称
     */
    private String deptName;

    /**
     * 合同号
     */
    private String contractNumber;

    /**
     * 调后单价
     */
    private BigDecimal compensateValue;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 执行模式 1, "基金补店-基金补库" 2, "厂补店-厂补库" 3, "厂补库-库补店"
     */
    private Integer compensaMode;


    private String orderDetailSerialNo;
}
