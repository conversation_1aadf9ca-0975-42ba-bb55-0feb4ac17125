package com.meta.supplychain.entity.dto.pms.demand;


import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPurchShipperReq;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Classname GroupKey
 * @Dscription TODO
 * @DATE 2025/6/4 15:00
 */
@Data
public class GroupKey {

    private final String supplierCode;
    private final String contractNo;
    private final String dockCode;
    private final String requirementBatch;
    private final Integer deliveryMethod;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupKey groupKey = (GroupKey) o;
        return Objects.equals(supplierCode, groupKey.supplierCode) &&
                Objects.equals(contractNo, groupKey.contractNo) &&
                Objects.equals(dockCode, groupKey.dockCode) &&
                Objects.equals(requirementBatch, groupKey.requirementBatch) &&
                Objects.equals(deliveryMethod, groupKey.deliveryMethod);
    }

    @Override
    public int hashCode() {
        return Objects.hash(supplierCode, contractNo, dockCode, requirementBatch, deliveryMethod);
    }
}
// 分组项容器
