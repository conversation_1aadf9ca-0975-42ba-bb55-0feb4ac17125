package com.meta.supplychain.enums.wds;

import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "移位单类型枚举",code = "WDMoveTypeEnum")
public enum WDMoveTypeEnum implements StandardEnum<Integer> {

    MOVE_SKU(1, "商品移位"),
    NOR_PICK(2, "良品分拣"),
    LOC_MOVE(3, "储位转移"),
    REMAIN_MOVE(4, "残品转移");

    private final Integer code;
    private final String desc;
}
