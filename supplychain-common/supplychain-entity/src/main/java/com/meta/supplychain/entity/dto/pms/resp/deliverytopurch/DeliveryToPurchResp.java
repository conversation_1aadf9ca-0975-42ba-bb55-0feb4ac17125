package com.meta.supplychain.entity.dto.pms.resp.deliverytopurch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class DeliveryToPurchResp {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "单内序号-服务端生成")
    private Long insideId;

    @Schema(description = "转采类型,0不可转采, 1可转采")
    private Integer type;

    @Schema(description = "转采状态,0未转采, 1已转采")
    private Integer status;

    @Schema(description = "商品类型,1主品,2赠品")
    private Integer goodsType;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品规格")
    private String skuModel;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "全路径品类编码,英文逗号分割")
    private String categoryCodeAll;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "进项税率，13%存13")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率，13%存13")
    private BigDecimal outputTaxRate;

    @Schema(description = "配送部门实际库存")
    private BigDecimal distDeptStockRealQty;

    @Schema(description = "配送部门可用库存")
    private BigDecimal distDeptStockAtpQty;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "采购批次")
    private String purchBatchNo;

    @Schema(description = "送货方式 0-到店，1-到客户")
    private Integer sendMode;

    @Schema(description = "配送数量")
    private BigDecimal deliveryQty;

    @Schema(description = "配送包装率")
    private BigDecimal deliveryUnitRate;

    @Schema(description = "确认转采购整件数量")
    private BigDecimal dtpWholeQty;

    @Schema(description = "确认转采购零头数量")
    private BigDecimal dtpOddQty;

    @Schema(description = "确认转采购数量")
    private BigDecimal dtpQty;

    @Schema(description = "不可转采原因")
    private String reason;

    @Schema(description = "配送部门编码")
    private String distDeptCode;

    @Schema(description = "配送部门名称")
    private String distDeptName;

    @Schema(description = "停靠点最近一次供应商编码,可能为空")
    private String lastSupplierCode;

    @Schema(description = "停靠点最近一次供应商名称,可能为空")
    private String lastSupplierName;

    @Schema(description = "采购包装率")
    private BigDecimal purchUnitRate;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;
}