package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商商家性质枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商商家性质", code = "mstSupplierBusinessTypeEnum")
public enum MstSupplierBusinessTypeEnum implements VerifiableEnum<Integer> {
    INDIVIDUAL_BUSINESS(1, "个体工商户"),
    COMPANY(2, "公司");

    private final Integer code;
    private final String desc;
}
