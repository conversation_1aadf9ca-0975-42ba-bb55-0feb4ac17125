package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "送货方式",code = "PmsSendModeEnum")
public enum PmsSendModeEnum implements VerifiableEnum<Integer> {
    TO_STORE(0, "到店"),
    TO_CUSTOMER(1, "到客户"),
    ;
    private Integer code;

    private String desc;

    public static PmsSendModeEnum getByCode(Integer code) {
        PmsSendModeEnum[] values = values();
        for (PmsSendModeEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        PmsSendModeEnum[] values = values();
        for (PmsSendModeEnum value : values) {
            if(value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return String.valueOf(code);
    }
}
