package com.meta.supplychain.serializes;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.meta.supplychain.annotation.DecimalScale;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * BigDecimal序列化器
 * 配合 DecimalScale 注解指定小数保留位数
 * 
 * <AUTHOR>
 */
@NoArgsConstructor
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> implements ContextualSerializer {

    /**
     * 默认小数位数
     */
    private int scale = 2;
    
    /**
     * 默认舍入模式
     */
    private RoundingMode roundingMode = RoundingMode.HALF_UP;

    private static final Map<String, BigDecimalSerializer> SERIALIZER_CACHE = new ConcurrentHashMap<>();

    public BigDecimalSerializer(int scale, RoundingMode roundingMode) {
        this.scale = scale;
        this.roundingMode = roundingMode;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty property) throws JsonMappingException {
        if (property != null) {
            DecimalScale annotation = property.getAnnotation(DecimalScale.class);
            if (annotation != null) {
                int annotationScale = annotation.value();
                RoundingMode annotationRoundingMode = annotation.roundingMode();
                String cacheKey = annotationScale + "_" + annotationRoundingMode.name();
                return SERIALIZER_CACHE.computeIfAbsent(cacheKey, k -> new BigDecimalSerializer(annotationScale, annotationRoundingMode));
            }
        }
        return this;
    }

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        // 设置小数位数并进行舍入
        BigDecimal scaledValue = value.setScale(scale, roundingMode).stripTrailingZeros();
        gen.writeNumber(scaledValue.toPlainString());
    }
} 