package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 供应商和地区店组群关系表
 * @TableName mst_supplier_group_ref
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_group_ref")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierGroupRefPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 店组群分类编码
     */
    private String deptGroupClassCode;

    /**
     * 店组群编码数组
     */
    private String groupCodeList;

    /**
     * 核算主体编码
     */
    private String accountCode;

    /**
     * 过账配送中心编码
     */
    private String deptCode;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 启用状态 0正常 1删除
     */
    private Integer delFlag;
}
