package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 差异处理单操作参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "差异处理单操作参数")
public class WdShipAccDiffBillOptParams {

    @Schema(description = "单据号")
    @NotBlank(message = "单据号不能为空")
    private String billNo;

    @Schema(description = "审核备注")
    private String remark;

    @Schema(description = "操作类型 1 审核 2 驳回")
    @NotNull(message = "操作类型不能为空")
    private Integer optType = 1;

    @Schema(description = "差异处理单明细")
    @NotEmpty(message = "差异处理单明细不能为空")
    @Valid
    List<DiffDetail> detailList;


    @Getter
    @Setter
    @NoArgsConstructor
    public static class DiffDetail {
        @Schema(description = "单内序号")
        @NotNull(message = "单内序号不能为空")
        private Long insideId;

        @Schema(description = "审核数量")
        @NotNull(message = "审核数量不能为空")
        private BigDecimal approveQty;

        @Schema(description = "审核备注")
        private String remark;

        /** 责任方:0 配送中心 |1 收货门店 WDAcceptDiffOwnerModeEnum*/
        @Schema(description = "责任方:0 配送中心 |1 收货门店 WDAcceptDiffOwnerModeEnum")
        @NotNull(message = "责任方不能为空")
        private Integer ownerMode = 0;
    }
}