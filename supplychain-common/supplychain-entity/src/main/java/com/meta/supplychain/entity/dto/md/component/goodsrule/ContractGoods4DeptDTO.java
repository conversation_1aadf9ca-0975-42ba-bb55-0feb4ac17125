package com.meta.supplychain.entity.dto.md.component.goodsrule;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ContractGoods4DeptDTO {
    @Schema(description = "合同num")
    private String contractNo;

    /** 类型,1:店主群、2:部门 */
    @Schema(description = "部门类型,1:店主群、2:部门")
    private Integer deptType;

    /** 编码 */
    @Schema(description = "部门(店组群)编码")
    private String code;

    /** 名称 */
    @Schema(description = "部门(店组群)编码")
    private String name;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品货号")
    private String skuNo;

    @Schema(description = "条码;SKU基本条码")
    private String barcode;

    @Schema(description = "规格型号;商品的规格;如500ML")
    private String skuModel;

    @Schema(description = "产地")
    private String producingArea;

    @Schema(description = "参考进价")
    private BigDecimal purchPrice;

    @Schema(description = "销售单价")
    private BigDecimal salePrice;

    @Schema(description = "进项税率，13%存13")
    private BigDecimal inputTaxRate;

    @Schema(description = "进价方式,1定价、2倒扣率")
    private Integer purchPriceMethod;

    @Schema(description = "进价方式,1定价、2倒扣率")
    private String purchPriceMethodDesc;

    @Schema(description = "倒扣率，13%存13")
    private BigDecimal deductionRate;

    @Schema(description = "含税进价")
    private BigDecimal purchTaxPrice;

    @Schema(description = "最高含税进价")
    private BigDecimal maxPurchTaxPrice;

    @Schema(description = "最后含税进价")
    private BigDecimal lastPurchTaxPrice;

    @Schema(description = "原进价方式,1定价、2倒扣率")
    private Integer oldPurchPriceMethod;

    @Schema(description = "原进价方式,1定价、2倒扣率")
    private String oldPurchPriceMethodDesc;

    @Schema(description = "原倒扣率，13%存13")
    private BigDecimal oldDeductionRate;

    @Schema(description = "原含税进价")
    private BigDecimal oldPurchTaxPrice;

    @Schema(description = "原最高含税进价")
    private BigDecimal oldMaxPurchTaxPrice;

    @Schema(description = "供货截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime supplyValidityTime;

    @Schema(description = "禁止退货控制 1逾量，2残损")
    private String banReturn;

    @Schema(description = "是否主供应商,0否,1是")
    private Integer mainSupplierMode;

    @Schema(description = "特供价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime specialStartTime;

    @Schema(description = "特供价结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime specialEndTime;

    @Schema(description = "特供价(含税)")
    private BigDecimal specialTaxPrice;

    @Schema(description = "特供价方式,-1无,0定价，1进价顺序，2售价倒扣")
    private Integer specialPriceMode;

    @Schema(description = "特供价倒扣率或者进价加价率，13%存13")
    private BigDecimal specialRate;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "当前生效定义单号")
    private String defineBillNo;

    @Schema(description = "取消定义单号（店组群->部门用）")
    private String cancelBillNo;

    @Schema(description = "经营方式,英文逗号分隔 J经销 D代销 L联营 Z租赁")
    private String operateMode;
}
