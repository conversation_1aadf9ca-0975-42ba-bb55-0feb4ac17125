package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BillDetailStateEnum {

    /**
     * 1未提单 ,2已提单 3 已作废
     */

    PENDING_AUDIT(1, "未提单"),
    AUDIT(2, "已提单"),
    EXTRACTED(3, "已作废"),

    ;

    private final Integer code;

    private final String desc;

    public static BillDetailStateEnum getEnumByCode(Integer code) {
        BillDetailStateEnum[] values = values();
        for (BillDetailStateEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


}
