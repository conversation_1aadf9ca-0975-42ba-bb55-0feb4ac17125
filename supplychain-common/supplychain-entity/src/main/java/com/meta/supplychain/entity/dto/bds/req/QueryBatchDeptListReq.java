package com.meta.supplychain.entity.dto.bds.req;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/17 9:36
 */
@Builder
@Setter
@Getter
@Data
public class QueryBatchDeptListReq {

    /**
     * 营业状态  1 营业 2 停业 3 关闭
     */
    private Integer openStatus;
    /**
     * 门店类型(1:门店2:配送)
     */
    private Integer deptType;
    /**
     * 店组群分类编码
     */
    private String classCode;

    /**
     * 店组群编码集合（二选一）
     */
    private List<String> codeList;
    /**
     * 门店编码集合（二选一）
     */
    private List<String> deptCodeList;


}
