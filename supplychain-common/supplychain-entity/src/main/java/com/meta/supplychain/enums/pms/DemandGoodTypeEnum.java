package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "配转采名称枚举",code = "DemandGoodTypeEnum")
public enum DemandGoodTypeEnum {

    /**
     * 商品类型,1主品,2赠品
     */

    NORMAL(1, "主品"),
    GIFT(2, "赠品"),


    ;

    private final Integer code;

    private final String desc;

    public static DemandGoodTypeEnum getEnumByCode(Integer code) {
        DemandGoodTypeEnum[] values = values();
        for (DemandGoodTypeEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    public static String ofCodeToDesc(Integer code) {
        for (DemandGoodTypeEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }

}
