package com.meta.supplychain.enums.wds;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 差异单状态枚举 1 待处理、2 已审核、3 已驳回
 *
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "差异单状态枚举",code = "WDAcceptDiffStatusEnum")
public enum WDAcceptDiffStatusEnum implements VerifiableEnum<Integer> {

    SHIP_DIFF_STATUS_PENDING(1, "待处理"),
    SHIP_DIFF_STATUS_APPROVED(2,  "已审核"),
    SHIP_DIFF_STATUS_REJECTED(3, "已驳回");

    private final Integer code;
    private final String desc;


}
