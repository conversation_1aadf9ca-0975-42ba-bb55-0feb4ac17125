package com.meta.supplychain.entity.dto.pms.req.billconvert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 10:55
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillConvertReq {
    @Schema(description = "来源单号")
    private String billNo;

    @Schema(description = "来源1:需求单3:手工配转采")
    private Integer sourceType;

    @Schema(description = "采购订单有效日期")
    private LocalDate purchValidityDate;

    @Schema(description = "采购订单送货日期")
    private LocalDate purchDeliverDate;

    @Schema(description = "配送订单有效日期")
    private LocalDate deliverValidityDate;

    @Schema(description = "配送订单送货日期")
    private LocalDate deliverDeliverDate;

    @Schema(description = "单据类别（-1:退货，1:要货）")
    private Integer billDirection;

    @Schema(description = "部门商品信息")
    private List<BillConvertDeptGoodsReq> deptGoodsList;
}
