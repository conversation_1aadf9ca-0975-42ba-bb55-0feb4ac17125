package com.meta.supplychain.entity.dto.stock.resp;

import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品库存
 *
 * @TableName stk_wh
 */
@Data
public class StkWhResp implements Serializable {

    /**
     * 商品spu编码
     */
    private String spuCode;

    /**
     * 商品sku编码
     */
    private String skuCode;

    /**
     * 仓库 编码
     */
    private String whCode;

    /**
     * 库存数据类型，1门店商品库存 2仓库商品库存
     */
    private Integer whType;

    /**
     * 部门 编码
     */
    private String deptCode;

    /**
     * 实库数量
     */
    private BigDecimal realQty = BigDecimal.ZERO;

    /**
     * 占用数量
     */
    private BigDecimal lockQty = BigDecimal.ZERO;

    /**
     * 活动库存
     */
    private BigDecimal actQty = BigDecimal.ZERO;

    /**
     * 活动占用
     */
    private BigDecimal actLockQty = BigDecimal.ZERO;

    /**
     * 冻结数量
     */
    private BigDecimal frozenQty = BigDecimal.ZERO;

    /**
     * 可超卖数量
     */
    private BigDecimal oversellQty = BigDecimal.ZERO;

    /**
     * 残损数量
     */
    private BigDecimal damQty = BigDecimal.ZERO;

    /**
     * 过期数量
     */
    private BigDecimal expireQty = BigDecimal.ZERO;

    /**
     * 临期品预留数量
     */
    private BigDecimal advQty = BigDecimal.ZERO;

    /**
     * 退配预留数量
     */
    private BigDecimal driLockQty = BigDecimal.ZERO;

    /**
     * 拨出预留数量
     */
    private BigDecimal toOutLockQty = BigDecimal.ZERO;

    /**
     * 拨入在途数量
     */
    private BigDecimal toInTransQty = BigDecimal.ZERO;

    /**
     * 退供预留数量
     */
    private BigDecimal drsLockQty = BigDecimal.ZERO;

    /**
     * 库调预留数量（库调损耗预留）
     */
    private BigDecimal iaLockQty = BigDecimal.ZERO;

    /**
     * 配送在途数量
     */
    private BigDecimal doTransQty = BigDecimal.ZERO;

    /**
     * 配送预留数量
     */
    private BigDecimal doLockQty = BigDecimal.ZERO;

    /**
     * 采购在途数量
     */
    private BigDecimal poTransQty = BigDecimal.ZERO;

    /**
     * 批销预留数量
     */
    private BigDecimal wsLockQty = BigDecimal.ZERO;

    /**
     * 折扣品条码预留数量
     */
    private BigDecimal discBarQty = BigDecimal.ZERO;

    /**
     * 临期品条码预留数量
     */
    private BigDecimal advBarQty = BigDecimal.ZERO;

    /**
     * 残次品条码预留数量
     */
    private BigDecimal damBarQty = BigDecimal.ZERO;

    /**
     * 可用数量
     */
    private BigDecimal atpQty = BigDecimal.ZERO;

    /**
     * 唯一索引字段拼接，方便批量查询
     */
    private String uniqKey;

    /**
     * 租户号
     */
    private String tenantId;

    /**
     * 创建人ssoId
     */
    private Long createUid;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改人ssoId
     */
    private Long updateUid;

    /**
     * 修改人编码
     */
    private String updateCode;

    /**
     * 修改人姓名
     */
    private String updateName;

    /**
     * 是否需要判断负库存标记，0否1是
     */
    private Integer checkNonNeg;

    /**
     * 是否需要判断临期库存标记，0否1是
     */
    private Integer checkAdvent;

    /**
     * 是否不管库存标记，0否1是
     */
    private Integer fullStock;

    /**
     * 是否覆盖更新，0否1是
     */
    private Integer coverUpdateFlag = 0;

    private Long version;


    public String getUniqKey() {
        if (uniqKey == null) {
            String key = String.join(":", whCode,skuCode);
            // 生成唯一索引字段,Md5加密
            uniqKey = DigestUtils.sha256Hex(key);
        }
        return uniqKey;
    }

    public BigDecimal getAtpQty() {
        // 实库数量-占用数量-冻结数量-残损数量+可超卖数量-临期品预留数量-过期数量-退配预留数量-拨出预留数量-退供预留数量-库调预留数量-配送预留数量-批销预留数量-折扣品条码预留数量-临期品条码预留数量-残次品条码预留数量
        return realQty.subtract(lockQty)
                .subtract(frozenQty)
                .subtract(damQty)
                .add(oversellQty)
                .subtract(advQty)
                .subtract(expireQty)
                .subtract(driLockQty)
                .subtract(toOutLockQty)
                .subtract(drsLockQty)
                .subtract(iaLockQty)
                .subtract(doLockQty)
                .subtract(wsLockQty)
                .subtract(discBarQty)
                .subtract(advBarQty)
                .subtract(damBarQty);
    }
}