package com.meta.supplychain.convert;

import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.po.wds.ShipBatchDetailPO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
@Mapper
public interface StockCostConvert {
    StockCostConvert INSTANCE = Mappers.getMapper(StockCostConvert.class);
    @Mapping(source = "shipQty", target = "realQty")
    @IterableMapping(elementTargetType = ShipBatchDetailPO.class) // 显式指定集合元素类型
    StkTaskItemExecuteDto shipBatchDetail2StockDetail(ShipBatchDetailPO shipBatchDetailList);
}
