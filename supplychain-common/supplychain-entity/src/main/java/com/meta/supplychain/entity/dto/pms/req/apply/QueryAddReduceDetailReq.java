package com.meta.supplychain.entity.dto.pms.req.apply;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 订货申请追加追减明细查询请求
 */
@Data
@Schema(description = "订货申请追加追减明细查询请求")
public class QueryAddReduceDetailReq {

    /**
     * 配送中心编码
     */
    @Schema(description = "配送中心编码")
    private String dcCode;

    @Schema(description = "审核日期结束")
    private String auditDateEnd;

    @Schema(description = "审核日期开始")
    private String auditDateStart;

    @Schema(description = "送货日期结束")
    private String deliverDateEnd;

    @Schema(description = "送货日期开始")
    private String deliverDateStart;

    @Schema(description = "有效日期结束")
    private String validityDateEnd;

    @Schema(description = "有效日期开始")
    private String validityDateStart;

    @Schema(description = "追加截止日期结束")
    private String appendValidityEnd;

    @Schema(description = "追加截止日期开始")
    private String appendValidityStart;

    /**
     * 采购批次
     */
    @Schema(description = "采购批次")
    private String purchBatchNo;

    /**
     * 订货部门编码
     */
    @Schema(description = "订货部门编码")
    private String deptCode;

    @Schema(description = "申请单号")
    private String billNo;

    /**
     * 管理分类编码
     */
    @Schema(description = "管理分类编码")
    private String manageCategoryCode;

    /**
     * 管理分类项编码
     */
    @Schema(description = "管理分类项编码")
    private String manageCategoryClass;

    /**
     * 属性编码
     */
    @Schema(description = "属性编码列表")
    private List<String> attributeCodes;

}
