package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelDemandBillReq {
    @Schema(description = "需求单号---除了新增，其他操作必填")
    private String billNo;

    @Schema(description = "作废类型,1作废需求单及原订货/退货申请行,2只作废需求单，原订货/退货申请行还原为待提单")
    private Integer cancelType;
}
