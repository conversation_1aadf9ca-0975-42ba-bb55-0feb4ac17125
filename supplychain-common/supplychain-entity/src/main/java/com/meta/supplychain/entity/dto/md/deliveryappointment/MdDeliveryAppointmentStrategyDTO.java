package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.md.MdAppointmentStrategyEnum;
import com.meta.supplychain.serializes.LocalTimeDeserializer;
import com.meta.supplychain.serializes.LocalTimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;

/**
 * 供应商预约策略DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约策略")
public class MdDeliveryAppointmentStrategyDTO extends BaseDTO {
    
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "配送预约策略单据号")
    private String billNo;

    @NotBlank(message = "配送部门编码不可为空")
    @Schema(description = "配送部门编码")
    private String deptCode;

    @Schema(description = "配送部门名称")
    private String deptName;

    @NotNull(message = "预约起始时间不可为空")
    @LocalDatetimePattern("HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @Schema(description = "预约起始时间")
    private LocalTime startTime;

    @NotNull(message = "预约截止时间不可为空")
    @LocalDatetimePattern("HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @Schema(description = "预约截止时间")
    private LocalTime endTime;

    @Schema(description = "状态（0 禁用；1启用）")
    @EnumValue(type = MdAppointmentStrategyEnum.class, required = true, message = "非法的状态值")
    private Integer status;

    @Length(max = 300)
    @Schema(description = "备注")
    private String remark;
} 