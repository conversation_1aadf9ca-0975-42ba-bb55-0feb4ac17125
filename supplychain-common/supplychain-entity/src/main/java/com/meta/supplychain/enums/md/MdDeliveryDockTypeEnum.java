package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "停靠点类型枚举", code = "mdDeliveryDockTypeEnum")
public enum MdDeliveryDockTypeEnum implements VerifiableEnum<Integer> {
    /** 不限 */
    UNLIMITED(1, "不限"),
    /** 直流 */
    DIRECT_FLOW(2, "直流"),
    /** 收货 */
    RECEIVING(3, "收货"),
    /** 发货 */
    SHIPPING(4, "发货")
    ;

    private final Integer code;
    private final String desc;
}
