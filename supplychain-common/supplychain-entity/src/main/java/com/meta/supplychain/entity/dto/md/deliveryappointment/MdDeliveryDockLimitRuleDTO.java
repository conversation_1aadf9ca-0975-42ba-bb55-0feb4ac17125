package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.serializes.LocalTimeDeserializer;
import com.meta.supplychain.serializes.LocalTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;

/**
 * 供应商预约策略停靠点每周限额规则DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约策略停靠点每周限额规则")
public class MdDeliveryDockLimitRuleDTO extends BaseDTO {
    
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "停靠点编码不可为空")
    @Schema(description = "停靠点编码")
    private String dockCode;

    @NotNull(message = "行号不可为空")
    @Schema(description = "行号")
    private Integer insideId;

    @NotNull(message = "起始时间不可为空")
    @LocalDatetimePattern("HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @Schema(description = "起始时间")
    private LocalTime startTime;

    @NotNull(message = "截止时间不可为空")
    @LocalDatetimePattern("HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @Schema(description = "截止时间")
    private LocalTime endTime;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周一限额")
    private Integer monday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周二限额")
    private Integer tuesday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周三限额")
    private Integer wednesday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周四限额")
    private Integer thursday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周五限额")
    private Integer friday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周六限额")
    private Integer saturday = 0;

    @Min(value = 0, message = "非法的限额数值")
    @Max(value = Integer.MAX_VALUE, message = "非法的限额数值")
    @Schema(description = "周日限额")
    private Integer sunday = 0;
} 