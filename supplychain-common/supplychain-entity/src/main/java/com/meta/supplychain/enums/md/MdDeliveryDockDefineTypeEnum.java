package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "供应商预约策略停靠点定义类型", code = "mdDeliveryDockDefineTypeEnum")
public enum MdDeliveryDockDefineTypeEnum implements VerifiableEnum<Integer> {
    BY_CATEGORY(1, "品类"),
    BY_PRODUCT(2, "商品");

    private final Integer code;
    private final String desc;
} 