package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商经营方式枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商经营方式", code = "mstSupplierOperateModeEnum")
public enum MstSupplierOperateModeEnum implements VerifiableEnum<String> {
    DISTRIBUTION("J", "经销"),
    CONSIGNMENT("D", "代销"),
    JOINT_OPERATION("L", "联营"),
    LEASE("Z", "租赁");

    private final String code;
    private final String desc;
}
