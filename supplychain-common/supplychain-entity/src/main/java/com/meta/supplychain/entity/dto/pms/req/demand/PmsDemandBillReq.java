package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandBillReq {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "1:暂存,2:提交,3暂存并提交,4自动需求单提交")
    private Integer opType = 1;

    @Schema(description = "需求单号---除了新增，其他操作必填")
    private String billNo;

    @Schema(description = "订货属性编码,逗号分割多个,要货多选,退货单选")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称,逗号分割多个,要货多选,退货单选")
    private String orderAttributeName;

    @Schema(description = "管理分类项编码")
    private String manageCategoryClass;

    @Schema(description = "管理分类编码")
    private String manageCategoryCode;

    @Schema(description = "管理分类名称")
    private String manageCategoryName;

    @Schema(description = "附件名称与地址,json格式[{name:,url:}]")
    private String attachmentUrl;

    @Schema(description = "采购订单有效日期")
    private LocalDate purchValidityDate;

    @Schema(description = "采购订单送货日期")
    private LocalDate purchDeliverDate;

    @Schema(description = "配送订单有效日期")
    private LocalDate deliverValidityDate;

    @Schema(description = "配送订单送货日期")
    private LocalDate deliverDeliverDate;

    @Schema(description = "退货原因编码-数据字典")
    private String refundReason;

    @Schema(description = "退货原因名称-数据字典")
    private String refundReasonDesc;

    @Schema(description = "商品流转规则：1.经营状态流转途径控制、2.只控制经营状态优先配送、3.只控制经营状态优先采购、4.全部不控制优先配送、5.全部不控制优先采购")
    private Integer goodCirculationRule;

    @Schema(description = "手工退货响应数量规则,1.按0，2.按可退数量")
    private Integer manualRefundResponseRule;

    @Schema(description = "单据类别（-1:退货，1:要货）")
    private Integer billDirection;

    @Schema(description = "需求单备注")
    private String remark;


    @Schema(description = "需求单来源0手工,1自动,2追加追减")
    private Integer billSource;

    @Schema(description = "页面选择的部门编码,英文逗号分割,只做页面回显不参与逻辑计算")
    private String deptCodeStr;

    @Schema(description = "需求单商品明细")
    private List<PmsDemandGoodsDetailReq> demandGoodsDetailList = new ArrayList<>();

    @Schema(description = "全部可转采与转采购信息回填")
    private List<PmsDemandDeliveryToPurchReq> deliveryToPurchParamDTO = new ArrayList<>();
}
