package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商结算方法枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商结算方法", code = "mstSupplierSettlementMethodEnum")
public enum MstSupplierSettlementMethodEnum implements VerifiableEnum<Integer> {
    CREDIT_SETTLEMENT(1, "账期结算"),
    PREPAYMENT(2, "预付款"),
    OTHER(3, "其他"),
    CASH_ON_DELIVERY(4, "现采现结");

    private final Integer code;
    private final String desc;
}
