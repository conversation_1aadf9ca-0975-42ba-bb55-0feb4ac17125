package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商表
 * @TableName mst_supplier
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家类型 1 供应商；2 pop联营；3 pop自营
     */
    private Integer type;

    /**
     * 商家编码
     */
    private String code;

    /**
     * 商家名称
     */
    private String name;

    /**
     * 商家简称
     */
    private String shortName;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 办公电话
     */
    private String phone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 启用状态 1启用；0停用
     */
    private Integer status;

    /**
     * 停用时间
     */
    private LocalDateTime downTime;

    /**
     * 商家分类编码
     */
    private String suppCateCode;

    /**
     * 商家分类名称
     */
    private String suppCateName;

    /**
     * 供应商类型
     */
    private Integer suppType;

    /**
     * 经营方式 J经销；D代销；L联营；Z租赁
     */
    private String operateMode;

    /**
     * 付款控制: 1允许付款 ；2不允许付款 ；3不允许结算
     */
    private Integer paymentControl;

    /**
     * 经营控制: 1不控制 ；2暂停进货 ；3清场
     */
    private Integer operateControl;

    /**
     * 结算模式: 1统一结算 ；2本地结算 ；3区域结算
     */
    private Integer settleMode;

    /**
     * 纳税人类型: 1一般纳税人； 2小规模纳税人
     */
    private Integer invoiceType;

    /**
     * 供应商税率
     */
    private String suppTaxRate;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String districtCode;

    /**
     * 区
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纳税人识别号
     */
    private String taxNumber;

    /**
     * 银联号
     */
    private String ibpsCode;

    /**
     * 银行类型
     */
    private String bankName;

    /**
     * 联行号
     */
    private String bankCode;

    /**
     * 开户名称
     */
    private String bankAccountName;

    /**
     * 开户名称全称
     */
    private String bankAccount;

    /**
     * 逻辑删除 1删除；0正常
     */
    private Integer delFlag;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 外部编码
     */
    private String outCode;

    /**
     * 来源 1页面新增；2接口同步
     */
    private Integer source;

    /**
     * 结算方法 1账期结算；2预付款；3其他；4现采现结
     */
    private Integer settlementMethod;

    /**
     * 代发订单推送时间,英文逗号分割,0030,0050
     */
    private String directOrderPushTime;

    /**
     * 是否订单控制：0-否，1-是
     */
    private Integer orderCtrl;

    /**
     * 是否合并直流订单：0-否，1-是
     */
    private Integer mergeDc;

    /**
     * 是否启用电子签：0-否，1-是
     */
    private Integer esign;

    /**
     * 是否启用数电票：0-否，1-是
     */
    private Integer einv;

    /**
     * 是否启用订单供应商确认：0-否，1-是
     */
    private Integer suppConf;

    /**
     * 企业性质 1 代理商；2生产商；3批发商；4其他
     */
    private Integer entType;

    /**
     * 商家性质 1 个体工商户；2 公司
     */
    private Integer suppBussinesType;

    /**
     * 法人代表
     */
    private String legalPersonName;

    /**
     * 法人证件类型：1居民身份证
     */
    private Integer legalCertType;

    /**
     * 法人证件号码
     */
    private String legalCertNumber;

    /**
     * 法人手机号
     */
    private String legalPhone;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 商家类别（数据字典，租户可自定义枚举）
     */
    private String merchantCategory;

    /**
     * 备注
     */
    private String remark;

    /**
     * 统一信用代码
     */
    private String creditCode;

    /**
     * 发票类型：1-专票，2-普票，3-收据
     */
    private Integer invoiceCategory;

    /**
     * 注册地址
     */
    private String invoiceAddress;

    /**
     * 收件人地址
     */
    private String recipientAddress;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人电话
     */
    private String recipientPhone;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 暂停经营时间
     */
    private LocalDateTime suspendBusinessTime;

    /**
     * 清场时间
     */
    private LocalDateTime clearanceTime;

    /**
     * 开票电话
     */
    private String invoicePhone;

    /**
     * 主营类目（品类编码）
     */
    private String categoryCode;

    /**
     * 主营类目（品类名称）
     */
    private String categoryName;

    /**
     * 主营类目（品类项编码）
     */
    private String categoryItemCode;

    /**
     * 开票银行：银行网点
     */
    private String invoiceBankName;

    /**
     * 开票银行账号
     */
    private String invoiceBankAccount;
}
