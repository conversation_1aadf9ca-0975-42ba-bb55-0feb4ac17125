package com.meta.supplychain.entity.dto.pms.req.purch;

import com.meta.supplychain.entity.dto.OpInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购计划单查询请求
 */
@Data
@Schema(description = "采购计划单查询请求")
public class SavePurchPricingReq {

    private OpInfo operatorInfo = new OpInfo();

    @Schema(description = "商品信息")
    @NotEmpty(message = "商品信息不能为空")
    private List<SavePurchPricingDetailReq> skuList;

    @Data
    @Schema(description = "商品信息不能为空")
    public static class SavePurchPricingDetailReq {
        @Schema(description = "采购订单号")
        @NotBlank(message = "采购订单号不能为空")
        private String billNo;

        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        private String skuCode;

        @Schema(description = "单内序号")
        @NotEmpty(message = "单内序号不能为空")
        private Long insideId;

        @Schema(description = "采购价格-调整前")
        private BigDecimal oldPurchPrice;

        @Schema(description = "采购价格-调整后")
        private BigDecimal newPurchPrice;

        @Schema(description = "采购金额-调整前")
        private BigDecimal oldPurchMoney;

        @Schema(description = "采购金额-调整后")
        private BigDecimal newPurchMoney;

        @Schema(description = "采购税金-调整前")
        private BigDecimal oldPurchTax;

        @Schema(description = "采购税金-调整后")
        private BigDecimal newPurchTax;

        @Schema(description = "备注")
        private String remark;
    }

}
