package com.meta.supplychain.entity.dto.wds.resp;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-4-7 10:10:49
 */
@Schema(description = "退货收货主表")
@Getter
@Setter
@ToString
public class QueryWdRefundAcceptBatchDetailResp {
    @Schema(description = "租户号")
    private Long tenantId;
    /** 单内序号 */
    @Schema(description = "单内序号")
    private Long insideId;

    /** 储位编码 */
    @Schema(description = "储位编码")
    private String locationCode;

    /** 储位名称 */
    @Schema(description = "储位名称")
    private String locationName;

    /** 商品类型 0商品1附赠商品2附赠赠品 */
    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "是否效期 0否1是")
    private Integer periodFlag;

    @Schema(description = "计量属性（0-普通，2-称重）")
    private Integer uomAttr;
    /** 品牌编码 */
    @Schema(description = "品牌编码")
    private String brandCode;

    /** 品牌名称 */
    @Schema(description = "品牌名称")
    private String brandName;

    /** 品类编码 */
    @Schema(description = "品类编码")
    private String categoryCode;

    /** 品类名称 */
    @Schema(description = "品类名称")
    private String categoryName;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String skuCode;

    /** 商品名称 */
    @Schema(description = "商品名称")
    private String skuName;

    /** 条码;SKU基本条码 */
    @Schema(description = "条码;SKU基本条码")
    private String barcode;

    /** 商品货号 */
    @Schema(description = "商品货号")
    private String goodsNo;

    /** 单位比率，包装率 */
    @Schema(description = "单位比率，包装率")
    private BigDecimal unitRate;

    /** 规格型号 */
    @Schema(description = "规格型号")
    private String skuModel;

    /** 单位 */
    @Schema(description = "单位")
    private String basicUnit;

    /** 整件单位 */
    @Schema(description = "整件单位")
    private String packageUnit;


    /** 整件包装率 */
    @Schema(description = "整件包装率")
    private BigDecimal packageRate;

    /** 订货包装率 */
    @Schema(description = "订货包装率")
    private BigDecimal orderUnitRate;

    /** 配送价 */
    @Schema(description = "配送价")
    private BigDecimal acceptTaxPrice;

    /** 数量 */
    @Schema(description = "数量")
    private BigDecimal acceptQty;

    /** 退货金额 */
    @Schema(description = "退货金额")
    private BigDecimal acceptTaxMoney;

    /** 退货税金 */
    @Schema(description = "退货税金")
    private BigDecimal acceptTax;

    /** 订单数量 */
    @Schema(description = "订单数量")
    private BigDecimal orderQty;

    /** 进项税率 */
    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    /** 销项税率 */
    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    /** 商品批号 */
    @Schema(description = "商品批号")
    private String periodBatchNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    private LocalDate productDate;

    /** 过期日期 */
    @Schema(description = "过期日期")
    private LocalDate expireDate;

    /** 批次号 */
    @Schema(description = "批次号")
    private String batchNo;

    /** 明细备注 */
    @Schema(description = "明细备注")
    private String remark;

    /** 箱数 */
    @Schema(description = "箱数")
    private BigDecimal wholeQty;

    /** 零头数量 */
    @Schema(description = "零头数量")
    private BigDecimal oddQty;

    /** 来源单据类型 */
    @Schema(description = "来源单据类型")
    private String srcBillType;

    /** 来源单据号 */
    @Schema(description = "来源单据号")
    private String srcBillNo;

    /** 来源单内序号 */
    @Schema(description = "来源单内序号")
    private Long srcInsideId;

    @Schema(description = "储位库存")
    private BigDecimal locationQty;

    /** 效期条码 */
    @Schema(description = "效期条码")
    private String periodBarcode;

    @Schema(description = "创建人编码")
    private String createCode;
    @Schema(description = "创建人名称")
    private String createName;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;



}
