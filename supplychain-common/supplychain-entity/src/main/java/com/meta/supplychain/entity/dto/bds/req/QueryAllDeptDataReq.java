package com.meta.supplychain.entity.dto.bds.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 批量查询店组群下所有门店信息
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryAllDeptDataReq {
    private Set<Long> groupIdList;
    private Set<String> groupCodeList;
    private String classCode;
    private Boolean deepSearch;
    private Integer current;
    private Integer pageSize;
}
