package com.meta.supplychain.entity.dto.pms.req.purch;

import com.meta.supplychain.entity.dto.OpInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 批量采购计划单操作请求
 */
@Data
@Schema(description = "采购计划单操作请求")
public class PurchasePlanMakeReq {
    private OpInfo operatorInfo = new OpInfo();

    @Schema(description = "采购计划单号")
    @NotEmpty(message = "采购单号不能为空")
    private String billNo;

    @Schema(description = "操作备注")
    private String remark;
}
