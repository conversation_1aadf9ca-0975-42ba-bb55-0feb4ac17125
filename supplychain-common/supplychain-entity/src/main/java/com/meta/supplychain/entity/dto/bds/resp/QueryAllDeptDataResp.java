package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Data;

import java.util.List;

@Data
public class QueryAllDeptDataResp {
    private List<DeptInfo> rows;
    private long total;

    @Data
    public static class DeptInfo {
        private String classCode;
        private String createCode;
        private String createName;
        private String createTime;
        private long createUid;
        private long delFlag;
        private String deptCode;
        private long deptId;
        private String deptName;
        private long deptStatus;
        private long deptType;
        private String groupCode;
        private Object groupFullName;
        private long groupId;
        private String groupName;
        private long id;
        private Object superiorsLink;
        private long tenantId;
        private String updateCode;
        private String updateName;
        private String updateTime;
        private Long updateUid;
    }
}
