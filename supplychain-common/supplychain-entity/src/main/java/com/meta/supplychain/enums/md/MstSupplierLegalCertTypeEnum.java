package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商法人证件类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商法人证件类型", code = "mstSupplierLegalCertTypeEnum")
public enum MstSupplierLegalCertTypeEnum implements VerifiableEnum<Integer> {
    ID_CARD(1, "居民身份证");

    private final Integer code;
    private final String desc;
}
