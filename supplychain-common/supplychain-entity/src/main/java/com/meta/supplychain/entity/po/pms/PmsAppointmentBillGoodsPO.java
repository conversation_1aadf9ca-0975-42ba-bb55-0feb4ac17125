package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.*;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 供应商预约单关联商品明细表
 * <AUTHOR>
 * @date 2025-4-20 20:50:57
 */
@TableName(value ="pms_appointment_bill_goods")
@Getter
@Setter
@ToString
public class PmsAppointmentBillGoodsPO extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 预约单据号
     */
    private String appointmentBillNo;

    /**
     * 采购订单号
     */
    private String purchBillNo;

    /**
     * 采购订单商品明细行id
     */
    private Long purchGoodsDetailId;

    /**
     * 采购订单商品明细行行号
     */
    private Long purchGoodsDetailInsideId;

    /**
     * 商品类型	0商品1附赠商品2附赠赠品
     */
    private Integer skuType;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 商品货号
     */
    private String goodsNo;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 单位
     */
    private String basicUnit;

    /**
     * 整件单位
     */
    private String packageUnit;

    /**
     * 规格
     */
    private String skuModel;

    /**
     * 计量属性	0：普通 1：计量 2：称重
     */
    private Integer uomAttr;

    /**
     * 进项税率
     */
    private BigDecimal inputTaxRate;

    /**
     * 商品包装率
     */
    private BigDecimal unitRate;

    /**
     * 订货包装率
     */
    private BigDecimal purchUnitRate;

    /**
     * 整件数量
     */
    private BigDecimal wholeQty;

    /**
     * 零头数量
     */
    private BigDecimal oddQty;

    /**
     * 采购数量
     */
    private BigDecimal purchQty;

    /**
     * 可约整件数量
     */
    private BigDecimal canAppointmentWholeQty;

    /**
     * 可约零头数量
     */
    private BigDecimal canAppointmentOddQty;

    /**
     * 可约数量
     */
    private BigDecimal canAppointmentQty;

    /**
     * 已经预约数量(快照时)
     */
    private BigDecimal appointedQty;

    /**
     * 本次预约件数
     */
    private BigDecimal appointmentWholeQty;

    /**
     * 本次预约零头数量
     */
    private BigDecimal appointmentOddQty;

    /**
     * 本次预约数量
     */
    private BigDecimal appointmentQty;

    /** 逻辑删除状态 1:删除 0:正常 */
    @TableLogic
    private Integer delFlag;

    private Long tenantId;
} 