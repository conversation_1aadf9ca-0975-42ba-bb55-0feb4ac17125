package com.meta.supplychain.entity.dto.goods.req;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:39
 */
@Data
@Builder
public class GoodsCategoryQueryReq {

    /**
     * 分类项编码
     */
    private String categoryClassCode;

    /**
     * 分类编码
     */
    private List<String> categoryCodes;

    private UserInfo userInfo;

    @Data
    public static class UserInfo {

        /**
         * 是否做权限控制（默认false）
         */
        private Boolean authFlag;


        private Boolean allFlag;

        /**
         * 用户id
         */
        private String userId;

        /**
         *
         */
        private String empCode;

    }
}
