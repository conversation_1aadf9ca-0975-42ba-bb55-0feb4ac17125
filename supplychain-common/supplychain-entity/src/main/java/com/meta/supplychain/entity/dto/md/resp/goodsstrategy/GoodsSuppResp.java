package com.meta.supplychain.entity.dto.md.resp.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 合同商品供应商信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSuppResp {

    @Schema(description = "商品编码")
    String skuCode;

    @Schema(description = "合同供应商列表")
    List<GoodsSuppDetailResp> supplierList;

}
