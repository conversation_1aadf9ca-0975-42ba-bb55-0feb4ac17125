package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "费用计算基准", code = "mdCalculationBaseEnum")
public enum MdCalculationBaseEnum implements VerifiableEnum<Integer> {
    FIXED_COST(1, "固定费用"),
    BY_SALE_AMOUNT(2, "按销售额计费"),
    BY_PURCHASE_AMOUNT(3, "按总采购额计费"),
    BY_RETURN_TO_STORE_AMOUNT(4, "按门店退额计费"),
    BY_TOTAL_DISTRIBUTION_AMOUNT(5, "按总配送额计费"),
    BY_POS_PAYMENT(6, "按POS支付计费"),
    BY_SETTLEMENT_AMOUNT(7, "按结款额计费"),
    BY_TAX_SALES_COST(8, "按含税销售成本计费"),
    BY_RETURN_FACTORY_AMOUNT(9, "按退厂额计费"),
    BY_PURCHASE_AMOUNT_NO_RETURN(10, "按总采购额计费（不含退货）"),
    BY_DC_PURCHASE_NO_RETURN(11, "按配送中心采购额计费（不含退货）"),
    BY_DC_PURCHASE_NO_DIRECT(12, "按配送中心采购额计费（不含直流）"),
    BY_DC_PURCHASE_ONLY_DIRECT(13, "按配送中心采购额计费（只含直流）"),
    BY_NEW_STORE_COUNT(14, "按新开店数计费"),
    BY_SALES_QUANTITY(15, "按销售数量计费"),
    BY_PURCHASE_QUANTITY(16, "按采购数量计费");

    private final Integer code;
    private final String desc;
}
