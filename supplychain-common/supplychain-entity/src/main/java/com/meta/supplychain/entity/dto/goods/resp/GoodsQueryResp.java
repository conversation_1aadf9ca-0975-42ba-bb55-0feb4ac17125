package com.meta.supplychain.entity.dto.goods.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:04
 */
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GoodsQueryResp {
    /**
     * 商品信息
     */
    @Schema(description = "商品信息")
    private GoodsInfo goodsInfo;
    /**
     * 关联商品（结构同商品信息）
     */
    @Schema(description = "关联商品（结构同商品信息）")
    private GoodsInfo relationGoodsInfo;

    /**
     * 子品（结构同商品信息）
     */
    @Schema(description = "子品（结构同商品信息）")
    private GoodsInfo childGoodsInfo;

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodsInfo {
        /**
         * SPU名称
         */
        @Schema(description = "SPU名称")
        private String spuName;
        /**
         * SPU货号
         */
        @Schema(description = "SPU货号")
        private String spuGoodsNo;
        /**
         * 是否SPU
         */
        @Schema(description = "是否SPU")
        private Boolean spuFlag;
        /**
         * SPU编码
         */
        @Schema(description = "SPU编码")
        private String spuCode;
        /**
         * SPU条码
         */
        @Schema(description = "SPU条码")
        private String spuBarcode;
        /**
         * SKU名称
         */
        @Schema(description = "SKU名称")
        private String skuName;
        /**
         * SKU货号
         */
        @Schema(description = "SKU货号")
        private String skuGoodsNo;
        /**
         * SKU编码
         */
        @Schema(description = "SKU编码")
        private String skuCode;
        /**
         * SKU条码
         */
        @Schema(description = "SKU条码")
        private String skuBarcode;
        /**
         * 整件包装率
         */
        @Schema(description = "整件包装率")
        private Integer packageNum;
        /**
         * 是否多包装商品
         */
        @Schema(description = "是否多包装商品")
        private Boolean packageFlag;
        /**
         * 经营方式(1:自营 2:联营 7:联营管库存 8:租赁 9:生鲜A进A出 10:生鲜A进B出 12:生鲜归集码)
         */
        @Schema(description = "经营方式(1:自营 2:联营 7:联营管库存 8:租赁 9:生鲜A进A出 10:生鲜A进B出 12:生鲜归集码)")
        private Integer operationModel;
        /**
         * 规格信息（5L等）
         */
        @Schema(description = "规格信息（5L等）")
        private String model;
        /**
         * 单位名称
         */
        @Schema(description = "单位名称")
        private String unitName;
        /**
         * 单位
         */
        @Schema(description = "单位")
        private String unit;

        /**
         * 是否在目录（0：否 1：是）
         */
        @Schema(description = "是否在目录（0：否 1：是）")
        private Integer inCatalog;

        /**
         * 计量属性（0：普通 1：计量 2：称重）
         */
        @Schema(description = "计量属性（0：普通 1：计量 2：称重）")
        private Integer mesureProperty;
        /**
         * 商品类型（1：单规格 2：多规格 3：组合商品）
         */
        @Schema(description = "商品类型（1：单规格 2：多规格 3：组合商品）")
        private Integer goodsType;
        /**
         * 组合类型（1：普通 2：多包装 3：散称标准份 4：散称主子码）
         */
        @Schema(description = "组合类型（1：普通 2：多包装 3：散称标准份 4：散称主子码）")
        private Integer combType;

        /**
         * 经营状态编码
         */
        @Schema(description = "经营状态编码")
        private String workStateCode;
        /**
         * 流转途径编码
         */
        @Schema(description = "流转途径编码")
        private String circulationModeCode;

        /**
         * 经营状态名称
         */
        @Schema(description = "经营状态名称")
        private String workStateName;
        /**
         * 流转途径名称
         */
        @Schema(description = "流转途径名称")
        private String circulationModeName;

        /**
         * 是否启用效期（0：否 1：是）
         */
        @Schema(description = "是否启用效期（0：否 1：是）")
        private Integer expiryDateControlFlag;
        /**
         * 收货提前期
         */
        @Schema(description = "收货提前期")
        private Integer recAdvDays;
        /**
         * 销售提前期
         */
        @Schema(description = "销售提前期")
        private Integer salePreDays;
        /**
         * 预警提前期
         */
        @Schema(description = "预警提前期")
        private Integer warnDays;

        /**
         * 生产日期规则（0：可为空 1：必填 2：不可填）
         */
        @Schema(description = "生产日期规则（0：可为空 1：必填 2：不可填）")
        private Integer productDateRule;
        /**
         * /到期日期规则（0：可为空 1：必填 2：不可填）
         */
        @Schema(description = "到期日期规则（0：可为空 1：必填 2：不可填）")
        private Integer expirateDateRule;
        /**
         * 效期条码规则（0：可为空 1：必填 2：不可填）
         */
        @Schema(description = "效期条码规则（0：可为空 1：必填 2：不可填）")
        private Integer validityCodeRule;

        /**
         * 毛重（0.1 毫克）
         */
        @Schema(description = "毛重（0.1 毫克）")
        private Long weight;


        private String specValTwoName;
        private String specValTwoCode;
        private String specValOneName;
        private String specValOneCode;
        private String specTwoName;
        private String specTwoCode;
        private String specOneName;
        private String specOneCode;
        /**
         * 参考进价（毫）
         */
        @Schema(description = "参考进价（毫）")
        private Long purchasePrice;

        /**
         * 零售价（毫）
         */
        @Schema(description = "零售价（毫）")
        private Long referPrice;

        /**
         * 进项税率
         */
        @Schema(description = "进项税率")
        private Integer inputTaxRate;
        /**
         * 品牌编码
         */
        @Schema(description = "品牌编码")
        private String brandCode;
        /**
         * 分类编码
         */
        @Schema(description = "分类编码")
        private String categoryCode;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称")
        private String categoryName;

        /**
         * 品牌名称
         */
        @Schema(description = "品牌名称")
        private String brandName;

        /**
         * 销项税率
         */
        @Schema(description = "销项税率")
        private String outputTaxRate;


        /**
         * 配送价（毫）
         */
        @Schema(description = "配送价（毫）")
        private Long dispatchPrice;

        /**
         * 保质期（天）
         */
        @Schema(description = "保质期（天）")
        private Integer shelfLifeDays;

        /**
         * 是否类别码（0：否 1：是）
         */
        @Schema(description = "是否类别码（0：否 1：是）")
        private Integer categoryCodeFlag;

        @Schema(description = "是否直流  1是 0否")
        private Integer directFlag;

        @Schema(description = "直流供应商编码")
        private String directSupplierCode;
    }


}
