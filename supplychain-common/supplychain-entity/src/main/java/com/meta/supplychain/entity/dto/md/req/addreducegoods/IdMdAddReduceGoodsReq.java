package com.meta.supplychain.entity.dto.md.req.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 14:50
 **/
@Schema(description = "追加追减商品设置")
@Data
public class IdMdAddReduceGoodsReq {

    @Schema(description = "商品设置id")
    @NotNull(message = "商品设置id不能为空")
    private Long id;

}
