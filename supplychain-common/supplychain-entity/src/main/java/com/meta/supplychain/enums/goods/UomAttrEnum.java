package com.meta.supplychain.enums.goods;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR> cat
 * @date 2024/5/16 15:23
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "计量属性状态码枚举", code = "uomAttrEnum")
public enum UomAttrEnum implements VerifiableEnum<Integer> {

    ORDINARY(0, "普通"),
    MEASUREMENT(1, "计量"),
    WEIGHT_GENERATION(2, "称重");
    ;
    @EnumValue
    private final Integer code;
    private final String desc;

    @JsonValue
    public Integer jsonValue() {
        return code;
    }

    public static String ofCodeToDesc(Integer code) {
        for (UomAttrEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }

}
