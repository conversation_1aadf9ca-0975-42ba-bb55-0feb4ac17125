package com.meta.supplychain.entity.dto.wds.resp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 差异处理单结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "差异处理单结果")
public class WdShipAccDiffBillResp {
    /** 单据号 */
    @Schema(description = "单据号")
    private String billNo;

    /** 配送单号 */
    @Schema(description = "配送单号")
    private String shipBillNo;
    /** 仓库编码 */
    @Schema(description = "仓库编码")
    private String whCode;
    /** 仓库名称 */
    @Schema(description = "仓库名称")
    private String whName;
    /**
     * 配送时间
     */
    @Schema(description = "配送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime shipTime;
    /** 验收单号 */
    @Schema(description = "验收单号")
    private String acceptBillNo;
    /** 部门编码 */
    @Schema(description = "部门编码")
    private String deptCode;
    /** 部门名称 */
    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "门店收货人编码")
    private String createCode;

    @Schema(description = "门店收货人名称")
    private String createName;

    @Schema(description = "收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


    /** 合计差异数量 */
    @Schema(description = "合计差异数量")
    private BigDecimal totalDiffQty;
    /** 合计差异金额(含税) */
    @Schema(description = "合计差异金额(含税)")
    private BigDecimal totalDiffTaxMoney;
    /** 合计差异税金 */
    @Schema(description = "合计差异税金")
    private BigDecimal totalDiffTax;

    /** 差异单状态枚举 1 待处理、2 已审核、3 已驳回 WDAcceptDiffStatusEnum */
    @Schema(description = "差异单状态枚举 1 待处理、2 已审核、3 已驳回 WDAcceptDiffStatusEnum ")
    private Integer status;
    /** 审核时间 */
    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approveTime;

    /** 审核人编码 */
    @Schema(description = "审核人编码")
    private String approveManCode;
    /** 审核人名称 */
    @Schema(description = "审核人名称")
    private String approveManName;
    /** 收货备注 */
    @Schema(description = "收货备注")
    private String accRemark;
    /** 审核备注 */
    @Schema(description = "审核备注")
    private String remark;



    // 下方为需求上 列表不需要的字段



    /** 仓库核算单位 */
    @Schema(description = "仓库核算单位")
    private String whAccCode;

    /** 入货部门核算单位 */
    @Schema(description = "入货部门核算单位")
    private String inAccCode;

    /** 单据类型 */
    @Schema(description = "单据类型")
    private Integer billType;



    /** 新的逆向配送发货单号 */
    @Schema(description = "新的逆向配送发货单号")
    private String refundShipBillNo;

    /** 出库核算日期 */
    @Schema(description = "出库核算日期")
    private LocalDate outAccDate;
    /** 入库核算日期 */
    @Schema(description = "入库核算日期")
    private LocalDate inAccDate;
    /** 单据方向 -1退货 1正向 */
    @Schema(description = "单据方向 -1退货 1正向")
    private Integer billDirection;


    /** 作废时间 */
    @Schema(description = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;
    /** 作废人编码 */
    @Schema(description = "作废人编码")
    private String cancelManCode;
    /** 作废人名称 */
    @Schema(description = "作废人名称")
    private String cancelManName;




}