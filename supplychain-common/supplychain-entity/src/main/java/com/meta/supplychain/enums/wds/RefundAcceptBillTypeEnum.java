package com.meta.supplychain.enums.wds;

import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退货收货单据类型枚举  0 配送  1 批销 RefundAcceptBillTypeEnum
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "退货收货单据类型枚举",code = "RefundAcceptBillTypeEnum")
public enum RefundAcceptBillTypeEnum {
    // 配送
    DELIVERY(0, "配送"),
    // 批销
    BATCH_SALE(1, "批销"),

    ;
    private final Integer code;
    private final String desc;
}
