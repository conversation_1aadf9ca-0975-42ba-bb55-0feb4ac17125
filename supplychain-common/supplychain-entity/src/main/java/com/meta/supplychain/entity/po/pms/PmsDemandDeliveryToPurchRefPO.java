package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@TableName(value ="pms_demand_delivery_to_purch_ref")
@Getter
@Setter
@ToString
public class PmsDemandDeliveryToPurchRefPO extends BaseEntity {
    /** ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 需求单号 */
    private String billNo;

    /** 需求配转采商品表单内序号,pms_demand_delivery_to_purch.inside_id */
    private Long deliveryToPurchInsideId;

    /** 需求商品部门表单内序号,pms_demand_dept_goods_detail.inside_id */
    private Long deptGoodsInsideId;

    /** 配送出货方,pms_demand_delivery_shipper.inside_id */
    private Long deliveryShipperInsideId;

    @Schema(description = "转采类型,0不可转采, 1可转采")
    private Integer type;
}