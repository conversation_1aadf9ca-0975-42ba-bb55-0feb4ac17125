package com.meta.supplychain.entity.dto.pms.resp.addReduce;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PmsOrderAllocateResp extends PmsAddReduceDetailResp {

    List<PmsOrderAllocateOrderInfo> orderAllocateOrderInfoList;

}
