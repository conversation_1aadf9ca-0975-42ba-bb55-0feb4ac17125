package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeCreateReq;
import com.meta.supplychain.entity.dto.md.resp.demandstrategyexclude.MdDemandStrategyExcludeResponseDTO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyExcludePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 需求策略自动需求单排除范围转换器
 * <AUTHOR>
 */
@Mapper
public interface MdDemandStrategyExcludeConvert {

    MdDemandStrategyExcludeConvert INSTANCE = Mappers.getMapper(MdDemandStrategyExcludeConvert.class);

    /**
     * PO转DTO
     */
    MdDemandStrategyExcludeResponseDTO po2dto(MdDemandStrategyExcludePO po);

    MdDemandStrategyExcludePO createDto2po(MdDemandStrategyExcludeCreateReq createReq);
} 