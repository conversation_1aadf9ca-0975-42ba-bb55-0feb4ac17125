package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 代配过账单主表
 * @TableName pms_account_bill
 */
@TableName(value ="pms_account_bill")
@Data
public class PmsAccountBillPO extends BaseEntity implements Serializable {
    /**
     * 主键序号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 租户号 */
    private Long tenantId;

    /**
     * 过账单号
     */
    private String billNo;

    /**
     * 来源单号
     */
    private String srcBillNo;

    /**
     * 配送中心编码
     */
    private String dcCode;

    /**
     * 配送中心名称
     */
    private String dcName;

    /**
     * 配送中心核算单位
     */
    private String dcAccCode;

    /**
     * 代配部门编码
     */
    private String accDeptCode;

    /**
     * 代配部门名称
     */
    private String accDeptName;

    /**
     * 过账类型	0-代配验收，1-代配调拨，2-代配退补
     */
    private Integer billType;

    /**
     * 单据方向 0-采购验收 1-采购验收冲红 2-采购退货 3-采购退货冲红 4-门店调拨，5-门店调拨冲红，6-退补，7-退补冲红
     */
    private Integer billDirection;

    /**
     * 调拨类型
     * 0-拨入部门和过账配送中心核算单位不一致
     * 1-拨出部门和过账配送中心核算单位不一致
     * 2-拨出拨入部门都和过账配送中心核算单位不一致
     */
    private Integer redeployType;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 补签序号
     */
    private String repairSignId;

    /**
     *拨入部门编码
     */
    private String inDeptCode;

    /**
     * 拨入部门名称
     */
    private String inDeptName;

    /**
     * 拨入部门核算单位
     */
    private String inAccCode;

    /**
     *拨出部门编码
     */
    private String outDeptCode;

    /**
     * 拨出部门名称
     */
    private String outDeptName;

    /**
     * 拨出部门核算单位
     */
    private String outAccCode;

    /**
     * 过账日期
     */
    private LocalDate accDate;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 