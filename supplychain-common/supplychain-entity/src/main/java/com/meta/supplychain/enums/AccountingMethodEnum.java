package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 核算方式
 *      * [{"label": "移动加权成本", "value": "0"},
 *      * {"label": "先进先出批次成本", "value": "1"},
 *      * {"label": "进价成本", "value": "2"}]
 * <AUTHOR> cat
 * @date 2024/8/1 17:09
 */
@Getter
@AllArgsConstructor
public enum AccountingMethodEnum {

    JIA_QUAN(0,"移动加权成本"),
    BATCH(1,"先进先出批次成本"),
    PURCHASE_COST(2,"进价成本"),
    FILES_PURCHASE(3,"档案进价")
    ;

    private Integer code;

    private String desc;

    public static final List<String> BATCH_OPEN_VALUE = Arrays.asList(String.valueOf(JIA_QUAN.getCode()), String.valueOf(BATCH.getCode()));

    /**
     * 是否启用批次
     * @return true or false
     */
    public static boolean useBatch(String value) {
        return AccountingMethodEnum.BATCH_OPEN_VALUE.contains(value);
    }

}
