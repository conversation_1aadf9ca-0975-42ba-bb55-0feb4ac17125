package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "自动生成需求单订货申请行失败处理规则", code = "mdOrderApplyFailRuleEnum")
public enum MdOrderApplyFailRuleEnum implements VerifiableEnum<Integer> {
    UPDATE_TO_CANCELED(1, "订货申请行更新为已作废状态"),
    UPDATE_TO_WAITING(2, "订货申请行更新为待提单状态");

    private final Integer code;
    private final String desc;
} 