package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentStrategyDTO;
import com.meta.supplychain.entity.po.md.MdDeliveryAppointmentStrategyPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商预约策略对象转换
 * <AUTHOR>
 */
@Mapper
public interface MdDeliveryAppointmentStrategyConvert {

    MdDeliveryAppointmentStrategyConvert INSTANCE = Mappers.getMapper(MdDeliveryAppointmentStrategyConvert.class);

    /**
     * PO转DTO
     * @param po PO对象
     * @return DTO对象
     */
    MdDeliveryAppointmentStrategyDTO po2dto(MdDeliveryAppointmentStrategyPO po);

    /**
     * PO列表转DTO列表
     * @param poList PO列表
     * @return DTO列表
     */
    List<MdDeliveryAppointmentStrategyDTO> po2dtoList(List<MdDeliveryAppointmentStrategyPO> poList);
    
    /**
     * DTO转PO
     * @param dto DTO对象
     * @return PO对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryAppointmentStrategyPO dto2po(MdDeliveryAppointmentStrategyDTO dto);
} 