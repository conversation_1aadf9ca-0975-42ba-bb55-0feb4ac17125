package com.meta.supplychain.entity.dto.md.req.demandstrategyexclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 需求策略自动需求单排除范围批量删除请求
 * <AUTHOR>
 */
@Data
public class MdDemandStrategyExcludeBatchDeleteReq {

    @Schema(description = "需求策略排除范围ID列表", required = true)
    @NotEmpty(message = "需求策略排除范围ID列表不能为空")
    private List<Long> ids;
}
