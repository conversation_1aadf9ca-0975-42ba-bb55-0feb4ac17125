package com.meta.supplychain.entity.dto.pms.resp.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询申请单对应订单打印信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询申请单对应订单打印信息")
public class QueryOrder4PrintResp {

    /**
     * 部门编码
     */
    @Schema(description = "订单id")
    private String orderId;

    /**
     * 申请类别 0-采购，1-配送
     */
    @Schema(description = "出货方式 0 采购 1 配送", required = true)
    private Integer shippingWay;

    /**
     * 出货方信息
     */
    @Schema(description = "出货方信息", required = true)
    public List<ShipperInfo> shipperInfos;

}
