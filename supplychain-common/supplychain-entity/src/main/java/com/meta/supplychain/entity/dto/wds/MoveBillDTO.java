package com.meta.supplychain.entity.dto.wds;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MoveBillDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * 仓库名称
     */
    private String whName;

    /**
     * 单据号
     */
    private String billNo;

    /**
     * 单据备注
     */
    private String remark;

    /**
     * 移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移
     */
    private Integer moveType;

    /**
     * 来源单号[收货退货]
     */
    private String srcBillNo;

    /**
     * 合计转移数量
     */
    private BigDecimal totalMoveQty;

    /**
     * 单据状态 -1处理中 1待审核 2已审核 9作废
     */
    private Integer status;

    /**
     * 移出储位编码
     */
    private String outLocationCode;

    /**
     * 移出储位名称
     */
    private String outLocationName;

    /**
     * 移入储位编码
     */
    private String inLocationCode;

    /**
     * 移入储位名称
     */
    private String inLocationName;

    /**
     * 作废时间
     */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime cancelTime;

    /**
     * 作废人编码
     */
    private String cancelManCode;

    /**
     * 作废人名称
     */
    private String cancelManName;

    /**
     * 确认时间
     */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime confirmTime;

    /**
     * 确认人编码
     */
    private String confirmManCode;

    /**
     * 确认人名称
     */
    private String confirmManName;


    /**
     * 移位明细
     */
    private List<MoveLocBatchDetailDTO> moveLocDtlList;

    @Schema(description = "创建人编码")
    private String createCode;
    @Schema(description = "创建人名称")
    private String createName;
    @Schema(description = "制单时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @Schema(description = "创建人ssoId")
    private String createUid;
    @Schema(description = "更新人ssoId")
    private String updateUid;
    @Schema(description = "更新人编码")
    private String updateCode;
    @Schema(description = "更新人名称")
    private String updateName;
    @Schema(description = "更新时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime updateTime;


}
