package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 供应商分类
 * @TableName mst_supplier_category
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_category")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierCategoryPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分类编码
     */
    private String cateCode;

    /**
     * 分类名称
     */
    private String cateName;

    /**
     * 系统1 自定义2
     */
    private Integer type;

    /**
     * 上级分类编码
     */
    private String parentCode;

    /**
     * 启用状态 1启用 0停用
     */
    private Integer status;

    /**
     * 是否末级节点 0否 1是
     */
    private Integer finalFlag;

    /**
     * 启用状态 0正常 1删除
     */
    private Integer delFlag;

    /**
     * 租户号
     */
    private Long tenantId;
}
