package com.meta.supplychain.entity.dto.pms.req.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Getter
@Setter
@ToString
public class PmsDemandDeliveryToPurchRefReq {

    /** 需求单号 */
    private String billNo;

    @Schema(description = "配转采商品单内序号")
    private Long deliveryToPurchInsideId;

    @Schema(description = "商品部门单内序号")
    private Long deptGoodsInsideId;

    @Schema(description = "配送出货方单内序号")
    private Long deliveryShipperInsideId;

    @Schema(description = "转采类型,0不可转采, 1可转采")
    private Integer type;
}