package com.meta.supplychain.entity.dto.pms.resp.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订货申请请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PmsPurchasePlanBillResp {
    @Schema(description = "主键序号")
    private Long id;

    @Schema(description = "采购计划单号")
    private String billNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "供应商合同号")
    private String contractNo;

    @Schema(description = "商品品项数")
    private Integer totalSkuCount;

    @Schema(description = "采购数量")
    private BigDecimal totalQty;

    @Schema(description = "采购金额（含税）")
    private BigDecimal totalTaxMoney;

    @Schema(description = "采购金额（含税）")
    private String totalTaxMoneyString;

    @Schema(description = "采购税金")
    private BigDecimal totalTax;

    @Schema(description = "采购税金")
    private String totalTaxString;

    @Schema(description = "有效日期")
    private LocalDate validityDate;

    @Schema(description = "状态 0-草稿，1-待审核，2-已审核，3-使用中，4-已完成，5-已过期，6-已关闭，7-已作废")
    private Integer status;

    @Schema(description = "状态 0-草稿，1-待审核，2-已审核，3-使用中，4-已完成，5-已过期，6-已关闭，7-已作废")
    private String statusDesc;

    @Schema(description = "采购计划备注")
    private String planRemark;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "作废备注")
    private String cancelRemark;

    @Schema(description = "打印次数")
    private Integer printCount;

    @Schema(description = "附件名称与地址,json格式[{'name':'','url':''}]")
    private String attachmentUrl;

    @Schema(description = "审核人工号")
    private String auditCode;

    @Schema(description = "审核人名称")
    private String auditName;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "作废人工号")
    private String cancelManCode;

    @Schema(description = "作废人名称")
    private String cancelManName;

    @Schema(description = "作废时间")
    private LocalDateTime cancelTime;

    @Schema(description = "创建人uid")
    private Long createUid;

    @Schema(description = "创建人工号")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人uid")
    private Long updateUid;

    @Schema(description = "修改人工号")
    private String updateCode;

    @Schema(description = "修改人姓名")
    private String updateName;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "采购计划明细列表")
    private List<PmsPurchasePlanDetailResp> detailList;

    @Schema(description = "采购订单明细列表")
    private List<PurchaseBillDetailResp> orderDetailList;

}
