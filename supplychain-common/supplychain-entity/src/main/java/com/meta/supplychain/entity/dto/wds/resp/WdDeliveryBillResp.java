package com.meta.supplychain.entity.dto.wds.resp;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单")
@Getter
@Setter
@ToString
public class WdDeliveryBillResp {
    /** 租户号 */
    @Schema(description = "租户号")
    private Long tenantId;
    @Schema(description = "配送单据号")
    private String billNo;
    @Schema(description = "配送部门编码")
    private String whCode;
    @Schema(description = "配送部门名称")
    private String whName;
    @Schema(description = "入货部门名称")
    private String inDeptName;
    @Schema(description = "入货部门编码")
    private String inDeptCode;
    @Schema(description = "单据类别 -1退配 1 配送 WDDeliveryOrderDirectionEnum")
    private Integer billDirection;
    @Schema(description = "是否直流[0 是 1 否 ]  YesOrNoEnum")
    private Integer directSign;

    @Schema(description = "直流采购订单号")
    private String directPurchaseBillNo;

    @Schema(description = "配送单据类型 0.仓库配送、1.仓间调拨 WDDeliveryOrderTypeEnum")
    private Integer billType;
    @Schema(description = "商品品项数")
    private Integer totalSkuCount;

    @Schema(description = "订单配送数量")
    private BigDecimal totalQty;
    @Schema(description = "订单配送金额")
    private String totalTaxMoney;
    @Schema(description = "订单配送税金")
    private String totalTax;

    @Schema(description = "订单有效日期")
    private LocalDate validDate;

    @Schema(description = "送货方式 PmsSendModeEnum ")
    private Integer deliveryType;

    /**
     * 送货方式 0-到店，1-到客户
     */
    @Schema(description = "送货方式 0-到店，1-到客户 PmsSendModeEnum")
    private Integer sendMode;

    @Schema(description = "订单送货日期")
    private LocalDate deliveryDate;

    @Schema(description = "客户编码")
    /**
     * 客户编码
     */
    private String customerCode;

    @Schema(description = "客户名称")
    /**
     * 客户名称
     */
    private String customerName;
    @Schema(description = "配送单来源 0 手工 1 需求单 WDDeliveryOrderSourceEnum")
    private Integer billSource;

    @Schema(description = "来源单号")
    private String srcBillNo;
    @Schema(description = "来源单据备注")
    private String srcBillRemark;

    @Schema(description = "退货原因编码")
    private String causeCode;
    @Schema(description = "退货原因名称")
    private String causeName;
    @Schema(description = "创建人ssoId")
    private String createUid;
    @Schema(description = "创建人编码")
    private String createCode;
    @Schema(description = "创建人名称")
    private String createName;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @Schema(description = "备注")
    private String remark;


    @Schema(description = "审核人编码")
    private String approveManCode;
    @Schema(description = "审核人名称")
    private String approveManName;
    @Schema(description = "审核时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime approveTime;







    @Schema(description = "收货联系地址")
    private String contactAddr;
    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "作废人编码")
    private String cancelManCode;
    @Schema(description = "作废人名称")
    private String cancelManName;
    @Schema(description = "作废时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime cancelTime;

    @Schema(description = "作废备注")
    private String cancelRemark;


    @Schema(description = "需求批次")
    private String purchBatchNo;
    @Schema(description = "订货属性名称")
    private String orderAttributeName;
    @Schema(description = "打印次数")
    private Integer printCount;

    @Schema(description = "配送订单单据状态 0草稿 1待审核 2审核 3已分配 4发货中 5已发货 6 已过期 7已作废  WDDeliveryOrderBillStatusEnum")
    private Integer status;

    @Schema(description = "入货方状态 0 未收货 1 已收货 2 未退货 3 已退货 WDDeliveryOrderAcceptSignEnum")
    private Integer acceptSign;

    @Schema(description = "是否可被调用 0可被调 1直流采购未验收 2 直流验收已冲红")
    private Integer canCallModel;
    // 新增相关

    @Schema(description = "收货联系电话")
    private String contactTel;

    @Schema(description = "附件地址")
    private String attachmentUrl;

    @Schema(description = "更新人ssoId")
    private String updateUid;
    @Schema(description = "更新人名称")
    private String updateName;

    @Schema(description = "更新人编码")
    private String updateCode;
    @Schema(description = "收货联系人")
    private String contactMan;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierCode;

    /** 供应商名称 */
    @Schema(description = "供应商名称")
    private String supplierName;
}
