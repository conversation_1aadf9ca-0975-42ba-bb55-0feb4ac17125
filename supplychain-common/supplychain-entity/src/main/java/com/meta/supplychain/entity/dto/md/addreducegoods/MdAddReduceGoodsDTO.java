package com.meta.supplychain.entity.dto.md.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 14:59
 **/
@Schema(description = "追加追减商品设置")
@Data
public class MdAddReduceGoodsDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "是否允许加单(0-否 1-是 默认1)")
    private Integer isAllowMultiBill;

    @Schema(description = "是否允许加单(0-否 1-是 默认1)")
    private String isAllowMultiBillDesc;

    @Schema(description = "是否允许减单(0-否 1-是 默认0)")
    private Integer isAllowReduceBill;

    @Schema(description = "是否允许减单(0-否 1-是 默认0)")
    private String isAllowReduceBillDesc;

    @Schema(description = "部门范围，1不限制部门，2指定部门")
    private Integer deptScope;

    @Schema(description = "部门范围，1不限制部门，2指定部门")
    private String deptScopeDesc;

    @Schema(description = "创建人工号")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人工号")
    private String updateCode;

    @Schema(description = "修改人姓名")
    private String updateName;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "部门信息")
    private List<MdAddReduceGoodsDeptDTO> detailList;
}
