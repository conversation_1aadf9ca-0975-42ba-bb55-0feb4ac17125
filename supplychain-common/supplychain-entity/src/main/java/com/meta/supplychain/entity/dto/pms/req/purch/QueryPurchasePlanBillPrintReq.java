package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 采购计划单详情查询请求
 */
@Data
@Schema(description = "采购计划单打印查询请求")
public class QueryPurchasePlanBillPrintReq {

    @NotEmpty(message = "单据号列表不能为空")
    @Schema(description = "单据号列表", required = true)
    private List<String> billNoList;

}
