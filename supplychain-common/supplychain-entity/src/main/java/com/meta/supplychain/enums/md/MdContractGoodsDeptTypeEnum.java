package com.meta.supplychain.enums.md;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "合同商品部门类型", code = "mdContractGoodsDeptTypeEnum")
public enum MdContractGoodsDeptTypeEnum implements VerifiableEnum<Integer> {

    DEPT_GROUP(1, "店组群"),
    DEPT(2, "部门"),

    ;

    @EnumValue
    private final Integer code;
    private final String desc;

    @JsonValue
    public Integer jsonValue() {
        return code;
    }

    public static String ofCodeToDesc(Integer code) {
        for (MdContractGoodsDeptTypeEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }
}
