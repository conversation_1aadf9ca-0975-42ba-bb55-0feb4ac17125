package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订货申请请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PmsPurchasePlanBillView {

    @ViewField(headerName = "采购计划单号")
    private String billNo;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    @ViewField(headerName = "供应商合同号")
    private String contractNo;

    @ViewField(headerName = "商品品项数")
    private Integer totalSkuCount;

    @ViewField(headerName = "计划单采购数量")
    private BigDecimal totalQty;

    @ViewField(headerName = "计划单采购金额")
    private String totalTaxMoney;

    @ViewField(headerName = "计划单采购税金")
    private String totalTax;

    @ViewField(headerName = "有效日期")
    private String validityDate;

    @ViewField(headerName = "创建人")
    private String creator;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "计划单备注")
    private String planRemark;

    @ViewField(headerName = "审核人")
    private String auditor;

    @ViewField(headerName = "审核时间")
    private String auditTime;

    @ViewField(headerName = "审核备注")
    private String auditRemark;

    @ViewField(headerName = "作废人")
    private String cancelMan;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    @ViewField(headerName = "作废备注")
    private String cancelRemark;

    @ViewField(headerName = "打印次数")
    private Integer printCount;

    @ViewField(headerName = "状态")
    private String statusDesc;

}
