package com.meta.supplychain.entity.dto.pms.req.appointment;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 停靠点时间统计查询对象
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class DockTimeStatsQueryDTO {

    /**
     * 计划到货开始时间
     */
    private LocalDateTime planArrivalTimeStart;

    /**
     * 计划到货结束时间
     */
    private LocalDateTime planArrivalTimeEnd;

    /**
     * 预约单状态
     */
    private Integer status;

    /**
     * 停靠点编码列表
     */
    private List<String> dockCodes;
} 