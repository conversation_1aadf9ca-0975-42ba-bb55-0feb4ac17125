package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/22 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractGoodsDeptCheckResultDTO {

    @Schema(description = "校验是否通过 true 通过 false 不通过")
    private Boolean checkFlag = false;

    @Schema(description = "商品信息 校验失败则不返回")
    private ContractGoodsCheckResultDTO skuDto;

    @Schema(description = "错误原因")
    private String errMsg;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContractGoodsCheckResultDTO {

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品条码")
        private String barcode;

        @Schema(description = "商品货号")
        private String skuNo;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "规格型号;商品的规格;如500ML")
        private String skuModel;

        @Schema(description = "产地")
        private String producingArea;

        @Schema(description = "参考进价")
        private BigDecimal purchPrice;

        @Schema(description = "参考零售价")
        private BigDecimal salePrice;

        @Schema(description = "价格上限")
        private BigDecimal ceilingPrice;

        @Schema(description = "价格下限")
        private BigDecimal floorPrice;

        @Schema(description = "进项税率，13%存13")
        private BigDecimal inputTaxRate;

        @Schema(description = "销项税率，13%存13")
        private BigDecimal outputTaxRate;

    }
}
