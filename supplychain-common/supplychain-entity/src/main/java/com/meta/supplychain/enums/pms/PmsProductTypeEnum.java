package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "商品类型", code = "pmsProductTypeEnum")
public enum PmsProductTypeEnum implements VerifiableEnum<Integer> {

    PRODUCT(0, "商品"),
    GIFT_PRODUCT(1, "附赠商品"),
    FREE_GIFT(2, "附赠赠品");

    private final Integer code;
    private final String desc;
} 