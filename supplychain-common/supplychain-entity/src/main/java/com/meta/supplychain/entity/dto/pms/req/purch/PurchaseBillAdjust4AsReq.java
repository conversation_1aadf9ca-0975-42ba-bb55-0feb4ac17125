package com.meta.supplychain.entity.dto.pms.req.purch;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 追加追减采购订单调整请求
 */
@Schema(description = "追加追减采购订单调整信息")
@Data
@Builder
public class PurchaseBillAdjust4AsReq {
    @Schema(description = "调整记录流水号")
    private String adjustBillNo;

    @Schema(description = "采购订单号")
    @NotBlank(message = "采购订单号不能为空")
    private String billNo;

    @Schema(description = "送货日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate deliverDate;

    @Schema(description = "有效日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate validityDate;

    @Schema(description = "订单调整备注")
    private String adjustRemark;

    @Schema(description = "来源 1-追加追减调整")
    private Integer billSource;

    @Schema(description = "调整商品列表")
    List<GoodsInfo4As> detailList;

    /**
     * 商品数量是否被更改过
     */
    private Boolean isChangeQty =false;

    @Data
    @Builder
    public static class GoodsInfo4As {

        @Schema(description = "单内序号")
        private Long insideId;

        @Schema(description = "调整数量")
        private BigDecimal adjustQty;

    }
}
