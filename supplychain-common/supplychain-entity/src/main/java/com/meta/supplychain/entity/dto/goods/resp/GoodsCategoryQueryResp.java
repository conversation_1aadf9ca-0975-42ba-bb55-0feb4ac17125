package com.meta.supplychain.entity.dto.goods.resp;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:04
 */
@Builder
@Data
public class GoodsCategoryQueryResp {
    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 上级分类X
     */
    private List<GoodsCategoryQueryResp> parentList;
}
