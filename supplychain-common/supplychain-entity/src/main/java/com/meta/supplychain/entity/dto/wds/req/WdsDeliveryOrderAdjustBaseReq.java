package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;


@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WdsDeliveryOrderAdjustBaseReq {
    @Schema(description = "配送订单号")
    @NotBlank(message = "配送订单号不能为空")
    private String billNo;

    @Schema(description = "送货日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate deliverDate;

    @Schema(description = "有效日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate validityDate;

    @Schema(description = "订单调整备注")
    private String adjustRemark;

    @Schema(description = "调整来源 0-手工调整，1-追减调整")
    @NotNull(message = "调整来源不能为空")
    private Integer billSource;

    /**
     * 商品数量是否被更改过
     */
    private Boolean isChangeQty = false;

    @Schema(description = "调整商品列表")
    List<WdsDeliveryOrderAdjustGoodsReq> detailList;
}
