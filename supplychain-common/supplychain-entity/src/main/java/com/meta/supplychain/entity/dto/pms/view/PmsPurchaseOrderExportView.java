package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购订单列表导出实体")
public class PmsPurchaseOrderExportView {

    @ViewField(headerName = "采购订单号")
    private String billNo;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    /**单据类别 -1:采退，1:采购*/
    @ViewField(headerName = "单据类别")
    private String billDirectionDesc;

    /**是否直流订单 0-非直流 1-直流*/
    @ViewField(headerName = "是否直流")
    private String directSignDesc;

    /**采购类型 0-门店采购，1-配送采购*/
    @ViewField(headerName = "采购类型")
    private String billTypeDesc;

    @ViewField(headerName = "供应商合同号")
    private String contractNo;

    @ViewField(headerName = "商品品项数")
    private Integer totalSkuCount;

    @ViewField(headerName = "订单采购数量")
    private BigDecimal totalQty;

    @ViewField(headerName = "订单采购金额")
    private String totalTaxMoney;

    @ViewField(headerName = "订单采购税金")
    private String totalTax;

    @ViewField(headerName = "送货日期")
    private String deliverDate;

    /**送货方式 0-到店，1-到客户*/
    @ViewField(headerName = "送货方式")
    private String sendModeDesc;

    @ViewField(headerName = "有效日期")
    private String validityDate;

    /**dockName*/
    @ViewField(headerName = "停靠点")
    private String dockName;

    /**来源 0-手工单，1-需求单，2-配转采*/
    @ViewField(headerName = "来源")
    private String billSourceDesc;

    @ViewField(headerName = "来源单号")
    private String srcBillNo;

    @ViewField(headerName = "来源单据备注")
    private String srcRemark;

    @ViewField(headerName = "采购计划单号")
    private String planBillNo;

    @ViewField(headerName = "退货原因")
    private String refundReasonDesc;

    @ViewField(headerName = "创建人")
    private String createName;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "采购订单备注")
    private String purchRemark;

    @ViewField(headerName = "审核人")
    private String auditName;

    @ViewField(headerName = "审核时间")
    private String auditTime;

    @ViewField(headerName = "审核备注")
    private String auditRemark;

    @ViewField(headerName = "作废人")
    private String cancelManName;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    @ViewField(headerName = "作废备注")
    private String cancelRemark;

    /**订货属性名称*/
    @ViewField(headerName = "订货属性")
    private String orderAttributeName;

    @ViewField(headerName = "需求批次")
    private String purchBatchNo;

    /**租户打印次数   供应商打印次数*/
    @ViewField(headerName = "打印次数")
    private Integer printCount;

    /**已读标记 0-否，未读  1-是，已读*/
    @ViewField(headerName = "已读标志")
    private String readSignDesc;

    @ViewField(headerName = "已读时间")
    private String readTime;

    /**状态 99-待转单， 0-草稿，1-待审核，2-已审核，3-收货中，4-已完成，5-已过期，6-已作废*/
    @ViewField(headerName = "状态")
    private String statusDesc;

    /**确认送货标记 0-无需确认，1-未确认，2-已确认*/
    @ViewField(headerName = "确认送货标记")
    private String confirmDeliverSignDesc;

    @ViewField(headerName = "确认时间")
    private String confirmDeliverTime;

    /**发货标记 0-未发货，1-已发货*/
    @ViewField(headerName = "发货标记")
    private String shipSignDesc;

    @ViewField(headerName = "发货时间")
    private String shipTime;

}
