package com.meta.supplychain.entity.dto.bds.req;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/17 9:36
 */
@Builder
@Setter
@Getter
@Data
public class QueryDeptTreeReq {


    /**
     * 是否控制权限 0 否 1 是
     */
    private Integer roleFlag;

    /**
     * 	是否展示叶子节点 0 否 1 是
     */
    private Integer showLeaf;

    /**
     * 是否展示数据详情 0 否 1 是
     */
    private Integer valueFlag;

    /**
     *
     */
    private QueryFilter queryFilter;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class QueryFilter {

        /**
         * 状态
         */
        private Integer status;

        /**
         *
         */
        private String classCode;

        /**
         * 店组群编码列表
         */
        private List<String> codeList;
    }
}
