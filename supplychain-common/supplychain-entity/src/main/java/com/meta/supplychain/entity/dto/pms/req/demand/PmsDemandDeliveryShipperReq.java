package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandDeliveryShipperReq {

    @Schema(description = "单内序号,前端生成，单据内唯一")
    private Long insideId;

    @Schema(description = "采购批次")
    private String purchBatchNo;

    @Schema(description = "上一级(pms_demand_dept_goods_detail)单内序号,前端生成，单据内唯一")
    private Long pinsideId;

    @Schema(description = "上上一级(pms_demand_goods_detail)单内序号,前端生成，单据内唯一")
    private Long goodsInsideId;

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "商品类型,0主品,1附赠赠品")
    private Integer goodsType;

    @Schema(description = "配送部门名称")
    private String distDeptName;

    @Schema(description = "促销活动名称")
    private String promoteName;

    @Schema(description = "配送包装率")
    private BigDecimal deliveryUnitRate;

    @Schema(description = "最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)")
    private BigDecimal lastPurchPrice;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "配送部门编码")
    private String distDeptCode;
    @Schema(description = "是否直流")
    private Integer directSign;
    @Schema(description = "转单标识,0普通, 1配转采")
    private Integer convertFlag;
    @Schema(description = "订货部门编码")
    private String orderDeptCode;
    @Schema(description = "状态,0未选择, 1选中")
    private Integer status;
    @Schema(description = "响应数量")
    private BigDecimal responseQty;
    @Schema(description = "档案配送价（部门商品配送价，优先 没有取 档案配送价 商品逻辑）")
    private BigDecimal goodsDistPrice;
    @Schema(description = "配送部门成本价")
    private BigDecimal distDeptCostPrice;
    @Schema(description = "配送部门实际库存")
    private BigDecimal distDeptStockRealQty;
    @Schema(description = "商品名称")
    private String skuName;
    @Schema(description = "促销价")
    private BigDecimal promotePrice;
    @Schema(description = "促销活动编码")
    private String promoteCode;
    @Schema(description = "配送金额")
    private BigDecimal distMoney;
    @Schema(description = "配送价格")
    private BigDecimal distPrice;
    @Schema(description = "配送部门可用库存")
    private BigDecimal distDeptStockAtpQty;
    @Schema(description = "配送价格单编号")
    private String distPriceBillNo;
    @Schema(description = "商品编码")
    private String skuCode;
    @Schema(description = "配送税金")
    private BigDecimal distTax;
    @Schema(description = "整件数量")
    private BigDecimal deliveryWholeQty;

    @Schema(description = "零头数量")
    private BigDecimal deliveryOddQty;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;
}
