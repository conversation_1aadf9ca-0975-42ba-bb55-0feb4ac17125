package com.meta.supplychain.entity.dto.md.req.demandstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 需求策略自动需求单批量删除请求
 * <AUTHOR>
 */
@Data
public class MdDemandStrategyAutoMappingBatchDeleteReq {
    
    @Schema(description = "需求组编码列表", required = true)
    @NotEmpty(message = "需求组编码列表不能为空")
    private List<String> demandGroupCodes;
}
