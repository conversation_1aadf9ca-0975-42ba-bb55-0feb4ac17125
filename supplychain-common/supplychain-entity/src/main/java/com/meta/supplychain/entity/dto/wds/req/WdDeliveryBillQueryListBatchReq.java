package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单查询")
@Getter
@Setter
@ToString
public class WdDeliveryBillQueryListBatchReq {


    @Schema(description = "配送订单单据状态 WDDeliveryOrderBillStatusEnum")
    private Integer status;

    @Schema(description = "订货属性编码列表 not in")
    private List<String> orderAttributeCodeListNotIn;

    private List<WdDeliveryBillQueryListBatchReqInner> batchParams;




}
