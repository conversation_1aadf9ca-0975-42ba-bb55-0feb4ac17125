package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "手动转采购订单状态", code = "mdManualToPurchaseOrderStatusEnum")
public enum MdManualToPurchaseOrderStatusEnum implements VerifiableEnum<Integer> {
    APPROVED(1, "已审核"),
    WAITING_APPROVAL(2, "待审核");

    private final Integer code;
    private final String desc;
} 