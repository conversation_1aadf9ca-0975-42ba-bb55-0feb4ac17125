package com.meta.supplychain.entity.dto.pms.req.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "代配过账单商品明细请求信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountBillDetailCreateReq {

    @Schema(description = "主键序号")
    private Long id;

    @Schema(description = "过账单号")
    private String billNo;

    @Schema(description = "配送中心编码")
    private String dcCode;

    @Schema(description = "配送中心名称")
    private String dcName;

    @Schema(description = "过账单单内序号")
    private Long insideId;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    @NotNull(message = "商品类型不能为空")
    private Integer skuType;

    @Schema(description = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String skuCode;

    @Schema(description = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String skuName;

    @Schema(description = "商品条码")
    @NotBlank(message = "商品条码不能为空")
    private String barcode;

    @Schema(description = "商品货号")
    @NotBlank(message = "商品货号不能为空")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "规格")
    private String skuModel;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    @NotNull(message = "销售模式不能为空")
    private Integer saleMode;

    @Schema(description = "进项税率")
    @NotNull(message = "进项税率不能为空")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    @NotNull(message = "销项税率不能为空")
    private BigDecimal outputTaxRate;

    @Schema(description = "计量属性 0：普通 1：计量 2：称重")
    @NotNull(message = "计量属性不能为空")
    private Integer uomAttr;

    @Schema(description = "效期商品标识 1是 0否")
    @NotNull(message = "效期商品标识不能为空")
    private Integer periodFlag;

    @Schema(description = "过账数量")
    @NotNull(message = "过账数量不能为空")
    private BigDecimal accQty;

    @Schema(description = "过账价格")
    @NotNull(message = "过账价格不能为空")
    private BigDecimal accPrice;

    @Schema(description = "过账金额")
    @NotNull(message = "过账金额不能为空")
    private BigDecimal accMoney;

    @Schema(description = "过账税金")
    @NotNull(message = "过账税金不能为空")
    private BigDecimal accTax;

    @Schema(description = "零售单价")
    @NotNull(message = "零售单价不能为空")
    private BigDecimal salePrice;

    @Schema(description = "零售金额")
    @NotNull(message = "零售金额不能为空")
    private BigDecimal saleMoney;

    @Schema(description = "来源单据类型")
    private String srcBillType;

    @Schema(description = "来源单号")
    private String srcBillNo;

    @Schema(description = "来源单单内序号")
    @NotNull(message = "来源单单内序号不能为空")
    private Long srcInsideId;

}
