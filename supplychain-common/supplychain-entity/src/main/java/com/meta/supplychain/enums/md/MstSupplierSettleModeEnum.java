package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商结算模式枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商结算模式", code = "mstSupplierSettleModeEnum")
public enum MstSupplierSettleModeEnum implements VerifiableEnum<Integer> {
    UNIFIED_SETTLEMENT(1, "统一结算"),
    LOCAL_SETTLEMENT(2, "本地结算"),
    REGIONAL_SETTLEMENT(3, "区域结算");

    private final Integer code;
    private final String desc;
}
