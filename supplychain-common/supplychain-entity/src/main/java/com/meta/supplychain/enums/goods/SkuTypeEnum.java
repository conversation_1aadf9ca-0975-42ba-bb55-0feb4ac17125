package com.meta.supplychain.enums.goods;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商品类型（0商品1附赠商品2附赠赠品）
 *
 * <AUTHOR> cat
 * @date 2024/5/16 15:23
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "商品类型", code = "skuTypeEnum")
public enum SkuTypeEnum implements VerifiableEnum<Integer> {

    NORMAL(0,"商品"),
    ADDITION(1,"附赠商品"),
    GIFT(2,"附赠赠品"),

    ;
    private Integer code;

    private String desc;

    public static SkuTypeEnum getInstance(Integer code) {
        return Arrays.stream(SkuTypeEnum.values())
                .filter(e -> Objects.equals(e.code, code))
                .findFirst()
                .orElse(null);
    }

}
