package com.meta.supplychain.entity.dto.xxjob;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/09 13:38
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XxlJobReq {
    /**
     * 任务描述
     */
    private String jobDesc;
    /**
     * 负责人
     */
    private String author;

    /**
     * 执行器，任务参数
     */
    private String executorParam;

    /**
     * 计划时间
     */
    private LocalDateTime startTime;

    /**
     * 计划时间前 min分钟后执行
     */
    private int min;

    /**
     * JobHandler
     */
    private String executorHandler;

    private Boolean isStart = false;
}
