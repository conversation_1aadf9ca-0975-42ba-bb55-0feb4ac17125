package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class QueryDeepDeptGroupListResp {
    private List<DeptGroupItem> rows;
    private long total;

    @Data
    public static class DeptGroupItem {
        private Object addDeptGroupFlag;
        private String classCode;
        private String className;
        private String code;
        private Object codeList;
        private String createCode;
        private String createName;
        private String createTime;
        private Long createUid;
        private long delFlag;
        private long id;
        private long isLeaf;
        private Integer level;
        private String name;
        private String parentCode;
        private Long parentId;
        private String parentName;
        private Object refDeptFlag;
        private long status;
        private Object superiorsLink;
        private long tenantId;
        private String updateCode;
        private String updateName;
        private String updateTime;
        private Long updateUid;

        private Set<DeptGroupItem> subDeptGroupSet = new HashSet<>();
    }
}
