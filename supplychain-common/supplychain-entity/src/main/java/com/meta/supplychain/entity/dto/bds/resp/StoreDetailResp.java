package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Builder
@Setter
@Getter
@Data
public class StoreDetailResp {
    /**
     * 部门编码
     */
    private String code;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门简称
     */
    private String nickName;

    /**
     * 启用状态 1启用 0停用
     */
    private Integer status;

    /**
     * 门店类型(1:门店2:配送)
     */
    private Integer type;

    /**
     * 类别 1实体 2虚拟
     */
    private Integer operateType;

    /**
     * 经营模式 1直营 2 加盟
     */
    private Integer operateMode;

    /**
     * 核算主体编号
     */
    private String accountCode;

    /**
     * 核算主体名称
     */
    private String accountName;

    /**
     * 负责人名称
     */
    private String managerUserName;

    /**
     * 负责人工号
     */
    private String managerUserEmpCode;

    /**
     * 过账配送中心编码
     */
    private String postingCenterCode;

    /**
     * 过账配送中心名称
     */
    private String postingCenterName;

    /**
     * 上级组织编码
     */
    private String orgCode;

    /**
     * 上级组织名称
     */
    private String orgName;

    /**
     * 参考部门编码
     */
    private String refDeptCode;

    /**
     * 参考部门名称
     */
    private String refDeptName;

    /**
     * 开业时间 yyyy-MM-dd HH:mm:ss
     */
    private String openTime;

    /**
     * 营业状态  1 营业 2 停业 3 关闭
     */
    private Integer openStatus;

    /**
     * 是否启用线上门店 0否 1是
     */
    private Integer onlineFlag;

    /**
     * 线上营业时间类型 1全天 2指定时间段
     */
    private Integer onlineTimeMode;

    /**
     * 线上营业开始时间
     */
    private String onlineStartTime;

    /**
     * 线上营业结束时间
     */
    private String onlineEndTime;

    /**
     * 线下营业时间类型 1全天 2指定时间段
     */
    private Integer offlineTimeMode;

    /**
     * 线下营业开始时间
     */
    private String offlineStartTime;

    /**
     * 线下营业结束时间
     */
    private String offlineEndTime;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 省份国标
     */
    private String provinceCode;

    /**
     * 所在省份
     */
    private String provinceName;

    /**
     * 城市国标
     */
    private String cityCode;

    /**
     * 所在城市
     */
    private String cityName;

    /**
     * 区国标
     */
    private String districtCode;

    /**
     * 所在区
     */
    private String districtName;

    /**
     * 街道国标
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 所在地址
     */
    private String address;

    /**
     * 完整地址，拼省市区provinceName+cityName+districtName+streetName+address
     */
    private String wholeAddress;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 经营面积
     */
    private String operateArea;

    /**
     * 图片
     */
    private String picUrl;

    /**
     * 门店照片
     */
    private String photo_url;

    /**
     * 客服电话
     */
    private String serviceHotline;
    /**
     * 管理分类项编码
     */
    private String goodsClass;


}
