package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.PageParams;
import com.meta.supplychain.entity.base.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 差异处理单查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "差异处理单 查询参数")
public class WdShipAccDiffBillQueryReq extends PageParams implements BaseReq {

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "配送中心")
    private String whCode;

    @Schema(description = "仓库编码列表")
    List<String> whCodeList;

    @Schema(description = "时间类型 0 收货时间 1 审核时间 2 发货时间")
    private Integer timeType;

    @Schema(description = "开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @Schema(description = "结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "单据状态列表 差异单状态枚举 1 待处理、2 已审核、3 已驳回 WDAcceptDiffStatusEnum ")
    private List<Integer> statusList;

    @Schema(description = "单据号")
    private String billNo;

    /** 配送单号 */
    @Schema(description = "配送单号")
    private String shipBillNo;

    @Schema(description = "部门编码列表")
    private List<String> deptCodeList;


    @Schema(description = "匹配差异单收货人，支持编码、名称模糊匹配")
    private String acceptManKey;

    @Schema(description = "匹配差异单审核人，支持编码、名称模糊匹配")
    private String approveManKey;

    @Schema(description = "单据状态")
    private Integer status;

    @Schema(description = "单据方向 -1退货 1正向")
    private Integer billDirection;

    //审核备注
    @Schema(description = "审核备注")
    private String remark;

    @Schema(description = "收货备注")
    private String accRemark;

    @Schema(description = "维护商品编码、名称、条码、货号，匹配存在商品的差异单")
    private String skuKeyWord;

    @Override
    public List<String> getReqDeptCodeList() {
        return whCodeList;
    }

    @Override
    public String getReqDeptCode() {
        return whCode;
    }

    @Override
    public void setReqDeptCodeList(List<String> deptCodeList) {
        this.whCodeList = deptCodeList;
    }
}