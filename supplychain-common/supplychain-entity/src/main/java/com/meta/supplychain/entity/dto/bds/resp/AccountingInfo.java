package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> cat
 * @date 2024/10/21 14:28
 */
@Getter
@Setter
@NoArgsConstructor
public class AccountingInfo {

    private String updateUid;
    private String updateTime;
    private String updateName;
    private String updateCode;
    private Long tenantId;
    private String supplierCode;
    private String status;
    private String receiveBankOutletCode;
    private String receiveBankCode;
    private String receiveAccount;
    /**
     * 过账配送中心编码
     */
    private String postingCenterCode;
    private String phone;
    private String paymentBankOutletCode;
    private String paymentBankCode;
    private String paymentAccount;
    /**
     * 核算主档名称
     */
    private String name;
    private String linkMan;
    private String invoiceNo;
    private Long id;
    private Long delFlag;
    private String customerCode;
    private String createUid;
    private String createTime;
    private String createName;
    private String createCode;
    /**
     * 核算主档编码
     */
    private String code;
}
