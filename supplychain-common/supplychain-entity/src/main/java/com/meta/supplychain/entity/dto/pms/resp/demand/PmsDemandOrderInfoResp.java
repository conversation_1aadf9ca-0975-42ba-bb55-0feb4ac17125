package com.meta.supplychain.entity.dto.pms.resp.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;


@Getter
@Setter
@ToString
public class PmsDemandOrderInfoResp {

    /** 单据类型,0采购, 1配送*/
    private Integer type;

    @Schema(description = "订货申请单号")
    private String applyBillNo;

    @Schema(description = "订货申请商品明细行号")
    private Long applyInsideId;

    @Schema(description = "数量")
    private BigDecimal qty;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "金额")
    private BigDecimal money;

    @Schema(description = "税金")
    private BigDecimal tax;

    public String getUniqKey() {
        return String.join("_", applyBillNo, applyInsideId.toString());
    }
}