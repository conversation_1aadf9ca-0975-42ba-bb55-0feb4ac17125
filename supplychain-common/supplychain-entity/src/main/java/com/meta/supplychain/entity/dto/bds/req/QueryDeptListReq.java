package com.meta.supplychain.entity.dto.bds.req;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/17 9:36
 */
@Builder
@Setter
@Getter
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeptListReq {

    /**
     * 营业状态  1 营业 2 停业 3 关闭
     */
    private Integer openStatus;
    /**
     * 启用状态 1启用 0停用
     */
    private Integer status;
    /**
     * 门店类型(1:门店2:配送)
     */
    private Integer type;

    private Integer pageSize;

    /**
     * 类别 1实体 2虚拟
     */
    private Integer operateType;
    /**
     * 门店编码
     */
    private String code;

    /**
     * 上级组织
     */
    private String orgId;

    /**
     * 门店名称&门店编码模糊匹配
     */
    private String rawInfo;

    /**
     * 创建时间 起始 yyyy-MM-dd HH:mm:ss
     */
    private String createTimeStart;

    /**
     * 创建时间 截止 yyyy-MM-dd HH:mm:ss
     */
    private String createTimeEnd;

    /**
     * ssoUserId
     */
    private Integer userId;

    /**
     * 部门编码
     */
    private List<String> deptCodeList;

    /**
     * 部门ID
     */
    private List<Long> deptIdList;

    /**
     * 业态列表
     */
    private List<String> bizModelList;

    /**
     * 业态
     */
    private String bizModel;


}
