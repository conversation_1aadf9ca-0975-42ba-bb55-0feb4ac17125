package com.meta.supplychain.entity.dto.wds;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.supplychain.entity.base.BaseEntity;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ShipBillWithAcceptInfoDTO extends BaseEntity implements Serializable {

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * 仓库名称
     */
    private String whName;

    /**
     * 配送核算单位
     */
    private String whAccCode;

    /**
     * 配送发货单号
     */
    private String shipBillNo;

    @Schema(description = "配送验收单据号")
    private String billNo;

    /**
     * 拨入部门编码
     */
    private String inDeptCode;

    /**
     * 拨入部门名称
     */
    private String inDeptName;

    /**
     * 门店经营模式 1 直营 2 加盟
     */
    private Integer deptOperateMode;

    /**
     * 入货部门核算单位
     */
    private String inAccCode;

    /**
     * 单据方向 -1退货 1正向  WDDeliveryOrderDirectionEnum
     */
    private Integer billDirection;

    /**
     * 配送单据类型 1.仓库配送、2.门店退仓、3.差异处理
     */
    private Integer billType;

    /**
     * 配送单来源 1波次拣货 2商品配送 3差异处理 4退配收货  WDShipSourceEnum
     */
    private Integer billSource;

    /**
     * 单据状态 -1处理中 0草稿 1待发货 2已发货 3已验收 9作废
     */
    private Integer status;

    /**
     * 代配过账类型：0直配 1代配 2内部进货 3内部销售 4拨出 5拨入 6退配 7代配退配
     */
    private Integer accSign;


    /**
     * 关联单号
     */
    private String srcBillNo;

    /**
     * 是否冲单0否 1是
     */
    private Integer reversalBillSign;

    /**
     * 冲红标志0否1是
     */
    private Integer reversalFlag;

    /**
     * 冲红日期
     */
    private LocalDateTime reversalDate;

    /**
     * 冲单号
     */
    private String reversalBillNo;

    /**
     * 合计配送数量
     */
    private BigDecimal totalShipQty;

    /**
     * 合计配送金额(含税)
     */
    private BigDecimal totalShipTaxMoney;

    /**
     * 合计配送税金
     */
    private BigDecimal totalShipTax;

    /**
     * 作废时间
     */
    private LocalDateTime cancelTime;

    /**
     * 作废人编码
     */
    private String cancelManCode;

    /**
     * 作废人名称
     */
    private String cancelManName;

    /**
     * 发货时间
     */
    private LocalDateTime shipTime;

    /**
     * 发货人编码
     */
    private String shipManCode;

    /**
     * 发货人名称
     */
    private String shipManName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提交人编码
     */
    private String submitManCode;

    /**
     * 提交人名称
     */
    private String submitManName;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 打印人编码
     */
    private String printManCode;

    /**
     * 打印人名称
     */
    private String printManName;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 是否打印 0否 1是
     */
    private Integer printSign;

    /**
     * 外部单号
     */
    private String outBillNo;

    /**
     * 送货方式 0-到店，1-到客户
     */
    private Integer sendMode;

    /** 修改时间 */
    private LocalDateTime createTime;

    /** 修改时间 */
    private LocalDateTime updateTime;

    /**
     * 配送订单单号 多个 , 隔开
     */
    private String deliveryBillNo;

    @Schema(description = "配送备注")
    private String remarkShip;

    @Schema(description = "验收备注")
    private String remarkAccept;


    @Schema(description = "收货时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime acceptTime;
    @Schema(description = "收货人编码")
    private String acceptManCode;
    @Schema(description = "收货人名称")
    private String acceptManName;
}
