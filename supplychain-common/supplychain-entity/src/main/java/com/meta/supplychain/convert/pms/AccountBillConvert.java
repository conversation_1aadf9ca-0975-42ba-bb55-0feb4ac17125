package com.meta.supplychain.convert.pms;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillDetailCreateReq;
import com.meta.supplychain.entity.po.pms.PmsAccountBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsAccountBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AccountBillConvert extends StandardEnumConvert {
    AccountBillConvert INSTANCE = Mappers.getMapper(AccountBillConvert.class);

    @Mapping(target = "id", ignore = true)
    PmsAccountBillPO convertToAccountBillPO(AccountBillCreateReq req);

    List<PmsAccountBillDetailPO> convertToAccountDetailPOList(List<AccountBillDetailCreateReq> detailList);
}