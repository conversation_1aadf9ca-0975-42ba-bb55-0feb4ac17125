package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单详情")
@Getter
@Setter
@Builder
public class WdDeliveryBillDetailForDemandResult {


    @Schema(description = "商品行列表")
    List<WdDeliveryBillDetailResult> detailList;
}
