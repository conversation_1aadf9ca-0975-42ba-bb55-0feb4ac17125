package com.meta.supplychain.convert.wds;


import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.req.PickBillDetailEditReq;
import com.meta.supplychain.entity.dto.wds.resp.PickBillExcelView;
import com.meta.supplychain.entity.dto.wds.resp.QueryPickBillDetailResult;
import com.meta.supplychain.entity.dto.wds.resp.QueryPickBillResult;
import com.meta.supplychain.entity.po.wds.PickBatchDetailPO;
import com.meta.supplychain.entity.po.wds.PickBillPO;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 实体转换
 */
@Mapper(componentModel = "spring")
public interface PickOrderConvert extends StandardEnumConvert {

    PickOrderConvert INSTANCE = Mappers.getMapper(PickOrderConvert.class);

    QueryPickBillResult pickOrderPO2VO(PickBillPO pickBillPO);

    List<QueryPickBillDetailResult> pickOrderDetailPO2VOList(List<PickBatchDetailPO> pickBatchDetailPOList);


    List<QueryPickBillResult> pickOrderPO2VOList(List<PickBillPO> pickBillPOList);

    List<PickBatchDetailPO> pickBatchDetailVO2POList(List<PickBillDetailEditReq> pickBillDetailEditList);

    /**
     * 拣货单导出视图转换
     * @param result
     * @return
     */
    @Mapping(target = "directSign", expression = "java(convertToDesc(\"WDDirectEnum\", result.getDirectSign()))")
    @Mapping(source = "status", target = "status", qualifiedByName = "statusDesc")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "pickTime", target = "pickTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime")
    @Mapping(target = "deliveryBillNoList", source = "deliveryBillNoList",qualifiedByName = "list2StrDesc")
    PickBillExcelView pickBillVO2View(QueryPickBillResult result);
    /**
     * 将状态枚举码转换为名称
     * @param status 状态枚举码
     * @return 状态名称
     */
    @Named("statusDesc")
    default String statusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        WDBillStatusEnum enumValue = WDBillStatusEnum.getInstance(status,WDBillStatusEnum.PICK_STATUS_1.getTableType());
        return enumValue != null ? enumValue.getDesc() : status.toString();
    }
    @Named("list2StrDesc")
    default String list2StrDesc(List<String> list) {
        if (list == null) {
            return "";
        }
        return StringUtils.join(list, ",");
    }
    /**
     * 合并编码和名称
     * @param code 编码
     * @param name 名称
     * @return 合并后的字符串
     */
    @Named("mergeCodeAndName")
    default String mergeCodeAndName(String code, String name) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.isBlank(name)) {
            return code;
        }
        return code + SysConstants.UNDERLINE_DELIMITER + name;
    }
}
