package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WdDeliveryBillQueryListBatchReqInner {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "入货部门编码")
    private String inDeptCode;

    @Schema(description = "配送部门编码")
    private String whCode;

    @Schema(description = "需求批次")
    private String requireBatch;

    @Schema(description = "商品类型 1, 主品 2, 赠品")
    private Integer goodsType;
}