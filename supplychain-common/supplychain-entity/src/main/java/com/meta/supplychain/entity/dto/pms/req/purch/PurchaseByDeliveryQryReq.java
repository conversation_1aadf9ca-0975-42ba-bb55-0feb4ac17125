package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 根据配送订单信息查询对于采购订单明细
 */
@Data
@Schema(description = "根据配送订单信息查询对于采购订单明细")
public class PurchaseByDeliveryQryReq {

    @Schema(description = "配送订单号")
    private String deliveryBillNo;

    @Schema(description = "配送订单商品行单内序号")
    private Long insideId;

    @Schema(description = "商品编码")
    private String skuCode;
}
