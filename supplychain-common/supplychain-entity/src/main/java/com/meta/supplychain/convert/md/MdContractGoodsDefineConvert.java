package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.contract.MdContractDTO;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractGoodsDefineDTO;
import com.meta.supplychain.entity.dto.md.req.contractdef.MdContractDefSaveReq;
import com.meta.supplychain.entity.dto.md.view.MdContractGoodsDefineView;
import com.meta.supplychain.entity.dto.md.view.MdContractView;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefinePO;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.md.MdContractGoodsDefineStatusEnum;
import com.meta.supplychain.enums.md.MdContractGoodsDefineTypeEnum;
import com.meta.supplychain.enums.md.MdContractOperateModeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdContractGoodsDefineConvert {
    MdContractGoodsDefineConvert INSTANCE = Mappers.getMapper(MdContractGoodsDefineConvert.class);

    MdContractGoodsDefinePO convertReq2po(MdContractDefSaveReq model);

    List<MdContractGoodsDefinePO> convertReq2poList(List<MdContractDefSaveReq> list);

    @Mapping(source = "operateMode", target = "operateMode")
    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "type", target = "type")
    @Mapping(source = "type", target = "typeDesc", qualifiedByName = "typeToDesc")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    @Mapping(source = "execTimeType", target = "execTimeType")
    @Mapping(source = "execTimeType", target = "execTimeTypeDesc", qualifiedByName = "execTimeTypeToDesc")
    @Mapping(source = "auditStatus", target = "auditStatus")
    @Mapping(source = "auditStatus", target = "auditStatusDesc", qualifiedByName = "auditStatusToDesc")
    MdContractGoodsDefineDTO convertPo2DTO(MdContractGoodsDefinePO model);

    @Mapping(source = "operateMode", target = "operateMode")
    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "type", target = "type")
    @Mapping(source = "type", target = "typeDesc", qualifiedByName = "typeToDesc")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    @Mapping(source = "execTimeType", target = "execTimeType")
    @Mapping(source = "execTimeType", target = "execTimeTypeDesc", qualifiedByName = "execTimeTypeToDesc")
    @Mapping(source = "auditStatus", target = "auditStatus")
    @Mapping(source = "auditStatus", target = "auditStatusDesc", qualifiedByName = "auditStatusToDesc")
    List<MdContractGoodsDefineDTO> convertPo2DTOList(List<MdContractGoodsDefinePO> list);

    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    MdContractGoodsDefineView convertDto2View(MdContractGoodsDefineDTO model);

    @Named("typeToDesc")
    default String typeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return MdContractGoodsDefineTypeEnum.ofCodeToDesc(code) != null ?
                MdContractGoodsDefineTypeEnum.ofCodeToDesc(code) : String.valueOf(code);
    }

    @Named("operateModeToDesc")
    default String operateModeToDesc(String code) {
        if (code == null) {
            return "";
        }
        return MdContractOperateModeEnum.ofCodeToDesc(code) != null ?
                MdContractOperateModeEnum.ofCodeToDesc(code) : code;
    }

    @Named("auditStatusToDesc")
    default String auditStatusToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "已通过" : "未通过";
    }

    @Named("execTimeTypeToDesc")
    default String execTimeTypeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "立即执行" : "指定时间执行";
    }

    @Named("statusToDesc")
    default String statusToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return MdContractGoodsDefineStatusEnum.ofCodeToDesc(code) != null ?
                MdContractGoodsDefineStatusEnum.ofCodeToDesc(code) : String.valueOf(code);
    }
}
