package com.meta.supplychain.convert.md;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.md.resp.order.MdOrderStrategyDTO;
import com.meta.supplychain.entity.dto.md.view.MdOrderStrategyImportView;
import com.meta.supplychain.entity.dto.md.view.MdOrderStrategyView;
import com.meta.supplychain.entity.po.md.MdOrderStrategyPO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 订单订货策略转换器
 * <AUTHOR>
 */
@Mapper
public interface MdOrderStrategyConvert extends StandardEnumConvert {
    
    MdOrderStrategyConvert INSTANCE = Mappers.getMapper(MdOrderStrategyConvert.class);
    
    /**
     * PO转DTO
     * @param po PO对象
     * @return DTO对象
     */
    MdOrderStrategyDTO po2dto(MdOrderStrategyPO po);
    
    /**
     * DTO转PO
     * @param dto DTO对象
     * @return PO对象
     */
    MdOrderStrategyPO dto2po(MdOrderStrategyDTO dto);
    
    /**
     * 合并编码和名称
     * @param code 编码
     * @param name 名称
     * @return 合并后的字符串
     */
    @Named("mergeCodeAndName")
    default String mergeCodeAndName(String code, String name) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.isBlank(name)) {
            return code;
        }
        return code + " - " + name;
    }
    
    /**
     * DTO转VIEW（导出视图）
     * @param dto 订单订货策略DTO
     * @return 订单订货策略导出视图
     */
    @Mapping(target = "deptType", expression = "java(convertToDesc(\"mdDeptTypeEnum\", dto.getDeptType()))")
    @Mapping(target = "enableReturn", expression = "java(convertToDesc(\"YesOrNoEnum\", dto.getEnableReturn()))")
    @Mapping(target = "enableSecondWhReturn", expression = "java(convertToDesc(\"YesOrNoEnum\", dto.getEnableSecondWhReturn()))")
    @Mapping(target = "warehouse", expression = "java(mergeCodeAndName(dto.getWhCode(), dto.getWhName()))")
    @Mapping(target = "secondWarehouse", expression = "java(mergeCodeAndName(dto.getSecondWhCode(), dto.getSecondWhName()))")
    @Mapping(target = "orderTimeInterval", expression = "java(joinOrderTimeInterval(dto.getStartTime(), dto.getEndTime()))")
    MdOrderStrategyView dto2view(MdOrderStrategyDTO dto);

    @Named("joinOrderTimeInterval")
    default String joinOrderTimeInterval(LocalTime startTime, LocalTime endTime) {
        if (startTime == null || endTime == null) {
            return "";
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        String start = dateTimeFormatter.format(startTime);
        String end = dateTimeFormatter.format(endTime);
        return start + " - " + end;
    }
    
    /**
     * ImportView转DTO
     * @param importView 导入视图对象
     * @return DTO对象
     */
    @Mapping(source = "deptType", target = "deptType")
    @Mapping(source = "deptCode", target = "deptCode")
    @Mapping(source = "deptName", target = "deptName")
    @Mapping(source = "whCode", target = "whCode")
    @Mapping(source = "whName", target = "whName")
    @Mapping(target = "startTime", source = "startTime")
    @Mapping(target = "endTime", source = "endTime")
    @Mapping(target = "mdOrderStrategyImportView", source = "importView")
    MdOrderStrategyDTO importView2dto(MdOrderStrategyImportView importView);
} 