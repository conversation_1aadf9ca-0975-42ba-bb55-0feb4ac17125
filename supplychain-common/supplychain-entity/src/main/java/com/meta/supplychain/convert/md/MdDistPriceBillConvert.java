package com.meta.supplychain.convert.md;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceBillDTO;
import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceBillWithDetailDTO;
import com.meta.supplychain.entity.po.md.MdDistPriceBillPO;
import com.meta.supplychain.entity.dto.md.view.MdDistPriceBillView;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * 配送价格单转换器
 * <AUTHOR>
 */
@Mapper
public interface MdDistPriceBillConvert extends StandardEnumConvert {

    MdDistPriceBillConvert INSTANCE = Mappers.getMapper(MdDistPriceBillConvert.class);

    MdDistPriceBillPO dto2po(MdDistPriceBillDTO dto);

    MdDistPriceBillDTO po2dto(MdDistPriceBillPO po);
    
    /**
     * 合并编码和名称
     * @param code 编码
     * @param name 名称
     * @return 合并后的字符串
     */
    @Named("mergeCodeAndName")
    default String mergeCodeAndName(String code, String name) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.isBlank(name)) {
            return code;
        }
        return code + " - " + name;
    }
    
    /**
     * 配送价格单DTO转导出视图
     * @param dto 配送价格单DTO
     * @return 配送价格单导出视图
     */
    @Mapping(target = "warehouse", expression = "java(mergeCodeAndName(dto.getWhCode(), dto.getWhName()))")
    @Mapping(target = "status", expression = "java(convertToDesc(\"distPriceOrderStatusEnum\", dto.getStatus()))")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "executionDatetime", target = "executionDatetime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime")
    MdDistPriceBillView dto2view(MdDistPriceBillWithDetailDTO dto);
} 