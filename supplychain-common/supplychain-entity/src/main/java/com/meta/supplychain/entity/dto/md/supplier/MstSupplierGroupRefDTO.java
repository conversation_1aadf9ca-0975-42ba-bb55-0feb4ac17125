package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家门店组关联DTO
 */
@Data
@Schema(description = "商家门店组关联信息")
public class MstSupplierGroupRefDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "商家编码不可为空")
    @Schema(description = "商家编码")
    private Long supplierCode;

    @NotBlank(message = "门店组编码不可为空")
    @Length(max = 50, message = "门店组编码长度不能超过50")
    @Schema(description = "门店组编码")
    private String storeGroupCode;

    @Length(max = 255, message = "门店组名称长度不能超过255")
    @Schema(description = "门店组名称")
    private String storeGroupName;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Boolean delFlag;
}
