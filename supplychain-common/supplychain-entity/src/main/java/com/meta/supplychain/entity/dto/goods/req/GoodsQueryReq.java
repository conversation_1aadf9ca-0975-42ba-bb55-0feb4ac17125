package com.meta.supplychain.entity.dto.goods.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:39
 */
@Getter
@Setter
@Builder
public class GoodsQueryReq {

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品编码（SPU编码/SKU编码）单次最多100
     */
    private List<String> goodsCodeList;

    /**
     * 是否查询属性项名称（true/false），默认false（经营状态、流转途径、品牌、分类等名称）
     */
    private Boolean attributeNameFlag;

}
