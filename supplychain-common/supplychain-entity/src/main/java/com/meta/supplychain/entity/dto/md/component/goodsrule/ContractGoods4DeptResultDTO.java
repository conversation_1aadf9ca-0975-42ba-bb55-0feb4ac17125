package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractGoods4DeptResultDTO {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "合同商品详情")
    private List<ContractGoods4DeptDTO>  detailList;
}
