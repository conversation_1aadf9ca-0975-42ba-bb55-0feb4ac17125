package com.meta.supplychain.entity.dto.wds.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@Schema(description = "查询配送验收明细详情")
public class AppQueryShipDetailForAcceptReq {

    @Schema(description = "配送单据号列表")
    @NotEmpty(message = "配送单据号列表不能为空")
    private List<String> shipBillNoList;
}
