package com.meta.supplychain.entity.dto.pms.view;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 订货申请请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PmsPurchasePlanDetailView {

    @Schema(description = "采购计划单号")
    private String billNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "商品类型 0-商品 1-附赠商品 2-附赠赠品")
    private Integer skuType;

    @Schema(description = "商品类型 0-商品 1-附赠商品 2-附赠赠品")
    private String skuTypeDesc;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "规格型号")
    private String skuModel;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMode;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private String saleModeDesc;

    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "计量属性 0-普通 1-计量 2-称重")
    private Integer uomAttr;

    @Schema(description = "计量属性 0-普通 1-计量 2-称重")
    private String uomAttrDesc;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "订货包装率")
    private BigDecimal purchUnitRate;

    @Schema(description = "促销期进价")
    private String promotePeriodPrice;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "合同特供价")
    private String contractSpecialPrice;

    @Schema(description = "合同进价")
    private String contractPrice;

    @Schema(description = "合同最高进价")
    private String contractMaxPrice;

    @Schema(description = "最后进价")
    private String lastPurchPrice;

    @Schema(description = "部门商品档案进价")
    private String deptGoodsPrice;

    @Schema(description = "档案进价")
    private String skuPurchPrice;

    @Schema(description = "采购价格")
    private String purchPrice;

    @Schema(description = "部门库存")
    private BigDecimal stockQty;

    @Schema(description = "部门可用库存")
    private BigDecimal atpQty;

    @Schema(description = "计划整件数量")
    private BigDecimal wholeQty;

    @Schema(description = "计划零头数量")
    private BigDecimal oddQty;

    @Schema(description = "计划采购数量")
    private BigDecimal purchQty;

    @Schema(description = "计划采购金额")
    private BigDecimal purchMoney;

    @Schema(description = "计划采购税金")
    private BigDecimal purchTax;

    @Schema(description = "剩余可采数量")
    private BigDecimal planReqQty;

    @Schema(description = "效期商品标识 1-是 0-否")
    private Integer periodFlag;

    @Schema(description = "效期商品标识 1-是 0-否")
    private String periodFlagDesc;

}
