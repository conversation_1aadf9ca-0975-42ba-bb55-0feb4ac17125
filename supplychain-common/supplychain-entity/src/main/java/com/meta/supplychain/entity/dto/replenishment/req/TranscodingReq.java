package com.meta.supplychain.entity.dto.replenishment.req;

import com.meta.supplychain.entity.dto.OpInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 转码基础入参
 */
@Getter
@Setter
@ToString
public class TranscodingReq {
    /**
     * 主键
     */
    private Long id;

    /**
     * 状态 0-启用，1-停用
     */
    private Integer state;

    /**
     * 单据号
     */
    private String billNumber;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 原料编码
     */
    private String rmGoodsCode;

    /**
     * 成品编码
     */
    private String fpGoodsCode;

    /**
     * 原料编码列表
     */
    private List<String> rmGoodsCodeList;

    /**
     * 成品编码列表
     */
    private List<String> fpGoodsCodeList;

    /**
     * 操作员信息
     */
    private Map<String,Object> operatorInfo;

}
