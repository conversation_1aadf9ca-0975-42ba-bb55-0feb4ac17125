package com.meta.supplychain.entity.dto.md.req.deliveryappointment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 停靠点限制规则查询请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "停靠点限制规则查询请求")
public class QueryDeliveryDockLimitReq {

    @Schema(description = "部门编码列表")
    private List<String> deptCodeList;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点类型列表(支持多值查询)")
    private List<Integer> dockTypeList;

    @Schema(description = "约束规则")
    private Integer constraintRule;

    @Schema(description = "时段行号")
    private Integer timeInsideId;
} 