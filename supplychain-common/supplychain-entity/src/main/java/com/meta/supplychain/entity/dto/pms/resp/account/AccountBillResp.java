package com.meta.supplychain.entity.dto.pms.resp.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "代配过账单响应信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountBillResp {

    @Schema(description = "过账单号")
    private String billNo;

    @Schema(description = "来源单号")
    private String srcBillNo;

    @Schema(description = "失败原因")
    private String failedReason;
}
