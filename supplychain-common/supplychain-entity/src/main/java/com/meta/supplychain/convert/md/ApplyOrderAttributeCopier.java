package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.orderattribute.ApplyOrderAttributeInfo;
import com.meta.supplychain.entity.po.md.MdApplyOrderAttributePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CommodityStrategyCopier
 * <p>商品订货策略copy
 * @Date 2024/3/15 16:00
 */
@Mapper
public interface ApplyOrderAttributeCopier {

    ApplyOrderAttributeCopier INSTANCE = Mappers.getMapper(ApplyOrderAttributeCopier.class);

    /**
     * 请求转数据库对象
     * @param req
     * @return
     */
    ApplyOrderAttributeInfo copyAttribute2Resp(MdApplyOrderAttributePO req);

    MdApplyOrderAttributePO convertAttribute(ApplyOrderAttributeInfo req);

    List<ApplyOrderAttributeInfo> copyAttributeList2Resp(List<MdApplyOrderAttributePO> req);



}
