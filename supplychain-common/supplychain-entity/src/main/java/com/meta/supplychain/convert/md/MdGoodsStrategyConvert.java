package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.goodsstrategy.GoodsStrategyDetailDTO;
import com.meta.supplychain.entity.dto.md.view.MdGoodsStrategyExcelDataVo;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.GoodsStrategyReq;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyLogResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyResp;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptOpLogPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface MdGoodsStrategyConvert {
    MdGoodsStrategyConvert INSTANCE = Mappers.getMapper(MdGoodsStrategyConvert.class);

    MdGoodsStrategyPO convertReqToPo(GoodsStrategyReq req);

    GoodsStrategyResp convertGoodsStrategy2Resp(MdGoodsStrategyPO commodityStrategies);

    List<MdGoodsStrategyDeptPO> convertDeptReqToDeptPo(List<GoodsStrategyDetailDTO> req);

    List<MdGoodsStrategyDeptOpLogPO> convertDept2LogList(List<MdGoodsStrategyDeptPO> mdGoodsStrategyPOList);

    List<GoodsStrategyResp> convertGoodsStrategy2RespList(List<MdGoodsStrategyPO> mdGoodsStrategyPOList);

    List<GoodsStrategyLogResp> convertLogPo2RespList(List<MdGoodsStrategyDeptOpLogPO> mdGoodsStrategyDeptOpLogPOList);

    List<GoodsStrategyDetailDTO> convertDeptPo2DetailList(List<MdGoodsStrategyDeptPO> commodityStrategyDeptList);

    MdGoodsStrategyExcelDataVo convertDetail2Excel(GoodsStrategyReq req);
}
