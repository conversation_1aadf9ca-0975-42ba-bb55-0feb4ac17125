package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoods4DeptDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.req.contract.MdContractGoodsDeptReq;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefineGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdContractGoodsDeptPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdContractGoodsDeptConvert {
    MdContractGoodsDeptConvert INSTANCE = Mappers.getMapper(MdContractGoodsDeptConvert.class);

    MdContractGoodsDeptPO convertReq2po(MdContractGoodsDeptReq mdContractGoodsDeptReq);

    List<MdContractGoodsDeptPO> convertReq2poList(List<MdContractGoodsDeptReq> mdContractGoodsDeptReqList);

    ContractGoodsDeptDTO convertPo2DTO(MdContractGoodsDeptPO mdContractGoodsPO);

    @Mapping(source = "purchPriceMethod", target = "oldPurchPriceMethod")
    @Mapping(source = "purchPriceMethod", target = "oldPurchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethod")
    @Mapping(source = "purchPriceMethod", target = "purchPriceMethodDesc", qualifiedByName = "purchPriceMethodToDesc")
    @Mapping(source = "deductionRate", target = "deductionRate")
    @Mapping(source = "deductionRate", target = "oldDeductionRate")
    @Mapping(source = "purchTaxPrice", target = "purchTaxPrice")
    @Mapping(source = "purchTaxPrice", target = "oldPurchTaxPrice")
    @Mapping(source = "purchTaxPrice", target = "lastPurchTaxPrice")
    @Mapping(source = "maxPurchTaxPrice", target = "maxPurchTaxPrice")
    @Mapping(source = "maxPurchTaxPrice", target = "oldMaxPurchTaxPrice")
    ContractGoods4DeptDTO convertPo2DeptDTO(MdContractGoodsDeptPO mdContractGoodsPO);

    @Mapping(source = "specialTaxPrice", target = "specialPrice")
    List<ContractGoodsDeptDTO> convertPo2DTOList(List<MdContractGoodsDeptPO> list);

    MdContractGoodsDeptPO convertDefinePo2Po(MdContractGoodsDefineGoodsDeptPO po);

    @Named("purchPriceMethodToDesc")
    default String purchPriceMethodToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "定价" : "倒扣率";
    }
}
