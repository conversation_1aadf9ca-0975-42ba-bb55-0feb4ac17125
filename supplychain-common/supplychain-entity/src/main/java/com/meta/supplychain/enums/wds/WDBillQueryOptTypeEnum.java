package com.meta.supplychain.enums.wds;

import com.meta.supplychain.enums.StandardEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * 单据详情查询类型枚举
 */
@Getter
@AllArgsConstructor
public enum WDBillQueryOptTypeEnum implements StandardEnum<Integer> {

    DETAIL(0, "详情"),
    OPT_DETAIL(1, "操作详情"),
    PRINT_DETAIL(2, "打印详情");
    private final Integer code;
    private final String desc;
}
