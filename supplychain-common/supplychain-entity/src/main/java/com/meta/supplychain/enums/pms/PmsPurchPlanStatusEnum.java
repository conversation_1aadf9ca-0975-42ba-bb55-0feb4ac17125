package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 单据状态枚举类
 *
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "采购计划单单据状态",code = "PmsPurchPlanStatusEnum")
public enum PmsPurchPlanStatusEnum {
    /**
     * 状态	0-草稿，1-待审核，2-已审核，3，使用中，4-已完成，5-已过期，6-已关闭，7-已作废
     */
    PURCH_STATUS_DRAFT(0,  "草稿"),
    PURCH_STATUS_PENDING_AUDIT(1,  "待审核"),
    PURCH_STATUS_AUDITED(2,  "已审核"),
    PURCH_STATUS_PART_ACCEPT(3,  "使用中"),
    PURCH_STATUS_FINISHED(4,  "已完成"),
    PURCH_STATUS_EXPIRED(5,  "已过期"),
    PURCH_STATUS_CANCELLED(6,  "已作废");

    private final Integer code;
    private final String desc;

    public static PmsPurchPlanStatusEnum getInstance(Integer code) {
        return Arrays.stream(PmsPurchPlanStatusEnum.values())
                .filter(e -> Objects.equals(e.code, code))
                .findFirst()
                .orElse(null);
    }
}
