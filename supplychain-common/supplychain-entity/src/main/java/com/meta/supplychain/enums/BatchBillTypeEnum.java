package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BatchBillTypeEnum {

    WORK_ORDER(0, "加工单"),
    SPLIT_ORDER(1, "拆分单"),
    ACCEPT_ORDER(2, "采购验收"),
    STORE_ALLOCATION(3, "门店调拨"),
    DELIVERY_ACCEPTANCE(4, "配送验收"),
    TRANSCODING_ORDER(5, "转码单"),
    DELIVERY_APPLICATION_ORDER(6, "配送申请订单"),




    ;


    // 编码
    private final Integer code;
    // 名称
    private final String name;
}
