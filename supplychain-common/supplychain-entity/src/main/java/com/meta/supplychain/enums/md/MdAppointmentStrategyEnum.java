package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "停靠点状态枚举", code = "mdAppointmentStrategyEnum")
public enum MdAppointmentStrategyEnum implements VerifiableEnum<Integer> {
    DRAFT(0, "禁用"),
    PENDING_SUBMIT(1, "启用");

    private final Integer code;
    private final String desc;
} 