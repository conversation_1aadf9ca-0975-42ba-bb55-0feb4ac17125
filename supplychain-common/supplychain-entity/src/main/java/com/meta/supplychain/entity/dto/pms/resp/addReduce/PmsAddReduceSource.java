package com.meta.supplychain.entity.dto.pms.resp.addReduce;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDetailDTO;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmsAddReduceSource extends ApplyBillDetailDTO {

    /**
     * 订退货属性
     */
    @Schema(description = "订退货属性编码")
    private String attributeCode;

    /**
     * 来源单订退货属性名称
     */
    @Schema(description = "来源单订退货属性名称")
    private String attributeName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 单据备注
     */
    @Schema(description = "单据备注")
    private String orderRemark;

    /**
     * 追加追减数量
     */
    @Schema(description = "追加追减数量")
    private BigDecimal addReduceQty;


}
