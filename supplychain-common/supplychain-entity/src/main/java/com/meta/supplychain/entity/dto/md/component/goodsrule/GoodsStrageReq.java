package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 订货策略查询
 */
@Data
public class GoodsStrageReq implements Serializable {

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "商品信息")
    private List<GoodsInfo> goodsInfos;

    @Schema(description = "是否查询订单订货策略,1查询订单订货策略,0不查询")
    private Integer orderQueryType = 0;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodsInfo {

        @Schema(description = "全路径品类编码,英文逗号分割")
        private String categoryCodeAll;

        /**
         * 商品编码
         */
        @Schema(description = "商品编码")
        private String skuCode;
    }
}
