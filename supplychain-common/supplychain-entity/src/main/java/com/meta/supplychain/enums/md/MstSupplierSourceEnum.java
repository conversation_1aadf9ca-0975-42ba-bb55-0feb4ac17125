package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商来源枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商来源", code = "mstSupplierSourceEnum")
public enum MstSupplierSourceEnum implements VerifiableEnum<Integer> {
    PAGE_ADD(1, "页面新增"),
    INTERFACE_SYNC(2, "接口同步");

    private final Integer code;
    private final String desc;
}
