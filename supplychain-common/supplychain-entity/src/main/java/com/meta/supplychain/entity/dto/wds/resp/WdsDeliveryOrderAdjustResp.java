package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送订单调整返回
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "配送订单调整返回")
public class WdsDeliveryOrderAdjustResp {
    /**
     * 配送订单号
     */
    @Schema(description = "配送订单号")
    private String billNo;

    /**
     * 失败原因
     */
    @Schema(description = "失败原因")
    private String failedReason;

    @Schema(description = "是否成功")
    private boolean success = true;


    public WdsDeliveryOrderAdjustResp(String failedReason, String billNo) {
        this.failedReason = failedReason;
        this.billNo = billNo;
        this.success = false;
    }

    public WdsDeliveryOrderAdjustResp(String billNo) {
        this.billNo = billNo;
    }
}
