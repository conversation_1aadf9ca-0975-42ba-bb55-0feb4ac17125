package com.meta.supplychain.entity.dto.wds;

import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptBillPO;
import com.meta.supplychain.enums.wds.WDShipAcceptOptTypeEnum;
import lombok.*;

import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveShipAcceptBillDTO {

    WdShipAcceptBillPO acceptBillPO;
    List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOS;
    boolean isUpdate;
    OpInfo opInfo;
    WDShipAcceptOptTypeEnum optTypeEnum;

    ShipBillPO shipBillPO;
}
