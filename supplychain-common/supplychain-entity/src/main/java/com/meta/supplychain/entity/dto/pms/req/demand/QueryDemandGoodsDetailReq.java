package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryDemandGoodsDetailReq {


    @Schema(description = "需求单号")
    private String billNumber;

    @Schema(description = "商品编码")
    private String skuCode;

}
