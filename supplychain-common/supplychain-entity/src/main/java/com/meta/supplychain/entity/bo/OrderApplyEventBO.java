package com.meta.supplychain.entity.bo;

import com.meta.supplychain.enums.OrderApplyStatusEventEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 订货申请事件BEAN
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderApplyEventBO<T> implements Serializable {

    /**
     * 当前事件
     */
    private OrderApplyStatusEventEnum curEventEnum;

    /**
     * 目标事件
     */
    private OrderApplyStatusEventEnum tarEventEnum;

    private T content;

}
