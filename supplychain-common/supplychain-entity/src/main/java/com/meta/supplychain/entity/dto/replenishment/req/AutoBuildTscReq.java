package com.meta.supplychain.entity.dto.replenishment.req;

import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.po.pms.PmsAcceptBillDetailPO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR> cat
 * @date 2024/11/21 17:12
 */
@Getter
@Setter
@Builder
public class AutoBuildTscReq {

    TranscodingBillDetail.TranscodingBillDetailBuilder builder;
    Boolean rmFlag;
    Boolean refundFlag;
    PmsAcceptBillDetailPO acceptBillDetail;
    BigDecimal scaleFactor;
    GoodsQueryResp.GoodsInfo goodsInfo;
}
