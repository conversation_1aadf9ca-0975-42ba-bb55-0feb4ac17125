package com.meta.supplychain.convert.wds;


import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.ShipBatchDetailDTO;
import com.meta.supplychain.entity.dto.wds.ShipBillDetailDTO;
import com.meta.supplychain.entity.dto.wds.req.ShipBillSaveReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryShipBillResp;
import com.meta.supplychain.entity.dto.wds.resp.ShipBillExcelView;
import com.meta.supplychain.entity.dto.wds.resp.ShipBillForAcceptExcelView;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 实体转换
 */
@Mapper(componentModel = "spring")
public interface ShipOrderConvert extends StandardEnumConvert {

    ShipOrderConvert INSTANCE = Mappers.getMapper(ShipOrderConvert.class);

    @Mapping(target = "id", ignore = true)
    ShipBillPO pickPO2ShipPO(PickBillPO pickBillPO);

    @Mapping(source = "refundBillNo", target = "refundAcceptBillNo")
    QueryShipBillResp shipPO2VO(ShipBillPO result);

    ShipBillPO shipVO2ShipPO(ShipBillSaveReq result);

    ShipBillDetailPO shipBillDetailDTO2PO(ShipBillDetailDTO result);

    ShipBatchDetailPO shipDetailPO2BatchDetailPO(ShipBillDetailPO result);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @IterableMapping(elementTargetType = ShipBatchDetailPO.class) // 显式指定集合元素类型
    List<ShipBatchDetailPO> pickDetailPO2ShipDetailPOList(List<PickBatchDetailPO> pickBatchDetailList);

    // 单个对象转换方法
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(source = "insideId", target = "srcInsideId")
    @Mapping(source = "billNo", target = "srcBillNo")
    @Mapping(source = "pickQty", target = "shipQty")
    @Mapping(source = "productDate", target = "productDate", qualifiedByName = "ToLocalDate")
    @Mapping(source = "expireDate", target = "expireDate", qualifiedByName = "ToLocalDate")
    ShipBatchDetailPO pickDetailPO2ShipDetailPO(PickBatchDetailPO pickBatchDetailPO);

    List<ShipBillDetailDTO> detailPO2VOList(List<ShipBillDetailPO> detailList);
    List<ShipBatchDetailDTO> batchDetailPO2VOList(List<ShipBatchDetailPO> batchDetailList);

    /**
     * 配送单导出视图转换
     * @param result
     * @return
     */
    @Mapping(source = "status", target = "status", qualifiedByName = "statusDesc")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime")
    @Mapping(target = "billSource", expression = "java(convertToDesc(\"WDShipSourceEnum\", result.getBillSource()))")
    @Mapping(target = "billType", expression = "java(convertToDesc(\"WDShipTypeEnum\", result.getBillType()))")
    @Mapping(target = "reversalBillSign", expression = "java(convertToDesc(\"YesOrNoEnum\", result.getReversalBillSign()))")
    @Mapping(target = "totalShipTaxMoney", expression = "java(formatMoney(result.getTotalShipTaxMoney()))")
    @Mapping(target = "totalShipTax", expression = "java(formatMoney(result.getTotalShipTax()))")
    ShipBillExcelView shipBillVO2View(QueryShipBillResp result);


    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    @Mapping(source = "acceptQty", target = "shipQty")
    @Mapping(source = "acceptPrice", target = "shipPrice")
    @Mapping(source = "acceptMoney", target = "shipTaxMoney")
    @Mapping(source = "acceptTax", target = "shipTax")
    @Mapping(source = "acceptQty", target = "acceptQty")
    @Mapping(source = "acceptPrice", target = "acceptTaxPrice")
    @Mapping(source = "acceptMoney", target = "acceptTaxMoney")
    @Mapping(source = "acceptTax", target = "acceptTax")
    ShipBatchDetailPO wdRefundAcceptBatchDetailPO2ShipBatchDetailPO(WdRefundAcceptBatchDetailPO wdRefundAcceptBatchDetailPO);



    List<ShipBatchDetailPO> wdRefundAcceptBatchDetailPOList2ShipBatchDetailPOList(List<WdRefundAcceptBatchDetailPO> wdRefundAcceptBatchDetailPOList);

    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    @Mapping(source = "approveQty", target = "shipQty")
    @Mapping(source = "diffTaxPrice", target = "shipPrice")
    @Mapping(source = "diffTaxMoney", target = "shipTaxMoney")
    @Mapping(source = "diffTax", target = "shipTax")
    @Mapping(source = "approveQty", target = "acceptQty")
    @Mapping(source = "diffTaxPrice", target = "acceptTaxPrice")
    @Mapping(source = "diffTaxMoney", target = "acceptTaxMoney")
    @Mapping(source = "diffTax", target = "acceptTax")
    ShipBatchDetailPO wdShipAccDiffBatchDetailPO2ShipBatchDetailPO(WdShipAccDiffBatchDetailPO wdRefundAcceptBatchDetailPO);


    List<ShipBatchDetailPO> wdShipAccDiffBatchDetailPOList2ShipBatchDetailPOList(List<WdShipAccDiffBatchDetailPO> wdRefundAcceptBatchDetailPOList);

    /**
     * 将状态枚举码转换为名称
     * @param status 状态枚举码
     * @return 状态名称
     */
    @Named("statusDesc")
    default String statusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        WDBillStatusEnum enumValue = WDBillStatusEnum.getInstance(status,WDBillStatusEnum.SHIP_STATUS_1.getTableType());
        return enumValue != null ? enumValue.getDesc() : status.toString();
    }
    /**
     * 合并编码和名称
     * @param code 编码
     * @param name 名称
     * @return 合并后的字符串
     */
    @Named("mergeCodeAndName")
    default String mergeCodeAndName(String code, String name) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.isBlank(name)) {
            return code;
        }
        return code + SysConstants.UNDERLINE_DELIMITER + name;
    }
    @Named("ToLocalDate")
    default LocalDate convertDate(Date date) {
        if (date == null) return null;
        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai")) // 显式指定时区
                .toLocalDate();
    }

    @Named("formatMoney")
    default String formatMoney(BigDecimal money) {
        if (money == null) return "";
        return money.setScale(2, RoundingMode.HALF_UP)
                .stripTrailingZeros()
                .toPlainString();
    }
}
