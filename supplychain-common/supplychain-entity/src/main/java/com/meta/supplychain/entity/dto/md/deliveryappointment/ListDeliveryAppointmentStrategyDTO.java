package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.meta.supplychain.entity.po.md.MdDeliveryDockStrategyPO;
import lombok.*;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/21 11:43
 **/
@Data
public class ListDeliveryAppointmentStrategyDTO {

    //配送部门编码列表
    private List<String> deliveryDeptCode;

    //部门编码列表
    private List<DeptInfo> deptList;

    private List<String> skuCodeList;

    private List<String> categoryCodeList;

    private List<MdDeliveryDockStrategyPO>  dockList;

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptInfo {
        //部门类型（1 部门 2 店组群； ）
        private Integer deptType;

        //部门编码
        private String deptCode;
    }

}


