package com.meta.supplychain.entity.dto.md.demandbatchstraegy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExistGoodsInfoQueryDTO {
    private String whCode;
    private String startTime;
    private String endTime;
    private Integer type;
    private String code;
}
