package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierCategoryDTO;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 供应商分类转换器
 */
@Mapper
public interface MstSupplierCategoryConvert {

    MstSupplierCategoryConvert INSTANCE = Mappers.getMapper(MstSupplierCategoryConvert.class);

    /**
     * DTO转PO
     */
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    MstSupplierCategoryPO dto2Po(MstSupplierCategoryDTO dto);

    /**
     * PO转DTO
     */
    MstSupplierCategoryDTO po2Dto(MstSupplierCategoryPO po);
}
