package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 要货规则查询
 */
@Data
public class GoodsStrategyResp implements Serializable {

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 商品/订单订货策略
     */
    @Schema(description = "商品订货策略")
    private List<GoodsStrategy> goodsStrategyList;

    @Schema(description = "订单订货策略")
    private OrderStrategyResp orderStrategyResp;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodsStrategy {

        /**
         * 传入的goodsCode
         */
        @Schema(description = "传入的goodsCode")
        private String skuCode;

        /**
         * 部门编码
         */
        @Schema(description = "部门编码")
        private String deptCode;

        /**
         * 部门类型 0-店组群, 1-具体部门
         */
        @Schema(description = "部门类型 0-店组群, 1-具体部门")
        private Integer deptType;

        /**
         * 商品（分类）编码
         */
        @Schema(description = "商品（分类）编码")
        private String goodsCode;

        /**
         * 配送中心编码
         */
        @Schema(description = "配送中心编码")
        private String whCode;

        /**
         * 配送中心名称
         */
        @Schema(description = "配送中心名称")
        private String whName;

        /**
         * 配送包装率
         */
        @Schema(description = "配送包装率")
        private BigDecimal deliveryUnitRate;

        /** 采购包装率 */
        @Schema(description = "采购包装率")
        private BigDecimal purchUnitRate;

        /**
         * 中央控制 0 否 1.是
         */
        @Schema(description = "中央控制 0 否 1.是")
        private Integer centralControl;

        @Schema(description = "转采购(0-否 1-是 默认0)")
        private Integer transferPurch;

        /**  */
        @Schema(description = "控制途径 0-供应商采购，1-配送调拨，2-供应商采购+配送调拨")
        private Integer controlPass;
    }

}
