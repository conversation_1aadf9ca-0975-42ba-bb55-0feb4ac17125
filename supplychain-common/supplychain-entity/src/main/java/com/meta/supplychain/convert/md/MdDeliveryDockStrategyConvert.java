package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryDockDeptDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryDockGoodsCategoryDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryDockLimitRuleDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryDockStrategyDTO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockDeptPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockGoodsCategoryPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockLimitRulePO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockStrategyPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 停靠点策略对象转换器
 * 
 * <AUTHOR>
 */
@Mapper
public interface MdDeliveryDockStrategyConvert {
    
    MdDeliveryDockStrategyConvert INSTANCE = Mappers.getMapper(MdDeliveryDockStrategyConvert.class);
    
    /**
     * PO -> DTO
     */
    MdDeliveryDockStrategyDTO po2dto(MdDeliveryDockStrategyPO po);
    
    /**
     * PO列表 -> DTO列表
     */
    List<MdDeliveryDockStrategyDTO> po2dtoList(List<MdDeliveryDockStrategyPO> poList);
    
    /**
     * DTO -> PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryDockStrategyPO dto2po(MdDeliveryDockStrategyDTO dto);
    
    /**
     * DTO列表 -> PO列表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    List<MdDeliveryDockStrategyPO> dto2poList(List<MdDeliveryDockStrategyDTO> dtoList);
    
    /**
     * 部门PO -> DTO
     */
    MdDeliveryDockDeptDTO deptPo2dto(MdDeliveryDockDeptPO po);
    
    /**
     * 部门PO列表 -> DTO列表
     */
    List<MdDeliveryDockDeptDTO> deptPo2dtoList(List<MdDeliveryDockDeptPO> poList);
    
    /**
     * 部门DTO -> PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryDockDeptPO deptDto2po(MdDeliveryDockDeptDTO dto);
    
    /**
     * 部门DTO列表 -> PO列表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    List<MdDeliveryDockDeptPO> deptDto2poList(List<MdDeliveryDockDeptDTO> dtoList);
    
    /**
     * 商品类别PO -> DTO
     */
    MdDeliveryDockGoodsCategoryDTO goodsCategoryPo2dto(MdDeliveryDockGoodsCategoryPO po);
    
    /**
     * 商品类别PO列表 -> DTO列表
     */
    List<MdDeliveryDockGoodsCategoryDTO> goodsCategoryPo2dtoList(List<MdDeliveryDockGoodsCategoryPO> poList);
    
    /**
     * 商品类别DTO -> PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryDockGoodsCategoryPO goodsCategoryDto2po(MdDeliveryDockGoodsCategoryDTO dto);
    
    /**
     * 商品类别DTO列表 -> PO列表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    List<MdDeliveryDockGoodsCategoryPO> goodsCategoryDto2poList(List<MdDeliveryDockGoodsCategoryDTO> dtoList);
    
    /**
     * 限制规则PO -> DTO
     */
    MdDeliveryDockLimitRuleDTO limitRulePo2dto(MdDeliveryDockLimitRulePO po);
    
    /**
     * 限制规则PO列表 -> DTO列表
     */
    List<MdDeliveryDockLimitRuleDTO> limitRulePo2dtoList(List<MdDeliveryDockLimitRulePO> poList);
    
    /**
     * 限制规则DTO -> PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryDockLimitRulePO limitRuleDto2po(MdDeliveryDockLimitRuleDTO dto);
    
    /**
     * 限制规则DTO列表 -> PO列表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    List<MdDeliveryDockLimitRulePO> limitRuleDto2poList(List<MdDeliveryDockLimitRuleDTO> dtoList);
} 