package com.meta.supplychain.entity.dto.stock.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品行信息
 *
 * <AUTHOR>
 * @date 2022/6/15 14:54
 * @since
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchExecRowVo {
    /**
     * 商品行ID
     */
    @Schema(title = "商品内部行编码",description = "商品内部行编码")
    private Long insideId;
    /**
     * 商品行ID
     */
    @Schema(title = "操作类型",description = "操作类型")
    private String operateCode;
    /**
     * 商品编码
     */
    @Schema(title = "商品编码",description = "商品编码")
    private String skuCode;
    /**
     * 合计数量（*10000）
     */
    @Schema(title = "库存数量",description = "库存数量")
    private BigDecimal realQty = BigDecimal.ZERO;
    /**
     * 合计成本金额（*100）
     */
    @Schema(title = "不含税成本金额",description = "不含税成本金额")
    private BigDecimal costMoney = BigDecimal.ZERO;
    /**
     * 合计税金（*100）
     */
    @Schema(title = "成本税金",description = "成本税金")
    private BigDecimal costTax = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "不含税成本单价",description = "不含税成本单价")
    private BigDecimal costPrice = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal costTaxPrice = BigDecimal.ZERO;
    /**
     * 合计含税成本金额（*100）
     */
    @Schema(title = "含税成本金额",description = "含税成本金额")
    private BigDecimal costTaxMoney = BigDecimal.ZERO;
    /**
     * 合计批次成本金额（*100）
     */
    @Schema(title = "批次成本不含税金额",description = "批次成本不含税金额")
    private BigDecimal batchCostMoney = BigDecimal.ZERO;
    /**
     * 合计批次税金（*100）
     */
    @Schema(title = "批次成本税金",description = "批次成本税金")
    private BigDecimal batchCostTax = BigDecimal.ZERO;
    /**
     * 批次平均单价
     */
    @Schema(title = "批次成本单价",description = "批次成本单价")
    private BigDecimal batchCostPrice = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "批次含税成本单价",description = "批次含税成本单价")
    private BigDecimal batchCostTaxPrice = BigDecimal.ZERO;
    /**
     * 合计批次含税成本金额（*100）
     */
    @Schema(title = "批次成本含税金额",description = "批次成本含税金额")
    private BigDecimal batchCostTaxMoney = BigDecimal.ZERO;
    /**
     * 平均单价（*10000）
     */
    @Schema(title = "发出成本单价",description = "发出成本单价")
    private BigDecimal outCostPrice = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "发出成本含税单价",description = "发出成本含税单价")
    private BigDecimal outCostTaxPrice = BigDecimal.ZERO;
    /**
     * 合计金额（*100）
     */
    @Schema(title = "发出不含税成本金额",description = "发出不含税成本金额")
    private BigDecimal outCostMoney = BigDecimal.ZERO;

    @Schema(title = "发出含税成本金额",description = "发出含税成本金额")
    private BigDecimal outCostTaxMoney = BigDecimal.ZERO;
    /**
     * 合计税金
     */
    @Schema(title = "发出税金",description = "发出税金")
    private BigDecimal outTax = BigDecimal.ZERO;

    /**
     * 调前成本单价（*10000）
     */
    @Schema(title = "调前成本单价",description = "调前成本单价")
    private BigDecimal beforeCostPrice = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "调前成本含税单价",description = "调前成本含税单价")
    private BigDecimal beforeCostTaxPrice = BigDecimal.ZERO;
    /**
     * 调前金额（*100）
     */
    @Schema(title = "调前金额",description = "调前金额")
    private BigDecimal beforeCostMoney = BigDecimal.ZERO;

    /**
     * 调前金额（*100）
     */
    @Schema(title = "调前金额",description = "调前金额")
    private BigDecimal beforeCostTaxMoney = BigDecimal.ZERO;
    /**
     * 调前税金（*100）
     */
    @Schema(title = "调前税金",description = "调前税金")
    private BigDecimal beforeTax = BigDecimal.ZERO;
    /**
     * 调后平均单价（*10000）
     */
    @Schema(title = "调后平均单价",description = "调后平均单价")
    private BigDecimal afterCostPrice = BigDecimal.ZERO;
    /**
     * 平均单价
     */
    @Schema(title = "调后成本含税单价",description = "调后成本含税单价")
    private BigDecimal afterCostTaxPrice = BigDecimal.ZERO;

    /**
     * 调后金额（*100）
     */
    @Schema(title = "调后金额",description = "调后金额")
    private BigDecimal afterCostMoney = BigDecimal.ZERO;

    /**
     * 调前金额（*100）
     */
    @Schema(title = "调前金额",description = "调前金额")
    private BigDecimal afterCostTaxMoney = BigDecimal.ZERO;
    /**
     * 调后税金（*100）
     */
    @Schema(title = "调后税金",description = "调后税金")
    private BigDecimal afterTax = BigDecimal.ZERO;
    /**
     * 商品行明细
     */
    @Schema(title = "效期商品行",description = "效期商品行")
    private List<BatchExecRowDetVo> batchSkuList;


}
