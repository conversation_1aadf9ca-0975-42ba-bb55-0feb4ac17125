package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 批量更新预约数量DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "批量更新预约数量DTO")
public class UpdateAppointmentQtyDTO {

    @NotNull(message = "明细ID不能为空")
    @Schema(description = "采购订单明细ID")
    private Long id;

    @NotNull(message = "预约数量不能为空")
    @Schema(description = "预约数量")
    private BigDecimal appointmentQty;
} 