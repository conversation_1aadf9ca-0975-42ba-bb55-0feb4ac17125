package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@TableName(value ="wd_refund_accept_batch_detail")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WdRefundAcceptBatchDetailPO extends BaseEntity {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 仓库编码 */
    private String whCode;

    /** 仓库名称 */
    private String whName;

    /** 单据号 */
    private String billNo;

    /** 单据类型 0 配送退货 */
    private Integer billType;

    /** 品牌编码 */
    private String brandCode;

    /** 品牌名称 */
    private String brandName;

    /** 品类编码 */
    private String categoryCode;

    /** 品类名称 */
    private String categoryName;

    /** 退货方部门编码 */
    private String inDeptCode;

    /** 退货方部门名称 */
    private String inDeptName;

    /** 单内序号 */
    private Long insideId;

    /** 储位编码 */
    private String locationCode;

    /** 储位名称 */
    private String locationName;
    
    /** 储位库存 */
    private BigDecimal locationQty;

    /** 商品类型 0商品1附赠商品2附赠赠品 */
    private Integer skuType;
    /**
     * 是否效期 0否1是
     */
    private Integer periodFlag;

    /**
     * 计量属性（0-普通，2-称重）
     */
    private Integer uomAttr;

    /** 商品编码 */
    private String skuCode;

    /** 商品名称 */
    private String skuName;

    /** 条码;SKU基本条码 */
    private String barcode;

    /** 商品货号 */
    private String goodsNo;
    
    /** 整件包装率 */
    private BigDecimal packageRate;
    
    /** 订货包装率 */
    private BigDecimal orderUnitRate;

    /** 单位 */
    private String basicUnit;
    
    /** 整件单位 */
    private String packageUnit;

    /** 规格型号 */
    private String skuModel;

    /** 配送价 */
    private BigDecimal acceptPrice;

    /** 数量 */
    private BigDecimal acceptQty;

    /** 退货金额 */
    private BigDecimal acceptMoney;

    /** 退货税金 */
    private BigDecimal acceptTax;

    /** 订单数量 */
    private BigDecimal orderQty;

    /** 进项税率 */
    private BigDecimal inputTaxRate;

    /** 销项税率 */
    private BigDecimal outputTaxRate;

    /** 商品批号 */
    private String periodBatchNo;

    /** 生产日期 */
    private LocalDate productDate;

    /** 过期日期 */
    private LocalDate expireDate;
    
    /** 效期条码 */
    private String periodBarcode;

    /** 明细备注 */
    private String remark;

    /** 箱数 */
    private BigDecimal wholeQty;

    /** 零头数量 */
    private BigDecimal oddQty;

    /** 来源单据类型 */
    private String srcBillType;

    /** 来源单据号 */
    private String srcBillNo;

    /** 来源单内序号 */
    private Long srcInsideId;



}