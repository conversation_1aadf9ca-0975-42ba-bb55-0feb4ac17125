package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-7 10:03:04
 */
@Schema(description = "移位单主表")
@Getter
@Setter
@ToString
public class QueryMoveBillReq extends PageParams {

    @Schema(description = "类型 1商品移位、2良品分拣、3储位转移、4残品转移")
    private Integer moveType;
    @Schema(description = "单据备注")
    private String remark;

    @Schema(description = "单据号")
    private String billNo;
    @Schema(description = "仓库名称")
    private String whName;
    @Schema(description = "仓库编码")
    private String whCode;
    @Schema(description = "仓库编码")
    private List<String> whCodeList;

    @Schema(description = "制单人搜索")
    private String createManSearch;

    @Schema(description = "单据状态 -1处理中 1待审核 2已审核 9作废")
    private Integer status;
    @Schema(description = "状态列表 单据状态 -1处理中 1待审核 2已审核 9作废")
    private List<Integer> statusList;

    @Schema(description = "时间类型 0 创建时间 1 审核时间 ")
    private int timeType;
    /**
     * 查询开始时间
     */
    @Schema(description = "查询开始时间")
    private String startTime;
    /**
     * 查询结束时间
     */
    @Schema(description = "查询结束时间")
    private String endTime;
    @Schema(description = "商品编码")
    private String skuCode;
    @Schema(description = "商品搜索")
    private String skuSearchKey;

    @Max(value = 100, message = "每页最大支持100条")
    private Long pageSize = 10L;
}
