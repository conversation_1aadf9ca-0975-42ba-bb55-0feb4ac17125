package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemCategoryResponseDTO;
import com.meta.supplychain.entity.po.md.MdExpenseItemCategoryPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MdExpenseItemCategoryConvert {

    MdExpenseItemCategoryConvert INSTANCE = Mappers.getMapper(MdExpenseItemCategoryConvert.class);

    MdExpenseItemCategoryResponseDTO po2dto(MdExpenseItemCategoryPO po);
}
