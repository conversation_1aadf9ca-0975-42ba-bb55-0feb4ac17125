package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsExcelDataDTO;
import com.meta.supplychain.entity.dto.md.view.MdAddReduceGoodsExcelDataView;
import com.meta.supplychain.entity.dto.md.view.MdAddReduceGoodsView;
import com.meta.supplychain.entity.po.md.MdAddReduceGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdAddReduceGoodsPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MdAddReduceGoodsConvert {
    MdAddReduceGoodsConvert INSTANCE = Mappers.getMapper(MdAddReduceGoodsConvert.class);

    @Mapping(source = "deptScope", target = "deptScope")
    @Mapping(source = "deptScope", target = "deptScopeDesc", qualifiedByName = "deptScopeToDesc")
    @Mapping(source = "isAllowMultiBill", target = "isAllowMultiBill")
    @Mapping(source = "isAllowMultiBill", target = "isAllowMultiBillDesc", qualifiedByName = "isAllowToDesc")
    @Mapping(source = "isAllowReduceBill", target = "isAllowReduceBill")
    @Mapping(source = "isAllowReduceBill", target = "isAllowReduceBillDesc", qualifiedByName = "isAllowToDesc")
    MdAddReduceGoodsDTO convertPo2Dto(MdAddReduceGoodsPO model);

    MdAddReduceGoodsView convertDto2View(MdAddReduceGoodsDTO model);

    MdAddReduceGoodsDeptDTO convertDeptPo2Dto(MdAddReduceGoodsDeptPO model);

    MdAddReduceGoodsExcelDataDTO convertView2Dto(MdAddReduceGoodsExcelDataView model);

    @Named("deptScopeToDesc")
    default String deptScopeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "不限制部门" : "指定部门";
    }

    @Named("isAllowToDesc")
    default String isAllowToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 1 ? "是" : "否";
    }

}
