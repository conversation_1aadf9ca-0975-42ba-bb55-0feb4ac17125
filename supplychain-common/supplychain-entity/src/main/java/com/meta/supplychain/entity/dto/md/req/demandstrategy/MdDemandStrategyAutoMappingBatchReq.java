package com.meta.supplychain.entity.dto.md.req.demandstrategy;

import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingGroupDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 需求策略自动需求单批量创建请求
 * <AUTHOR>
 */
@Data
public class MdDemandStrategyAutoMappingBatchReq {
    
    @Schema(description = "需求策略组列表", required = true)
    @Valid
    private List<MdDemandStrategyAutoMappingGroupDTO> items;
}
