package com.meta.supplychain.entity.dto.replenishment.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 转码单入参
 */
@Data
public class TranscodingBillReq {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户号
     */
    private String tenantId;

    /**
     * 单据号
     */
    private String billNumber;

    /**
     * 是否冲红单 0-否 1-是
     */
    private Integer isCancelBill;

    /**
     * 冲红单对应的原单据号
     */
    private String originalBillNumber;

    /**
     * 对应的冲红单号
     */
    private String cancelBillNumber;

    /**
     * 单据状态（0-草稿 1-待审核 2-已审核 3-作废）
     */
    private Integer billState;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 原料数量
     */
    private Long rmAmount;

    /**
     * 原料成本单价
     */
    private Long rmCostPrice;

    /**
     * 成品数量
     */
    private Long fpAmount;

    /**
     * 成品成本单价
     */
    private Long fpCostPrice;

    /**
     * 制单人编码
     */
    private String buildManCode;

    /**
     * 制单人名称
     */
    private String buildManName;

    /**
     * 最后修改人编码
     */
    private String modifyManCode;

    /**
     * 最后修改人名称
     */
    private String modifyManName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 审核人编码
     */
    private String submitManCode;

    /**
     * 审核人名称
     */
    private String submitManName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单日期 YYYYMMDD
     */
    private String accDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 商品明细
     */
    private List<TranscodingBillDetail> detailList;

    /**
     * 操作人信息
     */
    private Map<String,Object> operatorInfo;
}
