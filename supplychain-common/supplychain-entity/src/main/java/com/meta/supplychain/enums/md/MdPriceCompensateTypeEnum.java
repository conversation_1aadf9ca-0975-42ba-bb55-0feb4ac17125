package com.meta.supplychain.enums.md;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "降价补偿方式", code = "mdPriceCompensateTypeEnum")
public enum MdPriceCompensateTypeEnum implements VerifiableEnum<Integer> {

    TYPE_1(1, "厂补"),
    TYPE_2(2, "基金补"),
    TYPE_3(3, "厂补+库补店"),

    ;

    @EnumValue
    private final Integer code;
    private final String desc;

    @JsonValue
    public Integer jsonValue() {
        return code;
    }

    public static String ofCodeToDesc(Integer code) {
        for (MdPriceCompensateTypeEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }
}
