package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家经营联系人DTO
 */
@Data
@Schema(description = "商家经营联系人信息")
public class MstSupplierBusinessContactDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "商家编码不可为空")
    @Schema(description = "商家编码")
    private Long supplierCode;

    @NotBlank(message = "联系人姓名不可为空")
    @Length(max = 50, message = "联系人姓名长度不能超过50")
    @Schema(description = "联系人姓名")
    private String contactName;

    @Length(max = 50, message = "联系人职位长度不能超过50")
    @Schema(description = "联系人职位")
    private String contactPosition;

    @NotBlank(message = "联系人电话不可为空")
    @Length(max = 30, message = "联系人电话长度不能超过30")
    @Schema(description = "联系人电话")
    private String contactPhone;

    @Length(max = 64, message = "联系人邮箱长度不能超过64")
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Length(max = 255, message = "联系人地址长度不能超过255")
    @Schema(description = "联系人地址")
    private String contactAddress;

    @Schema(description = "是否主要联系人：0-否，1-是")
    private Boolean isPrimary;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Boolean delFlag;
}
