package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  单据来源 0 普通 1 批销 2 大数据 3 朝批
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/12 17:16
 */
@Getter
@AllArgsConstructor
public enum PmsBillSourceEnum {
    DEFAULT(0, "普通"),
    WHOLESALE(1, "批销"),
    BIGDATABASE(2, "大数据"),
//    CHAO_PI(3, "朝批"),
    TOBACCO(4, "烟草"),
    ERP(5, "ERP"),
    ;

    private Integer billSource;

    private String billSourceDesc;

    public static PmsBillSourceEnum getByType(Integer billSource) {
        for (PmsBillSourceEnum value : values()) {
            if(value.getBillSource().equals(billSource)) {
                return value;
            }
        }
        return DEFAULT;
    }
}
