package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商分类类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商分类类型", code = "mstSupplierCategoryTypeEnum")
public enum MstSupplierCategoryTypeEnum implements VerifiableEnum<Integer> {
    SYSTEM(1, "系统"),
    CUSTOM(2, "自定义");

    private final Integer code;
    private final String desc;
}
