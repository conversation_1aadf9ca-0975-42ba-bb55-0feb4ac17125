package com.meta.supplychain.convert.md;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.md.resp.MdDistPriceResponseDTO;
import com.meta.supplychain.entity.dto.md.view.MdDistPriceLogView;
import com.meta.supplychain.entity.dto.md.view.MdDistPriceView;
import com.meta.supplychain.enums.md.MdDistPriceDefineTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 配送价格视图转换器
 * <AUTHOR>
 */
@Mapper
public interface MdDistPriceViewConvert extends StandardEnumConvert {

    MdDistPriceViewConvert INSTANCE = Mappers.getMapper(MdDistPriceViewConvert.class);

    /**
     * 将MdDistPriceResponseDTO转换为MdDistPriceView（导出视图）
     * @param dto 配送价格DTO
     * @return 配送价格导出视图
     */
    @Mapping(target = "skuName", source = "dto", qualifiedByName = "joinSkuOrSkuClassName")
    @Mapping(target = "skuCode", source = "dto", qualifiedByName = "joinSkuOrSkuClassCode")
    @Mapping(target = "statementType", expression = "java(convertToDesc(\"distPriceDefineTypeEnum\", dto.getStatementType()))")
    @Mapping(target = "deptType", expression = "java(convertToDesc(\"mdDeptTypeEnum\", dto.getDeptType()))")
    @Mapping(target = "priceType", expression = "java(convertToDesc(\"distPriceTypeEnum\", dto.getPriceType()))")
    @Mapping(target = "valid", expression = "java(convertToDesc(\"validStatusEnum\", dto.getValid()))")
    @Mapping(source = "distTaxPrice", target = "distTaxPrice", qualifiedByName = "bigDecimalToString")
    @Mapping(source = "markupRate", target = "markupRate", qualifiedByName = "bigDecimalPercentToString")
    @Mapping(source = "deductionRate", target = "deductionRate", qualifiedByName = "bigDecimalPercentToString")
    @Mapping(source = "activationDatetime", target = "activationDatetime", qualifiedByName = "localDateTimeToString")
    @Mapping(source = "expirationDatetime", target = "expirationDatetime", qualifiedByName = "localDateTimeToString")
    MdDistPriceLogView dto2LogView(MdDistPriceResponseDTO dto);

    @Mapping(target = "skuName", source = "dto", qualifiedByName = "joinSkuOrSkuClassName")
    @Mapping(target = "skuCode", source = "dto", qualifiedByName = "joinSkuOrSkuClassCode")
    @Mapping(target = "statementType", expression = "java(convertToDesc(\"distPriceDefineTypeEnum\", dto.getStatementType()))")
    @Mapping(target = "deptType", expression = "java(convertToDesc(\"mdDeptTypeEnum\", dto.getDeptType()))")
    @Mapping(target = "priceType", expression = "java(convertToDesc(\"distPriceTypeEnum\", dto.getPriceType()))")
    @Mapping(source = "distTaxPrice", target = "distTaxPrice", qualifiedByName = "bigDecimalToString")
    @Mapping(source = "markupRate", target = "markupRate", qualifiedByName = "bigDecimalPercentToString")
    @Mapping(source = "deductionRate", target = "deductionRate", qualifiedByName = "bigDecimalPercentToString")
    MdDistPriceView dto2priceView(MdDistPriceResponseDTO dto);

    /**
     * 将BigDecimal转换为String
     * @param decimal BigDecimal数值
     * @return 格式化的数值字符串
     */
    @Named("bigDecimalToString")
    default String bigDecimalToString(BigDecimal decimal) {
        if (decimal == null) {
            return "";
        }
        return decimal.setScale(4, RoundingMode.HALF_UP).toPlainString();
    }
    @Named("bigDecimalPercentToString")
    default String bigDecimalPercentToString(BigDecimal decimal) {
        if (decimal == null) {
            return "";
        }
        return decimal.setScale(2, RoundingMode.HALF_UP).toPlainString() + " %";
    }

    /**
     * 将LocalDateTime转换为String
     * @param dateTime LocalDateTime日期时间
     * @return 格式化的日期时间字符串
     */
    @Named("localDateTimeToString")
    default String localDateTimeToString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Named("joinSkuOrSkuClassName")
    default String joinSkuOrSkuClassName(MdDistPriceResponseDTO dto) {
        if (dto == null) {
            return "";
        }
        if (MdDistPriceDefineTypeEnum.BY_PRODUCT.verifyByCode(dto.getStatementType())) {
            return dto.getSkuName();
        }
        if (MdDistPriceDefineTypeEnum.BY_CATEGORY.verifyByCode(dto.getStatementType())) {
            return dto.getSkuClassName();
        }
        return "";
    }

    @Named("joinSkuOrSkuClassCode")
    default String joinSkuOrSkuClassCode(MdDistPriceResponseDTO dto) {
        if (dto == null) {
            return "";
        }
        if (MdDistPriceDefineTypeEnum.BY_PRODUCT.verifyByCode(dto.getStatementType())) {
            return dto.getSkuCode();
        }
        if (MdDistPriceDefineTypeEnum.BY_CATEGORY.verifyByCode(dto.getStatementType())) {
            return dto.getSkuClassCode();
        }
        return "";
    }
} 