package com.meta.supplychain.entity.dto.pms.req.appointment;

import cn.linkkids.framework.croods.common.PageParams;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsBookingCategoryEnum;
import com.meta.supplychain.enums.pms.PmsBookingStatusEnum;
import com.meta.supplychain.enums.pms.PmsCarrierMethodEnum;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import com.metadata.idaas.client.model.LoginUserDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 预约单查询条件DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约单查询条件DTO")
public class PmsAppointmentBillQueryDTO extends PageParams {

    @Schema(description = "预约单据号（模糊匹配）")
    private String billNo;

    @Schema(description = "供应商编码列表")
    private List<String> supplierCodes;

    @Schema(description = "停靠点编码列表")
    private List<String> dockCodes;

    @EnumValue(type = PmsBookingStatusEnum.class)
    @Schema(description = "状态 枚举pmsBookingStatusEnum")
    private Integer status;

    @Schema(description = "部门编码列表")
    private List<String> deptCodes;

    @EnumValue(type = PmsCarrierMethodEnum.class)
    @Schema(description = "承运方式 枚举pmsCarrierMethodEnum")
    private Integer transportMode;

    @Schema(description = "承运备注（模糊匹配）")
    private String transportRemark;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "创建时间开始（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime createTimeStart;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "创建时间结束（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime createTimeEnd;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "提交时间开始（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime submitTimeStart;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "提交时间结束（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime submitTimeEnd;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "作废时间开始（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime cancelTimeStart;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "作废时间结束（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime cancelTimeEnd;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "计划到达时间开始（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime planArrivalTimeStart;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "计划到达时间结束（yyyy-MM-dd HH:mm:ss）")
    private LocalDateTime planArrivalTimeEnd;

    @EnumValue(type = PmsBookingCategoryEnum.class)
    @Schema(description = "预约类别 枚举pmsBookingCategoryEnum")
    private Integer billDirection;

    @Schema(description = "承运人（模糊匹配）")
    private String transportMan;

    @Schema(description = "预约备注（模糊匹配）")
    private String appointmentRemark;

    @EnumValue(type = YesOrNoEnum.class)
    @Schema(description = "是否直流 枚举YesOrNoEnum")
    private Integer directSign;

    @Schema(description = "承运联系手机（模糊匹配）")
    private String transportMobile;

    @Schema(description = "采购订单号（模糊匹配）")
    private String purchBillNo;

    /**
     * 导出查询 暂存登陆用户信息
     */
    private LoginUserDTO  loginUser;
} 