package com.meta.supplychain.entity.dto.wds.req;

import cn.linkkids.framework.croods.common.PageParams;
import com.meta.supplychain.entity.base.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-7 10:09:38
 */
@Schema(description = "退货收货主表")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWdRefundAcceptReq extends PageParams implements BaseReq {

    @Schema(description = "配送中心编码")
    private String whCode;

    /** 单据类型0配送 RefundAcceptBillTypeEnum */
    @Schema(description = "单据类型 0配送 RefundAcceptBillTypeEnum")
    private Integer billType;

    @Schema(description = "配送中心编码列表")
    private List<String> whCodeList;

    @Schema(description = "日期类型 0 制单日期 1 收货日期")
    private Integer timeType;

    @Schema(description = "开始时间 yyyy-MM-dd")
    private String beginTime;

    @Schema(description = "结束时间 yyyy-MM-dd")
    private String endTime;

    @Schema(description = " 单据状态-1处理中 0草稿 1待收货 2已收货 9已作废  RefundAcceptBillStatusEnum")
    private Integer status;

    @Schema(description = "单据状态列表")
    private List<Integer> statusList;

    @Schema(description = "退货收货单号")
    private String billNo;

    @Schema(description = "关联的配送单号")
    private String deliveryBillNo;

    @Schema(description = "退货方部门编码")
    private String inDeptCode;

    @Schema(description = "退货方部门编码列表")
    private List<String> inDeptCodeList;

    @Schema(description = "匹配退货收货单收货人，支持编码、名称模糊匹配")
    private String submitManKeyWord;

    @Schema(description = "商品编码、名称、条码、货号，模糊匹配")
    private String skuKeyWord;

    @Schema(description = "单据备注 模糊匹配")
    private String remark;


    @Override
    public List<String> getReqDeptCodeList() {
        return whCodeList;
    }

    @Override
    public String getReqDeptCode() {
        return whCode;
    }

    @Override
    public void setReqDeptCodeList(List<String> deptCodeList) {
        this.whCodeList = deptCodeList;
    }
}

