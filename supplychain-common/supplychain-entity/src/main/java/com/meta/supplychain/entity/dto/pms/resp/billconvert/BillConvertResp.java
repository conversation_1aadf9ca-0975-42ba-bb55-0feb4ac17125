package com.meta.supplychain.entity.dto.pms.resp.billconvert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;


/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 16:35
 **/
@Data
public class BillConvertResp {
    @Schema(description = "单据明细关联关系")
    private List<BillDetailRefInfo> billDetailRefList;

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BillDetailRefInfo {
        @Schema(description = "出货方单内序号")
        private Long shipperInsideId;

        @Schema(description = "出货方上级单内序号")
        private Long shipperPInsideId;

        @Schema(description = "目标单据号")
        private Long targetBillNo;

        @Schema(description = "出货途径0.采购 1.配送")
        private Integer shippingWay;
    }
}
