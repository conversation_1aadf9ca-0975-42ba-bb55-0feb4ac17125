package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购订单确认发货请求
 */
@Data
@Schema(description = "采购订单确认发货请求")
public class PurchaseBillconfirmShipReq {

    @Schema(description = "采购订单号")
    @NotBlank(message = "采购订单号不能为空")
    private String billNo;

    @NotEmpty(message = "商品列表不能为空")
    @Valid
    @Schema(description = "商品列表")
    private List<GoodsDetail> goodsList;

    @Data
    public static class GoodsDetail {

        @Schema(description = "采购订单号")
        @NotBlank(message = "采购订单号不能为空")
        private String billNo;

        @Schema(description = "单内序号")
        @NotNull(message = "单内序号不能为空")
        private Long insideId;

        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        private String skuCode;

        @Schema(description = "确认数量")
        @NotNull(message = "确认数量不能为空")
        private BigDecimal confirmQty;
    }
}
