package com.meta.supplychain.entity.dto.pms.resp.accept;


import cn.linkkids.framework.croods.common.date.LocalDates;
import com.alibaba.ageiport.processor.core.annotation.ViewField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Classname AcceptDetailResp
 * @Dscription TODO
 * @DATE 2025/5/19 11:42
 */
@Data
public class AcceptDetailView {

    @ViewField(headerName = "验收单号")
    private String billNo;

    @ViewField(headerName = "状态")
    private String statusDesc;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "验收部门编码")
    private String deptCode;

    @ViewField(headerName = "验收部门名称")
    private String deptName;

    @ViewField(headerName = "送货单号")
    private String sendBillNo;

    @ViewField(headerName = "入账时间")
    private String accDate;

    @ViewField(headerName = "制单时间")
    private String createTime;

    @ViewField(headerName = "制单人")
    private String creator;

    @ViewField(headerName = "采购订单号")
    private String purchBillNo;

    @ViewField(headerName = "审核时间")
    private String auditTime;

    @ViewField(headerName = "审核人")
    private String auditMan;

    @ViewField(headerName = "修改时间")
    private String updateTime;

    @ViewField(headerName = "修改人")
    private String updater;

    @ViewField(headerName = "提交时间")
    private String submitTime;

    @ViewField(headerName = "提交人")
    private String submitMan;

    @ViewField(headerName = "是否冲红")
    private String reversalBillSignDesc;

    @ViewField(headerName = "原单号")
    private String originBillNo;

    @ViewField(headerName = "商品编码")
    private String skuCode;

    @ViewField(headerName = "商品名称")
    private String skuName;

    @ViewField(headerName = "商品条码")
    private String barcode;

    @ViewField(headerName = "商品货号")
    private String goodsNo;

    @ViewField(headerName = "商品分类编码")
    private String categoryCode;

    @ViewField(headerName = "商品分类名称")
    private String categoryName;

    @ViewField(headerName = "商品规格")
    private String skuModel;

    @ViewField(headerName = "单位")
    private String basicUnit;

    @ViewField(headerName = "整件单位")
    private String wholeUnit;

    @ViewField(headerName = "包装率")
    private BigDecimal unitRate;

    @ViewField(headerName = "税率")
    private BigDecimal inputTaxRate;

    @ViewField(headerName = "整件数量")
    private BigDecimal wholeQty;

    @ViewField(headerName = "零头数量")
    private BigDecimal oddQty;

    @ViewField(headerName = "数量")
    private BigDecimal acceptQty;

    @ViewField(headerName = "单价")
    private String purchPrice;

    @ViewField(headerName = "税金")
    private String purchTax;

    @ViewField(headerName = "零售单价")
    private String salePrice;

    @ViewField(headerName = "零售金额")
    private String saleMoney;

    @ViewField(headerName = "效期批号")
    private String periodBatchNo;

    @ViewField(headerName = "效期条码")
    private String periodBarcode;

    @ViewField(headerName = "生产日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate productDate;

    @ViewField(headerName = "过期日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate expireDate;

    @ViewField(headerName = "备注")
    private String remark;
}
