package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListOrderDetailsReq {

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "部门商品行号")
    private Long pinsideId;
}
