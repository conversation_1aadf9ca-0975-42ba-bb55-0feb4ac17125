package com.meta.supplychain.entity.dto.pms.req.addReduce;

import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订货申请单追加追减订单分配")
public class PmsOrderAllocateSubmitReq extends PmsOrderAllocateReq{

    @Schema(description = "全部可转采与转采购信息回填---追加追减配转采使用")
    private List<PmsDemandDeliveryToPurchReq> deliveryToPurchParamDTO;

}
