package com.meta.supplychain.enums.common;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品类型 0.普通商品 1.附赠商品 2附赠赠品
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "商品类型",code = "SkuTypeEnum")
public enum SkuTypeEnum implements VerifiableEnum<Integer> {

    NORMAL(0,"普通商品"),
    GIFT(1,"附赠商品"),
    COMP_GIFT(2,"附赠商品");
    private Integer code;

    private String desc;

    public static SkuTypeEnum getByCode(Integer code) {
        SkuTypeEnum[] values = values();
        for (SkuTypeEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
