package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 供应商预约单主表
 * <AUTHOR>
 * @date 2025-4-20 20:45:32
 */
@TableName(value ="pms_appointment_bill")
@Getter
@Setter
@ToString
public class PmsAppointmentBillPO extends BaseEntity {

    private Long id;

    /**
     * 预约单据号
     */
    private String billNo;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 操作来源1-RMC,2SCM
     */
    private Integer opSource;

    /**
     * 订单截止日期开始,yyyy-MM-dd
     */
    private LocalDate startValidityTime;

    /**
     * 订单截止日期结束,yyyy-MM-dd
     */
    private LocalDate endValidityTime;

    /**
     * 订单送货日期开始,yyyy-MM-dd
     */
    private LocalDate startDeliverTime;

    /**
     * 订单送货日期结束,yyyy-MM-dd
     */
    private LocalDate endDeliverTime;

    /**
     * 预约类别1-采购,-1-采退
     */
    private Integer billDirection;

    /**
     * 是否直流0-非直流,1-直流
     */
    private Integer directSign;

    /**
     * 预约方式,1按商品,2按订单
     */
    private Integer appointmentMode;

    /**
     * 默认预约数量,1按0,2按剩余可约数量
     */
    private Integer defaultQtySign;

    /**
     * 停靠点编码
     */
    private String dockCode;

    /**
     * 停靠点名称
     */
    private String dockName;

    /**
     * 停靠点时段行号
     */
    private Integer dockTimeInsideId;

    /**
     * 承运方式,1自运、2托运
     */
    private Integer transportMode;

    /**
     * 承运人,默认供应商名称
     */
    private String transportMan;

    /**
     * 承运联系人
     */
    private String transportContacts;

    /**
     * 承运联系手机
     */
    private String transportMobile;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 计划到达时间,yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime planArrivalTime;

    /**
     * 计划停留时长(分钟)
     */
    private Integer planStayMinute;

    /**
     * 承运备注
     */
    private String transportRemark;

    /**
     * 预约备注
     */
    private String appointmentRemark;

    /**
     * 状态,1草稿，2已提交，3已作废
     */
    private Integer status;

    /**
     * 附件名称与地址,json格式[{"name":"","url":""}]
     */
    private String attachmentUrl;

    /**
     * 提交人code
     */
    private String submitManCode;

    /**
     * 提交人姓名
     */
    private String submitManName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 作废人code
     */
    private String cancelManCode;

    /**
     * 作废人名称
     */
    private String cancelManName;

    /**
     * 作废时间
     */
    private LocalDateTime cancelTime;

    /** 逻辑删除状态 1:删除 0:正常 */
    @TableLogic
    private Integer delFlag;

    private Long tenantId;
} 