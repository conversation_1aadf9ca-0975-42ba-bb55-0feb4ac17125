package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 差异处理单创建请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "差异处理单创建请求")
public class WdShipAccDiffBillCreateReq {

    @Schema(description = "验收单号")
    @NotBlank(message = "验收单号不能为空")
    private String acceptBillNo;

    @Schema(description = "部门编码")
    @NotBlank(message = "部门编码不能为空")
    private String deptCode;

    @Schema(description = "仓库编码")
    @NotBlank(message = "仓库编码不能为空")
    private String whCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "审核差异明细")
    @NotEmpty(message = "审核差异明细不能为空")
    private List<AuditDiffDetail> diffDetails;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "差异明细")
    public static class AuditDiffDetail {

        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        private String skuCode;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "批次号")
        private String batchNo;

        @Schema(description = "差异数量")
        @NotNull(message = "差异数量不能为空")
        private BigDecimal diffQty;

        @Schema(description = "差异原因")
        @NotBlank(message = "差异原因不能为空")
        private String diffReason;

        @Schema(description = "备注")
        private String remark;
    }
}