package com.meta.supplychain.enums.wds;

import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配送发货单类型枚举
 * 配送价模式 配送价取价模式0 配送价 1订单价 2成本价 3加价率
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "配送单取价策略枚举",code = "WDShipPricePolicyEnum")
public enum WDShipPricePolicyEnum implements StandardEnum<Integer> {

    SHIP_PRICE(0, "实时配送价"),
    ORDER_PRICE(1, "订单价格"),
    COST_PRICE(2, "成本价"),
    MAKEUP_PRICE(3, "加价率");

    private final Integer code;
    private final String desc;

}
