package com.meta.supplychain.entity.dto.pms.resp.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购订单明细基本信息
 */
@Schema(description = "采购订单明细基本信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDetailBasicDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "采购数量")
    private BigDecimal purchQty;

    /**
     * 已预约数量
     */
    private BigDecimal appointmentQty;
} 