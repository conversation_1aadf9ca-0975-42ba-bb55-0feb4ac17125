package com.meta.supplychain.entity.dto.md.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 15:11
 **/
@Schema(description = "追加追减商品部门表")
@Data
public class MdAddReduceGoodsDeptDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "sc_md_add_reduce_goods.id")
    private Long addReduceGoodsId;

    @Schema(description = "部门/店组编码")
    private String code;

    @Schema(description = "部门/店组名称")
    private String name;

    @Schema(description = "部门类型,1部门,2店组群")
    private Integer deptType;

}
