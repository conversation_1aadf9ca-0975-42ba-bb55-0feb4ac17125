package com.meta.supplychain.entity.dto.pms.req.deliverytopurch;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname DeliveryDetailReq
 * @Dscription TODO
 * @DATE 2025/6/3 17:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryDetailReq {

    @Schema(description = "配送部门名称")
    private String whName;
    @Schema(description = "配送部门编码")
    private String whCode;
    @Schema(description = "入货部门编码")
    private String inDeptCode;
    @Schema(description = "入货部门名称")
    private String inDeptName;
    @Schema(description = "时间类型 0 创建时间 1 审核时间 2 作废时间")
    private Integer timeType;
    @Schema(description = "开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginTime;
    @Schema(description = "结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;
    @Schema(description = "需求批次 模糊匹配")
    private String requireBatch;
    @Schema(description = "订货属性编码")
    private String orderAttributeCode;
    @Schema(description = "订货属性编码列表")
    private List<String> orderAttributeCodeList;
    @Schema(description = "有效日期  yyyy-MM-dd")
    private String validDate;
    @Schema(description = "匹配配送订单到店/送货日期  yyyy-MM-dd")
    private String toStoreDate;
    @Schema(description = "配送单据号 模糊匹配")
    private String billNo;
    @Schema(description = "管理分类项编码")
    private String manageCategoryCode;
}
