package com.meta.supplychain.enums.wds;


import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 责任方:0 配送中心 |1 收货门店
 *
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "差异单状态枚举",code = "WDAcceptDiffOwnerModeEnum")
public enum WDAcceptDiffOwnerModeEnum  implements VerifiableEnum<Integer> {

    OWNER_MODE_DELIVERY_CENTER(0, "配送中心"),
    OWNER_MODE_RECEIVE_STORE(1, "收货门店");

    private final Integer code;
    private final String desc;
}
