package com.meta.supplychain.entity.dto.goods.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> cat
 * @date 2024/4/12 14:41
 */
@Builder
@Data
@Getter
@Setter
public class GoodsSearchInfo {

    /**
     * 是否主商品（0：否 1：是）
     */
    private String mainSkuFlag;

    private String warnDays;
    private String validityCodeRule;
    private String unit;
    private String supplierId;
    private String spuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * SKU编码
     */
    private String skuCode;
    /**
     * sku货号
     */
    private String goodsNo;
    private String shelfLifeDays;
    private Long sendFlag;
    private String salePreDays;
    /**
     * 参考零售价
     */
    private String referPrice;
    /**
     * 参考进价
     */
    private String purchasePrice;
    private String recAdvDays;
    private String productDateRule;
    private String skuOperationModel;
    private String externalSpuCode;
    private String externalSkuCode;
    private Integer expiryDateControlFlag;
    private String expirateDateRule;
    private Long skuCooperatorId;
    private String categoryName;
    private String skuCategoryCodeOffLine;
    private String skuBrandName;
    private String skuBrandCodeOffLine;
    private String batchNumRule;
    private String skuBarCode;
    /**
     * 进项税率
     */
    private String inputTaxRate;
    /**
     * 销项税率
     */
    private String outputTaxRate;


    /**
     * 商品类型（1：单规格 2：多规格 3：组合）
     */
    private Integer goodsType;
    /**
     * 组合类型（1：普通 2：多包装 3：散称标准份 4：散称主子码）
     */
    private Integer combType;

    /**
     * 规格型号
     */
    private String skuSpecModel;

    /**
     * 价格上限
     */
    private String ceilingPrice;

    /**
     * 价格下限
     */
    private String floorPrice;

    /**
     * 产地
     */
    private String producingArea;
}
