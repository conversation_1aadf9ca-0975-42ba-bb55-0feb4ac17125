package com.meta.supplychain.convert.pms;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillGoodsDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillGoodsPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商预约单关联商品明细转换器
 * <AUTHOR>
 */
@Mapper
public interface PmsAppointmentBillGoodsConvert extends StandardEnumConvert {
    
    PmsAppointmentBillGoodsConvert INSTANCE = Mappers.getMapper(PmsAppointmentBillGoodsConvert.class);

    /**
     * PO转DTO
     */
    PmsAppointmentBillGoodsDTO convertPo2Dto(PmsAppointmentBillGoodsPO po);

    /**
     * PO列表转DTO列表
     */
    List<PmsAppointmentBillGoodsDTO> convertPo2DtoList(List<PmsAppointmentBillGoodsPO> poList);

    /**
     * DTO转PO - 忽略BaseDTO字段和id字段
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createCode", ignore = true),
            @Mapping(target = "createUid", ignore = true),
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateUid", ignore = true),
            @Mapping(target = "updateCode", ignore = true),
            @Mapping(target = "updateName", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PmsAppointmentBillGoodsPO convertDto2Po(PmsAppointmentBillGoodsDTO dto);

    /**
     * DTO列表转PO列表 - 忽略BaseDTO字段和id字段
     */
    List<PmsAppointmentBillGoodsPO> convertDto2PoList(List<PmsAppointmentBillGoodsDTO> dtoList);

    /**
     * 复制
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createCode", ignore = true),
            @Mapping(target = "createUid", ignore = true),
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateUid", ignore = true),
            @Mapping(target = "updateCode", ignore = true),
            @Mapping(target = "updateName", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "refPurchBill", ignore = true),
            @Mapping(target = "purchList", ignore = true)
    })
    PmsAppointmentBillGoodsDTO copyPlainProperty(PmsAppointmentBillGoodsDTO po);
}