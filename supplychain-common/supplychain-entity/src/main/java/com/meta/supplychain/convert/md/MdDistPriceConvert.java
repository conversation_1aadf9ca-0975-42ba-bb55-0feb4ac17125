package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceDTO;
import com.meta.supplychain.entity.dto.md.resp.MdDistPriceResponseDTO;
import com.meta.supplychain.entity.po.md.MdDistPricePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 配送价格对象转换器
 */
@Mapper
public interface MdDistPriceConvert {

    MdDistPriceConvert INSTANCE = Mappers.getMapper(MdDistPriceConvert.class);

    MdDistPriceResponseDTO po2dto(MdDistPricePO po);

    MdDistPriceDTO copyDto(MdDistPriceDTO origin);
} 