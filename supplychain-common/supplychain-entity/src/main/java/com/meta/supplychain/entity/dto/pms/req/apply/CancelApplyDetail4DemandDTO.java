package com.meta.supplychain.entity.dto.pms.req.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 批量订货申请单操作请求
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "批量作废需求单响应订货申请明细")
public class CancelApplyDetail4DemandDTO implements Serializable {

    /**
     * 生成的申请单号
     */
    @Schema(description = "申请单号")
    private String billNo;

    @Schema(description = "商品编码")
    private String skuCode;

    /**
     * 单内序号
     */
    @Schema(description = "单内序号")
    private Long insideId;


}
