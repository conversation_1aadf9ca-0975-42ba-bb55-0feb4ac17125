package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * 采购计划单数量修改请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PurchasePlanBillNumChangeReq {

    @Schema(description = "采购计划单号")
    @NotBlank(message = "采购计划单号不能为空")
    private String billNo;

    @Schema(description = "修改信息")
    @NotEmpty(message = "修改信息不能为空")
    private List<PurchasePlanBillNumDetailChangeReq> goodsList;

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PurchasePlanBillNumDetailChangeReq {

        @Schema(description = "商品类型")
        @NotNull(message = "商品类型不能为空")
        private Integer skuType;

        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        private String skuCode;

        @Schema(description = "修改计划采购数量 传正数")
        @NotNull(message = "修改计划采购数量不能为空")
        private BigDecimal updateQty;

        @Schema(description = "修改类型 1增加 2减少")
        @NotNull(message = "修改类型不能为空")
        private Integer updateType;
    }
}
