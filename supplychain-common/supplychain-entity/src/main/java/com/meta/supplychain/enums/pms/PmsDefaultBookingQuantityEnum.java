package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 默认预约数量枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "默认预约数量", code = "pmsDefaultBookingQuantityEnum")
public enum PmsDefaultBookingQuantityEnum implements VerifiableEnum<Integer> {

    BY_ZERO(1, "按0"),
    BY_REMAINING(2, "按剩余可约数量");

    private final Integer code;
    private final String desc;
} 