package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@TableName(value ="pms_dist2purch_supplier_record")
@Getter
@Setter
@ToString
public class PmsDist2purchSupplierRecordPO {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 租户号 */
    private Long tenantId;

    /** 配送部门编码 */
    private String distDeptCode;

    /** 配送部门名称 */
    private String distDeptName;

    /** 停靠点编码 */
    private String dockCode;

    /** 停靠点名称 */
    private String dockName;

    /** 商品编码 */
    private String skuCode;

    /** 商品名称 */
    private String skuName;

    /** 供应商编码 */
    private String supplierCode;

    /** 供应商名称 */
    private String supplierName;

    /** 是否最近供应商 0-否，1-是 */
    private Integer lastSign;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 最后更新时间 */
    private LocalDateTime updateTime;

    @Schema(description = "分组字段,服务端处理")
    @TableField(exist = false)
    private String uniKey;

    //`dist_dept_code`,`dock_code`,`sku_code`,`supplier_code`
    public String getUniKey() {
        StringBuffer sb = new StringBuffer();
        sb.append(distDeptCode).append("_").append(dockCode).append("_").append(skuCode).append("_").append(supplierCode);
        uniKey = sb.toString();
        return uniKey;
    }
}