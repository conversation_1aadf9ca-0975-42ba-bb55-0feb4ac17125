package com.meta.supplychain.entity.dto.pms.req.billconvert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 11:01
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillConvertDeptGoodsReq {
    @Schema(description = "单内序号,单据内唯一")
    private Long insideId;

    @Schema(description = "订货部门编码")
    private String deptCode;

    @Schema(description = "订货部门名称")
    private String deptName;

    @Schema(description = "出货途径0.采购 1.配送")
    private Integer shippingWay;

    @Schema(description = "送货方式 0-到店，1-到客户")
    private Integer sendMode;


    @Schema(description = "直流标识,直流标志 0-非直流 1-直流")
    private Integer directSign;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品类型,1主品,2赠品")
    private Integer goodsType;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "全路径品类编码,英文逗号分割")
    private String categoryCodeAll;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMode;

    @Schema(description = "计量属性 0：普通 1：计量 2：称重")
    private Integer uomAttr;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品规格")
    private String skuModel;

    @Schema(description = "销项税率，13%存13")
    private BigDecimal outputTaxRate;

    @Schema(description = "进项税率，13%存13")
    private BigDecimal inputTaxRate;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "商品总数量")
    private BigDecimal qty;

    @Schema(description = "商品总金额")
    private BigDecimal money;

    @Schema(description = "商品总整件数量")
    private BigDecimal wholeQty;

    @Schema(description = "商品总税金")
    private BigDecimal tax;

    //普通配送： 出货方-配送1个
    //直流：出货方-配送1个，出货方-采购1个
    //普通配送：配转采，出货方-配送1个，出货方-采购多个
    //普通采购：出货方-配送0，出货方-采购多个

    @Schema(description = "配送出货方")
    private List<BillConvertDeliveryShipperReq> deliveryShipperList;

    @Schema(description = "采购出货方")
    private List<BillConvertPurchShipperReq> purchShipperList;
}
