package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Setter
@Getter
@Data
public class DeptGroupTree {

    /**
     * id
     */
    private Long id;

    /**
     * 节点唯一编码
     */
    private String tid;

    /**
     * 数据Code(店组群或部门编码)
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 店组群分类项编码
     */
    private String groupCode;

    /**
     * 店组群分类项名称
     */
    private String groupName;

    /**
     * 数据节点类型 1组织机构 2员工 3部门 4配送 5店组群 6渠道分类
     */
    private Integer type;

    /**
     * 是否叶子节点 0否 1是
     */
    private Integer isLeaf;

    /**
     * 父id
     */
    private Integer pid;

    /**
     * 父缓存key
     */
    private String pCacheKey;

    /**
     * 子节点
     */
    private List<DeptGroupTree> child;

}
