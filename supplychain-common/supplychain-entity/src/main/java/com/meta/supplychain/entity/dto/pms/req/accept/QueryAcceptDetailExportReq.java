package com.meta.supplychain.entity.dto.pms.req.accept;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 验收单明细查询请求
 */
@Data
@Schema(description = "验收单明细查询请求")
public class QueryAcceptDetailExportReq extends PageParams {
    /**
     * 单据号
     */
    @Schema(description = "验收单号")
    @NotNull(message = "验收单号不能为空")
    private List<String> billNos;
}
