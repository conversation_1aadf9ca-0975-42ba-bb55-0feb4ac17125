package com.meta.supplychain.convert.wds;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryFulfillDetailPO;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillCreateDetail;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillCreateParams;
import com.meta.supplychain.entity.dto.wds.resp.WdDeliveryBillDetailResult;
import com.meta.supplychain.entity.dto.wds.resp.WdDeliveryBillExcelView;
import com.meta.supplychain.entity.dto.wds.resp.WdDeliveryBillResp;
import com.meta.supplychain.entity.dto.wds.resp.WdDeliveryFulfillDetailResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DeliveryOrderBillConvert  extends StandardEnumConvert {
    DeliveryOrderBillConvert MAPPER = Mappers.getMapper(DeliveryOrderBillConvert.class);


    @Mapping(target = "deliveryType", source = "sendMode")
    @Mapping(target = "totalTaxMoney", expression = "java(formatMoney(result.getTotalTaxMoney()))")
    @Mapping(target = "totalTax", expression = "java(formatMoney(result.getTotalTax()))")
    WdDeliveryBillResp convertPo2Vo(WdDeliveryBillPO result);


    List<WdDeliveryBillResp> convertPo2VoList(List<WdDeliveryBillPO> poList);


    // Convert to WdDeliveryBillPO
    @Mapping(target = "id", ignore = true)
    WdDeliveryBillPO convertToDeliveryBillPO(WdDeliveryBillCreateParams params);

    // Convert to List<WdDeliveryBillDetailPO>
    @Mapping(target = "id", ignore = true)
    WdDeliveryBillDetailPO convertToDeliveryBillDetailPO(WdDeliveryBillCreateDetail detail);


    List<WdDeliveryBillDetailPO> convertToDeliveryBillDetailPOList(List<WdDeliveryBillCreateDetail> detailList);

    /**
     * 实体到视图
     *
     * @param detail
     * @return
     */
    WdDeliveryBillDetailResult convertDetail2VO(WdDeliveryBillDetailPO detail);


    List<WdDeliveryBillDetailResult> convertDetailList2VO(List<WdDeliveryBillDetailPO> detailList);


    @Mapping(target = "billTypeDesc", expression = "java(convertToDesc(\"WDDeliveryOrderTypeEnum\", result.getBillType()))")
    @Mapping(target = "billDirectionDesc", expression = "java(convertToDesc(\"WDDeliveryOrderDirectionEnum\", result.getBillDirection()))")
    @Mapping(target = "directSignDesc", expression = "java(convertToDesc(\"YesOrNoEnum\", result.getDirectSign()))")
    @Mapping(target = "billSourceDesc", expression = "java(convertToDesc(\"WDDeliveryOrderSourceEnum\", result.getBillSource()))")
    @Mapping(target = "acceptSignDesc", expression = "java(convertToDesc(\"WDDeliveryOrderAcceptSignEnum\", result.getAcceptSign()))")
    @Mapping(target = "deliveryType", expression = "java(convertToDesc(\"WDDeliveryTypeEnum\", result.getDeliveryType()))")
    @Mapping(target = "statusDesc", expression = "java(convertToDesc(\"WDDeliveryOrderBillStatusEnum\", result.getStatus()))")
    @Mapping(source = "createTime", target = "createTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "cancelTime", target = "cancelTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "approveTime", target = "approveTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    WdDeliveryBillExcelView convertDetail2View(WdDeliveryBillResp result);


    List<WdDeliveryBillExcelView> convertDetail2ViewList(List<WdDeliveryBillResp> results);



    WdDeliveryFulfillDetailResult convertDetail2FulfillDetailVO(WdDeliveryFulfillDetailPO detail);

    List<WdDeliveryFulfillDetailResult> convertDetail2FulfillDetailVOList(List<WdDeliveryFulfillDetailPO> detailList);
}
