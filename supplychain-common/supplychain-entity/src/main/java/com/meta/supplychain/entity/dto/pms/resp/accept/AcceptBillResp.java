package com.meta.supplychain.entity.dto.pms.resp.accept;


import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname AcceptBillResp
 * @Dscription TODO
 * @DATE 2025/5/15 16:49
 */
@Data
public class AcceptBillResp {

    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;

    /**
     * 验收单号
     */
    @Schema(description="验收单号")
    private String billNo;

    /**
     * 采购订单号
     */
    @Schema(description="采购订单号")
    private String purchBillNo;

    /**
     * 单据状态 0-草稿 1-待审核 2-已审核 3-作废
     */
    @Schema(description="单据状态 0-草稿 1-待审核 2-已审核 3-作废")
    private Integer status;

    /**
     * 单据方向 1.验收，-1.退货
     */
    @Schema(description="单据方向 1.验收，-1.退货")
    private Integer billDirection;

    /**
     * 验收类型 0-普通验收 1-联营验收
     */
    @Schema(description="验收类型 0-普通验收 1-联营验收")
    private Integer type;

    /**
     * 单据来源 0 普通 1 批销 2 大数据 3 朝批
     */
    @Schema(description="单据来源 0 普通 1 批销 2 大数据 3 朝批")
    private Integer billSource;
    /**
     * 单据来源描述
     */
    @Schema(description="单据来源描述")
    private String billSourceDesc;

    /**
     * 购进类型 0-直配 1-代配
     */
    @Schema(description="购进类型 0-直配 1-代配")
    private Integer purchType;

    /**
     * 部门编码
     */
    @Schema(description="部门编码")
    private String deptCode;

    /**
     * 部门名称
     */
    @Schema(description="部门名称")
    private String deptName;

    /**
     * 采购类型 0-门店采购，1-配送采购
     */
    @Schema(description="采购类型 0-门店采购，1-配送采购")
    private Integer billType;

    /**
     * 储位编码
     */
    @Schema(description="储位编码")
    private String locationCode;

    /**
     * 储位名称
     */
    @Schema(description="储位名称")
    private String locationName;

    /**
     * 供应商编码
     */
    @Schema(description="供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description="供应商名称")
    private String supplierName;

    /**
     * 直发供应商编码
     */
    @Schema(description="直发供应商编码")
    private String directSupplierCode;

    /**
     * 直发供应商名称
     */
    @Schema(description="直发供应商名称")
    private String directSupplierName;

    /**
     * 直流标志 0-非直流 1-直流
     */
    @Schema(description="直流标志 0-非直流 1-直流")
    private Integer directSign;

    /**
     * 直流部门编码
     */
    @Schema(description="直流部门编码")
    private String directDeptCode;

    /**
     * 当前是否冲红单0,否1是
     */
    @Schema(description="当前是否冲红单0,否1是")
    private Integer reversalBillSign;

    /**
     * 冲红标志 0,否1是
     */
    @Schema(description="冲红标志 0,否1是")
    private Integer reversalFlag;

    /**
     * 送货单号
     */
    @Schema(description="送货单号")
    private String sendBillNo;

    /**
     * 原单号
     */
    @Schema(description="原单号")
    private String originBillNo;

    /**
     * 冲红单号
     */
    @Schema(description="冲红单号")
    private String reversalBillNo;

    /**
     * 合同号
     */
    @Schema(description="合同号")
    private String contractNo;

    /**
     * 补签序号
     */
    @Schema(description="补签序号")
    private String repairSignId;

    /**
     * 转码单单号
     */
    @Schema(description="转码单单号")
    private String tscBillNo;

    /**
     * 被修正的验收单单号
     */
    @Schema(description="被修正的验收单单号")
    private String amendAcceptBillNo;

    /**
     * 验收数量
     */
    @Schema(description="验收数量")
    private BigDecimal totalQty;

    /**
     * 金额（含税） 单位元
     */
    @Schema(description="金额（含税） 单位元")
    private BigDecimal totalMoney;

    /**
     * 管理分类编码
     */
    @Schema(description="管理分类编码")
    private String manageCategoryCode;

    /**
     * 管理分类名称
     */
    @Schema(description="管理分类名称")
    private String manageCategoryName;

    /**
     * 管理分类项编码
     */
    @Schema(description="管理分类项编码")
    private String manageCategoryClass;

    /**
     * 核算单位编码
     */
    @Schema(description="核算单位编码")
    private String accCode;

    /**
     * 入账时间
     */
    @Schema(description="入账时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate accDate;

    /**
     * 追加截止日期
     */
    @Schema(description="追加截止日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate appendValidity;

    /**
     * 打印次数
     */
    @Schema(description="打印次数")
    private Long printCount;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 供应商确认状态 0-未确认 1-已确认
     */
    @Schema(description="供应商确认状态 0-未确认 1-已确认")
    private Integer suppConfirmStatus;

    /**
     * 供应商确认人编码
     */
    @Schema(description="供应商确认人编码")
    private String suppConfirmCode;

    /**
     * 供应商确认人名称
     */
    @Schema(description="供应商确认人名称")
    private String suppConfirmName;

    /**
     * 供应商确认时间
     */
    @Schema(description="供应商确认时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime suppConfirmTime;

    /**
     * 提交人code
     */
    @Schema(description="提交人code")
    private String submitManCode;

    /**
     * 提交人名称
     */
    @Schema(description="提交人名称")
    private String submitManName;

    /**
     * 提交时间
     */
    @Schema(description="提交时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime submitTime;

    /**
     * 确认人code
     */
    @Schema(description="确认人code")
    private String confirmManCode;

    /**
     * 确认人名称
     */
    @Schema(description="确认人名称")
    private String confirmManName;

    /**
     * 确认时间
     */
    @Schema(description="确认时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime confirmTime;

    /**
     * 审核人code
     */
    @Schema(description="审核人code")
    private String auditManCode;

    /**
     * 审核人姓名
     */
    @Schema(description="审核人姓名")
    private String auditManName;

    /**
     * 审核时间
     */
    @Schema(description="审核时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime auditTime;

    /**
     * 作废人code
     */
    @Schema(description="作废人code")
    private String cancelManCode;

    /**
     * 作废人名称
     */
    @Schema(description="作废人名称")
    private String cancelManName;

    /**
     * 作废时间
     */
    @Schema(description="作废时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime cancelTime;

    /**
     * 供应商更新人编码
     */
    @Schema(description="供应商更新人编码")
    private String suppUpdateCode;

    /**
     * 供应商更新人名称
     */
    @Schema(description="供应商更新人名称")
    private String suppUpdateName;

    /**
     * 供应商更新时间
     */
    @Schema(description="供应商更新时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime suppUpdateTime;

    /**
     * 创建人ssoId
     */
    @Schema(description="创建人ssoId")
    private Long createUid;

    /**
     * 创建人编码
     */
    @Schema(description="创建人编码")
    private String createCode;

    /**
     * 创建人姓名
     */
    @Schema(description="创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    /**
     * 修改人ssoId
     */
    @Schema(description="修改人ssoId")
    private Long updateUid;

    /**
     * 修改人编码
     */
    @Schema(description="修改人编码")
    private String updateCode;

    /**
     * 修改人姓名
     */
    @Schema(description="修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @Schema(description="修改时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime updateTime;
    /**
     * WMS收货单号
     */
    @Schema(description = "WMS收货单号")
    private String wmsBillNo;
    /**
     * 删除标识
     */
    @Schema(description="删除标识")
    private Integer delFlag;

    @Schema(description="验收单明细")
    private List<AcceptDetailResp> detailList;

    /**
     * 批次供应商编码
     */
    @Schema(description="批次供应商编码")
    private String supplierCodeBatch;

    /**
     * 批次供应商名称
     */
    @Schema(description="批次供应商名称")
    private String supplierNameBatch;

    /**
     * 打印使用字段-部门地址wholeAddress
     */
    @Schema(description="打印使用字段-部门地址wholeAddress")
    private String deptAddress;

    /**
     * 打印使用字段-部门联系人managerUserName
     */
    @Schema(description="打印使用字段-部门联系人managerUserName")
    private String deptManage;

    /**
     * 打印使用字段-部门联系电话serviceHotline
     */
    @Schema(description="打印使用字段-部门联系电话serviceHotline")
    private String deptHotline;

    /**
     * 打印使用字段-供应商地址wholeAddress
     */
    @Schema(description="打印使用字段-供应商地址wholeAddress")
    private String supplierAddress;

    /**
     * 打印使用字段-供应商联系人linkname
     */
    @Schema(description="打印使用字段-供应商联系人linkname")
    private String supplierManage;

    /**
     * 打印使用字段-供应商联系电话phone
     */
    @Schema(description = "供应商联系电话")
    private String supplierHotline;
    /**
     * 门店经营模式 1 直营 2 加盟
     */
    @Schema(description = "门店经营模式 1 直营 2 加盟")
    private Integer deptOperateMode;
}
