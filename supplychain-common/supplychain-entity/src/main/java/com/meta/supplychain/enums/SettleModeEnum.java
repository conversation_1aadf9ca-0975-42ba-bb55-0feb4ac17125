package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算方式/模式: 1统一结算 2本地结算 3区域结算
 *
 * <AUTHOR> cat
 * @date 2024/8/29 17:04
 */
@Getter
@AllArgsConstructor
public enum SettleModeEnum  {

    COMMON_ACCOUNT(1,"1统一结算"),
    LOCAL_ACCOUNT(2,"2本地结算"),
    AREA_ACCOUNT(3,"3区域结算"),
    SELF_ACCOUNT(4,"4自采现结"),

    ;
    private Integer code;

    private String desc;

    public static SettleModeEnum getByCode(Integer code) {
        for (SettleModeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
