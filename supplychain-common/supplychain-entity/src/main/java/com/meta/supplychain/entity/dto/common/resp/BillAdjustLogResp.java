package com.meta.supplychain.entity.dto.common.resp;

import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购订单调整记录列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购订单调整记录列表")
public class BillAdjustLogResp {

    @Schema(description = "调整单流水号")
    private String adjustBillNo;

    @Schema(description = "单据类型 0-采购订单 1-配送订单")
    private Integer billType;

    @Schema(description = "调整来源 0-手工调整，1-追减调整")
    private Integer billSource;

    @Schema(description = "调整关联单据号")
    private String refBillNo;

    @Schema(description = "调整备注")
    private String remark;

    @Schema(description = "创建人ssoId")
    private Long createUid;

    @Schema(description = "创建人工号")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "流水记录")
    private List<BillAdjustLogPO> adjustLogList;

}
