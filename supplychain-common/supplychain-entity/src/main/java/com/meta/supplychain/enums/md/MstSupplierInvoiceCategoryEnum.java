package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商发票类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商发票类型", code = "mstSupplierInvoiceCategoryEnum")
public enum MstSupplierInvoiceCategoryEnum implements VerifiableEnum<Integer> {
    SPECIAL_INVOICE(1, "专票"),
    ORDINARY_INVOICE(2, "普票"),
    RECEIPT(3, "收据");

    private final Integer code;
    private final String desc;
}
