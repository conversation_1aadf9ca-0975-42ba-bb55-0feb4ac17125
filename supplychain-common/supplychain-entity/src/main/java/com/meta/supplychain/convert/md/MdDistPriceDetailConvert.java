package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceDetailDTO;
import com.meta.supplychain.entity.po.md.MdDistPriceDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 配送价格明细对象转换器
 */
@Mapper
public interface MdDistPriceDetailConvert {

    MdDistPriceDetailConvert INSTANCE = Mappers.getMapper(MdDistPriceDetailConvert.class);

    /**
     * PO转DTO
     */
    MdDistPriceDetailDTO po2dto(MdDistPriceDetailPO po);

    /**
     * DTO转PO
     */
    MdDistPriceDetailPO dto2po(MdDistPriceDetailDTO dto);
} 