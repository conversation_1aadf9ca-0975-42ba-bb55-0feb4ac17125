package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单调整返回
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单调整返回")
public class OrderAdjustResp {
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String billNo;

    /**
     * 失败原因
     */
    @Schema(description = "失败原因")
    private String failedReason;
}
