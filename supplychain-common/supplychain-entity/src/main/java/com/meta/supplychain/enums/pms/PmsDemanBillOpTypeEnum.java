package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 需求单操作类型 1:暂存,2:提交,3暂存并提交,4自动需求单提交
 */
@Getter
@AllArgsConstructor
public enum PmsDemanBillOpTypeEnum {
    STAGING(1, "暂存"),
    SUBMIT(2, "提交"),
    STAGING_SUMIT(3, "暂存并提交"),
    AUTO_SUBMIT(4, "自动需求单提交"),
    ;
    private Integer code;

    private String desc;


    public static PmsDemanBillOpTypeEnum getEnumByCode(Integer billSource) {
        PmsDemanBillOpTypeEnum[] values = values();
        for (PmsDemanBillOpTypeEnum value : values) {
            if(value.getCode().equals(billSource)) {
                return value;
            }
        }
        return null;
    }
}
