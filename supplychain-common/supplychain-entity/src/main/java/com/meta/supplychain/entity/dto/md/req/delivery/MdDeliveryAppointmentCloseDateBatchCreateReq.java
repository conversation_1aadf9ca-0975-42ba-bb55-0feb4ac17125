package com.meta.supplychain.entity.dto.md.req.delivery;

import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentCloseDateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Schema(description = "配送预约停止日期批量新增请求")
public class MdDeliveryAppointmentCloseDateBatchCreateReq {

    @NotBlank(message = "配送预约策略单据号不可为空")
    @Schema(description = "配送预约策略单据号")
    private String billNo;
    
    @Schema(description = "配送预约停止日期列表")
    @Valid
    private List<MdDeliveryAppointmentCloseDateDTO> closeDateList;
} 