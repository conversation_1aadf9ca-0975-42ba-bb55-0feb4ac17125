package com.meta.supplychain.entity.dto.pms.resp.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/19 16:28
 **/
@Data
public class PmsDemandDeliveryToPurchResultDTO {
    @Schema(description = "转采数据列表,转采类型type:,0不可转采, 1可转采")
    private List<PmsDemandDeliveryToPurchResp> deliveryToPurchList;

    @Schema(description = "配送转采购供应商信息,key=转采数据.insideId")
    private Map<Long,List<PmsDemandPurchShipperResp>> purchShipperResultMap;

    @Schema(description = "配送转采购配送来源明细信息,key=转采数据.insideId")
    private Map<Long,List<PmsDemandDeliveryToPurchDeliverySourceResp>> deliveryToPurchDeliverySourceMap;
}
