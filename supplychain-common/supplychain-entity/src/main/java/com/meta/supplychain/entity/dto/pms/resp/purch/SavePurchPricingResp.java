package com.meta.supplychain.entity.dto.pms.resp.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购计划单查询请求
 */
@Data
@Schema(description = "采购计划单查询请求")
public class SavePurchPricingResp {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "是否通过校验 true通过 false不通过")
    private Boolean isSuccess;

    @Schema(description = "失败原因")
    private String failedReason;

}
