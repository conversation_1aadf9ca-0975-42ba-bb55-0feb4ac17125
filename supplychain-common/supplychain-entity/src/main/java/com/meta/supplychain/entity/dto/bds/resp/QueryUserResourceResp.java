package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Setter
@Getter
@Data
public class QueryUserResourceResp {

    private Long userId;
    private List<ResourceList> resourceList;

    @Builder
    @Setter
    @Getter
    @Data
    public static class ResourceList {
        private String url;
        private String typeName;
        private Long type;
        private String resName;
        private String resCode;
        private Long id;
        private String icon;
        private String appCode;
    }
}
