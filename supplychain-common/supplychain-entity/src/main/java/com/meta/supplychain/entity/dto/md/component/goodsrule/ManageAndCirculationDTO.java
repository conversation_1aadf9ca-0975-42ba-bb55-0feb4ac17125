package com.meta.supplychain.entity.dto.md.component.goodsrule;

import com.meta.supplychain.entity.dto.goods.resp.ManageAndCirculationResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/05 11:06
 **/
@Data
public class ManageAndCirculationDTO {

    @Schema(description = "商品经营状态")
    private Map<String, ManageAndCirculationResp.WorkState> manageStateMap = new HashMap();

    @Schema(description = "商品流转途径")
    private Map<String, ManageAndCirculationResp.CirculationMode> circulationMap = new HashMap();
}
