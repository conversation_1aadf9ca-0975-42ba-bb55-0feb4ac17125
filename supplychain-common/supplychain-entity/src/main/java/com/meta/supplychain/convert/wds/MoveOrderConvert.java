package com.meta.supplychain.convert.wds;


import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.MoveBillDTO;
import com.meta.supplychain.entity.dto.wds.MoveLocBatchDetailDTO;
import com.meta.supplychain.entity.dto.wds.req.MoveBillSaveReq;
import com.meta.supplychain.entity.dto.wds.resp.MoveBillExcelView;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 实体转换
 */
@Mapper(componentModel = "spring")
public interface MoveOrderConvert extends StandardEnumConvert {

    MoveOrderConvert INSTANCE = Mappers.getMapper(MoveOrderConvert.class);


    MoveBillPO moveVO2PO(MoveBillSaveReq result);

    MoveBillDTO movePO2DTO(MoveBillPO result);

    MoveLocBatchDetailPO moveBillDetailDTO2PO(MoveLocBatchDetailDTO result);

    List<MoveLocBatchDetailDTO> moveBillDetailListPO2DTO(List<MoveLocBatchDetailPO> result);

    /**
     * 移位单导出视图转换
     * @param result
     * @return
     */
    @Mapping(source = "status", target = "status", qualifiedByName = "statusDesc")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmTime", target = "confirmTime")
    @Mapping(target = "moveType", expression = "java(convertToDesc(\"WDMoveTypeEnum\", result.getMoveType()))")
    MoveBillExcelView moveBillVO2View(MoveBillDTO result);
    /**
     * 合并编码和名称
     * @param code 编码
     * @param name 名称
     * @return 合并后的字符串
     */
    @Named("mergeCodeAndName")
    default String mergeCodeAndName(String code, String name) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.isBlank(name)) {
            return code;
        }
        return code + SysConstants.UNDERLINE_DELIMITER + name;
    }

    /**
     * 将状态枚举码转换为名称
     * @param status 状态枚举码
     * @return 状态名称
     */
    @Named("statusDesc")
    default String statusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        WDBillStatusEnum enumValue = WDBillStatusEnum.getInstance(status,WDBillStatusEnum.MOVE_STATUS_1.getTableType());
        return enumValue != null ? enumValue.getDesc() : status.toString();
    }
}
