package com.meta.supplychain.entity.dto.bds.req;

import lombok.*;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:39
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryUpDeptListReq {

    /**
     * 门店编码（二选一）
     */
    private String deptCode;

    /**
     * 店组群编码（二选一）
     */
    private Integer code;

    /**
     * 店组群分类编码
     */
    private String classCode;

    /**
     * 门店类型(1:门店2:配送)
     */
    private Integer deptType;

    /**
     * 营业状态  1 营业 2 停业 3 关闭
     */
    private Integer openStatus;

}
