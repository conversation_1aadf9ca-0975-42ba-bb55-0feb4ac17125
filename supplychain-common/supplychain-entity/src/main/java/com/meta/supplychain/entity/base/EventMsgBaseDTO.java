package com.meta.supplychain.entity.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单据传递的基础信息
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventMsgBaseDTO<T> {


    private String tenantId;

    /**
     *
     * 单号;
     *
     */
    private String billNo;

    /**
     * 单据操作类型
     * BillActionTypeEnum;
     *
     */
    private String billActionType;



    private T data;


}
