package com.meta.supplychain.entity.dto.goods.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class GoodsQuerySearchReq {
    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 查询数量（最大500）
     */
    private Integer limitNum;

    /**
     * 是否精确匹配（1：精确 2：模糊）
     */
    private String isExact;

    /**
     * 模糊检索（名称/编码/条码/货号/首字母）
     */
    private String keyWords;

    /**
     * 商品类型（1：单规格 2：多规格 3：组合商品）
     */
    private String goodsType;

    /**
     * SKU编码
     */
    private List<String> skuCodeList;

    /**
     * SPU编码
     */
    private List<String> spuCodeList;

    /**
     * 商品条码
     */
    private List<String> barCodeList;

    /**
     * 商品货号
     */
    private List<String> goodsNoList;
}
