package com.meta.supplychain.entity.dto.md.req.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExportQuery {

    @Schema(description = "导出字段中文名（Excel表头字段名）")
    private List<LinkedHashMap<String, String>> header;

    @Schema(description = "查询参数")
    private Map<String, Object> queryParams;

}
