package com.meta.supplychain.entity.dto.pms.resp.addReduce;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PmsAddReduceDetailResp {

    @Schema(description = "采购批次")
    private String purchBatchNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 计量属性（0：普通 1：计量 2：称重）
     */
    @Schema(description = "计量属性（0：普通 1：计量 2：称重）")
    private String uomAttr;

    @Schema(description = "商品（分类）编码")
    private String skuCode;

    @Schema(description = "商品（分类）名称")
    private String skuName;

    @Schema(description = "商品类型 1, 主品 2, 赠品")
    private Integer goodsType;

    /**
     * 单位，汉字文描，个，斤
     */
    @Schema(description="单位，汉字文描，个，斤")
    private String basicUnit;

    /**
     * 申请单单号
     */
    @Schema(description = "追加追减来源")
    private List<PmsAddReduceSource> addReduceSources;


    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "商品品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "整箱单位")
    private String wholeUnit;

    /**
     * 商品规格
     */
    @Schema(description = "商品规格")
    private String skuModel;

    /**
     * 进项税率
     */
    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "直流标志 0-否，1-是")
    private Integer directSign;

    /**
     * 需求追加追减数量
     */
    @Schema(description = "需求追加追减数量")
    private BigDecimal addReduceQty;

    /**
     * 调整后数量
     */
    @Schema(description = "调整后数量")
    private BigDecimal adjustQty;

    /**
     * 预计调整配送订单数量
     */
    @Schema(description = "预计调整配送订单数量")
    private BigDecimal expectedAdjustTransferQty;

    /**
     * 预计调整采购订单数量
     */
    @Schema(description = "预计调整采购订单数量")
    private BigDecimal expectedAdjustPurchQty;

    /**
     * 预计新生成配送订单数量
     */
    @Schema(description = "预计新生成配送订单数量")
    private BigDecimal transferNum;

    /**
     * 预计新生成采购订单数量
     */
    @Schema(description = "预计新生成采购订单数量")
    private BigDecimal purchNum;

    @Schema(description = "分配类型 0-更新 1-新增")
    private Integer allocateType;



}
