package com.meta.supplychain.enums.wds;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 单据状态枚举类
 *
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "退货收货单据状态",code = "RefundAcceptBillStatusEnum")
public enum RefundAcceptBillStatusEnum implements VerifiableEnum<Integer> {

    /**
     *
     *单据状态-1处理中 0草稿 1待收货 2已收货 9已作废
     */
    DELIVERY_STATUS_PROCESSING(-1, "处理中"),
//    DELIVERY_STATUS_DRAFT(0, "草稿"),
    DELIVERY_STATUS_PENDING_APPROVAL(1, "待收货"),
    DELIVERY_STATUS_APPROVED(2, "已收货"),
    DELIVERY_STATUS_CANCELED(9, "已作废");


    private final Integer code;
    private final String desc;


}
