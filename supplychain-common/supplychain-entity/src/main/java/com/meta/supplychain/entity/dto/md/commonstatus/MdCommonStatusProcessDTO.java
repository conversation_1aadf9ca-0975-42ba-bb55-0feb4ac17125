package com.meta.supplychain.entity.dto.md.commonstatus;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.supplychain.enums.md.MdCommonStatusBizTypeEnum;
import com.meta.supplychain.enums.md.MdCommonStatusModuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/14 15:50
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MdCommonStatusProcessDTO {
    /** 业务模块编码 */
    @Schema(description = "模块编码")
    private MdCommonStatusModuleEnum moduleCode;

    /** 业务模块名称 */
    @Schema(description = "模块名称")
    private String moduleName;

    /** 业务单据id */
    @Schema(description = "业务主键")
    private Long bizId;

    /** 业务单据编码 */
    @Schema(description = "业务编码")
    private String bizCode;

    /** 业务类型 */
    @Schema(description = "子模块名称")
    private MdCommonStatusBizTypeEnum bizType;

    /** 业务单据日期 */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime bizTime;

    /** 业务状态编码 */
    @Schema(description = "状态编码")
    private String statusCode;

    /** 业务状态名称 */
    @Schema(description = "部门名称")
    private String statusName;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 创建人工号 */
    private String createCode;

    /** 创建人ssoId */
    private Long createUid;

    /** 创建人姓名 */
    private String createName;

    /** 创建时间 */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private Date createTime;

    /** 修改人ssoId */
    private Long updateUid;

    /** 修改人工号 */
    private String updateCode;

    /** 修改人姓名 */
    private String updateName;

    /** 修改时间 */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private Date updateTime;
}
