package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "调整名称枚举",code = "PmsAdjustNameEnum")
public enum PmsAdjustNameEnum {

    DELIVER_DATE("deliverDate",  "送货日期"),
    VALIDITY_DATE("validityDate",  "有效日期"),
    CONTACT_MAN("contactMan", "联系人"),
    CONTACT_ADDR("contactAddr",  "联系地址"),
    CONTACT_TEL("contactTel",  "联系电话"),
    PURCH_REMARK("purchRemark", "备注"),
    REFUND_REASON("refundReason", "退货原因"),
    PURCH_PRICE("purchPrice",  "采购价格"),
    PURCH_QTY("purchQty",  "采购数量"),
    DELIVERY_PRICE("deliveryPrice",  "配送价格"),
    DELIVERY_QTY("purchQty",  "配送数量"),
    SEND_MODE("sendMode",  "送货方式"),

    ;

    private final String code;
    private final String desc;

    public static PmsAdjustNameEnum getEnumByCode(String code) {
        PmsAdjustNameEnum[] values = values();
        for (PmsAdjustNameEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
