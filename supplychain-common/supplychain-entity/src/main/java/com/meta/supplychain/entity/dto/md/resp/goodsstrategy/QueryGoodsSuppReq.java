package com.meta.supplychain.entity.dto.md.resp.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "配送订单")
@Getter
@Setter
@ToString
public class QueryGoodsSuppReq {

    @Schema(description = "部门编码列表")
    @NotEmpty(message = "部门编码列表不能为空")
    private List<String> deptCodeList;

    @Schema(description = "商品编码列表")
    @NotEmpty(message = "商品编码列表不能为空")
    private List<String> skuCodeList;

}
