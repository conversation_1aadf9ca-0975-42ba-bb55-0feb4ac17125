package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.contract.MdContractScopeDTO;
import com.meta.supplychain.entity.po.md.MdContractScopePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdContractScopeConvert {
    MdContractScopeConvert INSTANCE = Mappers.getMapper(MdContractScopeConvert.class);

    MdContractScopeDTO convertPo2Dto(MdContractScopePO mdContractScopePO);

    List<MdContractScopeDTO> convertPo2DtoList(List<MdContractScopePO> poList);
}
