package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandPurchShipperReq {
    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "单内序号,单据内全局唯一,配送转采购供应商与出货方-供应商内唯一")
    private Long insideId;

    @Schema(description = "上一级(pms_demand_dept_goods_detail)单内序号,前端生成，单据内唯一,convert_flag=1不填")
    private Long pinsideId;

    @Schema(description = "上上一级(pms_demand_goods_detail)单内序号,前端生成，单据内唯一,convert_flag=1不填")
    private Long goodsInsideId;

    @Schema(description = "需求配转采商品表单内序号,pms_demand_delivery_to_purch.inside_id,仅convert_flag=1有效")
    private Long deliveryToPurchInsideId;

    @Schema(description = "商品类型,0主品,1附赠赠品")
    private Integer goodsType;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;
    @Schema(description = "商品名称")
    private String skuName;
    @Schema(description = "直流标志 0-非直流 1-直流")
    private Integer directSign;
    @Schema(description = "促销期间价")
    private BigDecimal promotePeriodPrice;
    @Schema(description = "采购包装率")
    private BigDecimal purchUnitRate;

    @Schema(description = "合同进价")
    private BigDecimal contractPurchPrice;
    @Schema(description = "合同特供价")
    private BigDecimal contractSpecialPrice;
    @Schema(description = "最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)")
    private BigDecimal lastPurchPrice;
    @Schema(description = "采购金额")
    private BigDecimal purchMoney;
    @Schema(description = "采购税金")
    private BigDecimal purchTax;

    @Schema(description = "商品编码")
    private String skuCode;
    @Schema(description = "采购数量")
    private BigDecimal purchQty;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "供应商编码")
    private String supplierCode;
    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "最近一次供应商编码")
    private String lastSupplierCode;
    @Schema(description = "最近一次供应商名称")
    private String lastSupplierName;

    @Schema(description = "是否最近一次供应商编码,1是,0否")
    private Integer lastSupplierSign;

    //DemandConvertlEnum
    @Schema(description = "转单标识,0普通, 1配转采")
    private Integer convertFlag;
    @Schema(description = "订货部门编码")
    private String orderDeptCode;
    @Schema(description = "合同最高进价")
    private BigDecimal contractMaxPurchPrice;
    @Schema(description = "状态,0未选择, 1选中")
    private Integer status;
    @Schema(description = "是否主供应商,0否,1是")
    private Integer mainSupplierMode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;
    @Schema(description = "合同号")
    private String contractNo;
    @Schema(description = "采购价格")
    private BigDecimal purchPrice;

    @Schema(description = "整件数量")
    private BigDecimal purchWholeQty;

    @Schema(description = "零头数量")
    private BigDecimal purchOddQty;
}
