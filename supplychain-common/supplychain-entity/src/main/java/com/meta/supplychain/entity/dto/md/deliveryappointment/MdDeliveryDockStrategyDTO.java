package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.md.MdDeliveryDockConstraintRuleEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockStatusEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockTypeEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 供应商预约策略停靠点配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约策略停靠点配置")
public class MdDeliveryDockStrategyDTO extends BaseDTO {

    @NotNull(message = "操作类型不可为空")
    @Schema(description = "是否新增")
    private Boolean isCreateOpt;
    
    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "配送预约策略单号不可为空")
    @Schema(description = "配送预约策略单号")
    private String billNo;

    @NotBlank(message = "配送部门编码不可为空")
    @Schema(description = "配送部门编码")
    private String deptCode;

    @Schema(description = "配送部门名称")
    private String deptName;

    @NotBlank(message = "停靠点编码不可为空")
    @Schema(description = "停靠点编码")
    private String dockCode;

    @NotBlank(message = "停靠点名称不可为空")
    @Schema(description = "停靠点名称")
    private String dockName;

    @EnumValue(type = MdDeliveryDockTypeEnum.class, required = true, message = "停靠点类型不可为空")
    @Schema(description = "停靠点类型（1 不限；2 直流；3 收货；4 发货）")
    private Integer dockType;

    @EnumValue(type = MdDeliveryDockStatusEnum.class, required = true, message = "停靠点状态不可为空")
    @Schema(description = "状态（0 停用；1 启用）")
    private Integer status;

    @EnumValue(type = MdDeliveryDockConstraintRuleEnum.class, required = true, message = "停靠点限量规则不可为空")
    @Schema(description = "限量规则（0 不限；1 按整件数）")
    private Integer constraintRule;

    @Length(max = 300, message = "备注不可超过300个字符")
    @Schema(description = "备注")
    private String remark;

    @Valid
    @Schema(description = "部门列表")
    private List<MdDeliveryDockDeptDTO> dockDeptList;

    @Valid
    @Schema(description = "品类列表")
    private List<MdDeliveryDockGoodsCategoryDTO> dockCategoryList;

    @Valid
    @Size(min = 1, message = "时间段限额列表至少维护一条数据")
    @Schema(description = "时间段限额列表")
    private List<MdDeliveryDockLimitRuleDTO> dockLimitRuleList;
} 