package com.meta.supplychain.entity.dto.pms.demand;

import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/04 18:00
 **/
@Data
public class AutoApplyToDemandInsideIdDTO {
    /**
     * 需求单号
     */
    private String billNo;

    /**
     * 部门商品单内序号，全单据唯一
     */
    private Long deptGoodsInsideId = 0L;

    /**
     * 出货方-配送部门单内序号，全单据唯一
     */
    private Long deliveryInsideId = 0L;

    /**
     * 出货方-供应商单内序号，全单据唯一
     */
    private Long purchInsideId = 0L;

    /**
     * detailSourceRef单内序号，全单据唯一
     */
    private Long detailSourceRefInsideId = 0L;
}
