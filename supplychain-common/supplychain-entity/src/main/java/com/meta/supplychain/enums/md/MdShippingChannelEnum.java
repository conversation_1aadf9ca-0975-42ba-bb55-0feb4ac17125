package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出货途径枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "出货途径", code = "mdShippingChannelEnum")
public enum MdShippingChannelEnum implements VerifiableEnum<Integer> {

    PURCHASE(0, "采购"),
    DISTRIBUTION(1, "配送");

    private final Integer code;
    private final String desc;
}
