package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName add_reduce_log
 */
@TableName(value ="add_reduce_log")
@Data
public class AddReduceLogPO extends BaseEntity  implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务单据类型
     */
    private String billType;

    /**
     * 单据审核时间
     */
    private Date auditTime;

    /**
     * 订单号
     */
    private String billNo;

    /**
     * 单内序号
     */
    private Long insideId;

    /**
     * 单位比率，包装率
     */
    private BigDecimal purchUnitRate;

    /**
     * 订单数量
     */
    private BigDecimal orderQty;

    /**
     * 本次累计已调整数量
     */
    private BigDecimal adjustedQty;

    /**
     * 预计调整数量
     */
    private BigDecimal expectAdjustQty;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商品类型,1主品,2赠品
     */
    private Integer goodsType;

    /**
     * 商品（分类）编码
     */
    private String skuCode;

    /**
     * 商品（分类）名称
     */
    private String skuName;

    /**
     * 商品货号
     */
    private String goodsNo;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 单位，汉字文描，个，斤
     */
    private String basicUnit;

    /**
     * 单位比率，包装率
     */
    private BigDecimal unitRate;

    /**
     * 整箱单位
     */
    private String wholeUnit;

    /**
     * 商品规格
     */
    private String skuModel;

    /**
     * 进项税率
     */
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    private BigDecimal outputTaxRate;

    /**
     * 直流标志 0-否，1-是
     */
    private Integer directSign;

    /**
     * 需求追加追减数量
     */
    private BigDecimal addReduceQty;

    /**
     * 调整后数量
     */
    private BigDecimal adjustQty;
}