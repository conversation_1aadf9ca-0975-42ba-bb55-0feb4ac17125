package com.meta.supplychain.convert.wds;


import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.ShipBillWithAcceptInfoDTO;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBatchDetailCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WdShipAcceptBillCreateReq;
import com.meta.supplychain.entity.dto.wds.resp.*;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ShipAcceptConvert extends StandardEnumConvert {
    ShipAcceptConvert INSTANCE = Mappers.getMapper(ShipAcceptConvert.class);


    @Mapping(target = "createManCode", source = "createCode")
    @Mapping(target = "createManName", source = "createName")
    @Mapping(target = "billTypeName", expression = "java(convertToDesc(\"WDShipTypeEnum\", po.getBillType()))")
    @Mapping(target = "billSourceName", expression = "java(convertToDesc(\"WDShipSourceEnum\", po.getBillSource()))")
    @Mapping(target = "billDirectionName", expression = "java(convertToDesc(\"WDDeliveryOrderDirectionEnum\", po.getBillDirection()))")
    @Mapping(target = "directSignName", expression = "java(convertToDesc(\"YesOrNoEnum\", po.getDirectSign()))")
    @Mapping(target = "statusName", expression = "java(convertToDesc(\"WDShipAcceptBillStatusEnum\", po.getStatus()))")
    WdShipAcceptBillResp convertToResult(WdShipAcceptBillPO po);


    List<WdShipAcceptBillResp> convertToResultList(List<WdShipAcceptBillPO> poList);


    @Mapping(target = "createTime", source = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "confirmTime", source = "confirmTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "createMan", expression = "java(convertManCodeName(resp.getCreateManCode(), resp.getCreateManName()))")
    @Mapping(target = "confirmMan", expression = "java(convertManCodeName(resp.getConfirmManCode(), resp.getConfirmManName()))")
    ShipAcceptExcelView convertToExcelView(WdShipAcceptBillResp resp);

    List<ShipAcceptExcelView> convertToExcelViewList(List<WdShipAcceptBillResp> respList);


    WdShipAcceptBatchDetailResp convertToBatchDetailResult(WdShipAcceptBatchDetailPO po);


    List<WdShipAcceptBatchDetailResp> convertToBatchDetailResultList(List<WdShipAcceptBatchDetailPO> poList);
    
    /**
     * 将创建参数转换为PO对象
     *
     * @param createParams 创建参数
     * @return PO对象
     */
    WdShipAcceptBillPO convertToPO(WdShipAcceptBillCreateReq createParams);
    
    /**
     * 将批次明细创建参数转换为PO对象
     *
     * @param detailCreateParams 批次明细创建参数
     * @return 批次明细PO对象
     */
    WdShipAcceptBatchDetailPO convertToDetailPO(WdShipAcceptBatchDetailCreateReq detailCreateParams);
    
    /**
     * 将批次明细创建参数列表转换为PO对象列表
     *
     * @param detailCreateParamsList 批次明细创建参数列表
     * @return 批次明细PO对象列表
     */
    List<WdShipAcceptBatchDetailPO> convertToDetailPOList(List<WdShipAcceptBatchDetailCreateReq> detailCreateParamsList);


    @Mapping(target = "shipBillNo", source = "billNo")
    @Mapping(target = "remarkShip", source = "remark")
    @Mapping(target = "billNo", ignore = true)
    QueryShipBillForAcceptResp convertToQueryShipBillForAcceptResp(QueryShipBillResp queryShipBillResp);


    List<QueryShipBillForAcceptResp> convertToQueryShipBillForAcceptRespList(List<QueryShipBillResp> queryShipBillRespList);

    WdShipAcceptBatchDetailInlineResp convertToInlineResp(WdShipAcceptBatchDetailPO batchDetailResp);


    List<WdShipAcceptBatchDetailInlineResp> convertToInlineRespList(List<WdShipAcceptBatchDetailPO> batchDetailRespList);

    // ShipBatchDetailPO 转 WdShipAcceptBatchDetailPO
    @Mapping(target = "remarkShip", source = "remark")
    WdShipAcceptBatchDetailPO convertToShipAcceptBatchDetailPO(ShipBatchDetailPO batchDetailPO);

    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    @Mapping(target = "acceptPrice", source = "acceptPrice")
    @Mapping(target = "acceptTaxMoney", source = "acceptMoney")
    @Mapping(target = "acceptTax", source = "acceptTax")
    @Mapping(target = "acceptQty", source = "acceptQty")
    @Mapping(target = "shipPrice", source = "acceptPrice")
    @Mapping(target = "shipTaxMoney", source = "acceptMoney")
    @Mapping(target = "shipTax", source = "acceptTax")
    @Mapping(target = "shipQty", source = "acceptQty")
    WdShipAcceptBatchDetailPO convertToShipAcceptBatchDetailPO(WdRefundAcceptBatchDetailPO batchDetailPO);

    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    List<WdShipAcceptBatchDetailPO> convertToWdShipAcceptBatchDetailPOList(List<WdRefundAcceptBatchDetailPO> batchDetailPOList);


    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    @Mapping(target = "acceptPrice", source = "diffTaxPrice")
    @Mapping(target = "acceptQty", source = "approveQty")
    @Mapping(target = "shipPrice", source = "diffTaxPrice")
    @Mapping(target = "shipQty", source = "approveQty")
    WdShipAcceptBatchDetailPO convertDiff2ShipDetail(WdShipAccDiffBatchDetailPO batchDetailPO);

    // WdRefundAcceptBatchDetailPO 转 WdShipAcceptBatchDetailPO
    List<WdShipAcceptBatchDetailPO> convertDiff2ShipDetailList(List<WdShipAccDiffBatchDetailPO> batchDetailPOList);


    // ShipBillWithAcceptInfoDTO 转 QueryShipBillForAcceptResp
    QueryShipBillForAcceptResp convertDtoToQueryShipBillForAcceptResp(ShipBillWithAcceptInfoDTO shipBillWithAcceptInfoDTO);

    List<QueryShipBillForAcceptResp> convertDtoToQueryShipBillForAcceptRespList(List<ShipBillWithAcceptInfoDTO> shipBillWithAcceptInfoDTOList);

    @Mapping(source = "status", target = "status", qualifiedByName = "statusDesc")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "submitTime", target = "submitTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "updateTime", target = "updateTime")
    @Mapping(target = "billType", expression = "java(convertToDesc(\"WDShipTypeEnum\", result.getBillType()))")
    @Mapping(target = "totalShipTaxMoney", expression = "java(formatMoney(result.getTotalShipTaxMoney()))")
    ShipBillForAcceptExcelView shipBillVO2AcceptView(QueryShipBillForAcceptResp result);

    /**
     * 将状态枚举码转换为名称
     * @param status 状态枚举码
     * @return 状态名称
     */
    @Named("statusDesc")
    default String statusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        WDBillStatusEnum enumValue = WDBillStatusEnum.getInstance(status,WDBillStatusEnum.SHIP_STATUS_1.getTableType());
        return enumValue != null ? enumValue.getDesc() : status.toString();
    }
}
