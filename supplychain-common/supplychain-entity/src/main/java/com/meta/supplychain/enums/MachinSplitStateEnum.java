package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MachinSplitStateEnum {

    DRAFT(0, "草稿"),
    WAIT_AUDIT(1, "待审核"),
    AUDITED(2, "已审核"),
    CANCEL(3, "作废"),

    ;
    private Integer code;

    private String desc;

    public static MachinSplitStateEnum getEnumByCode(Integer code) {
        for (MachinSplitStateEnum value : values()) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
