package com.meta.supplychain.entity.dto.pms.resp.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/19 16:59
 **/
@Schema(description = "配转采门店配送来源明细")
@Data
public class PmsDemandDeliveryToPurchDeliverySourceResp {
    @Schema(description = "配转采商品单内序号")
    private Long deliveryToPurchInsideId;

    @Schema(description = "订货申请订单号")
    private String applyBillNo;

    @Schema(description = "订货申请行号")
    private Long applyInsideId;

    @Schema(description = "订货部门编码")
    private String deptCode;

    @Schema(description = "订货部门名称")
    private String deptName;

    @Schema(description = "客户编码")
    private String customerCode;

    @Schema(description = "来源客户名称")
    private String customerName;

    @Schema(description = "配送数量")
    private BigDecimal deliveryQty;
}
