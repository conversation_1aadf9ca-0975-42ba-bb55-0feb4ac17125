package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/22 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractGoodsQueryDTO {

    @Schema(description = "商品编码列表")
    private List<ContractGoods4DeptQueryDTO> skuList;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "是否取消 true取消 false不取消")
    private Boolean cancelFlag = false;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContractGoods4DeptQueryDTO {

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "部门编码")
        private List<String> deptCodeList;

        @Schema(description = "店组群编码")
        private List<String> groupCodeList;
    }
}
