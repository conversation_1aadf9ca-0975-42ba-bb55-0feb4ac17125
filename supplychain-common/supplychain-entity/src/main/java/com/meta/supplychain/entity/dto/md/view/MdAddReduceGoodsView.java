package com.meta.supplychain.entity.dto.md.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 14:59
 **/
@Schema(description = "追加追减商品设置")
@Data
public class MdAddReduceGoodsView {

    @ViewField(headerName = "商品编码")
    private String skuCode;

    @ViewField(headerName = "商品名称")
    private String skuName;

    @ViewField(headerName = "商品条码")
    private String barcode;

    @ViewField(headerName = "允许追加")
    private String isAllowMultiBillDesc;

    @ViewField(headerName = "允许追减")
    private String isAllowReduceBillDesc;

    @ViewField(headerName = "部门范围")
    private String deptScopeDesc;

    @ViewField(headerName = "创建人")
    private String creator;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "最后更新人")
    private String updater;

    @ViewField(headerName = "最后更新时间")
    private String updateTime;

}
