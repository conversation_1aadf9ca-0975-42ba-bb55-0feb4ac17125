package com.meta.supplychain.entity.dto.pms.req.purch;

import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsBillDirectionEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.util.List;

/**
 * 可预约采购订单查询请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "可预约采购订单查询请求")
public class QueryAbleAppointReq {
    private OpInfo operatorInfo = new OpInfo();

    @NotEmpty(message = "供应商编码不可为空")
    @Schema(description = "供应商编码")
    private List<String> supplierCodeList;

    @NotEmpty(message = "部门编码不可为空")
    @Schema(description = "部门编码")
    private List<String> deptCodeList;

    @EnumValue(type = PmsBillDirectionEnum.class, required = true, message = "非法的单据类别")
    @Schema(description = "单据类别（-1:采退，1:采购）枚举PmsBillDirectionEnum")
    private Integer billDirection;

    @EnumValue(type = YesOrNoEnum.class, required = true, message = "非法的直流订单标识")
    @Schema(description = "是否直流订单 0-非直流 1-直流 枚举YesOrNoEnum")
    private Integer directSign;

    @Schema(description = "订单开始有效日期")
    private LocalDate validityDateStart;

    @Schema(description = "订单结束有效日期")
    private LocalDate validityDateEnd;

    @Schema(description = "订单开始送货日期")
    private LocalDate deliverDateStart;

    @Schema(description = "订单结束送货日期")
    private LocalDate deliverDateEnd;

    @Schema(description = "采购订单号")
    private List<String> purchBillNoList;
}
