package com.meta.supplychain.entity.dto.md.req.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Schema(description = "合同列表分页查询请求")
@Data
public class MdContractQueryListReq {
    @Schema(description = "供应商编码")
    private List<String> supplierCodeList;

    @Schema(description = "合同开始日期 yyyy-MM-dd")
    private String startDate;

    @Schema(description = "合同结束日期 yyyy-MM-dd")
    private String endDate;

    @Schema(description = "合同状态,1草稿、2审核中、3待提交、4生效中、5待生效、6已失效、7已终止、8已过期、9已作废")
    private List<String> statusList;

    @Schema(description = "经营方式, J经销 D代销 L联营 Z租赁")
    private List<String> operateModeList;

    @Schema(description = "合同编号")
    private List<String> contractNoList;

    @Schema(description = "是否查询最大合同序号", defaultValue = "false")
    private Boolean maxSerialFlag = false;

}