package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "差异处理单批次明细响应")
public class WdShipAccDiffBatchDetailResp {

    /** 单内序号 */
    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "仓库编码")
    private String whCode;

    @Schema(description = "批次号")
    private String batchNo;

    /** 商品类型 0商品1附赠商品2附赠赠品 */
    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "是否效期 0否1是")
    private Integer periodFlag;

    @Schema(description = "计量属性（0-普通，2-称重）")
    private Integer uomAttr;

    /** 品牌编码 */
    @Schema(description = "品牌编码")
    private String brandCode;

    /** 品牌名称 */
    @Schema(description = "品牌名称")
    private String brandName;

    /** 品类编码 */
    @Schema(description = "品类编码")
    private String categoryCode;

    /** 品类名称 */
    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    /** 商品货号 */
    @Schema(description = "商品货号")
    private String goodsNo;

    /** 规格型号 */
    @Schema(description = "规格型号")
    private String skuModel;

    /** 单位 */
    @Schema(description = "计量单位")
    private String basicUnit;

    /** 整件包装率 */
    @Schema(description = "整件包装率")
    private BigDecimal packageRate;

    /** 整件单位 */
    @Schema(description = "整件单位")
    private String packageUnit;


    /** 配送数量     */
    @Schema(description = "配送数量")
    private BigDecimal shipQty;

    /** 配送单价 */
    @Schema(description = "配送单价")
    private BigDecimal shipPrice;
    /** 配送金额(含税) */
    @Schema(description = "配送金额(含税)")
    private BigDecimal shipTaxMoney;


    /** 收货数量 */
    @Schema(description = "收货数量")
    private BigDecimal acceptQty;

    /** 收货备注 */
    @Schema(description = "收货备注")
    private String accRemark;

    @Schema(description = "差异数量")
    private BigDecimal diffQty;

    /** 责任方:0拨出方|1拨入方 */
    @Schema(description = "责任方:0拨出方|1拨入方")
    private Integer ownerMode;

    /** 审核数量 */
    @Schema(description = "审核数量")
    private BigDecimal approveQty;

    @Schema(description = "差异原因")
    private String diffReason;

    @Schema(description = "审核备注")
    private String remark;




    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}