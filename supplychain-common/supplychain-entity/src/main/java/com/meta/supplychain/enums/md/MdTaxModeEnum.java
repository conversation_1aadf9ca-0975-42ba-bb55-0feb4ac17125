package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "税率方式", code = "taxModeEnum")
public enum MdTaxModeEnum implements VerifiableEnum<Integer> {
    FIXED_RATE(0, "固定税率"),
    PRODUCT_RATE(1, "商品税率");

    private final Integer code;
    private final String desc;
}
