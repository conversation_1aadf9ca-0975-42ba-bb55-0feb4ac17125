package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "自动生成需求单配送转采购规则", code = "mdDistToPurchRuleEnum")
public enum MdDistToPurchRuleEnum implements VerifiableEnum<Integer> {
    NO_TRANSFER(1, "不转采"),
    ALL_BY_RESPONSE_QTY(2, "所有商品按响应数量转采"),
    ONLY_STRATEGY_CONFIG(3, "只转策略配置商品按响应数量转采");

    private final Integer code;
    private final String desc;
} 