package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "停靠点类型枚举", code = "mdDeliveryDockStatusEnum")
public enum MdDeliveryDockStatusEnum implements VerifiableEnum<Integer> {
    ENABLE(1,"启用"),
    DISABLE(0,"停用");

    private final Integer code;
    private final String desc;
}
