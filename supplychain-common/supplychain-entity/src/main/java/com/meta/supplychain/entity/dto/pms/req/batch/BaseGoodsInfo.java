package com.meta.supplychain.entity.dto.pms.req.batch;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR> cat
 * @date 2024/8/2 15:29
 */
@Getter
@Setter
@Builder
public class BaseGoodsInfo {
    /**
     * 商品行号
     */
    private Long insideId;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 是否冲单0,否1是
     */
    private Integer isCancelBill;

    /**
     * 效期商品标识 1是 0否，默认0
     */
    private Integer periodFlag;

    /**
     * 关联单据号
     */
    private String billNumber;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商品类型 0-主表商品，1-子表商品
     */
    private Integer goodsType;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品sku编码
     */
    private String skuCode;

    /**
     * 零售单价
     */
    private BigDecimal referPrice;


    /**
     * 调拨单价
     */
    private BigDecimal purchPrice;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;


    /**
     * 单位比率 包装率
     */
    private BigDecimal unitRate;

    /**
     * 申请数量
     */
    private BigDecimal amount;


    /**
     * 调拨金额
     */
    private BigDecimal purchMoney;

    /**
     * 税金
     */
    private BigDecimal purchTax;

    /**
     * 零头数量
     */
    private BigDecimal remnantAmount;

    /**
     * 箱数
     */
    private BigDecimal wholeAmount;

    /**
     * 参考金额
     */
    private BigDecimal referMoney;

}
