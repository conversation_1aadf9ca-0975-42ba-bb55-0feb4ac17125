package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商经营控制枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商经营控制", code = "mstSupplierOperateControlEnum")
public enum MstSupplierOperateControlEnum implements VerifiableEnum<Integer> {
    NO_CONTROL(1, "不控制"),
    SUSPEND_PURCHASE(2, "暂停进货"),
    CLEARANCE(3, "清场");

    private final Integer code;
    private final String desc;
}
