package com.meta.supplychain.entity.dto.pms.resp.deliverytopurch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/20 16:57
 **/
@Schema(description = "配转采返回数据")
@Data
public class DeliveryToPurchResultResp {
    @Schema(description = "配转采数据,转采类型type:,0不可转采, 1可转采")
    private List<DeliveryToPurchResp> deliveryToPurchRespList;

    //配转采关联关系
    @Schema(description = "配送转采购关联数据,key=转采数据.insideId")
    private Map<Long,List<DeliveryToPurchRefResp>> deliveryToPurchRefMap;


    @Schema(description = "配送转采购配送来源明细信息,key=转采数据.insideId")
    private Map<Long,List<DeliveryToPurchDeliverySourceResp>> deliveryToPurchDeliverySourceMap;
}
