package com.meta.supplychain.entity.dto.pms.resp.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 订货申请价格响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订货申请价格响应")
public class ApplyPriceResp {

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String skuCode;

    /**
     * 配送中心
     */
    @Schema(description = "配送中心")
    private String distCode;

    /**
     * 申请单价
     */
    @Schema(description = "申请单价")
    private BigDecimal applyPrice;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNo;

    /**
     * 取价类型
     */
    @Schema(description = "取价类型")
    private Integer priceType;

}
