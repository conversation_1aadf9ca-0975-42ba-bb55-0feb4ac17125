package com.meta.supplychain.entity.dto.wds.req;

import com.meta.supplychain.entity.dto.wds.MoveLocBatchDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-7 9:31:59
 */
@Schema(description = "商品移库单保存")
@Getter
@Setter
@ToString
public class MoveBillSaveReq {

    @Schema(description = "商品移库单号")
    @NotBlank(message = "单号不能为空")
    private String billNo;

    @Schema(description = "操作类型 0 新增 1 编辑保存 2 新增审核 3编辑审核")
    @NotNull(message = "操作类型不能为空")
    private Integer optType;

    /**
     * 仓库编码
     */
    @Schema(description = "配送中心编码")
    @NotBlank(message = "配送中心编码不能为空")
    private String whCode;

    /**
     * 仓库名称
     */
    @Schema(description = "仓库名称")
    private String whName;

    /**
     * 移出储位编码
     */
    @Schema(description = "移出储位编码")
    private String outLocationCode;

    /**
     * 移出储位名称
     */
    @Schema(description = "移出储位名称")
    private String outLocationName;

    /**
     * 移入储位编码
     */
    @Schema(description = "移入储位编码")
    private String inLocationCode;

    /**
     * 移入储位名称
     */
    @Schema(description = "移入储位名称")
    private String inLocationName;

    /**
     * 移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移
     */
    @Schema(description = "移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移")
    @NotNull(message = "移位类型不能为空")
    private Integer moveType;

    /**
     * 来源单号[收货退货]
     */
    @Schema(description = "来源单号[收货退货]")
    private String srcBillNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "移位商品明细")
    @NotNull(message = "移位商品明细不能为空")
    private List<MoveLocBatchDetailDTO> detailList;
}

