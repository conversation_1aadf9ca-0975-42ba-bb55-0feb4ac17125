package com.meta.supplychain.entity.dto.pms.resp.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订货申请请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "申请单响应")
public class ApplyBillResp {

    /**
     * 生成的申请单号
     */
    @Schema(description = "生成的申请单号")
    private String billNumber;

    //失败原因
    @Schema(description = "失败原因")
    private String failedReason;


}
