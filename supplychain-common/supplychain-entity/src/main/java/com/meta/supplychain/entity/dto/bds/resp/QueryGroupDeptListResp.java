package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/17 9:37
 */
@Builder
@Setter
@Getter
@Data
public class QueryGroupDeptListResp {

    private Long total;

    private List<Rows> rows;

    @Builder
    @Setter
    @Getter
    @Data
    public static class Rows {
        private String updateUid;
        private String updateTime;
        private String updateName;
        private String updateCode;
        private Long tenantId;
        /**
         * 营业状态  1 营业 2 停业 3 关闭
         */
        private Long openStatus;
        private Long id;
        /**
         * 店组群名称
         */
        private String groupName;
        private Long groupId;
        /**
         * 店组群全称
         */
        private String groupFullName;

        /**
         * 店组群编码
         */
        private String groupCode;
        private Long deptType;

        private Long deptStatus;
        /**
         * 部门名称
         */
        private String deptName;
        private Long deptId;
        /**
         * 部门编码
         */
        private String deptCode;
        private Long delFlag;
        private Long createUid;
        private String createTime;
        private String createName;
        private String createCode;
        private String classCode;
    }
}
