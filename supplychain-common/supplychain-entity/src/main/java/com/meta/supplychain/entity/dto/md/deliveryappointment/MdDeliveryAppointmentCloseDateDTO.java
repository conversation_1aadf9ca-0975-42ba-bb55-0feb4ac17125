package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 供应商预约策略停止预约日期DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约策略停止预约日期")
public class MdDeliveryAppointmentCloseDateDTO extends BaseDTO {
    
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "配送预约策略单据号")
    private String billNo;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Schema(description = "停止预约起始时间")
    @NotNull(message = "停止预约起始时间不可为空")
    private LocalDate startTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Schema(description = "停止预约截止时间")
    @NotNull(message = "停止预约截止时间不可为空")
    private LocalDate endTime;
} 