package com.meta.supplychain.entity.dto.md.resp.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 15:02
 **/
@Schema(description = "追加追减商品设置校验失败数据")
@Data
public class CheckMdAddReduceGoodsResp {
    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "是否通过 false失败")
    private Boolean success;

    @Schema(description = "失败原因")
    private String failReason;

}
