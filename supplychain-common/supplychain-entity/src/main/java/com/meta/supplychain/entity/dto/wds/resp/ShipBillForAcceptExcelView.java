package com.meta.supplychain.entity.dto.wds.resp;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.alibaba.ageiport.processor.core.annotation.ViewField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@Data
public class ShipBillForAcceptExcelView {

    /**
     * 配送发货单号
     */
    @ViewField(headerName = "配送单号")
    private String shipBillNo;

    /**
     * 状态[  拣货状态 -1处理中  1待发货 2已发货 3验收中 4验收待审核 5已验收  9已作废]
     */
    @ViewField(headerName = "状态")
    private String status;
    @ViewField(headerName = "配送类型")
    private String billType;

    @ViewField(headerName = "配送部门编码")
    private String whCode;

    @ViewField(headerName = "配送部门名称")
    private String whName;


    @ViewField(headerName = "收货部门编码")
    private String inDeptCode;

    @ViewField(headerName = "收货部门名称")
    private String inDeptName;


    @ViewField(headerName = "配送数量")
    private BigDecimal totalShipQty;
    @ViewField(headerName = "配送金额")
    private String totalShipTaxMoney;

    /**
     * 备注
     */
    @ViewField(headerName = "备注")
    private String remarkAccept;

    @ViewField(headerName = "订单号")
    private String deliveryBillNo;

    @ViewField(headerName = "发货时间")
    private String shipTime;
    ;@ViewField(headerName = "发货人编码")
    private String shipManCode;
    ;@ViewField(headerName = "发货人名称")
    private String shipManName;

    @ViewField(headerName = "修改时间")
    private String updateTime;

    ;@ViewField(headerName = "修改人编码")
    private String updateCode;
    ;@ViewField(headerName = "修改人名称")
    private String updateName;

    @ViewField(headerName = "制单时间")
    private String createTime;
    ;@ViewField(headerName = "制单人编码")
    private String createCode;
    ;@ViewField(headerName = "制单人名称")
    private String createName;

    @ViewField(headerName = "提交时间")
    private String submitTime;
    ;@ViewField(headerName = "提交人编码")
    private String submitManCode;
    ;@ViewField(headerName = "提交人名称")
    private String submitManName;


}
