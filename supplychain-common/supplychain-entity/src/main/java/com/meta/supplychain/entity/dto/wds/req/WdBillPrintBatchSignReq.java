package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 打印批量回写操作
 */
@Schema(description = "打印批量回写操作")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class WdBillPrintBatchSignReq {
    @Schema(description = "单据号列表")
    @NotEmpty(message = "单据号不能为空")
    private List<String> billNoList;

}
