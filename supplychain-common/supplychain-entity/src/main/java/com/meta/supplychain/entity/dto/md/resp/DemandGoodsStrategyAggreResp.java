package com.meta.supplychain.entity.dto.md.resp;

import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsStrategyResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsDeliverPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsPurchPriceInfoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/09 23:53
 **/
@Data
public class DemandGoodsStrategyAggreResp {

    @Schema(description = "采购价格信息")
    private List<GoodsPurchPriceInfoResp> goodsPurchPriceInfoRespList;

    @Schema(description = "配送价格信息")
    private List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoRespList;


    @Schema(description = "商品订货策略")
    private GoodsStrategyResp goodsStrategyResp;
}
