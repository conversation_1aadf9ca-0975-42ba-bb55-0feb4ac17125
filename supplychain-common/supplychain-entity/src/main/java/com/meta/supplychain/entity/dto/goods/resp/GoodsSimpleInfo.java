package com.meta.supplychain.entity.dto.goods.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> cat
 * @date 2024/4/12 14:41
 */
@Builder
@Data
@Getter
@Setter
public class GoodsSimpleInfo {

    private String workStateName;
    private String workStateCode;
    private String warnDays;
    private String validityCodeRule;
    private String unit;
    private String supplierId;
    private String spuCode;
    /**
     * SKU编码
     */
    private String skuName;
    private String skuId;
    /**
     * SKU编码
     */
    private String skuCode;
    private String shelfLifeDays;
    private String sendSupplierCode;
    private Long sendFlag;
    private Long saleState;
    private String salePreDays;
    /**
     * 参考零售价
     */
    private Long referPrice;
    private String recAdvDays;
    private String productDateRule;
    private String operationModel;
    private Integer mesureProperty;
    private String externalSpuCode;
    private String externalSkuCode;
    private Integer expiryDateControlFlag;
    private String expirateDateRule;
    private Long cooperatorId;
    /**
     * 是否组合商品
     */
    private Boolean combFlag;
    private String circulationModeName;
    private String circulationModeCode;
    private String categoryName;
    private String categoryCode;
    private String brandName;
    private String brandCode;
    private String batchNumRule;
    private String barcode;
    /**
     * 进项税率
     */
    private String inputTaxRate;
    /**
     * 销项税率
     */
    private String outputTaxRate;


    /**
     * 商品类型（1：单规格 2：多规格 3：组合）
     */
    private Integer goodsType;
    /**
     * 组合类型（1：普通 2：多包装 3：散称标准份 4：散称主子码）
     */
    private Integer combType;

    /**
     * 规格型号
     */
    private String specModel;
}
