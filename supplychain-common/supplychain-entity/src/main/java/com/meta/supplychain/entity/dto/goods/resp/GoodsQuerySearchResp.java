package com.meta.supplychain.entity.dto.goods.resp;

import lombok.*;

import java.util.List;

/**
 * 商品信息
 */
@Builder
@Setter
@Getter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsQuerySearchResp {

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * 是否多包装商品
     */
    private Boolean packageFlag;

    /**
     * 是否SPU（0：否 1：是）
     */
    private Boolean spuFlag;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 商品类型（1：单规格 2：多规格 3：组合商品）
     */
    private Integer goodsType;

    /**
     * 组合类型（1：普通 2：多包装 3：散称标准份 4：散称主子码）
     */
    private Integer combType;

    /**
     * SPU条码
     */
    private String spuBarcode;

    /**
     * SPU货号
     */
    private String spuGoodsNo;

    /**
     * SKU条码
     */
    private String skuBarcode;

    /**
     * SKU货号
     */
    private String skuGoodsNo;

    /**
     * 规格信息（5L等）
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 经营方式(1:自营 2:联营 7:联营管库存 8:租赁 9:生鲜A进A出 10:生鲜A进B出 12:生鲜归集码)
     */
    private Integer operationModel;

    /**
     * 经营方式名称
     */
    private String operationModelName;

    /**
     * 计量属性（0：普通 1：计量 2：称重）
     */
    private Integer measureProperty;

    /**
     * 包装数量
     */
    private Integer packageNum;

    /**
     * 是否在目录（0：否 1：是）
     */
    private Integer inCatalog;

    /**
     * 经营状态编码
     */
    private String workStateCode;

    /**
     * 经营状态名称
     */
    private String workStateName;

    /**
     * 流转途径编码
     */
    private String circulationModeCode;

    /**
     * 流转途径名称
     */
    private String circulationModeName;

    /**
     * 毛重（kg）
     */
    private String weight;

    /**
     * 进项税率
     */
    private Integer inputTaxRate;

    /**
     * 销项税率
     */
    private String outputTaxRate;

    /**
     * 参考零售价（元）
     */
    private String referPrice;

    /**
     * 参考进价（元）
     */
    private String purchPrice;

    /**
     * 是否启用效期（0：否 1：是）
     */
    private Integer expiryDateControlFlag;
    /**
     * 收货提前期
     */
    private Integer recAdvDays;
    /**
     * 销售提前期
     */
    private Integer salePreDays;
    /**
     * 预警提前期
     */
    private Integer warnDays;

    /**
     * 批号规则（0：可为空 1：必填 2：不可填）
     */
    private Integer batchNumRule;
    /**
     * 生产日期规则（0：可为空 1：必填 2：不可填）
     */
    private Integer productDateRule;

    /**
     * /到期日期规则（0：可为空 1：必填 2：不可填）
     */
    private Integer expirateDateRule;

    /**
     * 效期条码规则（0：可为空 1：必填 2：不可填）
     */
    private Integer validityCodeRule;

    /**
     * 保质期（天）
     */
    private Integer shelfLifeDays;

    /**
     * 是否类别码（0：否 1：是）
     */
    private Integer categoryCodeFlag;

    /**
     * 关联商品（结构同商品信息）
     */
    private GoodsQuerySearchResp packageGoodsInfo;

    /**
     * 子品（结构同商品信息）
     */
    private GoodsQuerySearchResp childGoodsInfo;

    private List<MainGoods> mainGoodsList;
}
