package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchCateGoodsDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchRecordDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchStrategyDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchTimeSegmentDTO;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.CreateMdDemandBatchCateGoods;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.CreateMdDemandBatchStrategyReq;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.CreateMdDemandBatchTimeSegment;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.ModifyMdDemandBatchStrategyReq;
import com.meta.supplychain.entity.po.md.MdDemandBatchCateGoodsPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchRecordPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchStrategyPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchTimeSegmentPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdDemandBatchStrategyConvert {

    MdDemandBatchStrategyConvert INSTANCE = Mappers.getMapper(MdDemandBatchStrategyConvert.class);

    /**
     * 策略PO转DTO
     */
    MdDemandBatchStrategyDTO strategyPo2dto(MdDemandBatchStrategyPO po);

    MdDemandBatchRecordDTO strategyRecordPo2dto(MdDemandBatchRecordPO po);

    List<MdDemandBatchRecordDTO> strategyRecordPo2dtoList(List<MdDemandBatchRecordPO> list);
    
    /**
     * 时间段PO转DTO
     */
    MdDemandBatchTimeSegmentDTO timeSegmentPo2dto(MdDemandBatchTimeSegmentPO po);
    
    /**
     * 品类商品PO转DTO
     */
    MdDemandBatchCateGoodsDTO cateGoodsPo2dto(MdDemandBatchCateGoodsPO po);
    
    /**
     * 策略创建请求转PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    MdDemandBatchStrategyPO strategyRequest2po(CreateMdDemandBatchStrategyReq request);
    
    /**
     * 策略修改请求转PO
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    MdDemandBatchStrategyPO strategyModifyRequest2po(ModifyMdDemandBatchStrategyReq request);
    
    /**
     * 时间段创建请求转PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    MdDemandBatchTimeSegmentPO timeSegmentRequest2po(CreateMdDemandBatchTimeSegment request);
    
    /**
     * 品类商品创建请求转PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    MdDemandBatchCateGoodsPO cateGoodsRequest2po(CreateMdDemandBatchCateGoods request);
} 