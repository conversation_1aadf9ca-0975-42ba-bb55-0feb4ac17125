package com.meta.supplychain.entity.dto.md.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 15:11
 **/
@Schema(description = "追加追减商品部门表")
@Data
public class MdAddReduceGoodsDeptImportDTO {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "是否允许加单(0-否 1-是 默认1)")
    private Integer isAllowMultiBill;

    @Schema(description = "是否允许减单(0-否 1-是 默认0)")
    private Integer isAllowReduceBill;

    @Schema(description = "部门范围，1不限制部门，2指定部门")
    private Integer deptScope;

    @Schema(description = "sc_md_add_reduce_goods.id")
    private Long addReduceGoodsId;

    @Schema(description = "部门/店组编码")
    private String code;

    @Schema(description = "部门/店组名称")
    private String name;

    @Schema(description = "部门类型,1部门,2店组群")
    private Integer deptType;

}
