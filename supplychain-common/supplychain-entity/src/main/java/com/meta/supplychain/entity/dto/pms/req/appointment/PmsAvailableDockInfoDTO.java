package com.meta.supplychain.entity.dto.pms.req.appointment;

import com.meta.supplychain.annotation.DecimalScale;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 停靠点可预约信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "停靠点可预约信息")
public class PmsAvailableDockInfoDTO {

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "停靠点类型 枚举mdDeliveryDockTypeEnum")
    private Integer dockType;

    @Schema(description = "限量规则 枚举mdDeliveryDockConstraintRuleEnum")
    private Integer constraintRule;

    @Schema(description = "时段行号")
    private Integer insideId;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    @Schema(description = "可预约日期")
    private LocalDate date;

    @DecimalScale(value = 4)
    @Schema(description = "可预约数量")
    private BigDecimal availableCount;

    @DecimalScale(value = 4)
    @Schema(description = "已预约数量")
    private BigDecimal usedCount;
} 