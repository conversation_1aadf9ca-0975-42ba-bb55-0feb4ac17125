package com.meta.supplychain.entity.dto.pms.req.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
public class PmsDemandPruchDeliveryRefReq{

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "单据类型,0采购, 1配送,2配转采的采购,3配转采的采购超出的部分，4直流采购")
    private List<Integer> typeList;

    /** 下游单号(配送订单号或采购订单号) */
    private List<String> refBillNoList;

}