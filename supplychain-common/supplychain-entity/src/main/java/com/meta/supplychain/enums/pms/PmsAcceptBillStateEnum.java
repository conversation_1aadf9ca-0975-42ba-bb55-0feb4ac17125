package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum PmsAcceptBillStateEnum {

    DRAFT(0, "草稿",1L),
    PENDING_AUDIT(1, "待审核",1L),
    AUDIT(2, "已审核",1L),
    VOIDED(3, "已作废",3L)

    ;

    private final Integer billStateCode;

    private final String billStateName;

    /**
     * erp侧订单状态，1表示确认，2表示终止，3表示作废
     */
    private final Long erpBillState;

    public static PmsAcceptBillStateEnum getEnumByCode(Integer billStateCode) {
        PmsAcceptBillStateEnum[] values = values();
        for (PmsAcceptBillStateEnum value : values) {
            if(value.getBillStateCode().equals(billStateCode)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer billStateCode) {
        for (PmsAcceptBillStateEnum value : values()) {
            if(value.getBillStateCode().equals(billStateCode)) {
                return value.billStateName;
            }
        }
        return null;
    }

    /**
     *  返回-1 不允许  0允许 1.允许且需推erp
     * @param billStateCode
     * @return
     */
    public static boolean canUpdate(Integer billStateCode) {
        return DRAFT.getBillStateCode().equals(billStateCode) || PENDING_AUDIT.getBillStateCode().equals(billStateCode);

    }
}
