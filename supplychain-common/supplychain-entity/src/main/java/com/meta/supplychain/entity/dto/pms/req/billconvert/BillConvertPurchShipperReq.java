package com.meta.supplychain.entity.dto.pms.req.billconvert;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 11:01
 **/
public class BillConvertPurchShipperReq {
    @Schema(description = "单内序号,单据内唯一")
    private Long insideId;

    @Schema(description = "上一级insideId")
    private Long pInsideId;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;

    @Schema(description = "直流标志 0-非直流 1-直流")
    private Integer directSign;

    @Schema(description = "促销期间价")
    private BigDecimal promotePeriodPrice;

    @Schema(description = "采购包装率")
    private BigDecimal purchUnitRate;

    @Schema(description = "合同进价")
    private BigDecimal contractPurchPrice;

    @Schema(description = "合同特供价")

    private BigDecimal contractSpecialPrice;

    @Schema(description = "最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)")
    private BigDecimal lastPurchPrice;

    @Schema(description = "采购金额")
    private BigDecimal purchMoney;

    @Schema(description = "采购税金")
    private BigDecimal purchTax;

    @Schema(description = "采购数量")
    private BigDecimal purchQty;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "转单标识,0普通, 1配转采")
    private Integer convertFlag;

    @Schema(description = "订货部门编码")
    private String orderDeptCode;

    @Schema(description = "合同最高进价")
    private BigDecimal contractMaxPurchPrice;

    @Schema(description = "是否主供应商,0否,1是")
    private Integer mainSupplierMode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "采购价格")
    private BigDecimal purchPrice;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;
}
