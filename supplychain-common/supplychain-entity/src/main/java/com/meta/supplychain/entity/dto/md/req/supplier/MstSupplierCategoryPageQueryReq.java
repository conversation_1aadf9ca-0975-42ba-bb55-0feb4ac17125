package com.meta.supplychain.entity.dto.md.req.supplier;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商分类分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商分类分页查询请求")
public class MstSupplierCategoryPageQueryReq extends PageParams {

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "父级编码")
    private String parentCode;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "最终标识")
    private Integer finalFlag;

    @Schema(description = "分类编码/名称关键字（模糊查询）")
    private String rowInfo;
}
