package com.meta.supplychain.entity.dto.promotion.resp;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2023/5/5 17:36
 */
@Data
@Builder
public class PromotionPriceInfo implements Serializable {
    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 促销id
     */
    private Long promotionId;

    /**
     * 促销名称
     */
    private String promotionName;
}
