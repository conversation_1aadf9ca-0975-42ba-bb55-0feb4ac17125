package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR> cat
 * @date 2024/5/29 14:42
 */
@Builder
@Setter
@Getter
@Data
public class SupplierByCodeResp {

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 状态 1启用 0停用
     */
    private Integer status;

    /**
     * 外部编码
     */
    private Integer outCode;

    /**
     * 供应商分类
     */
    private String suppCateCode;

    /**
     * 省份国标
     */
    private String provinceCode;

    /**
     * 所在省份
     */
    private String provinceName;

    /**
     * 城市国标
     */
    private String cityCode;

    /**
     * 所在城市
     */
    private String cityName;

    /**
     * 区国标
     */
    private String districtCode;

    /**
     * 所在区
     */
    private String districtName;

    /**
     * 街道国标
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;


    /**
     * 完整地址，拼省市区provinceName+cityName+districtName+streetName+address
     */
    private String wholeAddress;

    /**
     * 结算方式/模式: 1统一结算 2本地结算 3区域结算
     */
    private Integer settleMode;

    /**
     * 核算主体信息
     */
    private List<ComplexSupplierInfo.AccountBodyList> accountBodyList;

    @Builder
    @Setter
    @Getter
    @Data
    public static class AccountBodyList {
        /**
         * 店组群编码
         */
        private List<String> groupCodeList;
        /**
         * 过账配送中心名称
         */
        private String deptName;
        /**
         * 店组群分类项编码
         */
        private String deptGroupClassCode;
        /**
         * 过账配送中心编码
         */
        private String deptCode;
        /**
         * 核算单位名称
         */
        private String accountName;
        /**
         * 核算单位编码
         */
        private String accountCode;
    }


}
