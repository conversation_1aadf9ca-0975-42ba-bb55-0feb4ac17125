package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceGoodsDTO;
import com.meta.supplychain.entity.po.md.MdDistPriceGoodsPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 配送价格商品对象转换器
 */
@Mapper(uses = {MdDistPriceDetailConvert.class})
public interface MdDistPriceGoodsConvert {

    MdDistPriceGoodsConvert INSTANCE = Mappers.getMapper(MdDistPriceGoodsConvert.class);

    /**
     * PO转DTO
     */
    MdDistPriceGoodsDTO po2dto(MdDistPriceGoodsPO po);

    /**
     * DTO转PO
     */
    MdDistPriceGoodsPO dto2po(MdDistPriceGoodsDTO dto);
} 