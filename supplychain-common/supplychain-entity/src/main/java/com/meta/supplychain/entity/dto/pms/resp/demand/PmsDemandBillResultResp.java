package com.meta.supplychain.entity.dto.pms.resp.demand;

import com.meta.supplychain.entity.dto.pms.demand.DemandBillHandleDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 19:10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandBillResultResp {
    private PmsDemandBillResp pmsDemandBillResp;

    private DemandBillHandleDataDTO demandBillHandleData;
}
