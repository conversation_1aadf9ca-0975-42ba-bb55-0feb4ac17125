package com.meta.supplychain.entity.dto.md.req.addreducegoods;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 15:02
 **/
@Schema(description = "追加追减商品设置")
@Data
public class CheckMdAddReduceReq {
    @Schema(description = "商品列表")
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    private List<CheckMdAddReduceGoodsReq> skuList;

    @Data
    public static class CheckMdAddReduceGoodsReq {

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "是否允许减单(0-否 1-是 默认0)")
        private Integer isAllowReduceBill;

        @Schema(description = "是否允许加单(0-否 1-是 默认1)")
        private Integer isAllowMultiBill;

        @Schema(description = "部门")
        @NotEmpty(message = "部门不能为空")
        private List<String> deptCodeList;
    }

}
