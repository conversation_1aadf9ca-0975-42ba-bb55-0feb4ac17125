package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 代配过账单明细表
 * @TableName pms_account_bill_detail
 */
@TableName(value ="pms_account_bill_detail")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PmsAccountBillDetailPO extends BaseEntity implements Serializable {
    /**
     * 主键序号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 过账单号
     */
    private String billNo;

    /**
     * 配送中心编码
     */
    private String dcCode;

    /**
     * 配送中心名称
     */
    private String dcName;

    /**
     * 单内序号
     */
    private Long insideId;

    /**
     * 商品类型	0商品1附赠商品2附赠赠品
     */
    private Integer skuType;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 商品货号
     */
    private String goodsNo;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 单位
     */
    private String basicUnit;

    /**
     * 整件单位
     */
    private String packageUnit;

    /**
     * 规格
     */
    private String skuModel;

    /**
     * 销售模式	1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码
     */
    private Integer saleMode;

    /**
     * 进项税率
     */
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    private BigDecimal outputTaxRate;

    /**
     * 计量属性	0：普通 1：计量 2：称重
     */
    private Integer uomAttr;

    /**
     * 效期商品标识 	1是 0否
     */
    private Integer periodFlag;

    /**
     * 过账数量
     */
    private BigDecimal accQty;

    /**
     * 过账价格
     */
    private BigDecimal accPrice;

    /**
     * 过账金额
     */
    private BigDecimal accMoney;

    /**
     * 过账税金
     */
    private BigDecimal accTax;

    /**
     * 零售单价
     */
    private BigDecimal salePrice;

    /**
     * 零售金额
     */
    private BigDecimal saleMoney;

    /**
     * 来源单据类型
     */
    private String srcBillType;

    /**
     * 来源单号
     */
    private String srcBillNo;

    /**
     * 来源单单内序号
     */
    private Long srcInsideId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 