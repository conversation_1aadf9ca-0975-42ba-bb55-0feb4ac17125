package com.meta.supplychain.entity.dto.md.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/09 23:56
 **/
@Data
public class DemandGoodsStrategyAggreReq {
    @Schema(description = "部门编码(采购)/入货部门编码(配送)")
    @NotBlank(message = "部门编码不能为空")
    private String deptCode;

    @Schema(description = "供应商编码-采购")
    private String supplierCode;

    @Schema(description = "价格类别:3采购订单/需求单(周从铭),4配转采")
    private Integer applyCate = 3;

    @Schema(description = "商品信息")
    @NotEmpty(message = "商品信息不能为空")
    @Valid
    private List<GoodsInfo> goodsInfoList;

    @Schema(description = "是否查询订单订货策略,1查询订单订货策略,0不查询")
    private Integer orderQueryType = 0;


    @Schema(description = "仓库编码-配送")
    private String whCode;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodsInfo{

        @Schema(description = "全路径品类编码,英文逗号分割")
        private String categoryCodeAll;

        @Schema(description = "品类编码")
        private String categoryCode;
        /**
         * 商品编码
         */
        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        String skuCode;

    }
}
