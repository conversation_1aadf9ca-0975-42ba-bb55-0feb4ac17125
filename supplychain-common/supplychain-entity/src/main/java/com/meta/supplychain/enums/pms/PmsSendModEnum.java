package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PmsSendModEnum {

    /**
     * 送货方式 0到店 1到客户
     */

    TOSHOP(0, "到店"),
    TOCUSTOMER(1, "到客户")
    ;
    private Integer code;

    private String name;

    public static PmsSendModEnum getEnumByCode(Integer billStateCode) {
        for (PmsSendModEnum value : values()) {
            if(value.getCode().equals(billStateCode)) {
                return value;
            }
        }
        return null;
    }
}
