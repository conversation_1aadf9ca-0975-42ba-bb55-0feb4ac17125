package com.meta.supplychain.entity.dto.pms.demand;

import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ManageAndCirculationDTO;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchDeliverySourceResp;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/03 13:37
 **/
@Data
public class AutoApplyToDemandDTO {
    private List<PmsApplyBillPO> applyBillList = new ArrayList<>();

    /**
     * key 订货申请单号
     */
    private Map<String,PmsApplyBillPO> applyBillMap = new HashMap<>();

    private List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();

    /**
     * 需求单来源0手工,1自动,2追加追减
     * PmsDemanBillSourceEnum
     */
    private Integer billSource;

    @Schema(description = "全部可转采与转采购信息回填---追加追减配转采使用")
    private List<PmsDemandDeliveryToPurchReq> deliveryToPurchParamDTO;

    @Schema(description = "成功响应的订货申请明细,不需要传")
    private List<PmsApplyBillDetailPO> successApplyBillDetailList = new ArrayList<>();

    @Schema(description = "响应失败的订货申请明细,不需要传")
    private List<PmsApplyBillDetailPO> failedApplyBillDetailList = new ArrayList<>();


    @Schema(description = "经营状态,流转途径,不需要传,需求单自己查询透传")
    private ManageAndCirculationDTO manageAndCirculation;

    @Schema(description = "部门商品信息key=部门编码_商品编码,不需要传,需求单自己查询透传")
    private Map<String, GoodsQueryResp> goodsQueryRespMap = new HashMap<>();

    @Schema(description = "单据来源基于订货申请单号与单内序号作为key,不需要传,需求单自己查询透传")
    private Map<String, DeliveryToPurchDeliverySourceResp> deliveryToPurchDeliverySourceRespMap = new HashMap<>();

    @Schema(description = "自动需求单配转采门店,不需要传,需求单自己查询透传")
    private Map<String,List<String>> autoDeliveryToPurchDeptMap = new HashMap<>();

    @Schema(description = "自动需求单配转采信息,不需要传,需求单自己查询透传")
    private DeliveryToPurchReq deliveryToPurchReq;
}
