package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约类别枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "预约类别", code = "pmsBookingCategoryEnum")
public enum PmsBookingCategoryEnum implements VerifiableEnum<Integer> {

    PURCHASE_DISTRIBUTION(1, "采购"),
    PURCHASE_RETURN(-1, "采退");

    private final Integer code;
    private final String desc;
} 