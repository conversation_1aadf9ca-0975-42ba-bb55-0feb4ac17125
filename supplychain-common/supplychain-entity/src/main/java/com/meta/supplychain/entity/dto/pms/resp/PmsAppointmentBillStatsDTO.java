package com.meta.supplychain.entity.dto.pms.resp;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.DecimalScale;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.enums.pms.PmsBookingDocumentSourceEnum;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 预约单查询结果DTO（包含统计信息）
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约单查询结果DTO（包含统计信息）")
public class PmsAppointmentBillStatsDTO {

    // 预约单基本信息
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预约单据号")
    private String billNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @EnumValue(type = PmsBookingDocumentSourceEnum.class)
    @Schema(description = "预约单据来源 枚举pmsBookingDocumentSourceEnum")
    private Integer opSource;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单截止日期开始")
    private LocalDate startValidityTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单截止日期结束")
    private LocalDate endValidityTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单送货日期开始")
    private LocalDate startDeliverTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单送货日期结束")
    private LocalDate endDeliverTime;

    @Schema(description = "预约类别 枚举pmsBookingCategoryEnum")
    private Integer billDirection;

    @Schema(description = "是否直流 枚举YesOrNoEnum")
    private Integer directSign;

    @Schema(description = "预约方式 枚举pmsBookingMethodEnum")
    private Integer appointmentMode;

    @Schema(description = "默认预约数量 枚举pmsDefaultBookingQuantityEnum")
    private Integer defaultQtySign;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "承运方式 枚举pmsCarrierMethodEnum")
    private Integer transportMode;

    @Schema(description = "承运人")
    private String transportMan;

    @Schema(description = "承运联系人")
    private String transportContacts;

    @Schema(description = "承运联系手机")
    private String transportMobile;

    @Schema(description = "车辆类型")
    private String carType;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "计划到达时间")
    private LocalDateTime planArrivalTime;

    @Schema(description = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @Schema(description = "承运备注")
    private String transportRemark;

    @Schema(description = "预约备注")
    private String appointmentRemark;

    @Schema(description = "状态 枚举pmsBookingStatusEnum")
    private Integer status;

    @Schema(description = "附件名称与地址")
    private String attachmentUrl;

    @Schema(description = "提交人code")
    private String submitManCode;

    @Schema(description = "提交人姓名")
    private String submitManName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "作废人code")
    private String cancelManCode;

    @Schema(description = "作废人名称")
    private String cancelManName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "作废时间")
    private LocalDateTime cancelTime;

    @Schema(description = "创建人ssoId")
    private Long createUid;

    @Schema(description = "创建人编码")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人ssoId")
    private Long updateUid;

    @Schema(description = "修改人编码")
    private String updateCode;

    @Schema(description = "修改人姓名")
    private String updateName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    // 统计信息
    @Schema(description = "关联的采购订单数量")
    private Integer purchBillCount;

    @Schema(description = "商品SKU数量")
    private Integer skuCount;

    @DecimalScale(value = 4)
    @Schema(description = "预约数量总计")
    private BigDecimal appointmentQtyCount;

    @DecimalScale(value = 4)
    @Schema(description = "预约整件数量总计")
    private BigDecimal appointmentWholeQtyCount;

    @DecimalScale(value = 4)
    @Schema(description = "预约零头数量总计")
    private BigDecimal appointmentOddQtyCount;
} 