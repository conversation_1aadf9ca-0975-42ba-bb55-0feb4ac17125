package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 *
 * 合同状态,1草稿、2审核中、3待提交、4生效中、5待生效、6已失效、7已终止、8已过期、9已作废
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "主档错误状态码枚举", code = "MdErrorCodeEnum")
public enum MdErrorCodeEnum implements VerifiableEnum<String> {
    /**
     * #错误码示例：SCMD001P001
     * #SC(领域)MD(子系统)001(模块代码)B(异常类型)001(具体异常码)
     * #B-业务错误
     * #P-参数错误
     * #N-网络错误
     * #D-数据库错误
     * #F-文件IO错误
     * #U-未知错误
     * #SCMD001P001  SCMD 供应链主数据  001模块代码 001错误代码  001:费用管理,002合同管理
     * #SC(领域-供应链)WDS(子系统-仓储配送)001(模块代码-波次管理)P(参数异常)001 SCWDS001P001  SCWDS 供应商仓库配送
     * #SC(领域-供应链)PMS(子系统-订货采购)001(模块代码-订货申请)P(参数异常)001 SCPMS001P001  SCPMS 供应商仓库配送
     * SCWDS001P001  供应链配送管理
     * SCWDS002P001  供应链波次管理
     * SCWDS003P001  供应链拣货管理
     * SCWDS004P001  供应链配送发货管理
     * SCWDS005P001  供应链配送验收管理
     * SCWDS006P001  供应链退配收货管理
     * SCWDS007P001  供应链仓库配送差异管理
     * SCWDS008P001  供应链仓库商品移位管理
     * SCWDS009P001  供应链加工拆分管理
     * SCWDS010P001  供应链调拨管理
     * SCWDS011P001  商品追加追减设置
     * SCWDS012P001  合同商品定义
     *
     */
    SCMD001B001("SCMD001B001","合同不存在",MdModuleCodeEnum.MD,true),
    SCMD001B002("SCMD001B002","合同状态不是草稿",MdModuleCodeEnum.MD,true),
    SCMD001B003("SCMD001B003","合同状态不是待审核",MdModuleCodeEnum.MD,true),
    SCMD001B004("SCMD001B004","合同状态不是草稿|待提交|待生效",MdModuleCodeEnum.MD,true),
    SCMD001B005("SCMD001B005","合同状态不是生效中",MdModuleCodeEnum.MD,true),
    SCMD001B006("SCMD001B006","合同提交审核失败",MdModuleCodeEnum.MD,true),
    SCMD001B007("SCMD001B007","合同审核失败",MdModuleCodeEnum.MD,true),
    SCMD001B008("SCMD001B008","合同作废失败",MdModuleCodeEnum.MD,true),
    SCMD001B009("SCMD001B009","合同终止失败",MdModuleCodeEnum.MD,true),
    SCMD001B010("SCMD001B010","合同补充条款暂存失败",MdModuleCodeEnum.MD,true),
    SCMD001B011("SCMD001B011","合同基本信息暂存失败",MdModuleCodeEnum.MD,true),
    SCMD001B012("SCMD001B012","合同提交审核失败",MdModuleCodeEnum.MD,true),
    SCMD001B013("SCMD001B013","合同商品条款保存失败",MdModuleCodeEnum.MD,true),
    SCMD001B014("SCMD001B014","合同商品保存失败",MdModuleCodeEnum.MD,true),
    SCMD001B015("SCMD001B015","合同商品部门保存失败",MdModuleCodeEnum.MD,true),
    SCMD001B016("SCMD001B016","当前合同存在已发起的草稿、待提交、审核中的合同，不能在发起补签",MdModuleCodeEnum.MD,true),
    SCMD001B017("SCMD001B017","当前合同存在已发起的草稿、待提交、审核中、待生效中的合同，不能在发起续签",MdModuleCodeEnum.MD,true),
    SCMD001B018("SCMD001B018","当前合同存不是草稿|待提交，不能编辑",MdModuleCodeEnum.MD,true),
    SCMD001B019("SCMD001B019","获取续签合同开始时间失败",MdModuleCodeEnum.MD,true),
    SCMD001B020("SCMD001B020","合同已存在延期待审核的延期合同，无法创建（可驳回重新提交）.",MdModuleCodeEnum.MD,true),
    SCMD001B021("SCMD001B021","合同已存在延期待生效的延期合同，无法创建（可修改待生效延期合同的结束时间）.",MdModuleCodeEnum.MD,true),
    SCMD001B022("SCMD001B022","合同未到可延期时间，无法延期",MdModuleCodeEnum.MD,true),
    SCMD001B023("SCMD001B023","对应的合同信息不存在，无法延期",MdModuleCodeEnum.MD,true),
    SCMD001B024("SCMD001B024","合同提交延期失败",MdModuleCodeEnum.MD,true),
    SCMD001B025("SCMD001B025","合同状态不是草稿和待提交",MdModuleCodeEnum.MD,true),


    SCMD999B001("SCMD999B001","商品不在目录",MdModuleCodeEnum.MD,true),
    SCMD999B002("SCMD999B002","商品进项税率不能为空",MdModuleCodeEnum.MD,true),
    SCMD999B003("SCMD999B003","商品进价不能为空",MdModuleCodeEnum.MD,true),
    SCMD999B004("SCMD999B004","商品经营方式不能为联营、租赁、AB生鲜、生鲜归集码",MdModuleCodeEnum.MD,true),
    SCMD999B005("SCMD999B005","类别码商品不允许录入",MdModuleCodeEnum.MD,true),
    SCMD999B006("SCMD999B006","商品经营状态或流转途径未配置",MdModuleCodeEnum.MD,true),
    SCMD999B007("SCMD999B007","传入的单据号规则不存在",MdModuleCodeEnum.MD,true),
    SCMD999B008("SCMD999B008","单据号规则未配置",MdModuleCodeEnum.MD,true),
    SCMD999B009("SCMD999B009","查询订货策略时商品分类必填",MdModuleCodeEnum.MD,true),
    SCMD999B010("SCMD999B010","查询订货策略错误",MdModuleCodeEnum.MD,true),
    SCMD999B011("SCMD999B011","查询biPass服务错误",MdModuleCodeEnum.MD,true),
    SCMD999B012("SCMD999B012","单次查询最大100条",MdModuleCodeEnum.MD,true),
    SCMD999B013("SCMD999B013","查询商品服务报错",MdModuleCodeEnum.MD,true),
    SCMD999B014("SCMD999B014","超出订单订货策略时间阈值",MdModuleCodeEnum.MD,true),

    /** 商品追加追减设置 */
    SCWDS011P001("SCWDS011P001","当前设置正在处理，请稍后重试...",MdModuleCodeEnum.WDS,true),
    SCWDS011P002("SCWDS011P002","保存失败，商品[%s]已存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P003("SCWDS011P003","修改失败，商品不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P004("SCWDS011P004","部门数据不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS011P005("SCWDS011P005","保存商品设置失败",MdModuleCodeEnum.WDS,true),
    SCWDS011P006("SCWDS011P006","删除失败，商品不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P007("SCWDS011P007","单次校验商品不能超过100",MdModuleCodeEnum.WDS,true),
    SCWDS011P008("SCWDS011P008","本次校验商品不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P009("SCWDS011P009","本次校验商品部门[%s]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P010("SCWDS011P010","本次校验商品追加设置不匹配",MdModuleCodeEnum.WDS,true),
    SCWDS011P011("SCWDS011P011","本次校验商品追减设置不匹配",MdModuleCodeEnum.WDS,true),
    SCWDS011P012("SCWDS011P012","商品编码和商品条码不能同时为空",MdModuleCodeEnum.WDS,true),
    SCWDS011P013("SCWDS011P013","部门类型0不可维护部门数据",MdModuleCodeEnum.WDS,true),
    SCWDS011P014("SCWDS011P014","部门类型1和2必须维护部门数据",MdModuleCodeEnum.WDS,true),
    SCWDS011P015("SCWDS011P015","店组群[%s]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P016("SCWDS011P016","部门[%s]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P017("SCWDS011P017","商品设置已存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P018("SCWDS011P018","商品设置部门[%s]已存在",MdModuleCodeEnum.WDS,true),
    SCWDS011P019("SCWDS011P019","设置类型不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS011P020("SCWDS011P020","部门类型不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS011P021("SCWDS011P021","商品[%s]重复",MdModuleCodeEnum.WDS,true),

    /** 合同商品定义 */
    SCWDS012P001("SCWDS012P001","当前单据正在处理，请稍后重试...",MdModuleCodeEnum.WDS,true),
    SCWDS012P002("SCWDS012P002","提交审核合同商品定义失败",MdModuleCodeEnum.WDS,true),
    SCWDS012P003("SCWDS012P003","合同商品定义不存在或状态已变更",MdModuleCodeEnum.WDS,true),
    SCWDS012P004("SCWDS012P004","作废合同商品定义失败",MdModuleCodeEnum.WDS,true),
    SCWDS012P005("SCWDS012P005","审核合同商品定义失败",MdModuleCodeEnum.WDS,true),
    SCWDS012P006("SCWDS012P006","合同商品定义状态不是待执行",MdModuleCodeEnum.WDS,true),
    SCWDS012P007("SCWDS012P007","保存合同商品定义失败",MdModuleCodeEnum.WDS,true),
    SCWDS012P008("SCWDS012P008","合同[{}]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS012P009("SCWDS012P009","查询商品不存在",MdModuleCodeEnum.WDS,true),
    SCWDS012P010("SCWDS012P010","查询商品[{}]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS012P011("SCWDS012P011","商品[{}],名称[{}],供应商编码[{}],供应商名称[{}],合同号[{}]部门/店组群有交叉",MdModuleCodeEnum.WDS,true),
    SCWDS012P012("SCWDS012P012","商品[{}]特供价开始日期不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P013("SCWDS012P013","商品[{}]特供价结束日期不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P014("SCWDS012P014","商品[{}]特供价不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P015("SCWDS012P015","商品[{}]降价补偿字段不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P016("SCWDS012P016","商品[{}]降价补偿库补店价格不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P017("SCWDS012P017","单次查询最大100条",MdModuleCodeEnum.WDS,true),
    SCWDS012P018("SCWDS012P018","部门与店组群列表不能同时为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P019("SCWDS012P019","部门[{}]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS012P020("SCWDS012P020","店组群[{}]不存在",MdModuleCodeEnum.WDS,true),
    SCWDS012P021("SCWDS012P021","查询部门服务异常",MdModuleCodeEnum.WDS,true),
    SCWDS012P022("SCWDS012P022","查询商品服务异常",MdModuleCodeEnum.WDS,true),
    SCWDS012P023("SCWDS012P023","推送财务服务异常",MdModuleCodeEnum.WDS,true),
    SCWDS012P024("SCWDS012P024","商品编码入参不能为空",MdModuleCodeEnum.WDS,true),
    SCWDS012P025("SCWDS012P025","商品[{}]不在合同范围内，无法添加",MdModuleCodeEnum.WDS,true),
    SCWDS012P026("SCWDS012P026","商品[{}]经营方式与选择合同不匹配",MdModuleCodeEnum.WDS,true),
    SCWDS012P027("SCWDS012P027","商品[{}]经营方式与选择合同不匹配",MdModuleCodeEnum.WDS,true),
    SCWDS012P028("SCWDS012P028","单规格商品[{}]只允许子商品，不允许多包装，散称标准份，散称主子码商品",MdModuleCodeEnum.WDS,true),
    SCWDS012P029("SCWDS012P029","多规格商品[{}]只允许主商品，不允许子码商品",MdModuleCodeEnum.WDS,true),
    SCWDS012P030("SCWDS012P030","组合商品[{}]只允许子商品（被组合商品）",MdModuleCodeEnum.WDS,true),
    SCWDS012P031("SCWDS012P031","商品[{}]不存在合同商品定义",MdModuleCodeEnum.WDS,true),

    /** 费用项目模块 - SCMD091 */
    SCMD091B001("SCMMD091B001","费用项目 {} 已存在",MdModuleCodeEnum.MD,true),
    SCMD091B002("SCMD091B002","不存在费用项目分类 {}",MdModuleCodeEnum.MD,true),
    SCMD091B003("SCMD091B003","费用项目分类已存在",MdModuleCodeEnum.MD,true),
    SCMD091B004("SCMD091B004","费用项目分类编码已达最大值",MdModuleCodeEnum.MD,true),
    SCMD091B005("SCMD091B005","当前费用项目分类已关联费用项目，不允许删除",MdModuleCodeEnum.MD,true),

    /** 配送价格模块 - SCMD002 */
    SCMD002B001("SCMD002B001","配送价格商品明细未指定归属的配送价格单",MdModuleCodeEnum.MD,true),
    SCMD002B002("SCMD002B002","非法的配送价格单类型",MdModuleCodeEnum.MD,true),
    SCMD002B003("SCMD002B003","配送价格单商品行缺少对应行号",MdModuleCodeEnum.MD,true),
    SCMD002B004("SCMD002B004","配送价格单商品行商品编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B005("SCMD002B005","配送价格单商品行商品名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B006("SCMD002B006","配送价格单商品行商品分类编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B007("SCMD002B007","配送价格单商品行商品分类名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B008("SCMD002B008","配送价格明细未指定归属的配送价格单",MdModuleCodeEnum.MD,true),
    SCMD002B009("SCMD002B009","配送价格单明细行缺少对应行号",MdModuleCodeEnum.MD,true),
    SCMD002B010("SCMD002B010","配送价格单明细行缺少对应商品行行号",MdModuleCodeEnum.MD,true),
    SCMD002B011("SCMD002B011","配送价格单明细行收货部门信息不完整",MdModuleCodeEnum.MD,true),
    SCMD002B012("SCMD002B012","配送价格单明细行商品编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B013("SCMD002B013","配送价格单明细行商品名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B014("SCMD002B014","配送价格单明细行商品分类编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B015("SCMD002B015","配送价格单明细行商品分类名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B016("SCMD002B016","配送价格单明细行配送价格类型不可为空",MdModuleCodeEnum.MD,true),
    SCMD002B017("SCMD002B017","配送价格单明细行包含非法的含税配送价",MdModuleCodeEnum.MD,true),
    SCMD002B018("SCMD002B018","配送价格单明细行包含非法的加价率",MdModuleCodeEnum.MD,true),
    SCMD002B019("SCMD002B019","配送价格单明细行包含非法的倒扣率",MdModuleCodeEnum.MD,true),
    SCMD002B020("SCMD002B020","非法的单据状态",MdModuleCodeEnum.MD,true),
    SCMD002B021("SCMD002B021","配送价格单缺少必要的配送中心信息",MdModuleCodeEnum.MD,true),
    SCMD002B022("SCMD002B022","非法的配送价格单定义方式",MdModuleCodeEnum.MD,true),
    SCMD002B023("SCMD002B023","非法的配送价格单执行方式",MdModuleCodeEnum.MD,true),
    SCMD002B024("SCMD002B024","保存配送价格单异常",MdModuleCodeEnum.MD,true),
    SCMD002B025("SCMD002B025","配送价格单商品信息保存失败：{}",MdModuleCodeEnum.MD,true),
    SCMD002B026("SCMD002B026","配送价格单商品明细信息保存失败：{}",MdModuleCodeEnum.MD,true),
    SCMD002B027("SCMD002B027","未知的配送价格单据号",MdModuleCodeEnum.MD,true),
    SCMD002B028("SCMD002B028","配送价格单 {} 不存在",MdModuleCodeEnum.MD,true),
    SCMD002B029("SCMD002B029","当前配送价格单不允许更新",MdModuleCodeEnum.MD,true),
    SCMD002B030("SCMD002B030","当前配送价格单不允许提交审核",MdModuleCodeEnum.MD,true),
    SCMD002B031("SCMD002B031","当前用户不允许操作审核",MdModuleCodeEnum.MD,true),
    SCMD002B032("SCMD002B032","非法的审核操作",MdModuleCodeEnum.MD,true),
    SCMD002B033("SCMD002B033","当前配送价格单不允许审核",MdModuleCodeEnum.MD,true),
    SCMD002B034("SCMD002B034","审核配送价格单失败，请稍后再试",MdModuleCodeEnum.MD,true),
    SCMD002B035("SCMD002B035","未查询到配送价格单 {}",MdModuleCodeEnum.MD,true),
    SCMD002B036("SCMD002B036","当前配送价格单不允许作废",MdModuleCodeEnum.MD,true),
    SCMD002B037("SCMD002B037","不支持的操作",MdModuleCodeEnum.MD,true),
    SCMD002B038("SCMD002B038","配送价格单 {} 生效失败",MdModuleCodeEnum.MD,true),
    SCMD002B039("SCMD002B039","未知的单据定义方式",MdModuleCodeEnum.MD,true),
    SCMD002B040("SCMD002B040","配送价格单明细行包含非法的含税配送价",MdModuleCodeEnum.MD,true),
    SCMD002B041("SCMD002B041","配送价格单明细行包含非法的加价率",MdModuleCodeEnum.MD,true),
    SCMD002B042("SCMD002B042","配送价格单明细行包含非法的倒扣率",MdModuleCodeEnum.MD,true),

    /** 需求策略模块 - SCMD003 */
    SCMD003B001("SCMD003B001","存在不完整的配置项",MdModuleCodeEnum.MD,true),
    SCMD003B002("SCMD003B002","自动转需求策略保存失败",MdModuleCodeEnum.MD,true),
    SCMD003B003("SCMD003B003","非法的配置项",MdModuleCodeEnum.MD,true),
    SCMD003B004("SCMD003B004","保存需求策略状态流转规则失败 {}",MdModuleCodeEnum.MD,true),

    /** 订货策略模块 - SCMD004 */
    SCMD004B001("SCMD004B001","部门类型和部门编码不能为空",MdModuleCodeEnum.MD,true),
    SCMD004B002("SCMD004B002","配送中心或者订货时间阈值不能为空",MdModuleCodeEnum.MD,true),

    /** 需求批次策略模块 - SCMD005 */
    SCMD005B001("SCMD005B001","需求单据号不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B002("SCMD005B002","部门编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B003("SCMD005B003","部门名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B004("SCMD005B004","仓库编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B005("SCMD005B005","仓库名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B006("SCMD005B006","非法的需求批次生成方式",MdModuleCodeEnum.MD,true),
    SCMD005B007("SCMD005B007","日期间隔应为非负整数",MdModuleCodeEnum.MD,true),
    SCMD005B008("SCMD005B008","需求批次初始日期不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B009("SCMD005B009","非法的需求批次延迟时间",MdModuleCodeEnum.MD,true),
    SCMD005B010("SCMD005B010","非法的品类级别",MdModuleCodeEnum.MD,true),
    SCMD005B011("SCMD005B011","未查询到该租户【商品分类编码长度】系统参数配置",MdModuleCodeEnum.MD,true),
    SCMD005B012("SCMD005B012","该租户商品分类编码最大为 {} 级",MdModuleCodeEnum.MD,true),
    SCMD005B013("SCMD005B013","非法访问",MdModuleCodeEnum.MD,true),
    SCMD005B014("SCMD005B014","修改需求批次单据号不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B015("SCMD005B015","不存在需求批次 {}",MdModuleCodeEnum.MD,true),
    SCMD005B016("SCMD005B016","需求批次时间段信息行号不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B017("SCMD005B017","需求批次时间段信息缺少关联的需求批次单据号",MdModuleCodeEnum.MD,true),
    SCMD005B018("SCMD005B018","非法的需求批次起止时间",MdModuleCodeEnum.MD,true),
    SCMD005B019("SCMD005B019","需求批次名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B020("SCMD005B020","送货天数应为非负整数",MdModuleCodeEnum.MD,true),
    SCMD005B021("SCMD005B021","非法的追加延迟时间",MdModuleCodeEnum.MD,true),
    SCMD005B022("SCMD005B022","不存在的需求批次ID {}",MdModuleCodeEnum.MD,true),
    SCMD005B023("SCMD005B023","按日期的需求批次策略只允许一条时段信息",MdModuleCodeEnum.MD,true),
    SCMD005B024("SCMD005B024","按日期的需求批次策略时段信息的开始时间应等于结束时间",MdModuleCodeEnum.MD,true),
    SCMD005B025("SCMD005B025","未知的需求批次生成方式",MdModuleCodeEnum.MD,true),
    SCMD005B026("SCMD005B026","非同一需求批次策略的时段信息",MdModuleCodeEnum.MD,true),
    SCMD005B027("SCMD005B027","需求批次商品信息行号不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B028("SCMD005B028","需求批次商品信息缺少关联的需求批次ID",MdModuleCodeEnum.MD,true),
    SCMD005B029("SCMD005B029","需求批次商品信息缺少关联的时间段ID",MdModuleCodeEnum.MD,true),
    SCMD005B030("SCMD005B030","非法的需求批次商品类型",MdModuleCodeEnum.MD,true),
    SCMD005B031("SCMD005B031","需求批次商品/品类编码不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B032("SCMD005B032","需求批次商品/品类名称不可为空",MdModuleCodeEnum.MD,true),
    SCMD005B033("SCMD005B033","需求批次ID {} 不存在时段ID {}",MdModuleCodeEnum.MD,true),
    SCMD005B034("SCMD005B034","需求批次策略品类级别为 {}，品类编码应为 {} 位",MdModuleCodeEnum.MD,true),
    SCMD005B035("SCMD005B035","非同一需求批次策略的商品信息",MdModuleCodeEnum.MD,true),
    SCMD005B036("SCMD005B036","时间段 {} 存在重复的商品/品类数据 {}",MdModuleCodeEnum.MD,true),
    SCMD005B037("SCMD005B037","配送仓 商品/品类 数据重复：{}",MdModuleCodeEnum.MD,true),

    /** 配送预约策略模块 - SCMD006 */
    SCMD006B001("SCMD006B001","结束时间不能小于开始时间",MdModuleCodeEnum.MD,true),
    SCMD006B002("SCMD006B002","部门 {} 不存在",MdModuleCodeEnum.MD,true),
    SCMD006B003("SCMD006B003","部门 {} 已存在预约配置",MdModuleCodeEnum.MD,true),
    SCMD006B004("SCMD006B004","存在重复的部门配置",MdModuleCodeEnum.MD,true),
    SCMD006B005("SCMD006B005","非法的状态值",MdModuleCodeEnum.MD,true),
    SCMD006B006("SCMD006B006","单据编号不能为空",MdModuleCodeEnum.MD,true),
    SCMD006B007("SCMD006B007","不存在预约策略单 {}",MdModuleCodeEnum.MD,true),
    SCMD006B008("SCMD006B008","存在非法的时间区间（截止时间不能小于起始时间）",MdModuleCodeEnum.MD,true),
    SCMD006B009("SCMD006B009","不存在供应商预约策略单 {}",MdModuleCodeEnum.MD,true),
    SCMD006B010("SCMD006B010","非法操作",MdModuleCodeEnum.MD,true),
    SCMD006B011("SCMD006B011","停靠点编码 {} 已被使用",MdModuleCodeEnum.MD,true),
    SCMD006B012("SCMD006B012","停靠点编码 {} 已被使用，请修改后重试",MdModuleCodeEnum.MD,true),
    SCMD006B013("SCMD006B013","停靠点 {} 不存在",MdModuleCodeEnum.MD,true),
    SCMD006B014("SCMD006B014","停靠点商品/品类信息验证失败: {}",MdModuleCodeEnum.MD,true),
    SCMD006B015("SCMD006B015","存在非法的时间区间（截止时间不能小于起始时间）",MdModuleCodeEnum.MD,true),
    SCMD006B016("SCMD006B016","同一天内的时段不可重叠",MdModuleCodeEnum.MD,true),

    /** 供应商相关 - SCMD007 */
    SCMD007B001("SCMD007B001", "供应商分类 {} 不存在", MdModuleCodeEnum.MD, true),

    /** 采购预约模块 - SCPMS001 */
    SCPMS001B001("SCPMS001B001","请检查商品采购数量/可预约数量/预约数量相关字段是否合法（总数量 = 整件数量 * 商品包装率 + 零头数量）：{}",MdModuleCodeEnum.PMS,true),
    SCPMS001B002("SCPMS001B002","存在重复的配送价格单 {}",MdModuleCodeEnum.PMS,true),
    SCPMS001B003("SCPMS001B003","存在重复的配送价格单商品明细行 {}",MdModuleCodeEnum.PMS,true),
    SCPMS001B004("SCPMS001B004","采购订单[{}] 预约数量/预约整件数 超过可预约数量限制",MdModuleCodeEnum.PMS,true),
    SCPMS001B005("SCPMS001B005","[{}] 预约数量/预约整件数 超过可预约数量限制",MdModuleCodeEnum.PMS,true),
    SCPMS001B006("SCPMS001B006","部门 {} 停靠点 {} 不满足该预约单预约条件",MdModuleCodeEnum.PMS,true),
    SCPMS001B007("SCPMS001B007","该停靠点 {} 下未找到对应的可用时段",MdModuleCodeEnum.PMS,true),
    SCPMS001B008("SCPMS001B008","停靠点类型与预约单类型不匹配（停靠点类型需为{}/不限）",MdModuleCodeEnum.PMS,true),
    SCPMS001B009("SCPMS001B009","当前预约单包含商品总数 {}，已超出 停靠点 {} 时段 {}~{} 可预约数量 {}",MdModuleCodeEnum.PMS,true),
    SCPMS001B010("SCPMS001B010","非法操作",MdModuleCodeEnum.PMS,true),
    SCPMS001B011("SCPMS001B011","未知的访问信息",MdModuleCodeEnum.PMS,true),
    SCPMS001B012("SCPMS001B012","SCM 员工 {} 未获取到对应供应商数据",MdModuleCodeEnum.PMS,true),
    SCPMS001B013("SCPMS001B013","预约单 {} 不存在",MdModuleCodeEnum.PMS,true),
    SCPMS001B014("SCPMS001B014","当前供应商 {} 部门 {} 正在操作预约，请稍后重试！",MdModuleCodeEnum.PMS,true),
    SCPMS001B015("SCPMS001B015","该预约单不存在商品明细行，请检查后重试！",MdModuleCodeEnum.PMS,true),
    SCPMS001B016("SCPMS001B016","停靠点 {} 时段行号 {} 正在执行预约操作，请稍后再试！",MdModuleCodeEnum.PMS,true),
    SCPMS001B017("SCPMS001B017","预约单 {} 提交失败",MdModuleCodeEnum.PMS,true),
    SCPMS001B018("SCPMS001B018","预约单 {} 作废失败",MdModuleCodeEnum.PMS,true),
    SCPMS001B019("SCPMS001B019","预约单包含的采购订单商品明细行 [采购订单商品明细行id] [采购订单商品明细行行号] 不可为空：{}",MdModuleCodeEnum.PMS,true),
    SCPMS001B020("SCPMS001B020","未查询到当前预约单包含的采购订单信息",MdModuleCodeEnum.PMS,true),
    SCPMS001B021("SCPMS001B021","该预约单包含的采购订单 {} 当前状态不满足可预约条件（非 已审核/待收货）",MdModuleCodeEnum.PMS,true),
    SCPMS001B022("SCPMS001B022","该预约单预约商品总数 {} 已超过停靠点 {} 可预约数量 {}",MdModuleCodeEnum.PMS,true),
    SCPMS001B023("SCPMS001B023", "数量信息校验未通过 {}", MdModuleCodeEnum.PMS,true),

    MD_BIZ_ERROR("20001","供应链主档业务异常",MdModuleCodeEnum.MD,true);
    
    private final String code;
    private final String desc;
    private final MdModuleCodeEnum moduleCode;
    private final boolean pushFlag;

    @Override
    public String getErrorCode() {
        return this.code;
    }

    @Override
    public String getErrorMsg() {
        return this.desc;
    }

    /**
     * 多语言错误码相关方法
     */
    @Override
    public String getLangErrorCode() {
        return this.code;
    }

    @Override
    public String getSourceErrorMsg() {
        return this.desc;
    }

    @Override
    public String getModule() {
        return this.moduleCode.getCode();
    }

    @Override
    public boolean getPushFlag() {
        return this.pushFlag;
    }
}
