package com.meta.supplychain.enums.wds;


import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配送订单送货方式
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "配送订单送货方式",code = "WDDeliveryTypeEnum")
public enum WDDeliveryTypeEnum implements VerifiableEnum<Integer> {

    TO_STORE(0, "到店"),
    TO_CUSTOMER(1, "到客户"),

    ;
    private Integer code;

    private String desc;


}
