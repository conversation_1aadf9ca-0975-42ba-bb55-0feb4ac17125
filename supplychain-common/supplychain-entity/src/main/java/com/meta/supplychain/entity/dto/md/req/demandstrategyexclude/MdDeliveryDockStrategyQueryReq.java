package com.meta.supplychain.entity.dto.md.req.demandstrategyexclude;

import com.meta.supplychain.enums.md.MdDeliveryDockStatusEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 停靠点策略查询请求
 * 
 * <AUTHOR>
 */
@Data
public class MdDeliveryDockStrategyQueryReq {

    @Schema(description = "停靠点关键字（编码或名称）")
    private String dockKeyword;

    @Schema(description = "配送部门编码列表")
    private List<String> deptCodeList;

    @EnumValue(type = MdDeliveryDockStatusEnum.class)
    @Schema(description = "状态 停靠点状态枚举mdDeliveryDockStatusEnum")
    private Integer status;
} 