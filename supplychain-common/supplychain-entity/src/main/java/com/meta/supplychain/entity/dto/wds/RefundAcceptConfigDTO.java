package com.meta.supplychain.entity.dto.wds;

import com.meta.supplychain.entity.po.wds.*;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class RefundAcceptConfigDTO {

    WdShipAcceptBillPO acceptBillPO;

    List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOList;

    ShipBillPO shipBillPO;

    List<ShipBatchDetailPO> shipBatchDetailPOS;

}
