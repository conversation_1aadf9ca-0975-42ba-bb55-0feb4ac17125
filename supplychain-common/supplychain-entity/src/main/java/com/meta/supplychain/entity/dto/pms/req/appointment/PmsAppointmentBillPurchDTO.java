package com.meta.supplychain.entity.dto.pms.req.appointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.DecimalScale;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsBillDirectionEnum;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 供应商预约单关联采购订单DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约单关联采购订单DTO")
public class PmsAppointmentBillPurchDTO extends BaseDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预约单据号")
    private String appointmentBillNo;

    @NotBlank(message = "采购订单号不能为空")
    @Schema(description = "采购订单号")
    private String purchBillNo;

    @NotBlank(message = "部门编码不能为空")
    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @NotNull(message = "单据类别不能为空")
    @EnumValue(type = PmsBillDirectionEnum.class, required = true, message = "单据类别值无效")
    @Schema(description = "单据类别 枚举PmsBillDirectionEnum")
    private Integer billDirection;

    @EnumValue(type = YesOrNoEnum.class, message = "是否直流订单值无效")
    @Schema(description = "是否直流订单 枚举YesOrNoEnum")
    private Integer directSign;

    @Schema(description = "合同号")
    private String contractNo;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "送货日期")
    private LocalDate deliverDate;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "有效日期")
    private LocalDate validityDate;

    @NotNull(message = "来源不能为空")
    @Schema(description = "来源（采购订单来源）")
    private Integer billSource;

    @Schema(description = "来源单号")
    private String refBillNo;

    @Schema(description = "来源单据备注")
    private String refRemark;

    @Schema(description = "采购订单备注")
    private String purchRemark;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称")
    private String orderAttributeName;

    @Schema(description = "需求批次")
    private String purchBatchNo;

    @Schema(description = "退货原因")
    private String refundReason;

    @Schema(description = "采购整件总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalWholeQty = BigDecimal.ZERO;

    @Schema(description = "采购零头总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalOddQty = BigDecimal.ZERO;

    @Schema(description = "采购总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalQty = BigDecimal.ZERO;

    @Schema(description = "可约整件总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalCanAppointmentWholeQty = BigDecimal.ZERO;

    @Schema(description = "可约零头总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalCanAppointmentOddQty = BigDecimal.ZERO;

    @Schema(description = "可约总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalCanAppointmentQty = BigDecimal.ZERO;

    @NotNull(message = "本次预约总件数不能为空")
    @Schema(description = "本次预约总件数")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalAppointmentWholeQty = BigDecimal.ZERO;

    @NotNull(message = "本次预约零头总数量不能为空")
    @Schema(description = "本次预约零头总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalAppointmentOddQty = BigDecimal.ZERO;

    @NotNull(message = "本次预约总数量不能为空")
    @Schema(description = "本次预约总数量")
    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    private BigDecimal totalAppointmentQty = BigDecimal.ZERO;

    @Schema(description = "商品信息")
    private List<PmsAppointmentBillGoodsDTO> goodsList = new ArrayList<>();

    /**
     * 以商品维度展示的时候用于暂存对应商品行id
     */
    @Schema(description = "采购订单商品明细行id")
    private Long purchGoodsDetailId;

    /**
     * 以商品维度展示的时候用于暂存对应商品行行号
     */
    @Schema(description = "采购订单商品明细行行号")
    private Long purchGoodsDetailInsideId;

    /**
     * 以商品维度展示的时候用于暂存对应商品行已预约数量
     */
    @Schema(description = "已预约数量(快照时)")
    private BigDecimal goodsAppointedQty = BigDecimal.ZERO;

    /**
     * 差异字段
     * 仅当改字段为true时，差异字段才有参考意义
     */
    @Schema(description = "当前采购单数据是否有变化")
    private Boolean changed = false;

    @Schema(description = "是否有对应的采购订单行")
    private Boolean exist = true;

    @Schema(description = "采购整件总数量")
    private BigDecimal totalWholeQtyChanged;

    @Schema(description = "采购零头总数量")
    private BigDecimal totalOddQtyChanged;

    @Schema(description = "采购总数量")
    private BigDecimal totalQtyChanged;

    @Schema(description = "可约整件总数量")
    private BigDecimal totalCanAppointmentWholeQtyChanged;

    @Schema(description = "可约零头总数量")
    private BigDecimal totalCanAppointmentOddQtyChanged;

    @Schema(description = "可约总数量")
    private BigDecimal totalCanAppointmentQtyChanged;

    /**
     * 预约数量校验
     */
    @SuppressWarnings("DuplicatedCode")
    public int validateAppointmentQty() {
        if (totalCanAppointmentQty.compareTo(totalAppointmentQty) < 0) {
            return -1;
        }
        if (totalCanAppointmentWholeQty.compareTo(totalAppointmentWholeQty) < 0) {
            return -2;
        }
        return 0;
    }

    /**
     * 差异对比
     */
    public void markChanged(PmsAppointmentBillPurchDTO latest) {
        if (latest == null) {
            return;
        }
        if (totalWholeQty.compareTo(latest.totalWholeQty) != 0) {
            changed = true;
            totalWholeQtyChanged = latest.totalWholeQty;
        }
        if (totalOddQty.compareTo(latest.totalOddQty) != 0) {
            changed = true;
            totalOddQtyChanged = latest.totalOddQty;
        }
        if (totalQty.compareTo(latest.totalQty) != 0) {
            changed = true;
            totalQtyChanged = latest.totalQty;
        }
        if (totalCanAppointmentQty.compareTo(latest.totalCanAppointmentQty) != 0) {
            changed = true;
            totalCanAppointmentQtyChanged = latest.totalCanAppointmentQty;
        }
        if (totalCanAppointmentWholeQty.compareTo(latest.totalCanAppointmentWholeQty) != 0) {
            changed = true;
            totalCanAppointmentWholeQtyChanged = latest.totalCanAppointmentWholeQty;
        }
        if (totalCanAppointmentOddQty.compareTo(latest.totalCanAppointmentOddQty) != 0) {
            changed = true;
            totalCanAppointmentOddQtyChanged = latest.totalCanAppointmentOddQty;
        }
    }

    // 汇总采购单据数量信息
    public void summarize() {
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }
        BigDecimal totalWholeQty = BigDecimal.ZERO, totalOddQty = BigDecimal.ZERO, totalQty = BigDecimal.ZERO,
                totalCanAppointmentWholeQty = BigDecimal.ZERO, totalCanAppointmentOddQty = BigDecimal.ZERO, totalCanAppointmentQty = BigDecimal.ZERO,
                totalAppointmentWholeQty = BigDecimal.ZERO, totalAppointmentOddQty = BigDecimal.ZERO, totalAppointmentQty = BigDecimal.ZERO;

        for (PmsAppointmentBillGoodsDTO goods : goodsList) {
            totalQty = totalQty.add(goods.getPurchQty());
            totalOddQty = totalOddQty.add(goods.getOddQty());
            totalWholeQty = totalWholeQty.add(goods.getWholeQty());
            totalCanAppointmentQty = totalCanAppointmentQty.add(goods.getCanAppointmentQty());
            totalCanAppointmentWholeQty = totalCanAppointmentWholeQty.add(goods.getCanAppointmentWholeQty());
            totalCanAppointmentOddQty = totalCanAppointmentOddQty.add(goods.getCanAppointmentOddQty());
            totalAppointmentQty = totalAppointmentQty.add(goods.getAppointmentQty());
            totalAppointmentOddQty = totalAppointmentOddQty.add(goods.getAppointmentOddQty());
            totalAppointmentWholeQty = totalAppointmentWholeQty.add(goods.getAppointmentWholeQty());
        }
        this.totalQty = totalQty;
        this.totalOddQty = totalOddQty;
        this.totalWholeQty = totalWholeQty;
        this.totalCanAppointmentWholeQty = totalCanAppointmentWholeQty;
        this.totalCanAppointmentOddQty = totalCanAppointmentOddQty;
        this.totalCanAppointmentQty = totalCanAppointmentQty;
        this.totalAppointmentWholeQty = totalAppointmentWholeQty;
        this.totalAppointmentOddQty = totalAppointmentOddQty;
        this.totalAppointmentQty = totalAppointmentQty;
    }
}