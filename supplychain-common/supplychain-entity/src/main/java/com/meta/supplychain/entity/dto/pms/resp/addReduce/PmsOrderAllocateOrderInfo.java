package com.meta.supplychain.entity.dto.pms.resp.addReduce;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PmsOrderAllocateOrderInfo {

    /**
     * 业务单据类型
     * DIRECT_PURCHASE("direct_purchase", "直流采购"),
     *     DIRECT_TRANSFER("direct_transfer", "直流配送"),
     *     STORE_TRANSFER("store_transfer", "门店配送"),
     *     TRANSFER_TO_PURCHASE("transfer_to_purchase", "配转采"),
     */
    @Schema(description="业务单据类型 direct_purchase:直流采购 direct_transfer, 直流配送  store_transfer, 门店配送  transfer_to_purchase, 配转采")
    private String billType;

    /**
     * 单据审核时间
     */
    @Schema(description="单据审核时间")
    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    private LocalDateTime auditTime;

    /**
     * 订单号
     */
    @Schema(description="订单号")
    private String billNo;

    /**
     * 单内序号
     */
    @Schema(description="单内序号")
    private Long insideId;

    /**
//     * 单位比率，包装率
     */
    @Schema(description="单位比率，包装率")
    private BigDecimal purchUnitRate;

    /**
     * 订单数量
     */
    @Schema(description="订单数量")
    private BigDecimal orderQty;

    /**
     * 本次累计已调整数量
     */
    @Schema(description="本次累计已调整数量")
    private BigDecimal adjustedQty;

    /**
     * 预计调整数量
     */
    @Schema(description="预计调整数量")
    private BigDecimal expectAdjustQty;

    /**
     * 零售单价
     */
    @Schema(description="零售单价")
    private BigDecimal salePrice;

    /**
     * 零售金额
     */
    @Schema(description="零售金额")
    private BigDecimal saleMoney;

    /**
     * 税金 单位元
     */
    @Schema(description="税金 单位元")
    private BigDecimal tax;

}
