package com.meta.supplychain.entity.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BaseEntity {
    /** 租户号 */
    @TableField(exist = false)
    private Long tenantId;

    @TableField(fill = FieldFill.INSERT)
    private String createCode;

    /** 创建人ssoId */
    @TableField(fill = FieldFill.INSERT)
    private Long createUid;

    /** 创建人姓名 */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 修改人ssoId */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUid;

    /** 修改人工号 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateCode;

    /** 修改人姓名 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateName;

    /** 修改时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /** 逻辑删除状态 1:删除 0:正常 */
    private Integer delFlag;
}
