package com.meta.supplychain.entity.dto.pms.resp.purch;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划单查询请求
 */
@Data
@Schema(description = "采购计划单查询请求响应")
public class QueryPurchPricingResp {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "明细列表")
    private List<QueryPurchPricingDetailResp> detailRespList;

    @Schema(description = "单据总数")
    private Integer billTotal;

    @Data
    public static class QueryPurchPricingDetailResp {
        @Schema(description = "主键")
        private Long id;

        @Schema(description = "采购订单号")
        private String billNo;

        @Schema(description = "部门编码")
        private String deptCode;

        @Schema(description = "部门名称")
        private String deptName;

        @Schema(description = "是否直流订单 0-非直流 1-直流")
        private Integer directSign;

        @Schema(description = "是否直流订单 0-非直流 1-直流")
        private String directSignDesc;

        @Schema(description = "供应商编码")
        private String supplierCode;

        @Schema(description = "供应商名称")
        private String supplierName;

        @Schema(description = "合同号")
        private String contractNo;

        @Schema(description = "送货日期")
        @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
        private LocalDate deliverDate;

        @Schema(description = "有效日期")
        @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
        private LocalDate validityDate;

        @Schema(description = "来源 0-手工单，1-需求单，2-配转采")
        private Integer billSource;

        @Schema(description = "来源 0-手工单，1-需求单，2-配转采")
        private String billSourceDesc;

        @Schema(description = "来源单号")
        private String srcBillNo;

        @Schema(description = "来源单据备注")
        private String srcRemark;

        @Schema(description = "采购订单备注")
        private String purchRemark;

        @Schema(description = "审核人工号")
        private String auditCode;

        @Schema(description = "审核人名称")
        private String auditName;

        @Schema(description = "审核时间")
        @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
        private LocalDateTime auditTime;

        @Schema(description = "审核备注")
        private String auditRemark;

        @Schema(description = "订货属性编码")
        private String orderAttributeCode;

        @Schema(description = "订货属性名称")
        private String orderAttributeName;

        @Schema(description = "需求批次")
        private String purchBatchNo;

        @Schema(description = "退货原因")
        private String refundReason;

        @Schema(description = "退货原因描述")
        private String refundReasonDesc;

        @Schema(description = "采购类型 0-门店采购，1-配送采购")
        private Integer billType;

        @Schema(description = "单内序号")
        private Long insideId;

        @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
        private Integer skuType;

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "商品条码")
        private String barcode;

        @Schema(description = "商品货号")
        private String goodsNo;

        @Schema(description = "品类编码")
        private String categoryCode;

        @Schema(description = "品类名称")
        private String categoryName;

        @Schema(description = "品牌编码")
        private String brandCode;

        @Schema(description = "品牌名称")
        private String brandName;

        @Schema(description = "单位")
        private String basicUnit;

        @Schema(description = "整件单位")
        private String packageUnit;

        @Schema(description = "规格")
        private String skuModel;

        @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
        private Integer saleMode;

        @Schema(description = "进项税率")
        private BigDecimal inputTaxRate;

        @Schema(description = "销项税率")
        private BigDecimal outputTaxRate;

        @Schema(description = "计量属性 0：普通 1：计量 2：称重")
        private Integer uomAttr;

        @Schema(description = "商品包装率")
        private BigDecimal unitRate;

        @Schema(description = "订货包装率")
        private BigDecimal purchUnitRate;

        @Schema(description = "促销期进价")
        private BigDecimal promotePeriodPrice;

        @Schema(description = "促销活动编码")
        private String promoteActivityCode;

        @Schema(description = "促销活动名称")
        private String promoteActivityName;

        @Schema(description = "合同特供价")
        private BigDecimal contractSpecialPrice;

        @Schema(description = "合同进价")
        private BigDecimal contractPrice;

        @Schema(description = "合同最高进价")
        private BigDecimal contractMaxPrice;

        @Schema(description = "最后进价")
        private BigDecimal lastPurchPrice;

        @Schema(description = "采购价格")
        private BigDecimal purchPrice;

        @Schema(description = "整件数量")
        private BigDecimal wholeQty;

        @Schema(description = "零头数量")
        private BigDecimal oddQty;

        @Schema(description = "采购数量")
        private BigDecimal purchQty;

        @Schema(description = "采购金额")
        private BigDecimal purchMoney;

        @Schema(description = "采购税金")
        private BigDecimal purchTax;

        @Schema(description = "零售单价")
        private BigDecimal salePrice;

        @Schema(description = "零售金额")
        private BigDecimal saleMoney;

        @Schema(description = "效期商品标识 0否 1是")
        private Integer periodFlag;

        @Schema(description = "效期条码")
        private String periodBarcode;

        @Schema(description = "效期批号")
        private String periodBatchNo;

        @Schema(description = "效期生产日期")
        @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
        private LocalDate productDate;

        @Schema(description = "效期到期日期")
        @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
        private LocalDate expireDate;
    }

}
