package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.md.MdDeptTypeEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 供应商预约策略部门条件限制DTO
 */
@Data
@Schema(description = "供应商预约策略部门条件限制")
public class MdDeliveryDockDeptDTO extends BaseDTO {
    
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "行号不可为空")
    @Schema(description = "行号")
    private Integer insideId;

    @NotBlank(message = "停靠点编码不可为空")
    @Schema(description = "停靠点编码")
    private String dockCode;

    @NotBlank(message = "部门编码不可为空")
    @Schema(description = "部门编码")
    private String deptCode;

    @NotBlank(message = "部门名称不可为空")
    @Schema(description = "部门名称")
    private String deptName;

    @EnumValue(type = MdDeptTypeEnum.class, required = true, message = "部门类型不可为空")
    @Schema(description = "部门类型枚举 MdDeptTypeEnum")
    private Integer deptType;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        MdDeliveryDockDeptDTO that = (MdDeliveryDockDeptDTO) o;
        return Objects.equals(deptCode, that.deptCode) && Objects.equals(deptType, that.deptType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), deptCode, deptType);
    }
}