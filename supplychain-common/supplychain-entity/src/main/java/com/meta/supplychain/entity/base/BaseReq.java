package com.meta.supplychain.entity.base;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.entity.dto.OpInfo;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface BaseReq {

    List<String> getReqDeptCodeList();

    String getReqDeptCode();

    void setReqDeptCodeList(List<String> deptCodeList);


    /**
     * 填充分管部门
     *
     * @param opInfo
     */
    default void fulfillDeptList(OpInfo opInfo){
        if (Objects.isNull(opInfo)) {
            BizExceptions.throwWithMsg("登录态缺失");
        }
        try {
            List<String> manageDeptCodeList = opInfo.getManageDeptCodeList();
            if (CollectionUtils.isEmpty(manageDeptCodeList)) {
                // 分管为空 不限制查询
                this.setReqDeptCodeList(null);
                return;
            }
            String reqDeptCode = getReqDeptCode();
            if (StringUtils.isNotBlank(reqDeptCode)) {
                if (!manageDeptCodeList.contains(reqDeptCode)) {
                    BizExceptions.throwWithMsg("当前分管部门没有查询权限");
                }
            }
            List<String> reqDeptCodeList = getReqDeptCodeList();
            if (CollectionUtils.isNotEmpty(reqDeptCodeList)) {
                // 取交集
                Sets.SetView<String> intersection = Sets.intersection(Sets.newHashSet(manageDeptCodeList), Sets.newHashSet(reqDeptCodeList));
                List<String> collect = new ArrayList<>(intersection);
                // 请求有限制部门  取交集
                this.setReqDeptCodeList(collect);
            }else {
                // 请求没有限制部门  限制部门为分管部门
                this.setReqDeptCodeList(manageDeptCodeList);
            }
            // 最后判断总部
            if (opInfo.getOriginDeptFlag()) {
                // 总部不限制
                this.setReqDeptCodeList(null);
            }
        } catch (Exception e) {
            BizExceptions.throwWithMsg("填充分管部门失败");
        }
    }
}
