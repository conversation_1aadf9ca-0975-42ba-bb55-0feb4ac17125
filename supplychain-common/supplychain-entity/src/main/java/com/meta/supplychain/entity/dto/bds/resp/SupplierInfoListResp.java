package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/5/29 14:42
 */
@Builder
@Data
public class SupplierInfoListResp {

    private Integer total;

    private List<SupplierInfo> rows;


    @Data
    public static class SupplierInfo{
        /**
         * 供应商编码
         */
        private String code;

        /**
         * 供应商名称
         */
        private String name;

        /**
         * 联系人
         */
        private String linkMan;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 状态 1启用 0停用
         */
        private Integer status;

        /**
         * 外部编码
         */
        private String outCode;
    }


}
