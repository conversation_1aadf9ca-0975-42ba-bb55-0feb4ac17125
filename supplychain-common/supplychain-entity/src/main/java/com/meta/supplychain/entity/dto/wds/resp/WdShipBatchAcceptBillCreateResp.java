package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(description = "配送验收单合并验收结果")
@Builder
public class WdShipBatchAcceptBillCreateResp {

    @Schema(description = "成功验收单号列表")
    List<String> successBillNoList;


    @Schema(description = "失败验收单号列表")
    List<ShipBatchAcceptErrorInfo> errorBillNoList;

    @Getter
    @Setter
    @Builder
    public static class ShipBatchAcceptErrorInfo{
        @Schema(description = "配送单号")
        String shipBillNo;

        @Schema(description = "错误信息")
        String errorMsg;
    }

}
