package com.meta.supplychain.convert.pms;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillGoodsDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPurchDTO;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPurchPO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商预约单关联采购订单转换器
 * <AUTHOR>
 */
@Mapper
public interface PmsAppointmentBillPurchConvert extends StandardEnumConvert {
    
    PmsAppointmentBillPurchConvert INSTANCE = Mappers.getMapper(PmsAppointmentBillPurchConvert.class);

    /**
     * PO转DTO
     */
    PmsAppointmentBillPurchDTO convertPo2Dto(PmsAppointmentBillPurchPO po);

    /**
     * PO列表转DTO列表
     */
    List<PmsAppointmentBillPurchDTO> convertPo2DtoList(List<PmsAppointmentBillPurchPO> poList);

    /**
     * DTO转PO - 忽略BaseDTO字段和id字段
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createCode", ignore = true),
            @Mapping(target = "createUid", ignore = true),
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateUid", ignore = true),
            @Mapping(target = "updateCode", ignore = true),
            @Mapping(target = "updateName", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PmsAppointmentBillPurchPO convertDto2Po(PmsAppointmentBillPurchDTO dto);

    /**
     * DTO列表转PO列表 - 忽略BaseDTO字段和id字段
     */
    List<PmsAppointmentBillPurchPO> convertDto2PoList(List<PmsAppointmentBillPurchDTO> dtoList);

    /**
     * 复制
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createCode", ignore = true),
            @Mapping(target = "createUid", ignore = true),
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateUid", ignore = true),
            @Mapping(target = "updateCode", ignore = true),
            @Mapping(target = "updateName", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "goodsList", ignore = true)
    })
    PmsAppointmentBillPurchDTO copyPlainProperty(PmsAppointmentBillPurchDTO po);

    /**
     * 原始采购订单/退单明细转供应商预约单
     * 针对归属同一采购单的商品进行转换
     */
    default PmsAppointmentBillPurchDTO convertOriginalBill2Dto(List<PurchaseBillDetailSumResp> originalBillList) {
        if (CollectionUtils.isEmpty(originalBillList)) {
            return null;
        }
        PurchaseBillDetailSumResp first = originalBillList.get(0);

        PmsAppointmentBillPurchDTO dto = new PmsAppointmentBillPurchDTO();
        // 采购订单号
        dto.setPurchBillNo(first.getBillNo());
        // 部门编码
        dto.setDeptCode(first.getDeptCode());
        // 部门名称
        dto.setDeptName(first.getDeptName());
        // 单据类别 (billDirection)
        dto.setBillDirection(first.getBillDirection());
        // 是否直流订单
        dto.setDirectSign(first.getDirectSign());
        // 合同号
        dto.setContractNo(first.getContractNo());
        // 送货日期
        dto.setDeliverDate(first.getDeliverDate());
        // 有效日期
        dto.setValidityDate(first.getValidityDate());
        // 来源
        dto.setBillSource(first.getBillSource());
        // 来源单号
        dto.setRefBillNo(first.getSrcBillNo());
        // 来源单据备注
        dto.setRefRemark(first.getSrcRemark());
        // 采购订单备注
        dto.setPurchRemark(first.getPurchRemark());
        // 审核时间
        dto.setAuditTime(first.getAuditTime());
        // 审核备注
        dto.setAuditRemark(first.getAuditRemark());
        // 订货属性编码
        dto.setOrderAttributeCode(first.getOrderAttributeCode());
        // 订货属性名称
        dto.setOrderAttributeName(first.getOrderAttributeName());
        // 需求批次
        dto.setPurchBatchNo(first.getPurchBatchNo());
        // 退货原因
        dto.setRefundReason(first.getRefundReasonDesc());
        
        // 采购汇总数量信息
        // 按照商品包装率重新计算整件数和零头数
        BigDecimal totalPurchQty = BigDecimal.ZERO, totalPurchOddQty = BigDecimal.ZERO, totalPurchWholeQty = BigDecimal.ZERO;

        // 可预约数量计算 & 商品明细行转换
        BigDecimal availableQty = BigDecimal.ZERO, availableOddQty = BigDecimal.ZERO, availableWholeQty = BigDecimal.ZERO;
        List<PmsAppointmentBillGoodsDTO> goodsList = new ArrayList<>();
        for (PurchaseBillDetailSumResp originalDetail : originalBillList) {
            if (originalDetail.getPurchQty() == null) {
                originalDetail.setPurchQty(BigDecimal.ZERO);
            }
            if (originalDetail.getAppointmentQty() == null) {
                originalDetail.setAppointmentQty(BigDecimal.ZERO);
            }
            // 错误兜底处理
            if (originalDetail.getPurchQty().compareTo(originalDetail.getAppointmentQty()) < 0) {
                originalDetail.setPurchQty(BigDecimal.ZERO);
                originalDetail.setAppointmentQty(originalDetail.getPurchQty());
            }

            // 采购订单原始数量
            // 重新按照商品订货包装率换算
            totalPurchQty = totalPurchQty.add(originalDetail.getPurchQty());

            BigDecimal goodsPurchWholeQty = originalDetail.getPurchQty().divide(originalDetail.getUnitRate(), 0, RoundingMode.FLOOR);
            originalDetail.setWholeQty(goodsPurchWholeQty);
            totalPurchWholeQty = totalPurchWholeQty.add(goodsPurchWholeQty);

            BigDecimal goodsPurchOddQty = originalDetail.getPurchQty().subtract(goodsPurchWholeQty.multiply(originalDetail.getUnitRate()));
            originalDetail.setOddQty(goodsPurchOddQty);
            totalPurchOddQty = totalPurchOddQty.add(goodsPurchOddQty);

            // 该商品行的可预约数量
            BigDecimal qty = originalDetail.getPurchQty().subtract(originalDetail.getAppointmentQty());
            BigDecimal wholeQyt = qty.divide(originalDetail.getUnitRate(), 0, RoundingMode.FLOOR);
            BigDecimal oddQty = qty.subtract(wholeQyt.multiply(originalDetail.getUnitRate()));

            // 汇总到订单维护
            availableQty = availableQty.add(qty);
            availableWholeQty = availableWholeQty.add(wholeQyt);
            availableOddQty = availableOddQty.add(oddQty);

            // 处理商品行信息
            PmsAppointmentBillGoodsDTO goodsDto = new PmsAppointmentBillGoodsDTO();

            // 记录该商品关联的订单
            goodsDto.setRefPurchBill(this.copyPlainProperty(dto));
            goodsDto.setPurchGoodsDetailId(goodsDto.getId());

            // 基本商品信息
            goodsDto.setPurchBillNo(originalDetail.getBillNo());
            goodsDto.setPurchGoodsDetailId(originalDetail.getId());
            goodsDto.setSkuType(originalDetail.getSkuType());
            goodsDto.setSkuCode(originalDetail.getSkuCode());
            goodsDto.setSkuName(originalDetail.getSkuName());
            goodsDto.setBarcode(originalDetail.getBarcode());
            goodsDto.setGoodsNo(originalDetail.getGoodsNo());
            goodsDto.setCategoryCode(originalDetail.getCategoryCode());
            goodsDto.setCategoryName(originalDetail.getCategoryName());
            goodsDto.setBrandCode(originalDetail.getBrandCode());
            goodsDto.setBrandName(originalDetail.getBrandName());
            goodsDto.setBasicUnit(originalDetail.getBasicUnit());
            goodsDto.setPackageUnit(originalDetail.getPackageUnit());
            goodsDto.setSkuModel(originalDetail.getSkuModel());
            goodsDto.setInputTaxRate(originalDetail.getInputTaxRate());
            goodsDto.setUnitRate(originalDetail.getUnitRate());
            goodsDto.setPurchUnitRate(originalDetail.getPurchUnitRate());
            goodsDto.setUomAttr(originalDetail.getUomAttr());
            goodsDto.setPurchGoodsDetailInsideId(originalDetail.getInsideId());

            // 采购数量信息
            goodsDto.setWholeQty(originalDetail.getWholeQty());
            goodsDto.setOddQty(originalDetail.getOddQty());
            goodsDto.setPurchQty(originalDetail.getPurchQty());

            // 计算可预约数量信息
            goodsDto.setCanAppointmentQty(qty);
            goodsDto.setCanAppointmentWholeQty(wholeQyt);
            goodsDto.setCanAppointmentOddQty(oddQty);

            // 已预约数量
            goodsDto.setAppointedQty(originalDetail.getAppointmentQty());

            // 添加到商品列表
            goodsList.add(goodsDto);
        }
        dto.setTotalQty(totalPurchQty);
        dto.setTotalWholeQty(totalPurchWholeQty);
        dto.setTotalOddQty(totalPurchOddQty);
        dto.setTotalCanAppointmentWholeQty(availableWholeQty);
        dto.setTotalCanAppointmentOddQty(availableOddQty);
        dto.setTotalCanAppointmentQty(availableQty);

        // 设置商品列表
        dto.setGoodsList(goodsList);

        // 更新归属订单数量
        for (PmsAppointmentBillGoodsDTO goods : goodsList) {
            goods.getRefPurchBill().setTotalQty(totalPurchQty);
            goods.getRefPurchBill().setTotalWholeQty(totalPurchWholeQty);
            goods.getRefPurchBill().setTotalOddQty(totalPurchOddQty);
        }
        
        return dto;
    }

    /**
     * 预约单维度转商品维度
     */
    default List<PmsAppointmentBillGoodsDTO> convertBillItem2GoodsItem(List<PmsAppointmentBillPurchDTO> billItemList) {
        if (CollectionUtils.isEmpty(billItemList)) {
            return Collections.emptyList();
        }

        // 按照 商品类型 & 商品编码 & 商品包装率 分组
        Map<String, List<PmsAppointmentBillGoodsDTO>> groupingGoodsItemMap = billItemList.stream()
                .map(PmsAppointmentBillPurchDTO::getGoodsList)
                .flatMap(List::stream)
                .filter(goodsItem -> goodsItem.getSkuType() != null
                        && StringUtils.isNotBlank(goodsItem.getSkuCode())
                        && goodsItem.getUnitRate() != null)
                .filter(goodsItem -> goodsItem.getRefPurchBill() != null)
                .collect(Collectors.groupingBy(goodsItem -> String.format("%s-%s-%s",
                        goodsItem.getSkuType(),
                        goodsItem.getSkuCode(),
                        goodsItem.getUnitRate().toPlainString())));

        // 以商品维度汇总数据
        List<PmsAppointmentBillGoodsDTO> pmsAppointmentBillGoodsList = new ArrayList<>();
        for (List<PmsAppointmentBillGoodsDTO> goodsItemGroup : groupingGoodsItemMap.values()) {
            BigDecimal purchQty = BigDecimal.ZERO, purchOddQty = BigDecimal.ZERO, purchWholeQty = BigDecimal.ZERO;
            BigDecimal availableQty = BigDecimal.ZERO, availableOddQty = BigDecimal.ZERO, availableWholeQty = BigDecimal.ZERO;
            BigDecimal appointmentQty = BigDecimal.ZERO, appointmentOddQty = BigDecimal.ZERO, appointmentWholeQty = BigDecimal.ZERO;

            PmsAppointmentBillGoodsDTO first = goodsItemGroup.get(0);

            for (PmsAppointmentBillGoodsDTO goodsItem : goodsItemGroup) {
                PmsAppointmentBillPurchDTO refPurchBill = goodsItem.getRefPurchBill();
                refPurchBill.setTotalQty(goodsItem.getPurchQty());
                refPurchBill.setTotalOddQty(goodsItem.getOddQty());
                refPurchBill.setTotalWholeQty(goodsItem.getWholeQty());
                refPurchBill.setTotalCanAppointmentQty(goodsItem.getCanAppointmentQty());
                refPurchBill.setTotalCanAppointmentOddQty(goodsItem.getCanAppointmentOddQty());
                refPurchBill.setTotalCanAppointmentWholeQty(goodsItem.getCanAppointmentWholeQty());
                refPurchBill.setTotalAppointmentQty(goodsItem.getAppointmentQty());
                refPurchBill.setTotalAppointmentOddQty(goodsItem.getAppointmentOddQty());
                refPurchBill.setTotalAppointmentWholeQty(goodsItem.getAppointmentWholeQty());
                refPurchBill.setPurchGoodsDetailId(goodsItem.getPurchGoodsDetailId());
                refPurchBill.setGoodsAppointedQty(goodsItem.getAppointedQty());
                refPurchBill.setPurchGoodsDetailInsideId(goodsItem.getPurchGoodsDetailInsideId());
                first.getPurchList().add(refPurchBill);

                purchQty = purchQty.add(goodsItem.getPurchQty());
                purchOddQty = purchOddQty.add(goodsItem.getOddQty());
                purchWholeQty = purchWholeQty.add(goodsItem.getWholeQty());
                availableQty = availableQty.add(goodsItem.getCanAppointmentQty());
                availableOddQty = availableOddQty.add(goodsItem.getCanAppointmentOddQty());
                availableWholeQty = availableWholeQty.add(goodsItem.getCanAppointmentWholeQty());
                appointmentQty = appointmentQty.add(goodsItem.getAppointmentQty());
                appointmentOddQty = appointmentOddQty.add(goodsItem.getAppointmentOddQty());
                appointmentWholeQty = appointmentWholeQty.add(goodsItem.getAppointmentWholeQty());
            }

            first.setPurchQty(purchQty);
            first.setOddQty(purchOddQty);
            first.setWholeQty(purchWholeQty);
            first.setCanAppointmentQty(availableQty);
            first.setCanAppointmentOddQty(availableOddQty);
            first.setCanAppointmentWholeQty(availableWholeQty);
            first.setAppointmentQty(appointmentQty);
            first.setAppointmentOddQty(appointmentOddQty);
            first.setAppointmentWholeQty(appointmentWholeQty);
            pmsAppointmentBillGoodsList.add(first);
        }

        return pmsAppointmentBillGoodsList;
    }

    /**
     * 商品维度转预约单维度
     */
    default List<PmsAppointmentBillPurchDTO> convertGoodsItem2PurchItem(List<PmsAppointmentBillGoodsDTO> goodsItemList) {
        if (CollectionUtils.isEmpty(goodsItemList)) {
            return Collections.emptyList();
        }

        HashMap<String, PmsAppointmentBillPurchDTO> mappingPurchBillNo2PurchItem = new HashMap<>();
        for (PmsAppointmentBillGoodsDTO goodsItem : goodsItemList) {
            List<PmsAppointmentBillPurchDTO> purchList = goodsItem.getPurchList();
            if (CollectionUtils.isEmpty(purchList)) {
                continue;
            }

            for (PmsAppointmentBillPurchDTO purchItem : purchList) {
                PmsAppointmentBillGoodsDTO copiedGoodsItem = PmsAppointmentBillGoodsConvert.INSTANCE.copyPlainProperty(goodsItem);
                copiedGoodsItem.setPurchBillNo(purchItem.getPurchBillNo());
                copiedGoodsItem.setPurchQty(purchItem.getTotalQty());
                copiedGoodsItem.setOddQty(purchItem.getTotalOddQty());
                copiedGoodsItem.setWholeQty(purchItem.getTotalWholeQty());
                copiedGoodsItem.setCanAppointmentQty(purchItem.getTotalCanAppointmentQty());
                copiedGoodsItem.setCanAppointmentOddQty(purchItem.getTotalCanAppointmentOddQty());
                copiedGoodsItem.setCanAppointmentWholeQty(purchItem.getTotalCanAppointmentWholeQty());
                copiedGoodsItem.setAppointmentQty(purchItem.getTotalAppointmentQty());
                copiedGoodsItem.setAppointmentOddQty(purchItem.getTotalAppointmentOddQty());
                copiedGoodsItem.setAppointmentWholeQty(purchItem.getTotalAppointmentWholeQty());
                copiedGoodsItem.setPurchGoodsDetailId(purchItem.getPurchGoodsDetailId());
                copiedGoodsItem.setPurchGoodsDetailInsideId(purchItem.getPurchGoodsDetailInsideId());
                copiedGoodsItem.setAppointedQty(purchItem.getGoodsAppointedQty());

                PmsAppointmentBillPurchDTO existPurchBill = mappingPurchBillNo2PurchItem.get(purchItem.getPurchBillNo());
                if (existPurchBill == null) {
                    purchItem.getGoodsList().add(copiedGoodsItem);
                    mappingPurchBillNo2PurchItem.put(purchItem.getPurchBillNo(), purchItem);
                    continue;
                }

                existPurchBill.setTotalQty(existPurchBill.getTotalQty().add(purchItem.getTotalQty()));
                existPurchBill.setTotalOddQty(existPurchBill.getTotalOddQty().add(purchItem.getTotalOddQty()));
                existPurchBill.setTotalWholeQty(existPurchBill.getTotalWholeQty().add(purchItem.getTotalWholeQty()));
                existPurchBill.setTotalCanAppointmentQty(existPurchBill.getTotalCanAppointmentQty().add(purchItem.getTotalCanAppointmentQty()));
                existPurchBill.setTotalCanAppointmentOddQty(existPurchBill.getTotalCanAppointmentOddQty().add(purchItem.getTotalCanAppointmentOddQty()));
                existPurchBill.setTotalCanAppointmentWholeQty(existPurchBill.getTotalCanAppointmentWholeQty().add(purchItem.getTotalCanAppointmentWholeQty()));
                existPurchBill.setTotalAppointmentQty(existPurchBill.getTotalAppointmentQty().add(purchItem.getTotalAppointmentQty()));
                existPurchBill.setTotalAppointmentOddQty(existPurchBill.getTotalAppointmentOddQty().add(purchItem.getTotalAppointmentOddQty()));
                existPurchBill.setTotalAppointmentWholeQty(existPurchBill.getTotalAppointmentWholeQty().add(purchItem.getTotalAppointmentWholeQty()));
                existPurchBill.getGoodsList().add(copiedGoodsItem);
            }
        }
        return new ArrayList<>(mappingPurchBillNo2PurchItem.values());
    }
}