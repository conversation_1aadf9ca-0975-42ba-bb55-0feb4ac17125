package com.meta.supplychain.entity.dto.goods.req;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "商品列表查询参数")
public class SimpleGoodsListQueryReq extends PageParams {

    @Schema(description = "商品编码集合")
    private List<String> skuCodeList;

    @Schema(description = "条码集合")
    private List<String> barCodeList;

}
