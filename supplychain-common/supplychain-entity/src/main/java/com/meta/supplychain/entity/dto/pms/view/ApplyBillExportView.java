package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 订货申请请求
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplyBillExportView {

    @ViewField(headerName = "申请单号")
    private String billNo;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    @ViewField(headerName = "状态")
    private String status;

    @ViewField(headerName = "订货属性")
    private String attributeCode;

    @ViewField(headerName = "管理分类编码")
    private String manageCategory;

    /**
     * 申请数量
     */
    @ViewField(headerName = "申请数量")
    private BigDecimal totalOrderQty;
    /**
     * 申请金额
     */
    @ViewField(headerName = "金额")
    private String totalPurchTaxMoney;
    /**
     * 订货税金 单位元
     */
    @ViewField(headerName = "申请税金")
    private String totalPurchTax;

    @ViewField(headerName = "送货日期")
    private String deliverDate;
    @ViewField(headerName = "有效日期")
    private String validityDate;
    @ViewField(headerName = "送货方式")
    private String sendMode;
    @ViewField(headerName = "制单时间")
    private String buildTime;
    @ViewField(headerName = "制单人")
    private String buildMan;
    @ViewField(headerName = "最后修改时间")
    private String updateTime;
    @ViewField(headerName = "修改人")
    private String modifyMan;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    @ViewField(headerName = "作废人")
    private String cancelMan;

     @ViewField(headerName = "备注")
    private String remark;


}
