package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName(value ="pms_purch_detail_ref")
@Getter
@Setter
@ToString
@Data
public class PmsPruchDetailRefPO implements Serializable {
    /** 关联主键序号 */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long tenantId;

    /** 采购订单号 */
    private String billNo;

    /** 采购订单单内序号 */
    private Long insideId;

    /** 商品编码 */
    private String skuCode;

    /** 商品名称 */
    private String skuName;

    /** 关联单对应商品采购数量 */
    private BigDecimal purchQty;

    /** 关联来源：0-手工单，1-需求单，2-配转采 */
    private Integer billSource;

    /** 关联单号（采购计划单号/需求单号/配送单号） */
    private String srcBillNo;

    /** 关联单单内序号 */
    private Long srcInsideId;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 最后更新时间 */
    private LocalDateTime updateTime;

    public String getBillInsideIdKey() {
        return  billNo + "-" +insideId ;
    }

    public String getSrcBillInsideIdKey() {
        return  srcBillNo + "-" +srcInsideId ;
    }
}