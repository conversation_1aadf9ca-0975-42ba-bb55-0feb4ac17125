package com.meta.supplychain.entity.dto.pms.req.appointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商预约单主表DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约单主表DTO")
public class PmsAppointmentBillDTO extends BaseDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预约单据号")
    private String billNo;

    @NotBlank(message = "部门编码不能为空")
    @Schema(description = "部门编码")
    private String deptCode;

    @NotBlank(message = "部门名称不能为空")
    @Schema(description = "部门名称")
    private String deptName;

    @NotBlank(message = "供应商编码不能为空")
    @Schema(description = "供应商编码")
    private String supplierCode;

    @NotBlank(message = "供应商名称不能为空")
    @Schema(description = "供应商名称")
    private String supplierName;

    @EnumValue(type = PmsBookingDocumentSourceEnum.class)
    @Schema(description = "操作来源 枚举pmsBookingDocumentSourceEnum")
    private Integer opSource;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单截止日期开始,yyyy-MM-dd")
    private LocalDate startValidityTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单截止日期结束,yyyy-MM-dd")
    private LocalDate endValidityTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单送货日期开始,yyyy-MM-dd")
    private LocalDate startDeliverTime;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @Schema(description = "订单送货日期结束,yyyy-MM-dd")
    private LocalDate endDeliverTime;

    @NotNull(message = "预约类别不能为空")
    @EnumValue(type = PmsBookingCategoryEnum.class, required = true, message = "预约类别值无效")
    @Schema(description = "预约类别 枚举pmsBookingCategoryEnum")
    private Integer billDirection;

    @NotNull(message = "是否直流不能为空")
    @EnumValue(type = YesOrNoEnum.class, message = "是否直流值无效")
    @Schema(description = "是否直流 枚举YesOrNoEnum")
    private Integer directSign;

    @NotNull(message = "预约方式不能为空")
    @EnumValue(type = PmsBookingMethodEnum.class, required = true, message = "预约方式值无效")
    @Schema(description = "预约方式 枚举pmsBookingMethodEnum")
    private Integer appointmentMode;

    @NotNull(message = "默认预约数量不能为空")
    @EnumValue(type = PmsDefaultBookingQuantityEnum.class, required = true, message = "默认预约数量值无效")
    @Schema(description = "默认预约数量 枚举pmsDefaultBookingQuantityEnum")
    private Integer defaultQtySign;

    @NotBlank(message = "停靠点编码不能为空")
    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点时段行号")
    private Integer dockTimeInsideId;

    @Schema(description = "停靠点名称")
    private String dockName;

    @NotNull(message = "承运方式不能为空")
    @EnumValue(type = PmsCarrierMethodEnum.class, required = true, message = "承运方式值无效")
    @Schema(description = "承运方式 枚举pmsCarrierMethodEnum")
    private Integer transportMode;

    @Length(max = 200, message = "承运人名称最大长度为200个字符")
    @NotBlank(message = "承运人不能为空")
    @Schema(description = "承运人,默认供应商名称")
    private String transportMan;

    @Length(max = 200, message = "承运联系人名称最大长度为200个字符")
    @NotBlank(message = "承运联系人不能为空")
    @Schema(description = "承运联系人")
    private String transportContacts;

    @Length(max = 32, message = "承运联系手机号最大长度为32个字符")
    @NotBlank(message = "承运联系手机不能为空")
    @Schema(description = "承运联系手机")
    private String transportMobile;

    @Schema(description = "车辆类型")
    private String carType;

    @NotNull(message = "计划到达时间不可为空")
    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "计划到达时间,yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planArrivalTime;

    @Schema(description = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @Length(max = 200, message = "承运备注最大长度为200个字符")
    @Schema(description = "承运备注")
    private String transportRemark;

    @Length(max = 200, message = "预约备注最大长度为200个字符")
    @Schema(description = "预约备注")
    private String appointmentRemark;

    @EnumValue(type = PmsBookingStatusEnum.class, message = "状态值无效")
    @Schema(description = "状态 枚举pmsBookingStatusEnum")
    private Integer status;

    @Length(max = 2048, message = "附件内容最大长度为2048个字符")
    @Schema(description = "附件名称与地址,json格式")
    private String attachmentUrl;

    @Schema(description = "提交人code")
    private String submitManCode;

    @Schema(description = "提交人姓名")
    private String submitManName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "作废人code")
    private String cancelManCode;

    @Schema(description = "作废人名称")
    private String cancelManName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "作废时间")
    private LocalDateTime cancelTime;

    @Valid
    @Schema(description = "商品列表（按照商品维度维护）")
    private List<PmsAppointmentBillGoodsDTO> goodsList;

    @Valid
    @Schema(description = "采购订单列表（按照订单维度维护）")
    private List<PmsAppointmentBillPurchDTO> purchList;
} 