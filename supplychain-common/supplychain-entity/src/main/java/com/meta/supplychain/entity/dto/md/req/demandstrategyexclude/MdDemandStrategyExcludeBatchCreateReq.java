package com.meta.supplychain.entity.dto.md.req.demandstrategyexclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 需求策略自动需求单排除范围批量创建请求
 * <AUTHOR>
 */
@Data
public class MdDemandStrategyExcludeBatchCreateReq {

    @Schema(description = "需求策略排除范围列表", required = true)
    @Valid
    private List<MdDemandStrategyExcludeCreateReq> items;
}
