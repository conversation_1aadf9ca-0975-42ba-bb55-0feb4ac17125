package com.meta.supplychain.entity.dto.pms.resp.accept;


import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.Data;


/**
 * <AUTHOR>
 * @Classname AcceptBillResp
 * @Dscription TODO
 * @DATE 2025/5/15 16:49
 */
@Data
public class AcceptBillView {

    /**
     * 验收单号
     */
    @ViewField(headerName = "验收单号")
    private String billNo;

    /**
     * 单据状态 0-草稿 1-待审核 2-已审核 3-作废
     */
    @ViewField(headerName = "单据状态")
    private String statusDesc;
    /**
     * 供应商编码
     */
    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ViewField(headerName = "供应商名称")
    private String supplierName;

    /**
     * 部门编码
     */
    @ViewField(headerName = "验收部门编码")
    private String deptCode;

    /**
     * 部门名称
     */
    @ViewField(headerName = "验收部门名称")
    private String deptName;

    /**
     * 管理分类编码
     */
    @ViewField(headerName = "管理分类")
    private String manageCategory;

    /**
     * 验收数量
     */
    @ViewField(headerName = "验收数量")
    private String totalQty;

    /**
     * 金额（含税） 单位元
     */
    @ViewField(headerName = "验收金额")
    private String totalMoney;

    /**
     * 备注
     */
    @ViewField(headerName = "备注")
    private String remark;
    /**
     * 送货单号
     */
    @ViewField(headerName = "送货单号")
    private String sendBillNo;

    /**
     * 入账时间
     */
    @ViewField(headerName = "入账时间")
    private String accDate;

    /**
     * 制单时间
     */
    @ViewField(headerName = "制单时间")
    private String createTime;

    /**
     * 制单人  编码+名称
     */
    @ViewField(headerName = "制单人")
    private String creator;

    /**
     * 采购订单号
     */
    @ViewField(headerName="采购订单号")
    private String purchBillNo;
    /**
     * 审核时间
     */
    @ViewField(headerName = "审核时间")
    private String auditTime;
    /**
     * 审核人   编码+名称
     */
    @ViewField(headerName = "审核人")
    private String auditMan;
    /**
     * 修改时间
     */
    @ViewField(headerName="修改时间")
    private String updateTime;

    /**
     * 修改人 编码+名称
     */
    @ViewField(headerName="修改人")
    private String updater;

    /**
     * 提交时间
     */
    @ViewField(headerName="提交时间")
    private String submitTime;

    /**
     * 提交人 编码+名称
     */
    @ViewField(headerName="提交人")
    private String submitMan;

    /**
     * 冲红标志 0,否1是
     */
    @ViewField(headerName="是否冲红")
    private String reversalBillSignDesc;

    /**
     * 原单号
     */
    @ViewField(headerName="原单号")
    private String originBillNo;

}
