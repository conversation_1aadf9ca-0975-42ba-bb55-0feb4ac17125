package com.meta.supplychain.enums.md;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "追加追减部门类型", code = "mdContractGoodsDeptTypeEnum")
public enum MdAddReduceGoodsDeptTypeEnum implements VerifiableEnum<Integer> {

    DEPT(1, "部门"),
    DEPT_GROUP(2, "店组群"),

    ;

    @EnumValue
    private final Integer code;
    private final String desc;

    @JsonValue
    public Integer jsonValue() {
        return code;
    }

    public static String ofCodeToDesc(Integer code) {
        for (MdAddReduceGoodsDeptTypeEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }
}
