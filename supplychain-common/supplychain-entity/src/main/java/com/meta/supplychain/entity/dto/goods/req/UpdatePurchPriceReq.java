package com.meta.supplychain.entity.dto.goods.req;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/5/16 18:06
 */
@Data
@Builder
public class UpdatePurchPriceReq {

    private String source;

    private String storeCode;

    private String serialNo;

    private List<GoodsPriceInfo> goodsList;



    @Data
    @Builder
    public static class GoodsPriceInfo {
        private String spuCode;
        private String skuCode;
        /**
         * 参考进价（单位：毫）
         */
        private Long purchPrice;
    }

}
