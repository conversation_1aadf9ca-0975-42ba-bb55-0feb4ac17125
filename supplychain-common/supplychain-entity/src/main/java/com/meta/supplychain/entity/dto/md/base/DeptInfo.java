package com.meta.supplychain.entity.dto.md.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "部门信息")
public class DeptInfo implements Serializable {
    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String deptCode;

    /**
     * 部门类型 0-店组群, 1-具体部门
     */
    @Schema(description = "部门类型：0-店组群, 1-具体部门")
    private Integer deptType;
}