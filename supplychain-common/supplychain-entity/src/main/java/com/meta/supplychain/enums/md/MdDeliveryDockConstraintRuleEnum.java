package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "停靠点类型限量规则枚举", code = "mdDeliveryDockConstraintRuleEnum")
public enum MdDeliveryDockConstraintRuleEnum implements VerifiableEnum<Integer> {
    /** 不限 */
    UNLIMITED(1, "不限"),
    /** 按整件数 */
    BY_WHOLE_PIECE(2, "按整件数");

    private final Integer code;
    private final String desc;
}
