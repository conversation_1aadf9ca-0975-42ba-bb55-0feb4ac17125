package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentCloseDateDTO;
import com.meta.supplychain.entity.dto.md.req.delivery.MdDeliveryAppointmentCloseDateCreateReq;
import com.meta.supplychain.entity.po.md.MdDeliveryAppointmentCloseDatePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdDeliveryAppointmentCloseDateConvert {

    MdDeliveryAppointmentCloseDateConvert INSTANCE = Mappers.getMapper(MdDeliveryAppointmentCloseDateConvert.class);

    MdDeliveryAppointmentCloseDateDTO po2dto(MdDeliveryAppointmentCloseDatePO po);

    List<MdDeliveryAppointmentCloseDateDTO> po2dtoList(List<MdDeliveryAppointmentCloseDatePO> poList);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MdDeliveryAppointmentCloseDatePO dto2po(MdDeliveryAppointmentCloseDateDTO dto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    List<MdDeliveryAppointmentCloseDatePO> dto2poList(List<MdDeliveryAppointmentCloseDateDTO> dtoList);
    
    MdDeliveryAppointmentCloseDatePO req2po(MdDeliveryAppointmentCloseDateCreateReq req);
    
    List<MdDeliveryAppointmentCloseDatePO> req2poList(List<MdDeliveryAppointmentCloseDateCreateReq> reqList);
} 