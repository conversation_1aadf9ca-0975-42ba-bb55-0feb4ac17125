package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 退库收货确认收货参数
 */
@Schema(description = "退库收货确认收货参数")
@Getter
@Setter
@ToString
public class RefundAcceptReq {

    @Schema(description = "退库收货单据号")
    @NotBlank(message = "退库收货单据号不能为空")
    String billNo;


    @Schema(description = "退库收货明细")
    @NotEmpty(message = "退库收货明细不能为空")
    @Valid
    private List<RefundAcceptDetailReq> goodsList;

    @Getter
    @Setter
    public static class RefundAcceptDetailReq{
        @Schema(description = "行号")
        private Long insideId;

        @Schema(description = "商品编码")
        @NotBlank(message = "商品编码不能为空")
        private String skuCode;

        /** 收货数量 */
        @Schema(description = "收货数量")
        @NotNull(message = "收货数量不能为空")
        private BigDecimal acceptQty;

        /** 商品批号 */
        @Schema(description = "商品批号")
        private String periodBatchNo;

        /** 生产日期 */
        @Schema(description = "生产日期")
        private LocalDate productDate;

        /** 过期日期 */
        @Schema(description = "过期日期")
        private LocalDate expireDate;

        /** 效期条码 */
        @Schema(description = "效期条码")
        private String periodBarcode;
    }
}
