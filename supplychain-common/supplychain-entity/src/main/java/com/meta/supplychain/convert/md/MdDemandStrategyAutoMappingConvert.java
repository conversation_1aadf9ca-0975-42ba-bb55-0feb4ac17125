package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingGroupDTO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyAutoMappingPO;
import com.meta.supplychain.enums.md.MdDemandConditionValueExtendEnum;
import com.meta.supplychain.enums.md.MdDemandStrategyAutoMappingConditionEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 需求策略自动需求单规则转换器
 */
@Mapper
public interface MdDemandStrategyAutoMappingConvert {
    MdDemandStrategyAutoMappingConvert INSTANCE = Mappers.getMapper(MdDemandStrategyAutoMappingConvert.class);
    
    /**
     * PO 转 DTO
     */
    MdDemandStrategyAutoMappingDTO po2dto(MdDemandStrategyAutoMappingPO po);
    
    /**
     * DTO 转 PO
     */
    MdDemandStrategyAutoMappingPO dto2po(MdDemandStrategyAutoMappingDTO dto);
    
    /**
     * 条件组转条件
     * @param groupDTO 条件组DTO
     * @return 条件列表
     */
    default List<MdDemandStrategyAutoMappingDTO> convertGroupDto2ConditionsDto(MdDemandStrategyAutoMappingGroupDTO groupDTO) {
        ArrayList<MdDemandStrategyAutoMappingDTO> strategyList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(groupDTO.getOperationStatusList())) {
            groupDTO.getOperationStatusList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.OPERATION_STATUS.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getCirculationChannelList())) {
            groupDTO.getCirculationChannelList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.CIRCULATION_CHANNEL.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getOrderReturnAttributeList())) {
            groupDTO.getOrderReturnAttributeList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.ORDER_RETURN_ATTRIBUTE.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getBillDirectionList())) {
            groupDTO.getBillDirectionList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.BILL_DIRECTION.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getShippingChannelList())) {
            groupDTO.getShippingChannelList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.SHIPPING_CHANNEL.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getIsDirectFlowList())) {
            groupDTO.getIsDirectFlowList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.IS_DIRECT_FLOW.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getDeptCodeList())) {
            groupDTO.getDeptCodeList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.TARGET_DEPT.getCode())
                        .demandConditionValueExtend(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_CODE.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getDeptGroupCodeList())) {
            groupDTO.getDeptGroupCodeList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.TARGET_DEPT.getCode())
                        .demandConditionValueExtend(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_GROUP_CODE.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        if (!CollectionUtils.isEmpty(groupDTO.getWhCodeList())) {
            groupDTO.getWhCodeList().forEach(conditionValue -> {
                MdDemandStrategyAutoMappingDTO strategy = MdDemandStrategyAutoMappingDTO.builder()
                        .demandCondition(MdDemandStrategyAutoMappingConditionEnum.TARGET_WAREHOUSE.getCode())
                        .demandConditionValueExtend(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_CODE.getCode())
                        .demandConditionValue(conditionValue)
                        .build();
                strategyList.add(strategy);
            });
        }

        return strategyList;
    }

    /**
     * 条件转条件组
     * @param conditions 条件列表
     * @return 条件组DTO
     */
    default MdDemandStrategyAutoMappingGroupDTO convertConditionsDto2GroupDto(List<MdDemandStrategyAutoMappingDTO> conditions) {
        MdDemandStrategyAutoMappingGroupDTO groupDTO = new MdDemandStrategyAutoMappingGroupDTO();
        if (CollectionUtils.isEmpty(conditions)) {
            return groupDTO;
        }

        // 默认conditions为同一个策略中的条件
        conditions.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDemandGroupCode()))
                .findFirst()
                .ifPresent(dto -> groupDTO.setStrategyGroupCode(dto.getDemandGroupCode()));

        // 按条件类型分组处理
        conditions.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDemandCondition()) && StringUtils.isNotBlank(dto.getDemandConditionValue()))
                .collect(Collectors.groupingBy(MdDemandStrategyAutoMappingDTO::getDemandCondition))
                .forEach((condition, values) -> {
                    Set<String> conditionValues = values.stream()
                            .map(MdDemandStrategyAutoMappingDTO::getDemandConditionValue)
                            .collect(Collectors.toSet());

                    if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.OPERATION_STATUS.getCode())) {
                        groupDTO.setOperationStatusList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.CIRCULATION_CHANNEL.getCode())) {
                        groupDTO.setCirculationChannelList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.ORDER_RETURN_ATTRIBUTE.getCode())) {
                        groupDTO.setOrderReturnAttributeList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.BILL_DIRECTION.getCode())) {
                        groupDTO.setBillDirectionList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.SHIPPING_CHANNEL.getCode())) {
                        groupDTO.setShippingChannelList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.IS_DIRECT_FLOW.getCode())) {
                        groupDTO.setIsDirectFlowList(conditionValues);
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.TARGET_DEPT.getCode())) {
                        Map<String, Set<String>> deptTypeGroups = values.stream()
                                .filter(dto -> StringUtils.isNotBlank(dto.getDemandConditionValueExtend()))
                                .collect(Collectors.groupingBy(
                                        MdDemandStrategyAutoMappingDTO::getDemandConditionValueExtend,
                                        Collectors.mapping(MdDemandStrategyAutoMappingDTO::getDemandConditionValue, Collectors.toSet())
                                ));
                        if (!CollectionUtils.isEmpty(deptTypeGroups.get(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_CODE.getCode()))) {
                            groupDTO.setDeptCodeList(deptTypeGroups.get(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_CODE.getCode()));
                        }
                        if (!CollectionUtils.isEmpty(deptTypeGroups.get(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_GROUP_CODE.getCode()))) {
                            groupDTO.setDeptGroupCodeList(deptTypeGroups.get(MdDemandConditionValueExtendEnum.VALUE_IS_DEPT_GROUP_CODE.getCode()));
                        }
                    } else if (Objects.equals(condition, MdDemandStrategyAutoMappingConditionEnum.TARGET_WAREHOUSE.getCode())) {
                        groupDTO.setWhCodeList(conditionValues);
                    }
                });
        return groupDTO;
    }
} 