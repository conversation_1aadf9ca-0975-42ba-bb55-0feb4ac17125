package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 移位单主表
 * @TableName wd_move_bill
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="wd_move_bill")
@Data
public class MoveBillPO extends BaseEntity implements java.io.Serializable{

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * 仓库名称
     */
    private String whName;

    /**
     * 单据号
     */
    private String billNo;

    /**
     * 单据备注
     */
    private String remark;

    /**
     * 移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移
     */
    private Integer moveType;

    /**
     * 来源单号[收货退货]
     */
    private String srcBillNo;

    /**
     * 合计转移数量
     */
    private BigDecimal totalMoveQty;

    /**
     * 单据状态 0未确认 1审批中 2已确认 3作废
     */
    private Integer status;

    /**
     * 移出储位编码
     */
    private String outLocationCode;

    /**
     * 移出储位名称
     */
    private String outLocationName;

    /**
     * 移入储位编码
     */
    private String inLocationCode;

    /**
     * 移入储位名称
     */
    private String inLocationName;

    /**
     * 作废时间
     */
    private LocalDateTime cancelTime;

    /**
     * 作废人编码
     */
    private String cancelManCode;

    /**
     * 作废人名称
     */
    private String cancelManName;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 确认人编码
     */
    private String confirmManCode;

    /**
     * 确认人名称
     */
    private String confirmManName;


}