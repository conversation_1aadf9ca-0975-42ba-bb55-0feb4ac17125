package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:04
 */
@Builder
@Setter
@Getter
@Data
public class QueryUpDeptListResp {
    /**
     * 商品信息
     */
    private Integer total;

    /**
     * 子品（结构同商品信息）
     */
    private List<UpDeptInfo> rows;


    @Getter
    @Setter
    public static class UpDeptInfo{

        /**
         * 自增主键
         */
        private Long id;
        /**
         * 店组群编码
         */
        private String code;
        /**
         * 店组群名称
         */
        private String name;
        /**
         * 上级店组群Id
         */
        private Long parentId;
        /**
         * 上级店组群
         */
        private String parentCode;
        /**
         * 上级店组群名称
         */
        private String parentName;
        /**
         * 分类编码
         */
        private String classCode;
        /**
         * 分类名称
         */
        private String className;
        /**
         * 启用状态 1启用 0停用
         */
        private Integer status;
        /**
         * 级别
         */
        private Integer level;
    }


}
