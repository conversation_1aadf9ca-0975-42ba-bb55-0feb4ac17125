package com.meta.supplychain.convert.pms;

import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDetailDTO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderApplyBillDetailConvert {
    OrderApplyBillDetailConvert MAPPER = Mappers.getMapper(OrderApplyBillDetailConvert.class);



    PmsApplyBillDetailPO convertDetail2PO(ApplyBillDetailDTO applyBillDetailDTO);


    List<PmsApplyBillDetailPO> convertDetailList2PO(List<ApplyBillDetailDTO> applyBillDetailDTO);

    ApplyBillDetailDTO convertDetailPO2DTO(PmsApplyBillDetailPO pmsApplyBillDetailPO);

    List<ApplyBillDetailDTO> convertDetailListPO2DTO(List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS);



}
