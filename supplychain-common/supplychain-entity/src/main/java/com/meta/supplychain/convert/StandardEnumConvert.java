package com.meta.supplychain.convert;

import com.meta.supplychain.enums.EnumContainer;
import com.meta.supplychain.enums.StandardEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;

public interface StandardEnumConvert {
    default <T extends StandardEnum<C>, C> String convertToDesc(String enumCode, C code) {
        if (code == null) {
            return null;
        }
        Class<?> enumClass = EnumContainer.getEnumClassByCode(enumCode);
        if (enumClass == null || !StandardEnum.class.isAssignableFrom(enumClass)) {
            return null;
        }

        @SuppressWarnings("all")
        Class<T> typedEnumClass = (Class<T>) enumClass;

        T enumValue = StandardEnum.codeOf(typedEnumClass, code);
        return enumValue != null ? enumValue.getDesc() : null;
    }

    default String convertManCodeName(String manCode, String manName) {
        return (StringUtils.isBlank(manCode) ? "" : manCode) + (StringUtils.isBlank(manName) ? "" : "_" + manName);
    }

    default String formatMoney(BigDecimal money) {
        if (money == null) return "";
        return money.setScale(2, RoundingMode.HALF_UP)
                .stripTrailingZeros()
                .toPlainString();
    }
}
