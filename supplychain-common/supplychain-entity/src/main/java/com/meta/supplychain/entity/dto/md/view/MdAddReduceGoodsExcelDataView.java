package com.meta.supplychain.entity.dto.md.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdAddReduceGoodsExcelDataView {

    @ViewField(headerName = "商品编码")
    private String skuCode;

    @ViewField(headerName = "商品条码")
    private String barCode;

    @ViewField(headerName = "设置类型")
    private Integer type;

    @ViewField(headerName = "部门类型")
    private Integer deptType;

    @ViewField(headerName = "部门编码")
    private String deptCodes;

    @ViewField(headerName = "失败原因", isErrorHeader = true)
    private String errorMsg;

    private String skuName;

    private String deptName;

    public void addErrorMsg(String errorMsg) {
        this.errorMsg = StringUtils.hasText(this.errorMsg) ? this.errorMsg + "; " + errorMsg : errorMsg;
    }

}
