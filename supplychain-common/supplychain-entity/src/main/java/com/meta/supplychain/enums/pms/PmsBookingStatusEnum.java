package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约单状态枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "预约单状态", code = "pmsBookingStatusEnum")
public enum PmsBookingStatusEnum implements VerifiableEnum<Integer> {

    DRAFT(1, "草稿"),
    SUBMITTED(2, "已提交"),
    CANCELLED(3, "已作废");

    private final Integer code;
    private final String desc;
} 