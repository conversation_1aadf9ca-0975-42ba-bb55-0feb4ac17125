package com.meta.supplychain.entity.dto.pms.resp.appointment;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 停靠点时间统计结果对象
 * 
 * <AUTHOR>
 */
@Data
public class DockTimeStatsDTO {

    /**
     * 停靠点编码
     */
    private String dockCode;

    /**
     * 计划到货日期（格式：yyyy-MM-dd）
     */
    private String planArrivalDate;

    /**
     * 停靠点时间内部ID
     */
    private Integer dockTimeInsideId;

    /**
     * 已使用的数量
     */
    private BigDecimal usedCount = BigDecimal.ZERO;

    /**
     * 本次预约整件数
     */
    private BigDecimal totalAppointmentWholeQty = BigDecimal.ZERO;
} 