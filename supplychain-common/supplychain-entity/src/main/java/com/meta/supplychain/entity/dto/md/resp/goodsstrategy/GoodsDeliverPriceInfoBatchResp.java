package com.meta.supplychain.entity.dto.md.resp.goodsstrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品价格信息
 *  配送订单 采购订单使用
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsDeliverPriceInfoBatchResp {

    @Schema(description = "仓库编码")
    private String whCode;

    @Schema(description = "入货部门编码")
    private String inDeptCode;

    @Schema(description = "商品价格信息列表")
    List<GoodsDeliverPriceInfoResp> deliverPriceInfoRespList;
}
