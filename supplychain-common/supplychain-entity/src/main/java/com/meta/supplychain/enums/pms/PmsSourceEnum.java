package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * ios android
 *
 * <AUTHOR> cat
 * @date 2024/4/24 19:33
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PmsSourceEnum {
    IOS("ios", "苹果"),
    ANDROID("android", "安卓"),

    ;
    private String code;

    private String desc;

    public static boolean isApp(String source) {
        if (StringUtils.isNotBlank(source)) {
            if (IOS.getCode().equals(source) || ANDROID.getCode().equals(source)) {
                return true;
            }
        }
        return false;
    }
}
