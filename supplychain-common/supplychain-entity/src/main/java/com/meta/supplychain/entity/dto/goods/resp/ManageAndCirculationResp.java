package com.meta.supplychain.entity.dto.goods.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 经营状态、流转途径
 *
 * <AUTHOR> cat
 * @date 2024/3/25 21:03
 */
@Getter
@Setter
public class ManageAndCirculationResp {

    @Schema(description = "商品经营状态")
    List<WorkState> manageStateList;

    @Schema(description = "商品流转途径")
    List<CirculationMode> circulationList;


    @Builder
    @Data
    @Getter
    @Setter
    public static class WorkState {

        /**
         * 编码
         */
        @Schema(description = "编码")
        private String managementStateNo;
        /**
         * 名称
         */
        @Schema(description = "名称")
        private String managementStateName;

        /**
         * 是否允许进货;1是 | 0否
         */
        @Schema(description = "是否允许进货;1是 | 0否")
        private Integer allowPurchaseFlag;
        /**
         * 是否允许进货退货;1是 | 0否
         */
        @Schema(description = "是否允许进货退货;1是 | 0否")
        private Integer allowPurchaseBackFlag;
        /**
         * 是否允许出货;1是 | 0否
         */
        @Schema(description = "是否允许出货;1是 | 0否")
        private Integer allowShipmentFlag;
        /**
         * 是否允许出货退货;1是 | 0否
         */
        @Schema(description = "是否允许出货退货;1是 | 0否")
        private Integer allowShipmentBackFlag;
        @Schema(description = "备注")
        private String remark;
    }

    @Builder
    @Data
    @Getter
    @Setter
    public static class CirculationMode{

        /**
         * 编码
         */
        @Schema(description = "编码")
        private String circulationWayNo;
        /**
         * 名称
         */
        @Schema(description = "名称")
        private String circulationWayName;

        /**
         * 是否允许拨入拨出;1是 | 0否
         */
        @Schema(description = "是否允许拨入拨出;1是 | 0否")
        private Integer allowPullFlag;

        /**
         * 是是否允许销售;1是 | 0否
         */
        @Schema(description = "是否允许销售;1是 | 0否")
        private Integer allowSaleFlag;
        /**
         * 是否允许从配送送货;1是 | 0否
         */
        @Schema(description = "是否允许从配送送货;1是 | 0否")
        private Integer allowFromDistributionFlag;
        /**
         * 是否允许从供应商配送;1是 | 0否
         */
        @Schema(description = "是否允许从供应商配送;1是 | 0否")
        private Integer allowFromSupplierFlag;
        /**
         * 是否允许退配送;1是 | 0否
         */
        @Schema(description = "是否允许退配送;1是 | 0否")
        private Integer allowBackDistributionFlag;
        /**
         * 是否允许退供应商;1是 | 0否
         */
        @Schema(description = "是否允许退供应商;1是 | 0否")
        private Integer allowBackSupplierFlag;
        @Schema(description = "备注")
        private String remark;
    }
}
