package com.meta.supplychain.enums.goods;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品类型（1：单规格 2：多规格 3：组合商品）
 *
 * <AUTHOR> cat
 * @date 2024/5/16 15:23
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "订货采购管理错误状态码枚举", code = "PmsErrorCodeEnum")
public enum GoodsSaleModeEnum implements VerifiableEnum<Integer> {

    SELF_OWNED(1, "自营"),
    JOINT_OPERATION(2, "联营"),
    JOINT_OPERATION_WITH_STOCK(7, "联营管库存"),
    RENTAL(8, "租赁"),
    FRESH_A_TO_A(9, "生鲜A进A出"),
    FRESH_A_TO_B(10, "生鲜A进B出"),
    FRESH_AGGREGATION(12, "生鲜归集码"),
    ;
    @EnumValue
    private final Integer code;
    private final String desc;

    @JsonValue
    public Integer jsonValue() {
        return code;
    }

    public static String ofCodeToDesc(Integer code) {
        for (GoodsSaleModeEnum em : values()) {
            if (em.code.equals(code)) {
                return em.getDesc();
            }
        }
        return "";
    }

}
