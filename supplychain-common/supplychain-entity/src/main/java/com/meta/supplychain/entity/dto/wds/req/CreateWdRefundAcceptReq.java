package com.meta.supplychain.entity.dto.wds.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "新增退货收货")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class CreateWdRefundAcceptReq {

    /** 单据号 */
    @Schema(description = "退货收货单号")
    @NotBlank(message = "单据号不能为空")
    private String billNo;

    /** 收货类型 0配送 */
    @Schema(description = "单据类型")
    @NotNull(message = "单据类型不能为空")
    private Integer billType;

    /** 仓库编码 */
    @Schema(description = "仓库编码")
    @NotBlank(message = "仓库编码不能为空")
    private String whCode;

    /** 仓库名称 */
    @Schema(description = "仓库名称")
    @NotBlank(message = "仓库名称不能为空")
    private String whName;

    /** 退货方部门编码 */
    @Schema(description = "退货方部门编码")
    @NotBlank(message = "退货方部门编码不能为空")
    private String inDeptCode;

    /** 退货方部门名称 */
    @Schema(description = "退货方部门名称")
    @NotBlank(message = "退货方部门名称不能为空")
    private String inDeptName;

    /** 发货单号 */
    @Schema(description = "配送单号")
    private String shipBillNo;

    /** 关联的配送订单号 */
    @Schema(description = "关联的配送订单号")
    private String deliveryBillNo;

    @Schema(description = "单据备注")
    private String remark;

    @Schema(description = "退货收货明细")
    @NotEmpty(message = "退货收货明细不能为空")
    @Valid
    private List<CreateWdRefundAcceptDetailReq> batchDetailList;

}
