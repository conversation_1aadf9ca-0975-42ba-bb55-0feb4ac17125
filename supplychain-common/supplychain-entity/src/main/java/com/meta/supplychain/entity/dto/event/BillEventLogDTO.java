package com.meta.supplychain.entity.dto.event;

import cn.linkkids.framework.croods.common.PageParams;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillEventLogDTO extends PageParams implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "单据号")
    private String billNo;

    /**
     * 业务单据类型
     */
    @Schema(description = "业务单据类型")
    private String billType;

    /**
     * 业务单据类型描述
     */
    @Schema(description = "业务单据类型描述")
    private String billTypeDesc;

    /**
     * 动作类型,审核,发货
     */
    @Schema(description = "动作类型")
    private String actionType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private String eventType;

    /**
     * 事件类型描述
     */
    @Schema(description = "事件类型描述")
    private String eventDesc;

    /**
     * 事件接收报文
     */
    @Schema(description = "事件接收报文")
    private String billContent;

    /**
     * 处理渠道
     */
    @Schema(description = "处理渠道")
    private String channel;

    /**
     * 系统实现类标记
     */
    private String supportType;

    /**
     * 目标系统编码,如WMS,STOCK,CHAOPI,PMS,WDS
     */
    @Schema(description = "目标系统编码")
    private String targetSystem;

    /**
     * 请求报文
     */
    private String reqContent;

    /**
     * 请求URL
     */
    private String reqUrl;

    /**
     * 返回报文
     */
    private String respData;

    /**
     * 当前事件执行是否成功,0:失败，1:成功，2:补偿成功
     */
    @Schema(description = "当前事件执行是否成功,0:失败，1:成功，2:补偿成功")
    private Integer isSuccess;

    /**
     * 异常原因
     */
    private String errorReason;

    /**
     * 最近一次补偿时间
     */
    private LocalDateTime retryTime;

    /**
     * 补偿次数
     */
    private Integer retryCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ssoId
     */
    private Long createUid;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ssoId
     */
    private Long updateUid;

    /**
     * 更新人编码
     */
    private String updateCode;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 平台租户号
     */
    private Long tenantId;

    /**
     * 删除标志 0正常 1删除
     */
    private Integer delFlag;
}