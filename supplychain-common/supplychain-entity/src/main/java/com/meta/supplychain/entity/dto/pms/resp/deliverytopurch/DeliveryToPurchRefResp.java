package com.meta.supplychain.entity.dto.pms.resp.deliverytopurch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Getter
@Setter
@ToString
public class DeliveryToPurchRefResp {

    @Schema(description = "配转采商品单内序号")
    private Long deliveryToPurchInsideId;

    @Schema(description = "商品部门单内序号")
    private Long deptGoodsInsideId;

    @Schema(description = "配送出货方单内序号")
    private Long deliveryShipperInsideId;

    @Schema(description = "转采类型,0不可转采, 1可转采")
    private Integer type;

    @Schema(description = "当配送订单时，配送订单号")
    private String billNo;
}