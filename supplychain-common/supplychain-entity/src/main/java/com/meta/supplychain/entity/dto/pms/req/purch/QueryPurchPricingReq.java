package com.meta.supplychain.entity.dto.pms.req.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划单查询请求
 */
@Data
@Schema(description = "采购计划单查询请求")
public class QueryPurchPricingReq {

    @Schema(description = "供应商编码")
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    @Schema(description = "采购订单号")
    private String billNo;

    @Schema(description = "单据类别（-1:采退，1:采购）")
    @NotNull(message = "单据类别不能为空")
    private Integer billDirection;

    @Schema(description = "是否直流订单 0-非直流 1-直流")
    private Integer directSign;

    @Schema(description = "部门编码列表")
    @NotEmpty(message = "部门编码列表不能为空")
    private List<String> deptCodeList;

    @Schema(description = "商品编码列表")
    @NotEmpty(message = "商品编码列表不能为空")
    private List<String> skuCodeList;

    @Schema(description = "订单开始有效日期 格式:yyyy-MM-dd")
    private LocalDate validityDateStart;

    @Schema(description = "订单结束有效日期 格式:yyyy-MM-dd")
    private LocalDate validityDateEnd;

    @Schema(description = "订单开始送货日期 格式:yyyy-MM-dd")
    private LocalDate deliverDateStart;

    @Schema(description = "订单结束送货日期 格式:yyyy-MM-dd")
    private LocalDate deliverDateEnd;

}
