package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家经营品牌关联DTO
 */
@Data
@Schema(description = "商家经营品牌关联信息")
public class MstSupplierBusinessBrandDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "商家编码不可为空")
    @Schema(description = "商家编码")
    private Long supplierCode;

    @NotBlank(message = "品牌编码不可为空")
    @Length(max = 128, message = "品牌编码长度不能超过128")
    @Schema(description = "品牌编码")
    private String brandCode;

    @Length(max = 255, message = "品牌名称长度不能超过255")
    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "是否自有品牌：0-否，1-是")
    private Integer selfOwned;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Integer delFlag;
}
