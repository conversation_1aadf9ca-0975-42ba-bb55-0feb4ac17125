package com.meta.supplychain.entity.dto.md.resp.deliveryappointment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 停靠点限制规则查询响应VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "停靠点限制规则查询响应对象")
public class MdDeliveryDockLimitVO {

    @Schema(description = "单据编码")
    private String billNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "停靠点类型")
    private Integer dockType;

    @Schema(description = "约束规则")
    private Integer constraintRule;

    @Schema(description = "规则内部ID")
    private Integer insideId;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    @Schema(description = "周一")
    private Integer monday;

    @Schema(description = "周二")
    private Integer tuesday;

    @Schema(description = "周三")
    private Integer wednesday;

    @Schema(description = "周四")
    private Integer thursday;

    @Schema(description = "周五")
    private Integer friday;

    @Schema(description = "周六")
    private Integer saturday;

    @Schema(description = "周日")
    private Integer sunday;

    /**
     * 根据日期获取可预约数量
     */
    public Integer getAvailableCount(LocalDate date) {
        if (date == null) {
            return 0;
        }
        switch (date.getDayOfWeek()) {
            case MONDAY: return monday;
            case TUESDAY: return tuesday;
            case WEDNESDAY: return wednesday;
            case THURSDAY: return thursday;
            case FRIDAY: return friday;
            case SATURDAY: return saturday;
            case SUNDAY: return sunday;
        }
        return 0;
    }
} 