package com.meta.supplychain.convert.wds;


import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.WaveOrderBillDTO;
import com.meta.supplychain.entity.dto.wds.WaveOrderReferDTO;
import com.meta.supplychain.entity.dto.wds.req.WaveBillReferReq;
import com.meta.supplychain.entity.dto.wds.req.WaveOrderCreateReq;
import com.meta.supplychain.entity.dto.wds.req.WaveOrderReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryWaveReferResult;
import com.meta.supplychain.entity.dto.wds.resp.QueryWaveUnDistSkuResult;
import com.meta.supplychain.entity.dto.wds.resp.WaveBillExcelView;
import com.meta.supplychain.entity.po.wds.WaveOrderBillPO;
import com.meta.supplychain.entity.po.wds.WaveOrderReferPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.enums.wds.WDWaveTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 实体转换
 */
@Mapper(componentModel = "spring")
public interface WaveOrderConvert extends StandardEnumConvert {

    WaveOrderConvert INSTANCE = Mappers.getMapper(WaveOrderConvert.class);

    WaveOrderBillPO waveOrderDTOToPO(WaveOrderBillDTO bill);

    WaveOrderBillDTO waveOrderParamsToDTO(WaveOrderCreateReq params);

    WaveOrderBillDTO waveOrderVOToDTO(WaveOrderReq request);

    WaveOrderBillDTO waveOrderPOToDTO(WaveOrderBillPO waveOrderPO);

    List<QueryWaveUnDistSkuResult> waveOrderUnDistDTOToPOList(List<WdDeliveryBillDetailPO> deliveryBillDetailPOList);

    List<QueryWaveReferResult> waveOrderReferPOToVOList(List<WdDeliveryBillPO> deliveryBillPOList);

    QueryWaveReferResult waveOrderReferPOToVO(WdDeliveryBillPO deliveryBillPO);

    List<WaveOrderReferPO> waveOrderReferDTOToPOList(List<WaveOrderReferDTO> waveOrderReferDTOList);

    WaveOrderBillDTO waveOrderReferParamsToDTO(WaveBillReferReq params);

    /**
     * 波次单导出视图转换
     * @param result
     * @return
     */
    @Mapping(target = "waveType", expression = "java(convertToDesc(\"WDWaveTypeEnum\", result.getWaveType()))")
    @Mapping(source = "status", target = "status", qualifiedByName = "statusDesc")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "distTime", target = "distTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "cancelTime", target = "cancelTime")
    WaveBillExcelView waveOrderDTOToView(WaveOrderBillDTO result);

    /**
     * 将状态枚举码转换为名称
     * @param status 状态枚举码
     * @return 状态名称
     */
    @Named("statusDesc")
    default String statusDesc(Integer status) {
        if (status == null) {
            return "";
        }
        WDBillStatusEnum enumValue = WDBillStatusEnum.getInstance(status,WDBillStatusEnum.WAVE_STATUS_0.getTableType());
        return enumValue != null ? enumValue.getDesc() : status.toString();
    }

    /**
     * 波次类型枚举码转换为名称
     * @param code  类型枚举码
     * @return 类型名称
     */
    @Named("waveTypeDesc")
    default String waveTypeDesc(Integer code) {
        if (code == null) {
            return "";
        }
        WDWaveTypeEnum enumValue = StandardEnum.codeOf(WDWaveTypeEnum.class, code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }
}
