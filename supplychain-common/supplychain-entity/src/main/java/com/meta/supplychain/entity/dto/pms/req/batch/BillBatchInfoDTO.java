package com.meta.supplychain.entity.dto.pms.req.batch;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Classname PmsBillBatchInfoDTO
 * @Dscription TODO
 * @DATE 2025/6/9 17:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillBatchInfoDTO {


    /**
     * 商品批次号
     */
    @Schema(description = "商品批次号")
    private String batchNo;

    /**
     * 关联合单据类型（0-加工单 1-拆分单 2-采购验收 3-门店调拨 4-配送验收 5-转码单 6-配送申请订单）
     */
    @Schema(description = "关联合单据类型（0-加工单 1-拆分单 2-采购验收 3-门店调拨 4-配送验收 5-转码单 6-配送申请订单）")
    private Integer billType;

    /**
     * 效期行号
     */
    @Schema(description = "效期行号")
    private Long insideId;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String skuCode;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNo;

    /**
     * 进项税率
     */
    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    /**
     * 商品批号
     */
    @Schema(description = "商品批号")
    private String skuBatchNo;

    /**
     * 数量/批次数量
     */
    @Schema(description = "数量/批次数量")
    private BigDecimal batchQty;

    /**
     * 批次成本单价
     */
    @Schema(description = "批次成本单价")
    private BigDecimal batchCostPrice;

    /**
     * 库存数量
     */
    @Schema(description = "库存数量")
    private BigDecimal stockQty;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 效期批号
     */
    @Schema(description = "效期批号")
    private String periodBatchNo;

    /**
     * 效期条码
     */
    @Schema(description = "效期条码")
    private String periodBarcode;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    private LocalDateTime productDate;

    /**
     * 过期日期
     */
    @Schema(description = "过期日期")
    private LocalDateTime expireDate;

    /**
     * 批次税金
     */
    @Schema(description = "批次税金")
    private BigDecimal batchTax;

    /**
     * 成本单价
     */
    @Schema(description = "成本单价")
    private BigDecimal costPrice;

}
