package com.meta.supplychain.entity.dto.md.supplier;

import com.meta.supplychain.enums.md.MstSupplierCategoryTypeEnum;
import com.meta.supplychain.enums.md.MstSupplierFinalFlagEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商分类DTO
 */
@Data
@Schema(description = "供应商分类信息")
public class MstSupplierCategoryDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "分类编码不可为空")
    @Length(max = 50, message = "分类编码长度不能超过50")
    @Schema(description = "分类编码")
    private String code;

    @NotBlank(message = "分类名称不可为空")
    @Length(max = 50, message = "分类名称长度不能超过50")
    @Schema(description = "分类名称")
    private String name;

    @Length(max = 50, message = "父级编码长度不能超过50")
    @Schema(description = "父级编码")
    private String parentCode;

    @NotNull(message = "分类类型不可为空")
    @EnumValue(type = MstSupplierCategoryTypeEnum.class, required = true)
    @Schema(description = "分类类型：系统1 自定义2")
    private Integer type;

    @NotNull(message = "是否末级节点不可为空")
    @EnumValue(type = MstSupplierFinalFlagEnum.class, required = true)
    @Schema(description = "是否末级节点：0否 1是")
    private Integer finalFlag;

    @Schema(description = "排序")
    private Integer sort;

    @Length(max = 255, message = "备注长度不能超过255")
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Boolean delFlag;
}
