package com.meta.supplychain.entity.dto.pms.demand;

import com.meta.supplychain.entity.po.pms.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 需求单处理组装后PO数据
 *
 * <AUTHOR>
 * @date 2025/05/19 15:01
 **/
@Data
public class DemandBillHandleDataDTO {
    private PmsDemandBillPO pmsDemandBillPO;
    private List<PmsDemandGoodsDetailPO> demandGoodsDetailPOList = new ArrayList<>();
    private List<PmsDemandDeptGoodsDetailPO> demandDeptGoodsDetailPOList = new ArrayList<>();
    private List<PmsDemandDetailSourceRefPO> demandDetailSourceRefPOList = new ArrayList<>();
    private List<PmsDemandDeliveryShipperPO> demandDeliveryShipperPOList = new ArrayList<>();
    private List<PmsDemandPurchShipperPO> demandPurchShipperPOList = new ArrayList<>();
    private List<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefPOList = new ArrayList<>();
    private List<PmsDemandDeliveryToPurchPO> deliveryToPurchPOList = new ArrayList<>();

    private Map<Long,List<PmsDemandDetailSourceRefPO>> detailSourceRefPOMap = new HashMap<>();
}
