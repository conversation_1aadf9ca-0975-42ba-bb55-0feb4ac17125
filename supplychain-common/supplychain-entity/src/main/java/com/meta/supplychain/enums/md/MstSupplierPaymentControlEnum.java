package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商付款控制枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商付款控制", code = "mstSupplierPaymentControlEnum")
public enum MstSupplierPaymentControlEnum implements VerifiableEnum<Integer> {
    ALLOW_PAYMENT(1, "允许付款"),
    FORBID_PAYMENT(2, "不允许付款"),
    FORBID_SETTLEMENT(3, "不允许结算");

    private final Integer code;
    private final String desc;
}
