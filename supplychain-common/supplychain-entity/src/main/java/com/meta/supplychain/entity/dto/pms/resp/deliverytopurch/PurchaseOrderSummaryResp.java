package com.meta.supplychain.entity.dto.pms.resp.deliverytopurch;


import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillChangeReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname PurchaseOrderSummaryResp
 * @Dscription TODO
 * @DATE 2025/6/4 15:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderSummaryResp {

    @Schema(description = "商品总数")
    private int totalItems;
    @Schema(description = "可转采商品数")
    private int convertibleItems;
    @Schema(description = "不转采商品数")
    private int notConvertedItems;
    @Schema(description = "不可转采商品数")
    private int nonConvertibleItems;
    @Schema(description = "总采购订单数")
    private int totalPurchaseOrders;
    @Schema(description = "总采购数量")
    private BigDecimal totalPurchaseQuantity;
    @Schema(description = "总采购金额")
    private BigDecimal totalPurchaseAmount;
    @Schema(description = "预览订单列表")
    private List<PurchaseBillChangeReq> purchaseOrders;
}
