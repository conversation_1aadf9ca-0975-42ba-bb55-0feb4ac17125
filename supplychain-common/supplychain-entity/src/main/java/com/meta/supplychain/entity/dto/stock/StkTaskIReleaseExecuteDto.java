package com.meta.supplychain.entity.dto.stock;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StkTaskIReleaseExecuteDto {

    /**
     * 释放单据的入货方部门编码
     */
    @Schema(title = "仓库编码",description = "仓库编码")
    private String whCode;
    /**
     * 操作类型
     */
    @Schema(title = "操作类型",description = "操作类型")
    private String operateCode;
    /**
     * 业务出入库类型 I 入库 O 出库 R 预留 T 在途
     */
    @Schema(title = "出入库类型",description = "业务出入库类型 I 入库 O 出库 R 预留 T 在途")
    private String ioType;

    /**
     * 商品编码
     */
    @Schema(title = "商品编码",description = "商品编码")
    private String skuCode;
    /**
     * 商品类型 0普通商品 1赠品
     */
    @Schema(title = "商品类型",description = "商品类型 0普通商品 1赠品")
    private Integer skuType;

    /**
     * 发生数量
     */
    @Schema(title = "发生数量",description = "发生数量")
    private BigDecimal realQty;

    @Schema(title = "发货仓库编码",description = "发货仓库编码")
    private String outWhCode;
    /**
     * 释放单据的单号
     */
    @Schema(title = "原单号",description = "原单号")
    private String srcBillNo;

    @Schema(title = "原单类型",description = "原单类型")
    private String srcBillType;
}
