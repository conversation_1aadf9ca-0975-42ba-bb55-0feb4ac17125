package com.meta.supplychain.entity.dto.pms.resp.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "需求单提交返回对象")
public class PmsSubmitBillResp {
    @Schema(description = "配送订单总数量")
    private Integer deliveryTotalCnt = 0;

    @Schema(description = "配送订单自动转单数量")
    private Integer deliveryCnt = 0;

    @Schema(description = "配送订单自动转单成果数量")
    private Integer deliverySuccessCnt= 0;

    @Schema(description = "配送订单自动转单失败")
    private List<FailedInfo> deliveryfailedInfoList;

    @Schema(description = "采购订单总数量")
    private Integer purchTotalCnt= 0;

    @Schema(description = "采购订单自动转单数量")
    private Integer purchCnt= 0;

    @Schema(description = "采购订单自动转单成果数量")
    private Integer purchSuccessCnt= 0;

    @Schema(description = "采购订单自动转单失败")
    private List<FailedInfo> purchfailedInfoList;

    @Getter
    @Setter
    @Builder
    public static class FailedInfo{
        @Schema(description = "单据号")
        String billNo;

        @Schema(description = "失败原因")
        String failReason;

    }
}
