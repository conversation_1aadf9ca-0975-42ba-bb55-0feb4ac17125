package com.meta.supplychain.entity.dto.pms.req.billconvert;

import com.meta.supplychain.entity.po.pms.*;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 16:35
 **/
@Data
public class BillConvertDataDTO {
    @Schema(description = "采购订单主表")
    private List<PmsPurchaseOrderPO> purchBillList = new ArrayList<>();

    @Schema(description = "采购订单明细表")
    private List<PmsPurchaseBillDetailPO> purchBillDetailList = new ArrayList<>();

    @Schema(description = "采购订单明细关联表")
    private List<PmsPruchDetailRefPO> purchBillDetailRefList = new ArrayList<>();

    @Schema(description = "需求单出货方与采购配送订单关联关系表")
    private List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = new ArrayList<>();

    @Schema(description = "配送订单主表")
    private List<WdDeliveryBillPO> deliveryBillList = new ArrayList<>();

    @Schema(description = "配送订单明细表")
    private List<WdDeliveryBillDetailPO> deliveryBillDetailList = new ArrayList<>();

    @Schema(description = "订货申请表")
    private List<PmsApplyBillPO> applyBillList = new ArrayList<>();

    @Schema(description = "订货申请明细表")
    private List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();

    @Schema(description = "采购订单配转采最近供应商")
    private List<PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordList = new ArrayList<>();
}
