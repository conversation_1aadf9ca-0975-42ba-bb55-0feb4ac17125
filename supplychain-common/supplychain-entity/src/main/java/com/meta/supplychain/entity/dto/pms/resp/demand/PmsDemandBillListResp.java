package com.meta.supplychain.entity.dto.pms.resp.demand;


import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "需求单列表返回对象")
public class PmsDemandBillListResp {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "单据类别（-1:退货，1:要货）")
    private Integer billDirection;

    @Schema(description = "需求单来源0手工,1自动,2追加追减")
    private Integer billSource;

    @Schema(description = "商品数量")
    private Integer totalSkuQty;

    @Schema(description = "订货部门数")
    private Integer totalOrderDeptQty;

    @Schema(description = "需求数量")
    private BigDecimal totalDemandQty;

    @Schema(description = "响应数量")
    private BigDecimal totalResponseQty;

    @Schema(description = "订货属性编码,逗号分割多个,要货多选,退货单选")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称,逗号分割多个,要货多选,退货单选")
    private String orderAttributeName;

    @Schema(description = "需求单备注")
    private String remark;

    @Schema(description = "采购订单有效日期")
    private LocalDate purchValidityDate;

    @Schema(description = "采购订单送货日期")
    private LocalDate purchDeliverDate;

    @Schema(description = "配送订单有效日期")
    private LocalDate deliverValidityDate;

    @Schema(description = "配送订单送货日期")
    private LocalDate deliverDeliverDate;

    /** 需求单状态1草稿,2提交,3已作废 */
    @Schema(description = "需求单状态1草稿,2提交,3已作废")
    private Integer status;

    @Schema(description = "退货原因编码-数据字典")
    private String refundReason;

    @Schema(description = "退货原因名称-数据字典")
    private String refundReasonDesc;

    @Schema(description = "创建人工号")
    private String createCode;

    @Schema(description = "创建人ssoId")
    private Long createUid;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "提交人ssoId")
    private Long submitUid;

    @Schema(description = "提交人编码")
    private String submitCode;

    @Schema(description = "提交人姓名")
    private String submitName;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime submitTime;

    @Schema(description = "作废类型,1作废需求单及原订货/退货申请行,2只作废需求单，原订货/退货申请行还原为待提单")
    private Integer cancelType;

    @Schema(description = "作废人ssoId")
    private Long cancelUid;

    @Schema(description = "作废人编码")
    private String cancelCode;

    @Schema(description = "作废人姓名")
    private String cancelName;

    @Schema(description = "作废时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime cancelTime;
}
