package com.meta.supplychain.entity.dto.md.component.goodsrule;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Classname CategoryCodeAll
 * @Dscription TODO
 * @DATE 2025/6/5 20:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryCodeAll {

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 品类全路径
     */
    private String categoryCodeAll;
}
