package com.meta.supplychain.entity.dto.wds.resp;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class MoveBillExcelView {

    /**
     * 移位单号
     */
    @ViewField(headerName = "移位单号")
    private String billNo;

    @ViewField(headerName = "配送中心编码")
    private String whCode;

    @ViewField(headerName = "配送中心名称")
    private String whName;

    /**
     * 状态
     */
    @ViewField(headerName = "状态")
    private String status;

    /**
     * 移位类型： 1商品移位、2良品分拣、3储位转移、4残品转移
     */
    @ViewField(headerName = "移位类型")
    private String moveType;

    /**
     * 移出储位名称
     */
    @ViewField(headerName = "移出储位编码")
    private String outLocationCode;
    /**
     * 移出储位名称
     */
    @ViewField(headerName = "移出储位名称")
    private String outLocationName;

    /**
     * 移入储位名称
     */
    @ViewField(headerName = "移入储位编码")
    private String inLocationCode;
    /**
     * 移入储位名称
     */
    @ViewField(headerName = "移入储位名称")
    private String inLocationName;

    /** 创建时间 */
    @ViewField(headerName = "制单时间")
    private String createTime;
    @ViewField(headerName = "制单人编码")
    private String createCode;
    @ViewField(headerName = "制单人名称")
    private String createName;

    /**
     * 审核时间
     */
    @ViewField(headerName = "审核时间")
    private String confirmTime;

    @ViewField(headerName = "审核人编码")
    private String confirmManCode;

    @ViewField(headerName = "审核人名称")
    private String confirmManName;


    @ViewField(headerName = "备注")
    private String remark;

}
