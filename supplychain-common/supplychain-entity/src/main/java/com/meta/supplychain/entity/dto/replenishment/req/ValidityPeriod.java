package com.meta.supplychain.entity.dto.replenishment.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ValidityPeriod
 * <p>效期
 * @Date 2024/3/26 19:34
 */
@Data
public class ValidityPeriod {

    /**
     * 关联单据类型
     */
    private Integer billType;

    /**
     * 效期批号
     */
    private String periodBatchNo;

    /**
     * 效期条码
     */
    private String periodBarcode;

    /**
     * 生产日期
     */
    private String mfg;

    /**
     * 到期日期
     */
    private String exp;

    /**
     * 数量
     */
    private Long num;

    /**
     * 效期状态（0-正常 1-临期，2-过期，默认正常）
     */
    private Integer periodState;

    /**
     * 是否有效 0-失效，1-有效
     */
    private Integer yn;

    /**
     * 赠品数量
     */
    private Long giftNum;

    /**
     * 调拨金额/订货金额（含税）
     */
    private String purchMoney;

    /**
     * 税金(调拨/订货税金 单位毫)
     */
    private String purchTax;

    /**
     * 参考金额（毫）
     */
    private Long referMoney;

    /**
     * 箱数
     */
    private Long wholeAmount;

    /**
     * 零头数量
     */
    private Long remnantAmount;

    /**
     * 商品行单内序号
     */
    private String insideId;

    /**
     * 效期行单内序号
     */
    private String productInsideid;

    /**
     * 打印字段--无税金额 单位毫
     */
    private String noTaxMoney;
}
