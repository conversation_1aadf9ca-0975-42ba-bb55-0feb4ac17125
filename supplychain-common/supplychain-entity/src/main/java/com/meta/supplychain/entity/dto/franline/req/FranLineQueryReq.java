package com.meta.supplychain.entity.dto.franline.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 加盟额度查询入参
 */
@Getter
@Setter
@Builder
public class FranLineQueryReq {
    /**
     * 加盟商编码（二选一）
     */
    private String franCode;

    /**
     * 加盟店编码（二选一）
     */
    private String storeCode;

    /**
     * 加盟店编码集合
     */
    private List<String> storeCodeList;
}
