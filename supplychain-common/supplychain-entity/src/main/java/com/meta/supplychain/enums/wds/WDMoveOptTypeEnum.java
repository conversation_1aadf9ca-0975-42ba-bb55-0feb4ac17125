package com.meta.supplychain.enums.wds;

import com.meta.supplychain.enums.StandardEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WDMoveOptTypeEnum implements StandardEnum<Integer> {

    CREATE(0, "新增保存", true, false),
    MODIFY(1, "编辑保存",false,false),
    CREATE_AUDIT(2, "新增审核",true,true),
    MODIFY_AUDIT(3, "编辑审核",false,true);
    private final Integer code;
    private final String desc;
    private final boolean insert;
    private final boolean audit;
}
