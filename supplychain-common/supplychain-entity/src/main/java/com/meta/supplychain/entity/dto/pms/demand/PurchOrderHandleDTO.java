package com.meta.supplychain.entity.dto.pms.demand;

import com.meta.supplychain.entity.po.pms.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/23 14:00
 **/
@Data
public class PurchOrderHandleDTO {
    @Schema(description = "采购订单主表")
    private PmsPurchaseOrderPO purchBill;

    @Schema(description = "采购订单明细表")
    private List<PmsPurchaseBillDetailPO> purchBillDetailList = new ArrayList<>();

    @Schema(description = "采购订单明细关联表")
    private List<PmsPruchDetailRefPO> purchBillDetailRefList = new ArrayList<>();

    @Schema(description = "需求单出货方与采购配送订单关联关系表")
    private List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = new ArrayList<>();

    @Schema(description = "采购订单明细表,key=商品编码_商品类型")
    private Map<String,PmsPurchaseBillDetailPO> purchaseBillDetailPOMap = new HashMap<>();

    @Schema(description = "采购订单配转采最近供应商,key为唯一键")
    private Map<String,PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordPOMap = new HashMap<>();
}
