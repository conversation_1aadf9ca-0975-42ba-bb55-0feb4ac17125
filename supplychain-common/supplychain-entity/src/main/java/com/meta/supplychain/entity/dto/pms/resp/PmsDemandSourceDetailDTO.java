package com.meta.supplychain.entity.dto.pms.resp;

import com.meta.supplychain.annotation.DecimalScale;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 需求来源详细信息DTO
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "需求来源详细信息DTO")
public class PmsDemandSourceDetailDTO {

    @Schema(description = "来源类型")
    private Integer type;

    @Schema(description = "来源单号")
    private String applyBillNo;

    @Schema(description = "来源单据行号")
    private Long srcInsideId;

    @Schema(description = "来源单据备注")
    private String remark;

    @Schema(description = "来源单据商品备注")
    private String goodsRemark;

    @Schema(description = "来源单据订退货编码")
    private String attributeCode;

    @Schema(description = "来源单据订退货名称")
    private String attributeName;

    @Schema(description = "来源单据出货途径")
    private String srcShippingWay;

    @Schema(description = "来源单据出货方名称")
    private String srcShipperName;

    @Schema(description = "来源单据出货方编码")
    private String srcShipperCode;

    @Schema(description = "来源单据管理分类编码")
    private String srcManageCategoryCode;

    @Schema(description = "来源单据管理分类名称")
    private String srcManageCategoryName;

    @Schema(description = "来源客户编码")
    private String srcCustomerCode;

    @Schema(description = "来源客户名称")
    private String srcCustomerName;

    @DecimalScale(value = 4)
    @Schema(description = "来源订货包装率")
    private BigDecimal srcDemandUnitRate;

    @DecimalScale(value = 4)
    @Schema(description = "来源整件数")
    private BigDecimal srcDemandWholeQty;

    @DecimalScale(value = 4)
    @Schema(description = "来源零头数")
    private BigDecimal srcDemandOddQty;

    @DecimalScale(value = 4)
    @Schema(description = "来源需求数量")
    private BigDecimal srcDemandQty;

    @DecimalScale(value = 4)
    @Schema(description = "响应订货包装率")
    private BigDecimal responseUnitRate;

    @DecimalScale(value = 4)
    @Schema(description = "响应整件数量")
    private BigDecimal responseWholeQty;

    @DecimalScale(value = 4)
    @Schema(description = "响应零头数量")
    private BigDecimal responseOddQty;

    @DecimalScale(value = 4)
    @Schema(description = "响应数量")
    private BigDecimal responseQty;

    @Schema(description = "需求批次")
    private String purchBatchNo;
} 