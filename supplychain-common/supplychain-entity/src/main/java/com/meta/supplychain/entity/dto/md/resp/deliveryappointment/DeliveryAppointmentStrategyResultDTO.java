package com.meta.supplychain.entity.dto.md.resp.deliveryappointment;

import com.meta.supplychain.entity.po.md.MdDeliveryDockDeptPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockGoodsCategoryPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockStrategyPO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/21 13:49
 **/
@Data
public class DeliveryAppointmentStrategyResultDTO {
    //配送对应的停靠点信息
    private List<MdDeliveryDockStrategyPO> dockStrategyPOList = new ArrayList<>();

    //配送对应的停靠点商品信息
    private List<MdDeliveryDockGoodsCategoryPO> dockGoodsCategoryPOList = new ArrayList<>();

    //配送对应的停靠点部门信息
    private List<MdDeliveryDockDeptPO> dockDeptPOList = new ArrayList<>();
}
