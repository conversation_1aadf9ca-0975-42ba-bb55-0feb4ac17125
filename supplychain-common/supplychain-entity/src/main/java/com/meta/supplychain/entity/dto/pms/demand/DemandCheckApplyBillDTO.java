package com.meta.supplychain.entity.dto.pms.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/18 16:04
 */
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class DemandCheckApplyBillDTO {
    /**
     * 商品信息
     */
    @Schema(description = "需求单号")
    private String demandBillNo;

    private List<ApplyInfo> applyInfoList;
    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyInfo {

        @Schema(description = "SPU名称")
        private String applyBillNo;
        /**
         * SPU货号
         */
        @Schema(description = "SPU货号")
        private Long srcInsideId;
    }


}
