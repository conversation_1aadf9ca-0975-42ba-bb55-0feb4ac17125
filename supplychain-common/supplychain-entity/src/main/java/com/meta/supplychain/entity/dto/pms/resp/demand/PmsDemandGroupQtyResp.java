package com.meta.supplychain.entity.dto.pms.resp.demand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/20 20:27
 **/
@Data
public class PmsDemandGroupQtyResp {

    @Schema(description = "草稿数量")
    private Integer status1Qty = 0;

    @Schema(description = "提交数量")
    private Integer status2Qty = 0;

    @Schema(description = "已作废数量")
    private Integer status3Qty = 0;
}
