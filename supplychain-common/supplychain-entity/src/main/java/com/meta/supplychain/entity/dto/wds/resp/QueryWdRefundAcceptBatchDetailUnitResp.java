package com.meta.supplychain.entity.dto.wds.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Schema(description = "退货收货打印详情")
@Getter
@Setter
@ToString
@Builder
public class QueryWdRefundAcceptBatchDetailUnitResp {

    @Schema(description = "单据信息")
    QueryWdRefundAcceptBillResp billInfo;

    @Schema(description = "商品行列表")
    List<QueryWdRefundAcceptBatchDetailResp> detailList;

}
