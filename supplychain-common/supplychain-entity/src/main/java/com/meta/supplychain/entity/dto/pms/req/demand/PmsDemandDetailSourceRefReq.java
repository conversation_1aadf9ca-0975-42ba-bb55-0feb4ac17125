package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandDetailSourceRefReq {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "单内序号,前端生成，单据内唯一")
    private Long insideId;

    @Schema(description = "上一级(pms_demand_dept_goods_detail)单内序号,前端生成，单据内唯一")
    private Long pinsideId;

    @Schema(description = "上上一级(pms_demand_goods_detail)单内序号,前端生成，单据内唯一")
    private Long goodsInsideId;

    @Schema(description = "0失败 1成功 2失效")
    private Integer status = 1;

    @Schema(description = "需求单号")
    private String demandBillNo;

    @Schema(description = "订货部门编码")
    private String orderDeptCode;

    @Schema(description = "订货部门名称")
    private String orderDeptName;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品类型,0主品,1附赠赠品")
    private Integer goodsType;

    @Schema(description = "来源单订退货属性编码")
    private String attributeCode;

    @Schema(description = "来源单订退货属性名称")
    private String attributeName;

    @Schema(description = "来源单号-申请单")
    private String applyBillNo;

    @Schema(description = "来源单内序号")
    private Long srcInsideId;

    @Schema(description = "来源客户编码")
    private String srcCustomerCode;

    @Schema(description = "需求零头数量")
    private BigDecimal srcDemandOddQty;


    @Schema(description = "来源单据送货方式 0-到店，1-到客户")
    private Integer srcSendMode;

    @Schema(description = "来源单据出货方编码 1.申请类型=采购，供应商编码 2-申请类型=配送，部门档案（部门类型=配送中心 且 状态<>停用）")
    private String srcShipperCode;

    @Schema(description = "来源单商品备注")
    private String goodsRemark;


    @Schema(description = "来源需求数量")
    private BigDecimal srcDemandQty;
    @Schema(description = "来源单据管理分类名称")
    private String srcManageCategoryName;

    @Schema(description = "采购批次")
    private String purchBatchNo;
    @Schema(description = "来源单据管理分类项编码")
    private String srcManageCategoryClass;

    @Schema(description = "来源客户地址")
    private String srcBuyerAddr;
    @Schema(description = "来源需求包装率")
    private BigDecimal srcDemandUnitRate;
    @Schema(description = "来源类型 1.门店要货(订货申请) 2.主动配货(主派)")
    private Integer type;
    @Schema(description = "来源单据出货方名称")
    private String srcShipperName;
    @Schema(description = "来源客户名称")
    private String srcCustomerName;
    @Schema(description = "来源客户手机")
    private String srcBuyerTel;

    @Schema(description = "需求整件数量")
    private BigDecimal srcDemandWholeQty;

    @Schema(description = "来源订货价")
    private BigDecimal srcOrderPrice;

    @Schema(description = "来源客户联系人")
    private String srcBuyerName;
    @Schema(description = "来源单据备注")
    private String remark;
    @Schema(description = "来源单据管理分类编码")
    private String srcManageCategoryCode;
    @Schema(description = "来源单据出货途径 0.采购 1.配送")
    private Integer srcShippingWay;

    @Schema(description = "来源退货原因编码")
    private String srcRefundReason;

    @Schema(description = "来源退货原因名称")
    private String srcRefundReasonDesc;

    @Schema(description = "响应数量")
    private BigDecimal responseQty;

    @Schema(description = "响应零头数量")
    private BigDecimal responseOddQty;

    @Schema(description = "响应整件数量")
    private BigDecimal responseWholeQty;

    @Schema(description = "响应订货包装率")
    private BigDecimal responseUnitRate;

}
