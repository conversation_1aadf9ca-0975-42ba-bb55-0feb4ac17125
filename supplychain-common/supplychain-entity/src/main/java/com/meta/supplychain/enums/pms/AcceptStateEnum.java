package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AcceptStateEnum {

    /**
     * 单据状态 0-草稿 1-待审核 2-已审核 3-作废
     */

    DRAFT(0, "草稿"),
    PENDING_AUDIT(1, "待审核"),
    AUDIT(2, "已审核"),
    VOIDED(3, "作废");

    private final Integer billStateCode;

    private final String billStateName;

    public static AcceptStateEnum getEnumByCode(Integer billStateCode) {
        AcceptStateEnum[] values = values();
        for (AcceptStateEnum value : values) {
            if(value.getBillStateCode().equals(billStateCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     *  返回-1 不允许  0允许 1.允许且需推erp
     * @param billStateCode
     * @return
     */
    public static boolean canUpdate(Integer billStateCode) {
        return canCancel(billStateCode);
    }

    /**
     *  返回-1 不允许  0允许 1.允许且需推erp
     * @param billStateCode
     * @return
     */
    public static boolean canCancel(Integer billStateCode) {
        return DRAFT.getBillStateCode().equals(billStateCode) || PENDING_AUDIT.getBillStateCode().equals(billStateCode);

    }

}
