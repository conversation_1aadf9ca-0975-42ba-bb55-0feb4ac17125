package com.meta.supplychain.convert.pms;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ViewField;
import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.view.PmsAppointmentDetailView;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 预约单详细信息转换器
 * <AUTHOR>
 */
@Mapper
public interface PmsAppointmentDetailConvert extends StandardEnumConvert {

    PmsAppointmentDetailConvert INSTANCE = Mappers.getMapper(PmsAppointmentDetailConvert.class);

    /**
     * 将PmsAppointmentDetailDTO转换为PmsAppointmentDetailView（导出视图）
     * @param dto 预约单详细信息DTO
     * @return 预约单详细信息导出视图
     */
    @Mapping(target = "billDirection", expression = "java(convertToDesc(\"pmsBookingCategoryEnum\", dto.getBillDirection()))")
    @Mapping(target = "directSign", expression = "java(convertToDesc(\"YesOrNoEnum\", dto.getDirectSign()))")
    @Mapping(target = "transportMode", expression = "java(convertToDesc(\"pmsCarrierMethodEnum\", dto.getTransportMode()))")
    @Mapping(target = "status", expression = "java(convertToDesc(\"pmsBookingStatusEnum\", dto.getStatus()))")
    @Mapping(target = "billSource", expression = "java(convertToDesc(\"pmsPurchaseOrderSourceEnum\", dto.getBillSource()))")
    @Mapping(target = "skuType", expression = "java(convertToDesc(\"SkuTypeEnum\", dto.getSkuType()))")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "planArrivalTime", target = "planArrivalTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "submitTime", target = "submitTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "deliverDate", target = "deliverDate")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "validityDate", target = "validityDate")
    PmsAppointmentDetailView dto2view(PmsAppointmentDetailDTO dto);

    @AfterMapping
    default void afterMapping(@MappingTarget Object target) {
        try {
            Field[] fields = target.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ViewField.class) && field.getType().equals(BigDecimal.class)) {
                    field.setAccessible(true);
                    BigDecimal value = (BigDecimal) field.get(target);
                    if (value != null) {
                        BigDecimal stripped = value.stripTrailingZeros();
                        // 避免出现科学计数法
                        if (stripped.scale() < 0) {
                            stripped = stripped.setScale(0, RoundingMode.UNNECESSARY);
                        }
                        field.set(target, stripped);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("MapStruct Convert BigDecimal字段处理失败", e);
        }
    }
} 