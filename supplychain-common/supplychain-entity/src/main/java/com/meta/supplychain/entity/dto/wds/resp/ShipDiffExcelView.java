package com.meta.supplychain.entity.dto.wds.resp;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 差异处理单导出模型
 */
@Data
public class ShipDiffExcelView {

    /** 单据号 */
    @ViewField(headerName= "单据号")
    private String billNo;

    /** 配送单号 */
    @ViewField(headerName= "配送单号")
    private String shipBillNo;
    /** 仓库编码 */
    @ViewField(headerName= "配送中心编码")
    private String whCode;
    /** 仓库名称 */
    @ViewField(headerName= "配送中心名称")
    private String whName;
    /**
     * 配送时间
     */
    @ViewField(headerName= "配送时间")
    private String shipTime;
    /** 验收单号 */
    @ViewField(headerName= "验收单号")
    private String acceptBillNo;

    /** 部门编码 */
    @ViewField(headerName= "收货部门编码")
    private String deptCode;
    /** 部门名称 */
    @ViewField(headerName= "收货部门名称")
    private String deptName;
    @ViewField(headerName = "门店收货人编码")
    private String createCode;

    @ViewField(headerName = "门店收货人名称")
    private String createName;
    @ViewField(headerName= "收货时间")
    private String createTime;
    /** 合计差异数量 */
    @ViewField(headerName= "差异数量")
    private BigDecimal totalDiffQty;
    /** 合计差异金额(含税) */
    @ViewField(headerName= "差异金额(含税)")
    private String totalDiffTaxMoney;
    /** 差异单状态枚举 1 待处理、2 已审核、3 已驳回 WDAcceptDiffStatusEnum */
    @ViewField(headerName= "状态")
    private String statusDesc;
    /** 审核时间 */
    @ViewField(headerName= "审核时间")
    private String approveTime;
    /** 审核人编码 */
    @ViewField(headerName = "审核人编码")
    private String approveManCode;
    /** 审核人名称 */
    @ViewField(headerName = "审核人名称")
    private String approveManName;
    /** 收货备注 */
    @ViewField(headerName= "收货备注")
    private String accRemark;
    /** 审核备注 */
    @ViewField(headerName= "审核备注")
    private String remark;
}