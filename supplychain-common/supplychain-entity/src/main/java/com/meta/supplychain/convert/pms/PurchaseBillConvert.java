package com.meta.supplychain.convert.pms;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillChangeReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillDetailCreateReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.*;
import com.meta.supplychain.entity.dto.pms.view.*;
import com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.enums.pms.DirectSignEnum;
import com.meta.supplychain.enums.pms.PmsPurchaseOrderSourceEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface PurchaseBillConvert extends StandardEnumConvert {
    PurchaseBillConvert INSTANCE = Mappers.getMapper(PurchaseBillConvert.class);

    PurchaseBillResp convertPo2Vo(PmsPurchaseOrderPO pmsPurchaseOrderPO);

    List<PurchaseBillDetailResp> convertPo2VoList(List<PmsPurchaseBillDetailPO> poList);

    @Mapping(target = "id", ignore = true)
    PmsPurchaseOrderPO convertToPurchaseOrderPO(PurchaseBillChangeReq req);

    List<PmsPurchaseBillDetailPO> convertToPurchBillDetailPOList(List<PurchaseBillDetailCreateReq> detailList);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "cancelTime", target = "cancelTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
    })
    PmsPurchaseOrderExportView convertExportView(PurchaseBillResp billResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
    })
    PmsPurchaseOrderScmExportView convertExportViewScm(PurchaseBillResp billResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
    })
    PmsReversesOrderScmExportView convertExportViewReversesScm(PurchaseBillResp billResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "fulfilTime", target = "fulfilTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "cancelTime", target = "cancelTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime"),
            @Mapping(source = "inputTaxRate", target = "inputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "outputTaxRate", target = "outputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "unitRate", target = "unitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchUnitRate", target = "purchUnitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "promotePeriodPrice", target = "promotePeriodPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "contractSpecialPrice", target = "contractSpecialPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "contractPrice", target = "contractPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "contractMaxPrice", target = "contractMaxPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "lastPurchPrice", target = "lastPurchPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchPrice", target = "purchPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchQty", target = "purchQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchMoney", target = "purchMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchTax", target = "purchTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentQty", target = "appointmentQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentRate", target = "appointmentRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilQty", target = "fulfilQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilMoney", target = "fulfilMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilRate", target = "fulfilRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "confirmQty", target = "confirmQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
    })
    PmsPurchaseDetailExportView convertDetailExportView(PurchaseBillDetailSumResp detailSumResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "fulfilTime", target = "fulfilTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime"),
            @Mapping(source = "inputTaxRate", target = "inputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "outputTaxRate", target = "outputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "unitRate", target = "unitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchUnitRate", target = "purchUnitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchPrice", target = "purchPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchQty", target = "purchQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchMoney", target = "purchMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchTax", target = "purchTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentQty", target = "appointmentQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentRate", target = "appointmentRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilQty", target = "fulfilQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilMoney", target = "fulfilMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilRate", target = "fulfilRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "confirmQty", target = "confirmQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
    })
    PmsPurchaseDetaiScmlExportView convertDetailExportViewScm(PurchaseBillDetailSumResp detailSumResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "fulfilTime", target = "fulfilTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(source = "inputTaxRate", target = "inputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "outputTaxRate", target = "outputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "unitRate", target = "unitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchUnitRate", target = "purchUnitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchPrice", target = "purchPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchQty", target = "purchQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchMoney", target = "purchMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchTax", target = "purchTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentQty", target = "appointmentQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentRate", target = "appointmentRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilQty", target = "fulfilQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilMoney", target = "fulfilMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilRate", target = "fulfilRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "confirmQty", target = "confirmQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
    })
    PmsReversesDetaiScmExportView convertReversesDetailExportViewScm(PurchaseBillDetailSumResp detailSumResp);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "fulfilTime", target = "fulfilTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "readTime", target = "readTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmDeliverTime", target = "confirmDeliverTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "shipTime", target = "shipTime"),
            @Mapping(source = "inputTaxRate", target = "inputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "outputTaxRate", target = "outputTaxRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "unitRate", target = "unitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchUnitRate", target = "purchUnitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchPrice", target = "purchPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchQty", target = "purchQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchMoney", target = "purchMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "purchTax", target = "purchTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentQty", target = "appointmentQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "appointmentRate", target = "appointmentRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilQty", target = "fulfilQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilMoney", target = "fulfilMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "fulfilRate", target = "fulfilRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "confirmQty", target = "confirmQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalQty", target = "totalQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryOrderUnitRate", target = "deliveryOrderUnitRate", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryPrice", target = "deliveryPrice", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryWholeQty", target = "deliveryWholeQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryOddQty", target = "deliveryOddQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryQty", target = "deliveryQty", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryTaxMoney", target = "deliveryTaxMoney", qualifiedByName = "doubleToStr"),
            @Mapping(source = "deliveryTax", target = "deliveryTax", qualifiedByName = "doubleToStr"),
    })
    PmsPurchaseDirectScmExportView convertDirectExportViewScm(PurchaseBillDetailWithDirectResp directResp);

    @Mapping(source = "directSign", target = "directSign")
    @Mapping(source = "directSign", target = "directSignDesc", qualifiedByName = "directSignToDesc")
    @Mapping(source = "billSource", target = "billSource")
    @Mapping(source = "billSource", target = "billSourceDesc", qualifiedByName = "billSourceToDesc")
    QueryPurchPricingResp.QueryPurchPricingDetailResp convertRespToResp(PurchaseBillDetailSumResp detailSumResp);

    List<PmsPruchDetailRefResp> convertRefList(List<PmsPruchDetailRefPO> poList);

    @Named("directSignToDesc")
    default String directSignToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        DirectSignEnum enumValue = DirectSignEnum.getByCode(code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }

    @Named("billSourceToDesc")
    default String billSourceToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        PmsPurchaseOrderSourceEnum enumValue = PmsPurchaseOrderSourceEnum.getByCode(code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }

    @Named("doubleToStr")
    default String doubleToStr(BigDecimal num) {
        if (num == null) {
            return "";
        }
        return num.stripTrailingZeros().toPlainString();
    }
}