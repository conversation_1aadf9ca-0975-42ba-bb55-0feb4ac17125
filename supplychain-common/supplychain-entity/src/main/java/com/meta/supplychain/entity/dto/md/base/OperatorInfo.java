package com.meta.supplychain.entity.dto.md.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 通用操作人信息
 */
@Data
public class OperatorInfo {

    @Schema(description = "租户号", accessMode = Schema.AccessMode.READ_ONLY)
    private String tenantId;

    @Schema(description = "创建人ssoId", accessMode = Schema.AccessMode.READ_ONLY)
    private Long createUid;

    @Schema(description = "创建人编码", accessMode = Schema.AccessMode.READ_ONLY)
    private String createCode;

    @Schema(description = "创建人姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String createName;

    @Schema(description = "创建时间", accessMode = Schema.AccessMode.READ_ONLY)
    private Date createTime;

    @Schema(description = "修改人ssoId", accessMode = Schema.AccessMode.READ_ONLY)
    private Long updateUid;

    @Schema(description = "修改人编码", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateCode;

    @Schema(description = "修改人姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateName;

    @Schema(description = "修改时间", accessMode = Schema.AccessMode.READ_ONLY)
    private Date updateTime;
}
