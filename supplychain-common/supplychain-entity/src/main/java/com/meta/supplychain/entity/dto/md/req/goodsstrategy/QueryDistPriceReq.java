package com.meta.supplychain.entity.dto.md.req.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * 配送价格单取价逻辑
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "配送价格查询请求")
public class QueryDistPriceReq implements Serializable {
    /**
     * 商品编码列表
     */
    @Schema(description = "商品编码列表")
    private Set<QueryDistPriceComplexReq.SkuCodeAndWhCode> skuList;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String deptCode;
}
