package com.meta.supplychain.entity.dto.md.deliveryappointment;

import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.md.MdDeliveryDockDefineTypeEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 供应商预约策略商品品类限制DTO
 */
@Data
@Schema(description = "供应商预约策略商品品类限制")
public class MdDeliveryDockGoodsCategoryDTO extends BaseDTO {
    
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "行号不能为空")
    @Schema(description = "行号")
    private Integer insideId;

    @NotBlank(message = "停靠点编码不能为空")
    @Schema(description = "停靠点编码，关联到停靠点策略主表")
    private String dockCode;

    @EnumValue(type = MdDeliveryDockDefineTypeEnum.class, required = true, message = "非法的停靠点定义方式")
    @Schema(description = "定义方式")
    private Integer type;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "品类编码")
    private String classCode;

    @Schema(description = "品类名称")
    private String className;

    @Schema(description = "品类级别")
    private Integer classLevel;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        MdDeliveryDockGoodsCategoryDTO that = (MdDeliveryDockGoodsCategoryDTO) o;
        if (MdDeliveryDockDefineTypeEnum.BY_CATEGORY.verifyByCode(this.type)) {
            return Objects.equals(type, that.type)
                    && Objects.equals(dockCode, that.dockCode)
                    && Objects.equals(classCode, that.classCode)
                    && Objects.equals(classLevel, that.classLevel);
        }
        if (MdDeliveryDockDefineTypeEnum.BY_PRODUCT.verifyByCode(this.type)) {
            return Objects.equals(type, that.type)
                    && Objects.equals(dockCode, that.dockCode)
                    && Objects.equals(skuCode, that.skuCode);
        }
        return true;
    }

    @Override
    public int hashCode() {
        if (MdDeliveryDockDefineTypeEnum.BY_CATEGORY.verifyByCode(this.type)) {
            return Objects.hash(type, dockCode, classCode, classLevel);
        }
        if (MdDeliveryDockDefineTypeEnum.BY_PRODUCT.verifyByCode(this.type)) {
            return Objects.hash(type, dockCode, skuCode);
        }
        return 0;
    }
}