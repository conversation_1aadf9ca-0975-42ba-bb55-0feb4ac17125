package com.meta.supplychain.entity.dto.goods.resp;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/12 14:41
 */
@Builder
@Data
public class SimpleGoodsResultResp {

    private Integer total;

    private List<SimpleGoodsResult> rows;

    @Data
    public static class SimpleGoodsResult {
        private String mainSpuCode2;
        private String mainSpuCode;
        private String mainSkuFlag;
        private Double skuCooperatorId;
        private Double goodsType;
        private Double combType;
        private String spuCode;
        private String spuName;
        private String spuBarCode;
        private String spuGoodsNo;
        private String skuCode;
        private String skuName;
        private String skuBarCode;
        private String goodsNo;
        private String skuCategoryCodeOffLine;
        private String categoryName;
        private String skuBrandCodeOffLine;
        private String skuBrandName;
        private Double measureproperty;
        private Double unit;
        private String unitName;
        private String skuSpecModel;
        private Double minPurchNums;
        private String skuOperationModel;
        private Double categoryCodeFlag;
        private Double sendFlag;
        private Double expiryDateControlFlag;
        private Double skuIsCoupon;
        private String inCatalog;
        private String workStateCode;
        private String circulationModeCode;
        private Double handleSerFlag;
        private String inventoryCategory;
        private String inventorySource;
        private String warehouseCode;
        private Double backFlushFlag;
        private String costPrice;
        private String marketPriceSpu;
        private String referPriceSpu;
        private String memberPriceSpu;
        private String purchasePriceSpu;
        private String marketPrice;
        private String referPrice;
        private String memberPrice;
        private String ceilingPrice;
        private String floorPrice;
        private String dispatchPrice;
        private String wholesalePrice;
        private String purchasePrice;
        private Double supplierId;
        private String buyerCode;
        private String buyerName;
        private Double mainPic;
        private Double detailFlag;
        private String skuIsSpec;
        private String producingArea;
        private String inputTaxRate;
        private String outputTaxRate;
        private String giftsTrialFlag;
        private String skuUpdateTime;
        private String stockNum;
        private Double skuState;
        private String overAllState;
        private Double distriFlag;
        private String skuSaleAttr;
        private String skuSpecAttr;
    }

}
