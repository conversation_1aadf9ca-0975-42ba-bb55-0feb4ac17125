package com.meta.supplychain.entity.dto.md.req.demandbatchstraegy;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/07 16:09
 **/
@Data
@Schema(description = "需求批次记录查询")
public class MdDemandBatchRecordByDeptSkuReq extends PageParams {

    @Schema(description = "到店日期YYYY-MM-dd")
    private String arrivalDate;

    @Schema(description = "商品信息")
    private List<GoodsQuery> goodsList;

    @Data
    public static class GoodsQuery {
        @Schema(description = "配送部门编码")
        private String deptCode;

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品所有的品类编码")
        private List<String> categoryCodeList;
    }
}
