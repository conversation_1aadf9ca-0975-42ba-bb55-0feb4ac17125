package com.meta.supplychain.entity.dto.pms.apply;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/21 18:59
 **/
@Data
public class UpdateRespDemandDTO {
    private String demandBillNo;

    /**
     * 1未提单 ,2已提单 3 已作废
     */
    private Integer status;

    /**
     *
     */
    private String responseCreator;

    private String responseCreateCode;

    private Long responseCreateUid;

    private String responseCreateName;

    private LocalDateTime responseCreateTime;

    private List<ApplyInfo> applyInfoList;

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyInfo {

        private String billNo;
        /**
         * SPU货号
         */
        private Long insideId;
    }
}
