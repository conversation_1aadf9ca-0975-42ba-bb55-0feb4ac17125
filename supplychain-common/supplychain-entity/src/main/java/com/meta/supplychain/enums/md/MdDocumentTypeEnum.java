package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单据类别枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "单据类别", code = "mdDocumentTypeEnum")
public enum MdDocumentTypeEnum implements VerifiableEnum<Integer> {

    ORDER(1, "订货"),
    RETURN(2, "退货");

    private final Integer code;
    private final String desc;
}
