package com.meta.supplychain.entity.dto.md.req.addreducegoods;

import com.meta.supplychain.entity.dto.OpInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 14:50
 **/
@Schema(description = "追加追减商品设置表")
@Data
public class UpdateMdAddReduceReq {

    private OpInfo operatorInfo = new OpInfo();

    @Schema(description = "商品编码")
    @NotEmpty(message = "商品编码不能为空")
    private String skuCode;

    @Schema(description = "是否允许减单(0-否 1-是)")
    private Integer isAllowReduceBill;

    @Schema(description = "是否允许加单(0-否 1-是)")
    private Integer isAllowMultiBill;

    @Schema(description = "部门范围，1不指定部门，2指定部门")
    private Integer deptScope;

    @Schema(description = "商品部门信息")
    @Valid
    private List<CreateMdAddReduceGoodsDeptReq> deptList;
}
