package com.meta.supplychain.entity.dto.pms.req.demand;


import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryDemandBillListReq extends PageParams {

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "需求单状态1草稿,2提交,3已作废")
    private Integer status;

    @Schema(description = "单据类别（-1:退货，1:要货）")
    private Integer billDirection;

    @Schema(description = "开始创建时间,yyyy-MM-dd")
    private String startCreateTime;

    @Schema(description = "结束创建时间,yyyy-MM-dd")
    private String endCreateTime;

    @Schema(description = "开始提交时间,yyyy-MM-dd")
    private String startSubmitTime;

    @Schema(description = "结束提交时间,yyyy-MM-dd")
    private String endSubmitTime;

    @Schema(description = "创建人(工号姓名)")
    private String createEmp;

    @Schema(description = "提交人(工号姓名)")
    private String submitEmp;

    @Schema(description = "需求单备注")
    private String remark;

    @Schema(description = "不需要传")
    private Long currentSize;

    public String getEndCreateTime() {
        if(StringUtils.isNotEmpty(endCreateTime)){
            return endCreateTime + " 23:59:59";
        }
        return endCreateTime;
    }



    public String getEndSubmitTime() {
        if(StringUtils.isNotEmpty(endSubmitTime)){
            return endSubmitTime + " 23:59:59";
        }
        return endSubmitTime;
    }

}
