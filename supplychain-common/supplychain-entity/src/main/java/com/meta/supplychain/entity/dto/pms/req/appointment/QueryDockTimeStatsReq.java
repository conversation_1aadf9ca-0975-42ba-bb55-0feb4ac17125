package com.meta.supplychain.entity.dto.pms.req.appointment;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 停靠点时间统计查询请求
 * 
 * <AUTHOR>
 */
@Data
public class QueryDockTimeStatsReq {

    /**
     * 计划到达时间开始
     */
    private LocalDateTime planArrivalTimeStart;

    /**
     * 计划到达时间结束
     */
    private LocalDateTime planArrivalTimeEnd;

    /**
     * 预约单状态
     */
    private Integer status;

    /**
     * 停靠点编码列表
     */
    private List<String> dockCodes;
} 