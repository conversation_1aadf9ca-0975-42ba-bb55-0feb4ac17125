package com.meta.supplychain.entity.dto.pms.req.billconvert;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 11:01
 **/
public class BillConvertDeliveryShipperReq {
    @Schema(description = "单内序号,单据内唯一")
    private Long insideId;

    @Schema(description = "上一级单内序号,单据内唯一")
    private Long pInsideId;

    @Schema(description = "采购批次")
    private String purchBatchNo;

    @Schema(description = "配送部门编码")
    private String distDeptCode;

    @Schema(description = "配送部门名称")
    private String distDeptName;

    @Schema(description = "促销活动名称")
    private String promoteName;

    @Schema(description = "配送包装率")
    private BigDecimal deliveryUnitRate;

    @Schema(description = "最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)")
    private BigDecimal lastPurchPrice;

    @Schema(description = "是否直流")
    private Integer directSign;

    @Schema(description = "转单标识,0普通, 1配转采")
    private Integer convertFlag;

    @Schema(description = "订货部门编码")
    private String orderDeptCode;

    @Schema(description = "档案配送价（部门商品配送价，优先 没有取 档案配送价 商品逻辑）")
    private BigDecimal goodsDistPrice;

    @Schema(description = "配送部门成本价")
    private BigDecimal distDeptCostPrice;

    @Schema(description = "促销价")
    private BigDecimal promotePrice;

    @Schema(description = "促销活动编码")
    private String promoteCode;

    @Schema(description = "配送金额")
    private BigDecimal distMoney;

    @Schema(description = "配送价格")
    private BigDecimal distPrice;

    @Schema(description = "配送价格单编号")
    private String distPriceBillNo;

    @Schema(description = "配送税金")
    private BigDecimal distTax;

    @Schema(description = "数量")
    private BigDecimal qty;
}
