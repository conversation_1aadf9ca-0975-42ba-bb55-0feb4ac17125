package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DemandConvertlEnum {

    /**
     * 转单标识,0普通, 1配转采
     */

    NORMAL(0, "普通"),
    DELIVERY_TO_PURCH(1, "配转采"),


    ;

    private final Integer code;

    private final String desc;

    public static DemandConvertlEnum getEnumByCode(Integer code) {
        DemandConvertlEnum[] values = values();
        for (DemandConvertlEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


}
