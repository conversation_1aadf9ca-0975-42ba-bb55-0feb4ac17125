package com.meta.supplychain.entity.dto.md.req.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * 配送价格复杂查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "配送价格复杂查询请求")
public class QueryDistPriceComplexReq implements Serializable {
    
    /**
     * 部门编码和部门类型组合列表
     */
    @Schema(description = "部门编码和部门类型组合列表")
    private Set<DeptCodeAndType> deptCodeAndTypes;

    /**
     * 商品编码和配送中心编码组合列表
     */
    @Schema(description = "商品编码和配送中心编码组合列表")
    private Set<SkuCodeAndWhCode> skuCodeAndWhCodes;

    /**
     * 商品品类编码前缀
     */
    @Schema(description = "商品品类编码")
    private Set<String> skuClassCodeList;

    /**
     * 配送中心编码列表
     */
    @Schema(description = "配送中心编码列表")
    private Set<String> whCodes;

    /**
     * 定义类型
     */
    private Integer statementType;

    /**
     * 部门编码和部门类型组合
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptCodeAndType {
        @Schema(description = "部门编码")
        private String deptCode;

        @Schema(description = "部门类型")
        private Integer deptType;

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;
            DeptCodeAndType that = (DeptCodeAndType) o;
            return Objects.equals(deptCode, that.deptCode) && Objects.equals(deptType, that.deptType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(deptCode, deptType);
        }
    }

    /**
     * 商品编码和配送中心编码组合
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuCodeAndWhCode {
        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "配送中心编码")
        private String whCode;

        @Schema(description = "商品末级分类")
        private String skuClassCode;

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;
            SkuCodeAndWhCode that = (SkuCodeAndWhCode) o;
            return Objects.equals(skuCode, that.skuCode) && Objects.equals(whCode, that.whCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(skuCode, whCode);
        }
    }
} 