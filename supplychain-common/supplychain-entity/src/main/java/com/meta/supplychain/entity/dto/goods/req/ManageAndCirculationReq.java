package com.meta.supplychain.entity.dto.goods.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 经营状态、流转途径
 *
 * <AUTHOR> cat
 * @date 2024/3/25 21:03
 */
@Getter
@Setter
@Builder
public class ManageAndCirculationReq {
    /**
     * 经营状态编码（不传默认查全部）
     */
    List<String> workStateCodes;

    /**
     * 流转途径编码（不传默认查全部）
     */
    List<String> circulationModeCodes;
}
