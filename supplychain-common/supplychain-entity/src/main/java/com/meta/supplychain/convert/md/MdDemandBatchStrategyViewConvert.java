package com.meta.supplychain.convert.md;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchStrategyDTO;
import com.meta.supplychain.entity.dto.md.view.MdDemandBatchStrategyView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 需求批次策略视图转换器
 * <AUTHOR>
 */
@Mapper
public interface MdDemandBatchStrategyViewConvert extends StandardEnumConvert {

    MdDemandBatchStrategyViewConvert INSTANCE = Mappers.getMapper(MdDemandBatchStrategyViewConvert.class);

    /**
     * 将MdDemandBatchStrategyDTO转换为MdDemandBatchStrategyView（导出视图）
     * @param dto 需求批次策略DTO
     * @return 需求批次策略导出视图
     */
    @Mapping(target = "generateMethod", expression = "java(convertToDesc(\"demandBatchGenerateMethodEnum\", dto.getGenerateMethod()))")
    @Mapping(source = "initialDate", target = "initialDate", qualifiedByName = "localDateToString")
    @Mapping(source = "createTime", target = "createTime", qualifiedByName = "localDateTimeToString")
    @Mapping(source = "updateTime", target = "updateTime", qualifiedByName = "localDateTimeToString")
    MdDemandBatchStrategyView dto2view(MdDemandBatchStrategyDTO dto);

    /**
     * 将LocalDate转换为String
     * @param date LocalDate日期
     * @return 格式化的日期字符串
     */
    @Named("localDateToString")
    default String localDateToString(LocalDate date) {
        if (date == null) {
            return "";
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 将LocalDateTime转换为String
     * @param dateTime LocalDateTime日期时间
     * @return 格式化的日期时间字符串
     */
    @Named("localDateTimeToString")
    default String localDateTimeToString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
} 