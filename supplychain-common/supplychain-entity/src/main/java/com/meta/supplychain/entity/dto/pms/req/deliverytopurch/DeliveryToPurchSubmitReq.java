package com.meta.supplychain.entity.dto.pms.req.deliverytopurch;


import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname DeliveryToPurchSubmitReq
 * @Dscription TODO
 * @DATE 2025/6/4 10:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryToPurchSubmitReq {
    @Schema(description = "采购订单有效日期  yyyy-MM-dd")
    @NotNull(message = "采购订单有效日期不能为空")
    private LocalDate validDate;
    @Schema(description = "采购订单送货日期  yyyy-MM-dd")
    @NotNull(message = "采购订单送货日期不能为空")
    private LocalDate toStoreDate;
    @Schema(description = "采购订单备注")
    private String remark;
    @Schema(description = "部门编码")
    private String deptCode;
    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "订货属性编码")
    @NotBlank(message = "订货属性编码不能为空")
    private String orderAttributeCode;
    @Schema(description = "订货属性名称")
    @NotBlank(message = "订货属性名称不能为空")
    private String orderAttributeName;

    @Schema(description = "转采购商品")
    @NotNull(message = "转采购商品不能为空")
    private List<PmsDemandDeliveryToPurchReq> deliveryGoodsList;
}
