package com.meta.supplychain.entity.dto.wds.resp;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-4-7 10:10:49
 */
@Schema(description = "退货收货主表")
@Getter
@Setter
@ToString
public class QueryWdRefundAcceptBillResp {
    @Schema(description = "租户号")
    private Long tenantId;
    /** 单据号 */
    @Schema(description = "退货收货单号")
    private String billNo;

    /** 单据类型0配送 RefundAcceptBillTypeEnum */
    @Schema(description = "单据类型0配送 RefundAcceptBillTypeEnum")
    private Integer billType;

    /** 仓库编码 */
    @Schema(description = " 配送部门编码")
    private String whCode;

    /** 仓库名称 */
    @Schema(description = "配送部门名称")
    private String whName;

    /** 单据状态-1处理中 0草稿 1待收货 2已收货 9已作废 */
    @Schema(description = "单据状态-1处理中 0草稿 1待收货 2已收货 9已作废 RefundAcceptBillStatusEnum")
    private Integer status;


    /** 退货方部门编码 */
    @Schema(description = "退货方部门编码")
    private String inDeptCode;

    /** 退货方部门名称 */
    @Schema(description = "退货方部门名称")
    private String inDeptName;

    /** 合计退货数量 */
    @Schema(description = "合计退货数量")
    private BigDecimal totalAcceptQty;

    /** 合计退货金额 */
    @Schema(description = "合计退货金额")
    private BigDecimal totalAcceptTaxMoney;

    /** 合计退货税金 */
    @Schema(description = "合计退货税金")
    private BigDecimal totalAcceptTax;

    /** 收货时间 */
    @Schema(description = "收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitTime;

    /** 收货人编码 */
    @Schema(description = "收货人编码")
    private String submitManCode;

    /** 确认人名称 */
    @Schema(description = "收货人名称")
    private String submitManName;


    /** 作废时间 */
    @Schema(description = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    /** 作废人编码 */
    @Schema(description = "作废人编码")
    private String cancelManCode;

    /** 作废人名称 */
    @Schema(description = "作废人名称")
    private String cancelManName;

    /** 发货单号 */
    @Schema(description = "生成的逆向配送单号")
    private String shipBillNo;

    /** 生成的逆向配送验收单号 */
    @Schema(description = "生成的逆向配送验收单号")
    private String acceptBillNo;

    /** 关联的配送订单号 */
    @Schema(description = "关联的配送订单号")
    private String deliveryBillNo;

    @Schema(description = "单据备注 模糊匹配")
    private String remark;

    @Schema(description = "创建人ssoId")
    private String createUid;
    @Schema(description = "创建人编码")
    private String createCode;
    @Schema(description = "创建人名称")
    private String createName;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    /** 修改人工号 */
    @Schema(description = "修改人工号")
    private String updateCode;

    /** 修改人姓名 */
    @Schema(description = "修改人姓名")
    private String updateName;

    /** 修改时间 */
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime updateTime;




}
