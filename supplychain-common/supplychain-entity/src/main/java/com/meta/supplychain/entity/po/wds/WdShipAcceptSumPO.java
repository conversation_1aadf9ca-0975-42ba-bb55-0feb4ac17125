package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.*;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value ="wd_ship_accept_sum")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WdShipAcceptSumPO {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 配送验收单号 */
    private String billNo;

    /** 配送单据号 */
    private String shipBillNo;

    /** 合并验收单据号 */
    private String unionBillNo;

    /** 租户号 */
    @TableField(exist = false)
    private Long tenantId;

    @TableField(fill = FieldFill.INSERT)
    private String createCode;

    /** 创建人ssoId */
    @TableField(fill = FieldFill.INSERT)
    private Long createUid;

    /** 创建人姓名 */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /** 逻辑删除状态 1:删除 0:正常 */
    private Integer delFlag;
}