package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMappingDTO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyStatusMappingPO;
import com.meta.supplychain.enums.md.MdDeptTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 需求策略需求单转单状态映射规则转换器
 */
@Mapper
public interface MdDemandStrategyStatusMappingConvert {
    MdDemandStrategyStatusMappingConvert INSTANCE = Mappers.getMapper(MdDemandStrategyStatusMappingConvert.class);
    
    /**
     * PO 转 DTO
     */
    MdDemandStrategyStatusMappingDTO po2dto(MdDemandStrategyStatusMappingPO po);
    
    /**
     * DTO 转 PO
     */
    MdDemandStrategyStatusMappingPO dto2po(MdDemandStrategyStatusMappingDTO dto);

    /**
     * 组DTO转单个DTO
     */
    MdDemandStrategyStatusMappingDTO groupDto2singleDto(MdDemandStrategyStatusMapping4DeptDTO groupDTO);

    /**
     * 单个DTO转组DTO
     */
    MdDemandStrategyStatusMapping4DeptDTO singleDto2groupDto(MdDemandStrategyStatusMappingDTO groupDTO);
    
    /**
     * 部门状态映射DTO转单个状态映射DTO
     * @param statusMappingForDeptDTO 部门状态映射DTO
     * @return 单个状态映射DTO列表
     */
    default List<MdDemandStrategyStatusMappingDTO> convertDeptStatusMappingDto2single(MdDemandStrategyStatusMapping4DeptDTO statusMappingForDeptDTO) {
        ArrayList<MdDemandStrategyStatusMappingDTO> singleDtoList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(statusMappingForDeptDTO.getDeptCodeList())) {
            statusMappingForDeptDTO.getDeptCodeList().forEach(deptCode -> {
                if (!CollectionUtils.isEmpty(statusMappingForDeptDTO.getDemandBillTypeList())) {
                    statusMappingForDeptDTO.getDemandBillTypeList().forEach(demandBillType -> {
                        MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                        singleDto.setDeptCode(deptCode);
                        singleDto.setDeptType(MdDeptTypeEnum.DEPT.getCode());
                        singleDto.setDemandBillType(demandBillType);
                        singleDtoList.add(singleDto);
                    });
                }else {
                    MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                    singleDto.setDeptCode(deptCode);
                    singleDto.setDeptType(MdDeptTypeEnum.DEPT.getCode());
                    singleDtoList.add(singleDto);
                }
            });
        }

        if (!CollectionUtils.isEmpty(statusMappingForDeptDTO.getDeptGroupCodeList())) {
            statusMappingForDeptDTO.getDeptGroupCodeList().forEach(deptCode -> {
                if (!CollectionUtils.isEmpty(statusMappingForDeptDTO.getDemandBillTypeList())) {
                    statusMappingForDeptDTO.getDemandBillTypeList().forEach(demandBillType -> {
                        MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                        singleDto.setDeptCode(deptCode);
                        singleDto.setDeptType(MdDeptTypeEnum.DEPT_GROUP.getCode());
                        singleDto.setDemandBillType(demandBillType);
                        singleDtoList.add(singleDto);
                    });
                }else {
                    MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                    singleDto.setDeptCode(deptCode);
                    singleDto.setDeptType(MdDeptTypeEnum.DEPT_GROUP.getCode());
                    singleDtoList.add(singleDto);
                }
            });
        }

        // 未拆分到门店或店组群
        if (CollectionUtils.isEmpty(singleDtoList)) {
            if (!CollectionUtils.isEmpty(statusMappingForDeptDTO.getDemandBillTypeList())) {
                statusMappingForDeptDTO.getDemandBillTypeList().forEach(demandBillType -> {
                    MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                    singleDto.setDemandBillType(demandBillType);
                    singleDtoList.add(singleDto);
                });
            }else {
                MdDemandStrategyStatusMappingDTO singleDto = groupDto2singleDto(statusMappingForDeptDTO);
                singleDtoList.add(singleDto);
            }
        }

        return singleDtoList;
    }

    /**
     * 单个状态映射DTO转部门状态映射DTO
     * @param statusMappingDtoList 单个状态映射DTO列表
     * @return 部门状态映射DTO
     */
    default MdDemandStrategyStatusMapping4DeptDTO convertStatusMappingDto2DeptGroup(List<MdDemandStrategyStatusMappingDTO> statusMappingDtoList) {
        if (CollectionUtils.isEmpty(statusMappingDtoList)) {
            return null;
        }
        Set<String> deptCodeList = statusMappingDtoList.stream()
                .filter(singleDto -> StringUtils.isNotBlank(singleDto.getDeptCode())
                        && MdDeptTypeEnum.DEPT.verifyByCode(singleDto.getDeptType()))
                .map(MdDemandStrategyStatusMappingDTO::getDeptCode)
                .collect(Collectors.toSet());
        Set<String> deptGroupCodeList = statusMappingDtoList.stream()
                .filter(singleDto -> StringUtils.isNotBlank(singleDto.getDeptCode())
                        && MdDeptTypeEnum.DEPT_GROUP.verifyByCode(singleDto.getDeptType()))
                .map(MdDemandStrategyStatusMappingDTO::getDeptCode)
                .collect(Collectors.toSet());
        Set<String> billTypeList = statusMappingDtoList.stream()
                .map(MdDemandStrategyStatusMappingDTO::getDemandBillType)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        MdDemandStrategyStatusMapping4DeptDTO groupDto = singleDto2groupDto(statusMappingDtoList.get(0));
        groupDto.setDeptCodeList(new ArrayList<>(deptCodeList));
        groupDto.setDeptGroupCodeList(new ArrayList<>(deptGroupCodeList));
        groupDto.setDemandBillTypeList(new ArrayList<>(billTypeList));
        return groupDto;
    }
} 