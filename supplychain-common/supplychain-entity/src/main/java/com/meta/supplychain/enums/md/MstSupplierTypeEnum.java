package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商家类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "商家类型", code = "mstSupplierTypeEnum")
public enum MstSupplierTypeEnum implements VerifiableEnum<Integer> {
    SUPPLIER(1, "供应商"),
    POP_JOINT(2, "pop联营"),
    POP_SELF(3, "pop自营");

    private final Integer code;
    private final String desc;
}
