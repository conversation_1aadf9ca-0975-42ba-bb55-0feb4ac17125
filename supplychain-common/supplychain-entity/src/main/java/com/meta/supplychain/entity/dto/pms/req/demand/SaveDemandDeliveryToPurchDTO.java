package com.meta.supplychain.entity.dto.pms.req.demand;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveDemandDeliveryToPurchDTO {

    @Schema(description = "需求单号")
    private String billNo;

    @Schema(description = "全部可转采与转采购信息回填")
    private List<PmsDemandDeliveryToPurchReq> deliveryToPurchParamDTO;
}
