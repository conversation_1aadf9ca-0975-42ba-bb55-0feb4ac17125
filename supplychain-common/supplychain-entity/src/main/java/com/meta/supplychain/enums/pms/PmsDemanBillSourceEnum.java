package com.meta.supplychain.enums.pms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 需求单来源
 */
@Getter
@AllArgsConstructor
public enum PmsDemanBillSourceEnum {
    MUAL(0, "手工"),
    AUTO(1, "自动"),
    ZHUI_JIA_JIAN(2, "追加追减"),
    ;
    private Integer code;

    private String desc;


    public static PmsDemanBillSourceEnum getEnumByCode(Integer billSource) {
        PmsDemanBillSourceEnum[] values = values();
        for (PmsDemanBillSourceEnum value : values) {
            if(value.getCode().equals(billSource)) {
                return value;
            }
        }
        return null;
    }
}
