package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@TableName(value ="wd_refund_accept_bill")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WdRefundAcceptBillPO extends BaseEntity {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 仓库编码 */
    private String whCode;

    /** 仓库名称 */
    private String whName;

    /** 单据号 */
    private String billNo;

    /** 单据类型0配送 RefundAcceptBillTypeEnum */
    private Integer billType;

    /** 单据状态-1处理中 0草稿 1待收货 2已收货 9已作废  RefundAcceptBillStatusEnum */
    private Integer status;

    /** 退货方部门编码 */
    private String inDeptCode;

    /** 退货方部门名称 */
    private String inDeptName;

    /**
     * 门店经营模式 1 直营 2 加盟
     */
    private Integer deptOperateMode;

    /** 发货单号 */
    private String shipBillNo;

    /** 配送验收单号 */
    private String acceptBillNo;

    /** 关联的配送订单号 */
    private String deliveryBillNo;

    /** 合计退货数量 */
    private BigDecimal totalAcceptQty;

    /** 合计退货金额 */
    private BigDecimal totalAcceptTaxMoney;

    /** 合计退货税金 */
    private BigDecimal totalAcceptTax;

    /** 确认时间 */
    private LocalDateTime submitTime;

    /** 确认人编码 */
    private String submitManCode;

    /** 确认人名称 */
    private String submitManName;

    /** 单据备注 */
    private String remark;

    /** 作废时间 */
    private LocalDateTime cancelTime;

    /** 作废人编码 */
    private String cancelManCode;

    /** 作废人名称 */
    private String cancelManName;

    /** 打印次数 */
    private Integer printCount;
}
