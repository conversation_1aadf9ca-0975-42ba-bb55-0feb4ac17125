package com.meta.supplychain.entity.dto.pms.resp;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.DecimalScale;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsBookingCategoryEnum;
import com.meta.supplychain.enums.pms.PmsBookingStatusEnum;
import com.meta.supplychain.enums.pms.PmsCarrierMethodEnum;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 预约单详细信息DTO（三表关联结果）
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约单详细信息DTO")
public class PmsAppointmentDetailDTO {

    // =============== 预约单主表字段 ===============
    @Schema(description = "预约单据号")
    private String billNo;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @EnumValue(type = PmsBookingCategoryEnum.class)
    @Schema(description = "预约类别")
    private Integer billDirection;

    @EnumValue(type = YesOrNoEnum.class)
    @Schema(description = "是否直流")
    private Integer directSign;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "计划到达时间")
    private LocalDateTime planArrivalTime;

    @Schema(description = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @EnumValue(type = PmsCarrierMethodEnum.class)
    @Schema(description = "承运方式")
    private Integer transportMode;

    @Schema(description = "承运人")
    private String transportMan;

    @Schema(description = "承运联系手机")
    private String transportMobile;

    @Schema(description = "承运备注")
    private String transportRemark;

    @Schema(description = "预约备注")
    private String appointmentRemark;

    @Schema(description = "创建人姓名")
    private String createName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "提交人姓名")
    private String submitManName;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @EnumValue(type = PmsBookingStatusEnum.class)
    @Schema(description = "状态")
    private Integer status;

    // =============== 采购订单关联表字段 ===============
    @Schema(description = "采购订单号")
    private String purchBillNo;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "送货日期")
    private LocalDate deliverDate;

    @Schema(description = "有效日期")
    private LocalDate validityDate;

    @Schema(description = "来源")
    private Integer billSource;

    @Schema(description = "来源单号")
    private String refBillNo;

    @Schema(description = "来源单据备注")
    private String refRemark;

    @Schema(description = "采购订单备注")
    private String purchRemark;

    @LocalDatetimePattern("yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称")
    private String orderAttributeName;

    @Schema(description = "需求批次")
    private String purchBatchNo;

    @Schema(description = "退货原因")
    private String refundReason;

    // =============== 商品明细表字段 ===============
    @Schema(description = "商品类型")
    private Integer skuType;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "基本单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "规格")
    private String skuModel;

    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @DecimalScale(value = 4)
    @Schema(description = "整件数量")
    private BigDecimal wholeQty;

    @DecimalScale(value = 4)
    @Schema(description = "零头数量")
    private BigDecimal oddQty;

    @DecimalScale(value = 4)
    @Schema(description = "采购数量")
    private BigDecimal purchQty;

    @DecimalScale(value = 4)
    @Schema(description = "本次预约件数")
    private BigDecimal appointmentWholeQty;

    @DecimalScale(value = 4)
    @Schema(description = "本次预约零头数量")
    private BigDecimal appointmentOddQty;

    @DecimalScale(value = 4)
    @Schema(description = "本次预约数量")
    private BigDecimal appointmentQty;
} 