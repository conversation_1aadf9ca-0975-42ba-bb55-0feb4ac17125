package com.meta.supplychain.entity.dto.stock.req;

import com.meta.supplychain.entity.dto.stock.LocStockDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商品储位库存移库
 */
@Getter
@Setter
@Builder
public class LocStockMoveReq {

    private String sourceAppCode;

    /**
     * 门店编码
     */
    private String deptCode;

    /**
     * 单据号
     */
    private String billNo;

    /**
     * 单据类型
     */
    //private String billType;

    /**
     * 操作编码
     */
    //private String operateCode;


    /**
     * 商品列表
     */
    private List<LocStockDTO> skuList;

}
