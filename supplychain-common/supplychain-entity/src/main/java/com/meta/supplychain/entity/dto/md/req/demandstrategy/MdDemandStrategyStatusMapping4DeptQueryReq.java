package com.meta.supplychain.entity.dto.md.req.demandstrategy;

import com.meta.supplychain.enums.md.MdBillDirectionEnum;
import com.meta.supplychain.enums.md.MdDemandStrategyBizConditionEnum;
import com.meta.supplychain.enums.md.MdDemandStrategyStatusConditionEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 需求策略需求单转单状态映射规则DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "需求策略需求单转单状态映射规则")
public class MdDemandStrategyStatusMapping4DeptQueryReq {

    /** 场景编码(自动/手动生成需求单转订单状态) */
    @Schema(description = "场景编码 枚举mdDemandStrategyStatusConditionEnum")
    @EnumValue(type = MdDemandStrategyStatusConditionEnum.class)
    private List<String> demandTransferConditionList;

    /** 场景业务编码-配送采购订货/门店采购订货 */
    @Schema(description = "场景业务编码 枚举mdDemandStrategyBizConditionEnum")
    @EnumValue(type = MdDemandStrategyBizConditionEnum.class)
    private List<String> demandTransferConditionBizList;

    /** 场景业务编码值-已转单、待转单 */
    @Schema(description = "场景业务编码值")
    private List<String> valList;

    /** 单据类型编码-订货、退货 */
    @Schema(description = "单据类型 枚举mdDemandStrategyBizConditionEnum")
    @EnumValue(type = MdBillDirectionEnum.class)
    private List<String> demandBillTypeList;

    /** 部门编码(多个存多条) */
    @Schema(description = "部门编码列表")
    @Builder.Default
    private List<String> deptCodeList = new ArrayList<>();

    @Builder.Default
    @Schema(description = "店组群编码列表")
    private List<String> deptGroupCodeList = new ArrayList<>();
} 