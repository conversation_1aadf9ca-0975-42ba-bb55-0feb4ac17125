package com.meta.supplychain.entity.dto.replenishment.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 转码单商品明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranscodingBillDetail {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户号
     */
    private String tenantId;

    /**
     * 单据号
     */
    private String billNumber;

    /**
     * 单内序号
     */
    private String insideId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 原料商品编码
     */
    private String rmGoodsCode;

    /**
     * 原料商品名称
     */
    private String rmGoodsName;

    /**
     * 原料商品条码
     */
    private String rmBarcode;

    /**
     * 原料商品货号
     */
    private String rmGoodsNo;

    /**
     * 原料规格型号
     */
    private String rmGoodsSpec;

    /**
     * 原料单位
     */
    private String rmBasicUnit;

    /**
     * 原料销售模式（1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码）
     */
    private Integer rmSaleMod;

    /**
     * 原料税率
     */
    private Long rmTaxRate;

    /**
     * 原料是否效期品（0-否 1-是）
     */
    private Integer rmIsExpiration;

    /**
     * 原料是否称重品（0-否，1是）
     */
    private Integer rmIsWeight;

    /**
     * 原料库存数量
     */
    private Long rmStockNum;

    /**
     * 原料数量
     */
    private Long rmAmount;

    /**
     * 原料成本单价
     */
    private Long rmCostPrice;

    /**
     * 原料成本金额（含税）
     */
    private Long rmCostMoney;

    /**
     * 原料成本税金
     */
    private Long rmCostTax;

    /**
     * 原料零售单价，单位毫
     */
    private Long rmSalePrice;

    /**
     * 原料零售金额，单位毫
     */
    private Long rmSaleMoney;

    /**
     * 成品商品编码
     */
    private String fpGoodsCode;

    /**
     * 成品商品名称
     */
    private String fpGoodsName;

    /**
     * 成品商品条码
     */
    private String fpBarcode;

    /**
     * 成品商品货号
     */
    private String fpGoodsNo;

    /**
     * 成品规格型号
     */
    private String fpGoodsSpec;

    /**
     * 成品单位
     */
    private String fpBasicUnit;

    /**
     * 成品销售模式（1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码）
     */
    private Integer fpSaleMod;

    /**
     * 成品税率
     */
    private Long fpTaxRate;

    /**
     * 成品是否效期品（0-否 1-是）
     */
    private Integer fpIsExpiration;

    /**
     * 成品是否称重品（0-否，1是）
     */
    private Integer fpIsWeight;

    /**
     * 成品库存数量
     */
    private Long fpStockNum;

    /**
     * 成品数量
     */
    private Long fpAmount;

    /**
     * 成品成本单价
     */
    private Long fpCostPrice;

    /**
     * 成品成本金额（含税）
     */
    private Long fpCostMoney;

    /**
     * 成品成本税金
     */
    private Long fpCostTax;

    /**
     * 成品零售单价，单位毫
     */
    private Long fpSalePrice;

    /**
     * 成品零售金额，单位毫
     */
    private Long fpSaleMoney;

    /**
     * 比例系数
     */
    private Long scaleFactor;

    /**
     * 制单人编码
     */
    private String buildManCode;

    /**
     * 制单人名称
     */
    private String buildManName;

    /**
     * 最后修改人编码
     */
    private String modifyManCode;

    /**
     * 最后修改人名称
     */
    private String modifyManName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单日期 YYYYMMDD
     */
    private String accDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 原料批次接口返回信息
     */
    private String rmBatchInfo;

    /**
     * 成品批次接口返回信息
     */
    private String fpBatchInfo;

    /**
     * 原料关联效期信息
     */
    private List<ValidityPeriod> rmExpiration;

    /**
     * 成品关联效期信息
     */
    private List<ValidityPeriod> fpExpiration;
}
