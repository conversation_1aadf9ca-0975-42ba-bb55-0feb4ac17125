package com.meta.supplychain.entity.dto.goods.req;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class GoodsSearchQueryReq extends PageParams {

    @Schema(description = "门店编码")
    private String storeCode;

    @Schema(description = "产品编码")
    private List<String> spuCodes;

    @Schema(description = "商品编码")
    private List<String> skuCodes;

    @Schema(description = "品牌编码")
    private List<String> brandsCodes;

    @Schema(description = "品类编码")
    private List<String> categoryCodes;

    @Schema(description = "来源（APPCODE）")
    private String source;
}
