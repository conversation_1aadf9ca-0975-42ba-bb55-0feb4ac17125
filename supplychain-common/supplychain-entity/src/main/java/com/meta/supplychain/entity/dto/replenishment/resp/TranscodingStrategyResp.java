package com.meta.supplychain.entity.dto.replenishment.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 转码策略单出参
 */
@Data
public class TranscodingStrategyResp {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户号
     */
    private String tenantId;

    /**
     * 单内序号
     */
    private String insideId;

    /**
     * 状态（0-启用 1-停用）
     */
    private Integer state;

    /**
     * 原料商品编码
     */
    private String rmGoodsCode;

    /**
     * 原料商品名称
     */
    private String rmGoodsName;

    /**
     * 原料商品条码
     */
    private String rmBarcode;

    /**
     * 原料商品货号
     */
    private String rmGoodsNo;

    /**
     * 原料规格型号
     */
    private String rmGoodsSpec;

    /**
     * 原料单位
     */
    private String rmBasicUnit;

    /**
     * 原料税率
     */
    private Long rmTaxRate;

    /**
     * 原料零售单价，单位毫
     */
    private Long rmSalePrice;

    /**
     * 原料是否效期品（0-否 1-是）
     */
    private Integer rmIsExpiration;

    /**
     * 原料是否称重品（0-否，1是）
     */
    private Integer rmIsWeight;

    /**
     * 成品商品编码
     */
    private String fpGoodsCode;

    /**
     * 成品商品名称
     */
    private String fpGoodsName;

    /**
     * 成品商品条码
     */
    private String fpBarcode;

    /**
     * 成品商品货号
     */
    private String fpGoodsNo;

    /**
     * 成品规格型号
     */
    private String fpGoodsSpec;

    /**
     * 成品单位
     */
    private String fpBasicUnit;

    /**
     * 成品税率
     */
    private Long fpTaxRate;

    /**
     * 成品零售单价，单位毫
     */
    private Long fpSalePrice;

    /**
     * 成品是否效期品（0-否 1-是）
     */
    private Integer fpIsExpiration;

    /**
     * 成品是否称重品（0-否，1是）
     */
    private Integer fpIsWeight;

    /**
     * 比例系数
     */
    private Long scaleFactor;

    /**
     * 制单人编码
     */
    private String buildManCode;

    /**
     * 制单人名称
     */
    private String buildManName;

    /**
     * 最后修改人编码
     */
    private String modifyManCode;

    /**
     * 最后修改人名称
     */
    private String modifyManName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
