package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 追加追减类型枚举
 */
@Getter
@AllArgsConstructor
public enum AddReduceTypeEnum {

    DIRECT_PURCHASE("direct_purchase", "直流采购"),
    DIRECT_TRANSFER("direct_transfer", "直流配送"),
    STORE_TRANSFER("store_transfer", "门店配送"),
    TRANSFER_TO_PURCHASE("transfer_to_purchase", "配转采"),
    ;
    private final String code;

    private final String desc;

}
