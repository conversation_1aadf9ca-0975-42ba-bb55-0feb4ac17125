package com.meta.supplychain.entity.dto.pms.resp.demand;


import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeptGoodsDetailReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandGoodsDetailResp {
    /**
     * 单内序号
     */
    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "需求单号")
    private Long billNo;
    @Schema(description = "响应整件数量")
    private BigDecimal responseWholeQty;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "全路径品类编码,英文逗号分割")
    private String categoryCodeAll;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMode;

    @Schema(description = "计量属性 0：普通 1：计量 2：称重")
    private Integer uomAttr;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "需求金额税金")
    private BigDecimal demandTax;
    @Schema(description = "响应金额税金")
    private BigDecimal responseTax;
    @Schema(description = "商品条码")
    private String barcode;
    @Schema(description = "需求金额")
    private BigDecimal demandMoney;
    @Schema(description = "需求数量")
    private BigDecimal demandQty;
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "销项税率，13%存13")
    private BigDecimal outputTaxRate;
    @Schema(description = "整件单位")
    private String packageUnit;
    @Schema(description = "商品货号")
    private String goodsNo;
    @Schema(description = "响应数量")
    private BigDecimal responseQty;
    @Schema(description = "响应金额")
    private BigDecimal responseMoney;
    @Schema(description = "商品名称")
    private String skuName;
    @Schema(description = "需求整件数量")
    private BigDecimal demandWholeQty;

    @Schema(description = "进项税率，13%存13")
    private BigDecimal inputTaxRate;
    @Schema(description = "响应零头数量")
    private BigDecimal responseOddQty;
    @Schema(description = "商品编码")
    private String skuCode;
    @Schema(description = "商品类型,0主品,1附赠赠品")
    private Integer goodsType;

    @Schema(description = "需求零头数量")
    private BigDecimal demandOddQty;
    @Schema(description = "商品规格")
    private String skuModel;

    @Schema(description = "部门商品明细")
    private List<PmsDemandDeptGoodsDetailResp> demandDeptGoodsDetailList;
}
