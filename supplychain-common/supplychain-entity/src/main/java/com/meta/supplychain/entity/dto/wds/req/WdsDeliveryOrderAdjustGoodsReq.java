package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WdsDeliveryOrderAdjustGoodsReq {

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "调整类型 0-追加 1-追减")
    @NotNull(message = "调整类型不能为空")
    private Integer adjustType = 0;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "调整数量")
    private BigDecimal adjustQty;

    @Schema(description = "配送价格-调整后")
    private BigDecimal deliveryPrice;
}
