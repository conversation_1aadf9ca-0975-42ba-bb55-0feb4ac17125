package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/9/11 16:54
 */
@Builder
@Setter
@Getter
@Data
public class ComplexSupplierInfo {
    private String taxNumber;
    private String suppTaxRate;
    private String suppCateName;
    private String suppCateCode;
    private Long status;
    private Long settleMode;
    private String provinceName;
    private String provinceCode;
    private String phone;
    private Long paymentControl;
    private String outCode;
    private String operateMode;
    private Long operateControl;
    private String name;
    private String linkMan;
    private Long invoiceType;
    private Long id;
    private String ibpsCode;
    private String districtName;
    private String districtCode;
    private String createTime;
    private String createName;
    private String createCode;
    /**
     * 供应商编码
     */
    private String code;
    private String cityName;
    private String cityCode;
    private String bankName;
    private String bankCode;
    private String bankAccountName;
    private String bankAccount;
    private String address;
    /**
     * 核算主体信息
     */
    private List<AccountBodyList> accountBodyList;

    @Builder
    @Setter
    @Getter
    @Data
    public static class AccountBodyList {
        /**
         * 店组群编码
         */
        private List<String> groupCodeList;
        /**
         * 过账配送中心名称
         */
        private String deptName;
        /**
         * 店组群分类项编码
         */
        private String deptGroupClassCode;
        /**
         * 过账配送中心编码
         */
        private String deptCode;
        /**
         * 核算单位名称
         */
        private String accountName;
        /**
         * 核算单位编码
         */
        private String accountCode;
    }
}
