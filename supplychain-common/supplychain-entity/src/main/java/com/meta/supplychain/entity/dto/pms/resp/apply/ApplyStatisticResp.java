package com.meta.supplychain.entity.dto.pms.resp.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 多个状态 门店调拨单数量
 *
 * <AUTHOR> cat
 * @date 2024/4/2 16:25
 */
@Getter
@Setter
@Schema(description = "订货申请单状态统计响应")
public class ApplyStatisticResp {

    @Schema(description = "单据状态")
    private Integer status;

    @Schema(description = "数量")
    private Long num;

}
