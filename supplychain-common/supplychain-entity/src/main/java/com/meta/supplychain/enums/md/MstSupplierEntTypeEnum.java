package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商企业性质枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商企业性质", code = "mstSupplierEntTypeEnum")
public enum MstSupplierEntTypeEnum implements VerifiableEnum<Integer> {
    AGENT(1, "代理商"),
    MANUFACTURER(2, "生产商"),
    WHOLESALER(3, "批发商"),
    OTHER(4, "其他");

    private final Integer code;
    private final String desc;
}
