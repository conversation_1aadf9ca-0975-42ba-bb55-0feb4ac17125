package com.meta.supplychain.entity.dto.pms.resp.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购订单关联信息
 */
@Schema(description = "采购订单关联信息")
@Data
public class PmsPruchDetailRefResp {
    @Schema(description = "关联主键序号")
    private Long id;

    @Schema(description = "采购订单号")
    private String billNo;

    @Schema(description = "采购订单单内序号")
    private Long insideId;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "关联单对应商品采购数量")
    private BigDecimal purchQty;

    @Schema(description = "关联来源：0-手工单，1-需求单，2-配转采")
    private Integer billSource;

    @Schema(description = "关联单号（采购计划单号/需求单号/配送单号）")
    private String srcBillNo;

    @Schema(description = "关联单单内序号")
    private Long srcInsideId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间")
    private LocalDateTime updateTime;
}
