package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预约单详细信息导出视图
 * <AUTHOR>
 */
@Data
public class PmsAppointmentDetailView {

    // =============== 预约单主表字段 ===============
    @ViewField(headerName = "预约单号")
    private String billNo;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    @ViewField(headerName = "预约类别")
    private String billDirection;

    @ViewField(headerName = "是否直流")
    private String directSign;

    @ViewField(headerName = "计划到达时间")
    private String planArrivalTime;

    @ViewField(headerName = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @ViewField(headerName = "停靠点编码")
    private String dockCode;

    @ViewField(headerName = "承运方式")
    private String transportMode;

    @ViewField(headerName = "承运人")
    private String transportMan;

    @ViewField(headerName = "承运联系手机")
    private String transportMobile;

    @ViewField(headerName = "承运备注")
    private String transportRemark;

    @ViewField(headerName = "预约备注")
    private String appointmentRemark;

    @ViewField(headerName = "创建人")
    private String createName;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "提交人")
    private String submitManName;

    @ViewField(headerName = "提交时间")
    private String submitTime;

    @ViewField(headerName = "状态")
    private String status;

    // =============== 采购订单关联表字段 ===============
    @ViewField(headerName = "采购订单号")
    private String purchBillNo;

    @ViewField(headerName = "合同号")
    private String contractNo;

    @ViewField(headerName = "送货日期")
    private String deliverDate;

    @ViewField(headerName = "有效日期")
    private String validityDate;

    @ViewField(headerName = "来源")
    private String billSource;

    @ViewField(headerName = "来源单号")
    private String refBillNo;

    @ViewField(headerName = "来源单据备注")
    private String refRemark;

    @ViewField(headerName = "采购订单备注")
    private String purchRemark;

    @ViewField(headerName = "审核时间")
    private String auditTime;

    @ViewField(headerName = "审核备注")
    private String auditRemark;

    @ViewField(headerName = "订货属性编码")
    private String orderAttributeCode;

    @ViewField(headerName = "订货属性名称")
    private String orderAttributeName;

    @ViewField(headerName = "需求批次")
    private String purchBatchNo;

    @ViewField(headerName = "退货原因")
    private String refundReason;

    // =============== 商品明细表字段 ===============
    @ViewField(headerName = "商品类型")
    private String skuType;

    @ViewField(headerName = "商品编码")
    private String skuCode;

    @ViewField(headerName = "商品名称")
    private String skuName;

    @ViewField(headerName = "商品条码")
    private String barcode;

    @ViewField(headerName = "商品货号")
    private String goodsNo;

    @ViewField(headerName = "品类编码")
    private String categoryCode;

    @ViewField(headerName = "品类名称")
    private String categoryName;

    @ViewField(headerName = "基本单位")
    private String basicUnit;

    @ViewField(headerName = "整件单位")
    private String packageUnit;

    @ViewField(headerName = "规格")
    private String skuModel;

    @ViewField(headerName = "进项税率")
    private BigDecimal inputTaxRate;

    @ViewField(headerName = "商品包装率")
    private BigDecimal unitRate;

    @ViewField(headerName = "整件数量")
    private BigDecimal wholeQty;

    @ViewField(headerName = "零头数量")
    private BigDecimal oddQty;

    @ViewField(headerName = "采购数量")
    private BigDecimal purchQty;

    @ViewField(headerName = "本次预约件数")
    private BigDecimal appointmentWholeQty;

    @ViewField(headerName = "本次预约零头数量")
    private BigDecimal appointmentOddQty;

    @ViewField(headerName = "本次预约数量")
    private BigDecimal appointmentQty;
} 