package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批次效期表
 * @TableName pms_bill_batch_info
 */
@TableName(value ="pms_bill_batch_info")
@Data
public class PmsBillBatchInfoPO implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 商品批次号
     */
    private String batchNo;

    /**
     * 关联合单据类型（0-加工单 1-拆分单 2-采购验收 3-门店调拨 4-配送验收 5-转码单 6-配送申请订单）
     */
    private Integer billType;

    /**
     * 订单号
     */
    private String billNo;

    /**
     * 效期行号
     */
    private Long insideId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 进项税率
     */
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    private BigDecimal outputTaxRate;

    /**
     * 商品批号
     */
    private String skuBatchNo;

    /**
     * 数量/批次数量
     */
    private BigDecimal batchQty;

    /**
     * 批次成本单价
     */
    private BigDecimal batchCostPrice;

    /**
     * 库存数量
     */
    private BigDecimal stockQty;

    /**
     * 调拨金额/订货金额/成本金额（含税）
     */
    private BigDecimal purchTaxMoney;

    /**
     * 调拨税金/订货税金/成本税金
     */
    private BigDecimal purchTax;

    /**
     * 参考金额/批次成本金额
     */
    private BigDecimal referMoney;

    /**
     * 整件数量
     */
    private BigDecimal wholeQty;

    /**
     * 零头数量
     */
    private BigDecimal oddQty;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品行单内序号
     */
    private Long skuInsideId;

    /**
     * 效期批号
     */
    private String periodBatchNo;

    /**
     * 效期条码
     */
    private String periodBarcode;

    /**
     * 生产日期
     */
    private LocalDateTime productDate;

    /**
     * 过期日期
     */
    private LocalDateTime expireDate;

    /**
     * 批次税金
     */
    private BigDecimal batchTax;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 建档时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}