package com.meta.supplychain.entity.dto.stock;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StkTaskItemExecuteDto {

    @Schema(title = "门店编码",description = "门店编码")
    private String deptCode;
    @Schema(title = "门店名称",description = "门店名称")
    private String deptName;
    @Schema(title = "仓库编码",description = "仓库编码")
    private String whCode;
    @Schema(title = "仓库名称",description = "仓库名称")
    private String whName;
    @Schema(title = "仓库类型",description = "仓库类型")
    private Integer whType;
    /**
     * 储位编码
     */
    @Schema(title = "储位编码",description = "储位编码")
    private String locationCode;
    /**
     * 储位编码
     */
    @Schema(title = "储位名称",description = "储位名称")
    private String locationName;
    @Schema(title = "储位类型",description = "储位类型")
    private String locationType;
    /**
     * 单内序号
     */
    @Schema(title = "单内序号",description = "单内序号")
    private Long insideId;
    /**
     * 单据类型 po do
     */
    @Schema(title = "单据类型",description = "单据类型")
    private String billType;
    /**
     * 操作类型
     */
    @Schema(title = "操作类型",description = "操作类型")
    private String operateCode;
    /**
     * 业务出入库类型 I 入库 O 出库 R 预留 T 在途
     */
    @Schema(title = "出入库类型",description = "业务出入库类型 I 入库 O 出库 R 预留 T 在途")
    private String ioType;
    /**
     * 核算单位编码
     */
    @Schema(title = "核算单位编码",description = "核算单位编码")
    private String accountCode;

    /**
     * 核算单位名称
     */
    @Schema(title = "核算单位名称",description = "核算单位名称")
    private String accountName;

    /**
     * 供应商编码
     */
    @Schema(title = "供应商编码",description = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(title = "供应商名称",description = "供应商名称")
    private String supplierName;

    /**
     * 合同号
     */
    @Schema(title = "合同号",description = "合同号")
    private String contractNo;

    /**
     * 原单效期行号
     */
    @Schema(title = "效期行号",description = "原单效期行号")
    private Integer periodInsideId;

    /**
     * 原单效期行号
     */
    @Schema(title = "原单效期行号",description = "原单效期行号")
    private Integer srcPeriodInsideId;
    /**
     * 发货效期行号
     */
    @Schema(title = "配送单/配送验收单商品效期行号",description = "配送单/配送验收单商品效期行号")
    private Integer deliveryPeriodInsideId;

    /**
     * 商品编码
     */
    @Schema(title = "商品编码",description = "商品编码")
    private String skuCode;

    /**
     * 商品编码
     */
    @Schema(title = "商品名称",description = "商品名称")
    private String skuName;

    /**
     * 商品类型 0普通商品 1赠品
     */
    @Schema(title = "商品类型",description = "商品类型 0普通商品 1赠品")
    private Integer skuType;


    /**
     * 批次号
     */
    @Schema(title = "批次号",description = "批次号")
    private String batchNo;

    /**
     * 效期商品条码
     */
    @Schema(title = "效期商品条码",description = "效期商品条码")
    private String periodBarcode;

    /**
     * 效期批号
     */
    @Schema(title = "效期批号",description = "效期批号")
    private String periodBatchNo;

    /**
     * 生产日期
     */
    @Schema(title = "生产日期",description = "生产日期")
    private LocalDate productDate;

    /**
     * 过期日期
     */
    @Schema(title = "过期日期",description = "过期日期")
    private LocalDate expiryDate;

    /**
     * 商品spu编码
     */
    @Schema(title = "商品SPU编码",description = "商品SPU编码")
    private String spuCode;

    /**
     * 条码;SKU基本条码
     */
    @Schema(title = "条码",description = "条码")
    private String barcode;

    /**
     * 商品规格
     */
    @Schema(title = "商品规格",description = "商品规格")
    private String skuModel;
    /**
     * 特殊条码类型
     */
    @Schema(title = "特殊条码类型",description = "特殊条码类型")
    private Integer specType;
    /**
     * 特殊条码，仅特殊条码商品有该数据
     */
    @Schema(title = "特殊条码",description = "特殊条码，仅特殊条码商品有该数据")
    private String specBarcode;

    /**
     * 销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出
     */
    @Schema(title = "销售模式",description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出")
    private Integer saleMode;

    /**
     * 计量属性;0普通|2称重
     */
    @Schema(title = "计量属性",description = "计量属性;0普通|2称重")
    private Integer uomAttr;

    /**
     * 是否效期 0否1是
     */
    @Schema(title = "是否效期",description = "是否效期 0否1是")
    private Integer periodFlag;

    /**
     * 进项税率
     */
    @Schema(title = "进项税率",description = "进项税率")
    private BigDecimal inTaxRate;

    /**
     * 销项税率
     */
    @Schema(title = "销项税率",description = "销项税率")
    private BigDecimal outTaxRate;

    /**
     * 发生数量
     */
    @Schema(title = "发生数量",description = "发生数量")
    private BigDecimal realQty;

    /**
     * 残损数量
     */
    @Schema(title = "残损数量",description = "残损数量")
    private BigDecimal damQty;

    /**
     * 含税销售单价
     */
    @Schema(title = "含税销售单价",description = "含税销售单价")
    private BigDecimal salePrice;

    /**
     * 不含税销售金额
     */
    @Schema(title = "不含税销售金额",description = "不含税销售金额")
    private BigDecimal saleMoney;

    /**
     * 含税销售金额
     */
    @Schema(title = "含税销售金额",description = "含税销售金额")
    private BigDecimal saleTaxMoney;

    /**
     * 销售税金
     */
    @Schema(title = "销售税金",description = "销售税金")
    private BigDecimal saleTax;

    /**
     * 折扣金额
     */
    @Schema(title = "折扣金额",description = "折扣金额")
    private BigDecimal discountMoney;

    /**
     * 不含税成本
     */
    @Schema(title = "不含税成本",description = "不含税成本")
    private BigDecimal costMoney;

    /**
     * 含税成本
     */
    @Schema(title = "含税成本",description = "含税成本")
    private BigDecimal costTaxMoney;

    /**
     * 成本税金
     */
    @Schema(title = "成本税金",description = "成本税金")
    private BigDecimal costTax;

    /**
     * 不含税成本单价
     */
    @Schema(title = "不含税成本单价",description = "不含税成本单价")
    private BigDecimal costPrice;

    /**
     * 含税成本单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal costTaxPrice;

    /**
     * 不含税成本
     */
    @Schema(title = "不含税成本",description = "不含税成本")
    private BigDecimal batchCostMoney;

    /**
     * 含税成本
     */
    @Schema(title = "含税成本",description = "含税成本")
    private BigDecimal batchCostTaxMoney;

    /**
     * 成本税金
     */
    @Schema(title = "成本税金",description = "成本税金")
    private BigDecimal batchCostTax;

    /**
     * 不含税成本单价
     */
    @Schema(title = "不含税成本单价",description = "不含税成本单价")
    private BigDecimal batchCostPrice;

    /**
     * 含税成本单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal batchCostTaxPrice;

    /**
     * 不含税成本
     */
    @Schema(title = "不含税成本",description = "不含税成本")
    private BigDecimal outCostMoney;

    /**
     * 含税成本
     */
    @Schema(title = "含税成本",description = "含税成本")
    private BigDecimal outCostTaxMoney;

    /**
     * 成本税金
     */
    @Schema(title = "成本税金",description = "成本税金")
    private BigDecimal outCostTax;

    /**
     * 不含税成本单价
     */
    @Schema(title = "不含税成本单价",description = "不含税成本单价")
    private BigDecimal outCostPrice;

    /**
     * 含税成本单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal outCostTaxPrice;

    /**
     * 关联单号 父单号、验收、销售、拨出等单号
     */
    @Schema(title = "原单编码",description = "原单编码")
    private String srcBillNo;

    /**
     * 关联单号类型
     */
    @Schema(title = "原单类型",description = "原单类型")
    private String srcBillType;
    /**
     * 关联单号类型
     */
    @Schema(title = "原单行号",description = "原单行号")
    private Integer srcInsideId;

    /**
     * 关联单号 父单号、验收、销售、拨出等单号
     */
    @Schema(title = "关联单号",description = "关联单号")
    private String refBillNo;

    /**
     * 关联单号类型
     */
    @Schema(title = "关联单号",description = "关联单号")
    private String refBillType;

    //配送验收单入账日期
    private LocalDate refBillAccDate;

    /**
     * 释放单号
     */
    @Schema(title = "释放单号",description = "释放单号")
    private String releaseBillNo;

    /**
     * 释放单据类型
     */
    @Schema(title = "释放单据类型",description = "释放单据类型")
    private String releaseBillType;

    /**
     * 发货单据号
     */
    @Schema(title = "配送单号",description = "配送单/配送验收单")
    private String deliveryBillNo;

    /**
     * 发货单内行号
     */
    @Schema(title = "配送单行内号",description = "配送单/配送验收单行内号")
    private Integer deliveryInsideId;

    /**
     * 目标部门编码
     */
    @Schema(title = "目标部门编码",description = "目标部门编码")
    private String targetDeptCode;

    /**
     * 目标部门名称
     */
    @Schema(title = "目标部门名称",description = "目标部门名称")
    private String targetDeptName;

    /**
     * 目标仓库编码
     */
    @Schema(title = "目标仓库编码",description = "目标仓库编码")
    private String targetWhCode;

    /**
     * 目标仓库名称
     */
    @Schema(title = "目标仓库名称",description = "目标仓库名称")
    private String targetWhName;

    @Schema(description = "出货方仓库编码")
    private String outWhCode;

    /**
     * 目标仓库类型
     */
    @Schema(title = "目标仓库类型",description = "目标仓库类型")
    private Integer targetWhType;

    /**
     * 调后不含税单价（注意：值*10000）
     */
    @Schema(title = "调整后不含税单价",description = "调整后不含税单价")
    private BigDecimal adjustCostPrice;
    /**
     * 调后含税单价（注意：值*10000）
     */
    @Schema(title = "调整后含税单价",description = "调整后含税单价")
    private BigDecimal adjustCostTaxPrice;

    /**
     * 调后不含税单价（注意：值*10000）
     */
    @Schema(title = "调整前不含税单价",description = "调整前不含税单价")
    private BigDecimal beforeCostPrice;
    /**
     * 调后含税单价（注意：值*10000）
     */
    @Schema(title = "调整前含税单价",description = "调整前含税单价")
    private BigDecimal beforeCostTaxPrice;

    /**
     * 库价调整-调整库存成本（手工录入比传）
     * 调前库存数量，更新时根据调前库存数量做乐观锁
     */
    private BigDecimal adjustReferenceQuantity;

    /**
     * 库价调整-调整库存成本（手工录入比传）
     * 调前库存单价，更新时根据调前单价做乐观锁
     *
     */
    private BigDecimal adjustReferenceUnitPrice;
    /**
     * 手工改价标识 0否1是
     */
    @Schema(title = "手工改价标识",description = "手工改价标识 0否1是")
    private Integer changePriceFlag;

    @Schema(title = "加价率",description = "加价率（单位例子: 20% 传 0.2）")
    private BigDecimal markupRate;
    /**
     * 是否负库存 0 否 1 是
     */
    @Schema(title = "负库存标识",description = "是否负库存 0 否 1 是")
    private Integer negativeAllowedFlag;

//    @Schema(title = "商品效期列表",description = "商品效期列表")
//    private List<StkTaskItemPeriodExecuteDto> periodList;

}
