package com.meta.supplychain.entity.dto.pms.req.account;

import com.meta.supplychain.entity.dto.OpInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "代配过账单新增请求信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountBillCreateReq {
    private OpInfo operatorInfo = new OpInfo();

    @Schema(description = "序号")
    private Long id;

    /**
     * 过账单号
     */
    private String billNo;

    @Schema(description = "来源单号")
    @NotBlank(message = "来源单号不能为空")
    private String srcBillNo;

    @Schema(description = "配送中心编码")
    @NotBlank(message = "配送中心编码不能为空")
    private String dcCode;

    @Schema(description = "配送中心名称")
    @NotBlank(message = "配送中心名称不能为空")
    private String dcName;

    @Schema(description = "配送中心核算单位")
    @NotBlank(message = "配送中心核算单位不能为空")
    private String dcAccCode;

    @Schema(description = "代配部门编码")
    @NotBlank(message = "代配部门编码不能为空")
    private String accDeptCode;

    @Schema(description = "代配部门名称")
    @NotBlank(message = "代配部门名称不能为空")
    private String accDeptName;

    @Schema(description = "过账类型 0-代配验收，1-代配调拨，2-代配退补")
    @NotNull(message = "过账类型不能为空")
    private Integer billType;

    @Schema(description = "单据方向 0-采购验收 1-采购验收冲红 2-采购退货 3-采购退货冲红 4-门店调拨，5-门店调拨冲红，6-库存退补，7-销售退补，8-销售退补冲红")
    @NotNull(message = "单据方向不能为空")
    private Integer billDirection;

    @Schema(description = "调拨类型 0-拨入部门和过账配送中心核算单位不一致 1-拨出部门和过账配送中心核算单位不一致 2-拨出拨入部门都和过账配送中心核算单位不一致")
    private Integer redeployType;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "补签序号")
    private String repairSignId;

    @Schema(description = "拨入部门编码")
    private String inDeptCode;

    @Schema(description = "拨入部门名称")
    private String inDeptName;

    @Schema(description = "拨入部门核算单位")
    private String inAccCode;

    @Schema(description = "拨出部门编码")
    private String outDeptCode;

    @Schema(description = "拨出部门名称")
    private String outDeptName;

    @Schema(description = "拨出部门核算单位")
    private String outAccCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人工号")
    private String createCode;

    @Schema(description = "创建人名称")
    private String createName;

    /**是否http请求*/
    private Boolean isInternal = false;

    @NotEmpty(message = "商品信息不能为空")
    @Schema(description = "商品信息列表")
    @Valid
    List<AccountBillDetailCreateReq> detailList;
}
