package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 移位单明细表
 * @TableName wd_move_loc_batch_detail
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="wd_move_loc_batch_detail")
@Data
public class MoveLocBatchDetailPO extends BaseEntity implements java.io.Serializable{
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * 仓库名称
     */
    private String whName;

    /**
     * 单据号
     */
    private String billNo;

    /**
     * 单内序号
     */
    private Long insideId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 条码;SKU基本条码
     */
    private String barcode;

    /**
     * 商品货号
     */
    private String goodsNo;

    /**
     * 计量单位;商品计量单位
     */
    private BigDecimal unit;

    /**
     * 计量属性;0普通|2称重
     */
    private Integer uomAttr;

    /**
     * 商品规格
     */
    private String skuModel;

    /**
     * 单位
     */
    private String basicUnit;

    /**
     * 整件单位
     */
    private String packageUnit;

    /**
     * 单位包装率
     */
    private BigDecimal unitRate;

    /**
     * 整件包装率
     */
    private BigDecimal packageRate;

    /**
     * 是否效期 0否1是
     */
    private Integer periodFlag;

    /**
     * 移出储位编码
     */
    private String outLocationCode;

    /**
     * 移出储位名称
     */
    private String outLocationName;

    /**
     * 移入储位编码
     */
    private String inLocationCode;

    /**
     * 移入储位名称
     */
    private String inLocationName;

    /**
     * 移出可用库存
     */
    private BigDecimal outAtpQty;

    /**
     * 移入可用库存
     */
    private BigDecimal inAtpQty;

    /**
     * 箱数
     */
    private BigDecimal wholeQty;

    /**
     * 零头数量
     */
    private BigDecimal oddQty;

    /**
     * 转移数量
     */
    private BigDecimal moveQty;

    /**
     * 商品批号
     */
    private String periodBatchNo;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 过期日期
     */
    private Date expireDate;

    /**
     * 效期条码
     */
    private String periodBarcode;

    /**
     * 明细备注
     */
    private String remark;

    /**
     * 来源单据类型
     */
    private String srcBillType;

    /**
     * 来源单据号
     */
    private String srcBillNo;

    /**
     * 来源单内序号
     */
    private Long srcInsideId;

}