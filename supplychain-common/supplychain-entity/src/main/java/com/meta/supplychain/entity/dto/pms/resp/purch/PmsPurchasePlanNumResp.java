package com.meta.supplychain.entity.dto.pms.resp.purch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 订货申请请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PmsPurchasePlanNumResp {

    @Schema(description = "草稿数量")
    private Long draftNum;

    @Schema(description = "待审核数量")
    private Long pendingAuditNum;

    @Schema(description = "已审核数量")
    private Long auditedNum;

    @Schema(description = "使用中数量")
    private Long partAcceptNum;

    @Schema(description = "已完成数量")
    private Long finishedNum;

    @Schema(description = "已过期数量")
    private Long expiredNum;

    @Schema(description = "已作废数量")
    private Long cancelledNum;
}
