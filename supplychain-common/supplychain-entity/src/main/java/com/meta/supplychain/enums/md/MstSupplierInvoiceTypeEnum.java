package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商纳税人类型枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商纳税人类型", code = "mstSupplierInvoiceTypeEnum")
public enum MstSupplierInvoiceTypeEnum implements VerifiableEnum<Integer> {
    GENERAL_TAXPAYER(1, "一般纳税人"),
    SMALL_SCALE_TAXPAYER(2, "小规模纳税人");

    private final Integer code;
    private final String desc;
}
