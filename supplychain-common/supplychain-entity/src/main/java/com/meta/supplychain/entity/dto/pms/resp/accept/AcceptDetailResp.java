package com.meta.supplychain.entity.dto.pms.resp.accept;


import cn.linkkids.framework.croods.common.date.LocalDates;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Classname AcceptDetailResp
 * @Dscription TODO
 * @DATE 2025/5/19 11:42
 */
@Data
public class AcceptDetailResp {

    /**
     * 验收单号
     */
    @Schema(description = "验收单号")
    private String billNo;

    /**
     * 采购订单号
     */
    @Schema(description = "采购订单号")
    private String purchBillNo;

    /**
     * 单据状态 0-草稿 1-待审核 2-已审核 3-作废
     */
    @Schema(description = "单据状态 0-草稿 1-待审核 2-已审核 3-作废")
    private Integer status;

    /**
     * 单据方向 1.验收，-1.退货
     */
    @Schema(description = "单据方向 1.验收，-1.退货")
    private Integer billDirection;

    /**
     * 验收类型 0-普通验收 1-联营验收
     */
    @Schema(description = "验收类型 0-普通验收 1-联营验收")
    private Integer type;

    /**
     * 单据来源 0 普通 1 批销 2 大数据 3 朝批
     */
    @Schema(description = "单据来源 0 普通 1 批销 2 大数据 3 朝批")
    private Integer billSource;

    /**
     * 购进类型 0-直配 1-代配
     */
    @Schema(description = "购进类型 0-直配 1-代配")
    private Integer purchType;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String deptCode;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 采购类型 0-门店采购，1-配送采购
     */
    @Schema(description = "采购类型 0-门店采购，1-配送采购")
    private Integer billType;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 直发供应商编码
     */
    @Schema(description = "直发供应商编码")
    private String directSupplierCode;

    /**
     * 直发供应商名称
     */
    @Schema(description = "直发供应商名称")
    private String directSupplierName;

    /**
     * 直流标志 0-非直流 1-直流
     */
    @Schema(description = "直流标志 0-非直流 1-直流")
    private Integer directSign;

    /**
     * 直流部门编码
     */
    @Schema(description = "直流部门编码")
    private String directDeptCode;

    /**
     * 当前是否冲红单0,否1是
     */
    @Schema(description = "当前是否冲红单0,否1是")
    private Integer reversalBillSign;

    /**
     * 冲红标志 0,否1是
     */
    @Schema(description = "冲红标志 0,否1是")
    private Integer reversalFlag;

    /**
     * 送货单号
     */
    @Schema(description = "送货单号")
    private String sendBillNo;

    /**
     * 原单号
     */
    @Schema(description = "原单号")
    private String originBillNo;

    /**
     * 冲红单号
     */
    @Schema(description = "冲红单号")
    private String reversalBillNo;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNo;

    /**
     * 补签序号
     */
    @Schema(description = "补签序号")
    private String repairSignId;

    /**
     * 转码单单号
     */
    @Schema(description = "转码单单号")
    private String tscBillNo;

    /**
     * 被修正的验收单单号
     */
    @Schema(description = "被修正的验收单单号")
    private String amendAcceptBillNo;

    /**
     * 验收数量
     */
    @Schema(description = "验收数量")
    private BigDecimal totalQty;

    /**
     * 金额（含税） 单位元
     */
    @Schema(description = "金额（含税） 单位元")
    private BigDecimal totalMoney;

    /**
     * 核算单位编码
     */
    @Schema(description = "核算单位编码")
    private String accCode;

    /**
     * 入账时间
     */
    @Schema(description = "入账时间")
    private LocalDateTime accDate;

    /**
     * 追加截止日期
     */
    @Schema(description = "追加截止日期")
    private LocalDateTime appendValidity;

    /**
     * 打印次数
     */
    @Schema(description = "打印次数")
    private Long printCount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 供应商确认状态 0-未确认 1-已确认
     */
    @Schema(description = "供应商确认状态 0-未确认 1-已确认")
    private Integer suppConfirmStatus;

    /**
     * 供应商确认人编码
     */
    @Schema(description = "供应商确认人编码")
    private String suppConfirmCode;

    /**
     * 供应商确认人名称
     */
    @Schema(description = "供应商确认人名称")
    private String suppConfirmName;

    /**
     * 供应商确认时间
     */
    @Schema(description = "供应商确认时间")
    private LocalDateTime suppConfirmTime;

    /**
     * 提交人code
     */
    @Schema(description = "提交人code")
    private String submitManCode;

    /**
     * 提交人名称
     */
    @Schema(description = "提交人名称")
    private String submitManName;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 确认人code
     */
    @Schema(description = "确认人code")
    private String confirmManCode;

    /**
     * 确认人名称
     */
    @Schema(description = "确认人名称")
    private String confirmManName;

    /**
     * 确认时间
     */
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    /**
     * 审核人code
     */
    @Schema(description = "审核人code")
    private String auditManCode;

    /**
     * 审核人姓名
     */
    @Schema(description = "审核人姓名")
    private String auditManName;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 作废人code
     */
    @Schema(description = "作废人code")
    private String cancelManCode;

    /**
     * 作废人名称
     */
    @Schema(description = "作废人名称")
    private String cancelManName;

    /**
     * 作废时间
     */
    @Schema(description = "作废时间")
    private LocalDateTime cancelTime;

    /**
     * 供应商更新人编码
     */
    @Schema(description = "供应商更新人编码")
    private String suppUpdateCode;

    /**
     * 供应商更新人名称
     */
    @Schema(description = "供应商更新人名称")
    private String suppUpdateName;

    /**
     * 供应商更新时间
     */
    @Schema(description = "供应商更新时间")
    private LocalDateTime suppUpdateTime;

    @Schema(description = "冲红日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime reversalDate;

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "商品分类编码")
    private String categoryCode;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品规格")
    private String skuModel;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMode;

    @Schema(description = "单位，汉字文描，个，斤")
    private String basicUnit;

    @Schema(description = "单位比率，包装率")
    private BigDecimal unitRate;

    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "验收数量")
    private BigDecimal acceptQty;

    @Schema(description = "订货单价 单位元")
    private BigDecimal purchPrice;

    @Schema(description = "订货金额（含税） 单位元")
    private BigDecimal purchTaxMoney;

    @Schema(description = "订货金额(无税) 单位元")
    private BigDecimal purchMoney;

    @Schema(description = "订货税金 单位元")
    private BigDecimal purchTax;

    @Schema(description = "特供价 单位元")
    private BigDecimal specialPrice;

    @Schema(description = "商品售价 单位元")
    private BigDecimal salePrice;

    @Schema(description = "销售金额 单位元")
    private BigDecimal saleMoney;

    @Schema(description = "效期条码")
    private String periodBarcode;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "商品批号")
    private String periodBatchNo;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate productDate;

    @Schema(description = "过期日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate expireDate;

    @Schema(description = "效期数量")
    private BigDecimal periodQty;

    @Schema(description = "效期状态 0-正常 1-临期 2-过期")
    private Integer validityStatus;

    @Schema(description = "箱码")
    private String boxCode;

    @Schema(description = "订货途径 0-采购，1-配送")
    private Integer orderRoute;

    @Schema(description = "收货提前期")
    private Integer leadTime;

    @Schema(description = "效期商品标识 1是 0否，默认0")
    private Integer periodFlag;

    @Schema(description = "商品库存")
    private BigDecimal stockQty;

    @Schema(description = "商品分类名称")
    private String categoryName;

    @Schema(description = "包装率单位")
    private String packageUnitName;

    @Schema(description = "整件数量")
    private BigDecimal wholeQty;

    @Schema(description = "零头数量")
    private BigDecimal oddQty;

    @Schema(description = "订货数量")
    private BigDecimal orderQty;

    @Schema(description = "流转途径编码")
    private String circulationModeCode;

    @Schema(description = "经营状态编码")
    private String workStatusCode;

    @Schema(description = "经营状态名称")
    private String workStatusName;

    @Schema(description = "在途库存")
    private BigDecimal onWayStockQty;

    @Schema(description = "计量属性（0：普通 1：计量 2：称重）")
    private Integer uomAttr;

    @Schema(description = "批次接口返回信息")
    private String batchInfo;

    @Schema(description = "整箱单位")
    private String wholeUnit;

    @Schema(description = "合同经营方式 0-经销 1-代销 2-联营 3-租赁")
    private Integer manageModel;

    @Schema(description = "扩展字段")
    private String extData;

    @Schema(description = "管理分类项编码")
    private String manageCategoryClass;

    @Schema(description = "管理分类编码")
    private String manageCategoryCode;

    @Schema(description = "管理分类名称")
    private String manageCategoryName;

    @Schema(description = "验收退补价格")
    private BigDecimal acceptRefundPrice;

    @Schema(description = "取价类型1, 验收退补价格 2, 合同商品进价 3, 合同商品特供价")
    private Integer priceType;

    @Schema(description = "促销期进价")
    private BigDecimal promotePeriodPrice;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;

    @Schema(description = "合同特供价")
    private BigDecimal contractSpecialPrice;

    @Schema(description = "合同进价")
    private BigDecimal contractPrice;

    @Schema(description = "合同最高进价")
    private BigDecimal contractMaxPrice;

    @Schema(description = "最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)")
    private BigDecimal lastPurchPrice;

    @Schema(description = "来源单据类型")
    private String srcBillType;

    @Schema(description = "来源单据号")
    private String srcBillNo;

    @Schema(description = "来源单内序号")
    private Long srcInsideId;

    @Schema(description = "创建人ssoId")
    private Long createUid;

    @Schema(description = "创建人编码")
    private String createCode;

    @Schema(description = "创建人姓名")
    private String createName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "修改人ssoId")
    private Long updateUid;

    @Schema(description = "修改人编码")
    private String updateCode;

    @Schema(description = "修改人姓名")
    private String updateName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 储位编码
     */
    @Schema(description = "储位编码")
    private String locationCode;

    /**
     * 储位名称
     */
    @Schema(description = "储位名称")
    private String locationName;

    /**
     * 数量/批次数量
     */
    @Schema(description = "数量/批次数量")
    private BigDecimal batchQty;

    /**
     * 批次成本单价
     */
    @Schema(description = "批次成本单价")
    private BigDecimal batchCostPrice;

    /**
     * 批次税金
     */
    @Schema(description = "批次税金")
    private BigDecimal batchTax;

    /**
     * 成本单价
     */
    @Schema(description = "批次成本金额")
    private BigDecimal referMoney;

    /**
     * 门店经营模式 1 直营 2 加盟
     */
    @Schema(description = "门店经营模式 1 直营 2 加盟")
    private String deptOperateMode;
}
