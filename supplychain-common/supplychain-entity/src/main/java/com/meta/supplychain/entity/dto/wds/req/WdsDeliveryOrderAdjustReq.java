package com.meta.supplychain.entity.dto.wds.req;


import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjust4AsReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 追加追减配送订单调整信息
 */
@Schema(description = "追加追减配送订单调整信息")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WdsDeliveryOrderAdjustReq extends WdsDeliveryOrderAdjustBaseReq{

    @Schema(description = "联系人")
    private String contactMan;

    @Schema(description = "联系地址")
    private String contactAddr;

    @Schema(description = "联系电话")
    private String contactTel;

    @Schema(description = "配送订单备注")
    private String purchRemark;

    @Schema(description = "退货原因")
    private String refundReason;

    @Schema(description = "退货原因描述")
    private String refundReasonDesc;

    @Schema(description = "送货方式 0-到店，1-到客户，默认到店")
    private Integer sendMode;

    @Schema(description = "调整商品列表")
    List<WdsDeliveryOrderAdjustGoodsReq> detailList;




}
