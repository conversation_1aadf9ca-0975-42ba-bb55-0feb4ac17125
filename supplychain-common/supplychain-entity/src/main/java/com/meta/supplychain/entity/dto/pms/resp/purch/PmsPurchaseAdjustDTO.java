package com.meta.supplychain.entity.dto.pms.resp.purch;

import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjustReq;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
public class PmsPurchaseAdjustDTO {
    // 调整记录流水号
    private String adjustBillNo;
    //调整信息
    PurchaseBillAdjustReq adjustReq;
    //原单
    PmsPurchaseOrderPO oldBill;
    //调整后主表
    PmsPurchaseOrderPO newBill;
    /**
     * 记录原单变化后明细
     */
    List<PmsPurchaseBillDetailPO> afterBillDetail;

    /**
     * 记录明细变化差值
     */
    List<PmsPurchaseBillDetailPO> changeBillDetail;

    List<BillAdjustLogPO> billAdjustLog;

    /**
     * 记录计划单更新信息
     */
    List<PmsPurchasePlanDetailPO> planDetailList;

    /**
     * 所有明细汇总
     */
    Map<Long, PmsPurchaseBillDetailPO> existDetailMap;

}
