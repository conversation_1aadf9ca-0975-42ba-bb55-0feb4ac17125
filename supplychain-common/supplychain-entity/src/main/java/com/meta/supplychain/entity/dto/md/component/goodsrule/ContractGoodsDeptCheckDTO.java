package com.meta.supplychain.entity.dto.md.component.goodsrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/22 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractGoodsDeptCheckDTO {

    @Schema(description = "商品编码列表")
    @NotEmpty(message = "商品编码列表不能为空")
    private List<String> skuCodeList;

    @Schema(description = "供应商编码")
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    @Schema(description = "合同号")
    @NotNull(message = "合同号不能为空")
    private String contractNo;

    @Schema(description = "是否取消 true取消 false不取消")
    private Boolean cancelFlag = false;

}
