package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约方式枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "预约方式", code = "pmsBookingMethodEnum")
public enum PmsBookingMethodEnum implements VerifiableEnum<Integer> {

    BY_PRODUCT(1, "按商品"),
    BY_ORDER(2, "按订单");

    private final Integer code;
    private final String desc;
} 