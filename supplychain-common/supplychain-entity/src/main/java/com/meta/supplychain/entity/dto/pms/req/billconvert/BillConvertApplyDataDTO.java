package com.meta.supplychain.entity.dto.pms.req.billconvert;

import com.meta.supplychain.entity.po.pms.*;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/22 16:35
 **/
@Data
public class BillConvertApplyDataDTO {

    @Schema(description = "订货申请表")
    private List<PmsApplyBillPO> applyBillList = new ArrayList<>();

    @Schema(description = "订货申请明细表")
    private List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();
}
