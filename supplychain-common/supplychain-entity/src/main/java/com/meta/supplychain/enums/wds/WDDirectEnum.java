package com.meta.supplychain.enums.wds;

import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.validation.annotation.EnumMark;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  直流标记枚举[0 非直流 | 1 直流]
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "直流标记枚举",code = "WDDirectEnum")
public enum WDDirectEnum implements StandardEnum<Integer> {

    NOT_DIRECT(0, "非直流"),
    DIRECT(1, "直流");

    private final Integer code;

    private final String desc;
}
