package com.meta.supplychain.entity.dto.bds.resp;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/4/17 9:37
 */
@Builder
@Setter
@Getter
@Data
public class QueryDeptGroupBatchResp {

    private String code;

    private List<DeptGroup> deptGroupList;


    @Data
    public static class DeptGroup{


        /**
         * 店组群编码
         */
        private String code;

        /**
         * 店组群名称
         */
        private String name;

        /**
         * 父级店组群Id
         */
        private Long parentId;

        /**
         * 父级店组群编码
         */
        private String parentCode;

        /**
         * 父级店组群名称
         */
        private String parentName;

        /**
         * 店组群分类编码
         */
        private String classCode;

        /**
         * 店组群分类名称
         */
        private String className;

        /**
         * 状态  1：启用  0：停用
         */
        private Integer status;
    }
}
