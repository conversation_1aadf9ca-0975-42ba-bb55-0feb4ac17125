package com.meta.supplychain.entity.dto.pms.req.addReduce;

import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsAddReduceSource;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmsOrderAllocateDetail {

    @Schema(description = "采购批次")
    private String purchBatchNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "商品（分类）编码")
    private String skuCode;

    @Schema(description = "商品（分类）名称")
    private String skuName;

    @Schema(description = "商品类型 1, 主品 2, 赠品")
    private Integer goodsType;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "商品品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "单位 存储枚举，需要通过配置转换")
    private String basicUnit;

    @Schema(description = "整箱单位")
    private String wholeUnit;

    /**
     * 商品规格
     */
    @Schema(description = "商品规格")
    private String skuModel;

    /**
     * 进项税率
     */
    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "直流标志 0-否，1-是")
    private Integer directSign;

    /**
     * 需求追加追减数量
     */
    @Schema(description = "需求追加追减数量")
    private BigDecimal addReduceQty;

    /**
     * 调整后数量
     */
    @Schema(description = "调整后数量")
    private BigDecimal adjustQty;

    @Schema(description = "配送部门编码")
    private String distCode;

    /**
     * 追加追减规则 1.优先调整现有订单  2. 追加生成新订单
     */
    @Schema(description = "配送部门编码")
    private Integer addReduceRule;

    /**
     * 申请单单号
     */
    @Schema(description = "追加追减来源")
    private List<PmsAddReduceSource> addReduceSources;

    public String getAddReduceKey() {
        return  purchBatchNo + "-" +deptCode + "-" + skuCode +  "-" + goodsType;
    }

}
