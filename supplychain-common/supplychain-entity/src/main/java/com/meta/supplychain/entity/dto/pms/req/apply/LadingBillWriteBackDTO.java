package com.meta.supplychain.entity.dto.pms.req.apply;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 批量订货申请单操作请求
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LadingBillWriteBackDTO implements Serializable {

    /**
     * 主键id
     */
    @Schema(description = "申请单商品行号")
    private Long insideId;

    /**
     * 生成的申请单号
     */
    @Schema(description = "申请单号")
    private String billNo;

}
