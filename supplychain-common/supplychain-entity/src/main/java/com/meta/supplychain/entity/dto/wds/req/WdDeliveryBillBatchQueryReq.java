package com.meta.supplychain.entity.dto.wds.req;

import com.meta.supplychain.enums.wds.WDDeliveryOptTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 配送订单操作
 */
@Schema(description = "配送订单操作")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class WdDeliveryBillBatchQueryReq {
    @Schema(description = "配送单据号列表")
    @NotEmpty(message = "配送单据号不能为空")
    private List<String> billNoList;

}
