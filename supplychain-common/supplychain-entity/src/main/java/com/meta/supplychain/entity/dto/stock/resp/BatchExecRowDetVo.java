package com.meta.supplychain.entity.dto.stock.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商品行明细信息
 *
 * <AUTHOR>
 * @date 2022/6/15 14:58
 * @since 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchExecRowDetVo {

    /**
     * 批次号
     */
    @Schema(title = "批次号",description = "批次号")
    private String batchNo;
    /**
     * 业务类型编码
     */
    @Schema(title = "操作类型",description = "操作类型")
    private String operateCode;
    /**
     * 商品行号
     */
    @Schema(title = "商品行号",description = "商品行号")
    private String insideId;
    /**
     * 效期行号
     */
    @Schema(title = "效期行号",description = "效期行号")
    private Integer periodInsideId;
    /**
     * 商品编码
     */
    @Schema(title = "商品编码",description = "商品编码")
    private String skuCode;
    /**
     * 合同号
     */
    @Schema(title = "合同号",description = "合同号")
    private String contractNo;
    /**
     * 供应商编码
     */
    @Schema(title = "供应商编码",description = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(title = "供应商名称",description = "供应商名称")
    private String supplierName;

    /**
     * 效期批号
     */
    @Schema(title = "效期批号",description = "效期批号")
    private String periodBatchNo;
    /**
     * 生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    @Schema(title = "生产日期",description = "生产日期 yyyy-MM-dd")
    private LocalDate productDate;
    /**
     * 有效期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    @Schema(title = "过期日期",description = "过期日期 yyyy-MM-dd")
    private LocalDate expiryDate;
    /**
     * 效期条码
     */
    @Schema(title = "效期条码",description = "效期条码")
    private String periodBarcode;
    /**
     * 数量(*10000)
     */
    @Schema(title = "数量",description = "数量")
    private BigDecimal realQty =BigDecimal.ZERO;
    /**
     * 成本金额（*100）
     */
    @Schema(title = "不含税成本金额",description = "不含税成本金额")
    private BigDecimal costMoney =BigDecimal.ZERO;
    /**
     * 成本税金（*100）
     */
    @Schema(title = "含税成本金额",description = "含税成本金额")
    private BigDecimal costTaxMoney =BigDecimal.ZERO;
    /**
     * 批次成本（*100）
     */
    @Schema(title = "批次不含税成本金额",description = "批次不含税成本金额")
    private BigDecimal batchCostMoney =BigDecimal.ZERO;
    /**
     * 批次成本税金（*100）
     */
    @Schema(title = "批次含税成本金额",description = "批次含税成本金额")
    private BigDecimal batchCostTaxMoney =BigDecimal.ZERO;
    /**
     * 拨出金额(*100)
     */
    @Schema(title = "拨出不含税金额",description = "拨出不含税金额")
    private BigDecimal outCostMoney =BigDecimal.ZERO;
    /**
     * 拨出税金（*100）
     */
    @Schema(title = "拨出含税金额",description = "拨出含税金额")
    private BigDecimal outCostTaxMoney =BigDecimal.ZERO;

    /**
     * 零售金额
     */
    @Schema(title = "不含销售金额",description = "不含销售金额")
    private BigDecimal saleMoney =BigDecimal.ZERO;

    /**
     * 零售税金
     */
    @Schema(title = "销售税金",description = "销售税金")
    private BigDecimal saleTax=BigDecimal.ZERO;

    /**
     * 成本单价
     */
    @Schema(title = "不含税成本单价",description = "不含税成本单价")
    private BigDecimal costPrice=BigDecimal.ZERO;
    /**
     * 成本单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal costTaxPrice=BigDecimal.ZERO;
    /**
     * 批次成本单价
     */
    @Schema(title = "不含税批次成本单价",description = "不含税批次成本单价")
    private BigDecimal batchCostPrice=BigDecimal.ZERO;
    /**
     * 成本单价
     */
    @Schema(title = "含税成本单价",description = "含税成本单价")
    private BigDecimal batchCostTaxPrice=BigDecimal.ZERO;
    /**
     * 计算方式 1先进先出 2移动加权
     */
    @Schema(title = "计算方式",description = "计算方式 1先进先出 2移动加权")
    private Integer calculateMethod;
}
