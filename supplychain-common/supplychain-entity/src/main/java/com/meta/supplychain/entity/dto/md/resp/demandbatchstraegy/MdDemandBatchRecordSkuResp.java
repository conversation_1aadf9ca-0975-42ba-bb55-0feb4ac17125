package com.meta.supplychain.entity.dto.md.resp.demandbatchstraegy;

import com.meta.supplychain.entity.dto.md.demandbatchstraegy.MdDemandBatchRecordDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/15 13:44
 **/
@Data
public class MdDemandBatchRecordSkuResp {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "采购批次记录")
    private List<MdDemandBatchRecordDTO> mdDemandBatchRecordList;

}
