package com.meta.supplychain.entity.dto.wds.resp;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class QueryWdRefundAcceptExcelView {

    @ViewField(headerName = "退货收货单号")
    private String billNo;

    @ViewField(headerName = "配送中心")
    private String wh;

    @ViewField(headerName = "单据状态")
    private String statusDesc;

    @ViewField(headerName = "退货部门")
    private String inDept;

    @ViewField(headerName = "收货数量")
    private BigDecimal totalAcceptQty;

    @ViewField(headerName = "收货金额")
    private String totalAcceptTaxMoney;

    @ViewField(headerName = "收货税金")
    private String totalAcceptTax;

    @ViewField(headerName = "制单时间")
    private String createTime;

    @ViewField(headerName = "制单人编码")
    private String createCode;
    @ViewField(headerName = "制单人名称")
    private String createName;

    @ViewField(headerName = "收货时间")
    private String submitTime;

    /** 收货人编码 */
    @ViewField(headerName = "收货人编码")
    private String submitManCode;

    /** 确认人名称 */
    @ViewField(headerName = "收货人名称")
    private String submitManName;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    /** 作废人编码 */
    @ViewField(headerName = "作废人编码")
    private String cancelManCode;

    /** 作废人名称 */
    @ViewField(headerName = "作废人名称")
    private String cancelManName;

    @ViewField(headerName = "配送单号")
    private String shipBillNo;

    @ViewField(headerName = "配送订单号")
    private String deliveryBillNo;

    @ViewField(headerName = "单据备注")
    private String remark;
}