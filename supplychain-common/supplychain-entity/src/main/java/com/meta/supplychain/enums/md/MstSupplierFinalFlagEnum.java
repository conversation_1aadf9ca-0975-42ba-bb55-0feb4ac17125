package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商分类是否末级节点枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商分类是否末级节点", code = "mstSupplierFinalFlagEnum")
public enum MstSupplierFinalFlagEnum implements VerifiableEnum<Integer> {
    NOT_FINAL(0, "否"),
    FINAL(1, "是");

    private final Integer code;
    private final String desc;
}
