package com.meta.supplychain.entity.dto.pms.resp.purch;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采购订单明细汇总含直流
 */
@Schema(description = "采购订单明细汇总含直流")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDetailWithDirectResp {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "采购订单号")
    private String billNo;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "采购类型 0-门店采购，1-配送采购")
    private Integer billType;

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer skuType;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "规格")
    private String skuModel;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMode;

    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "计量属性 0：普通 1：计量 2：称重")
    private Integer uomAttr;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "订货包装率")
    private BigDecimal purchUnitRate;

    @Schema(description = "促销期进价")
    private BigDecimal promotePeriodPrice;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "合同特供价")
    private BigDecimal contractSpecialPrice;

    @Schema(description = "合同进价")
    private BigDecimal contractPrice;

    @Schema(description = "合同最高进价")
    private BigDecimal contractMaxPrice;

    @Schema(description = "最后进价")
    private BigDecimal lastPurchPrice;

    @Schema(description = "采购价格")
    private BigDecimal purchPrice;

    @Schema(description = "部门库存")
    private BigDecimal stockQty;

    @Schema(description = "部门可用库存")
    private BigDecimal atpQty;

    @Schema(description = "采购计划可采数量")
    private BigDecimal planReqQty;

    @Schema(description = "整件数量")
    private BigDecimal wholeQty;

    @Schema(description = "零头数量")
    private BigDecimal oddQty;

    @Schema(description = "采购数量")
    private BigDecimal purchQty;

    @Schema(description = "采购金额")
    private BigDecimal purchMoney;

    @Schema(description = "采购税金")
    private BigDecimal purchTax;

    @Schema(description = "零售单价")
    private BigDecimal salePrice;

    @Schema(description = "零售金额")
    private BigDecimal saleMoney;

    @Schema(description = "效期商品标识 0否 1是")
    private Integer periodFlag;

    @Schema(description = "效期条码")
    private String periodBarcode;

    @Schema(description = "效期批号")
    private String periodBatchNo;

    @Schema(description = "效期生产日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate productDate;

    @Schema(description = "效期到期日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate expireDate;

    @Schema(description = "预约数量")
    private BigDecimal appointmentQty;

    @Schema(description = "预约率")
    private BigDecimal appointmentRate;

    @Schema(description = "履行数量")
    private BigDecimal fulfilQty;

    @Schema(description = "履行金额")
    private BigDecimal fulfilMoney;

    @Schema(description = "数量履行率")
    private BigDecimal fulfilRate;

    @Schema(description = "最后履行时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime fulfilTime;

    @Schema(description = "确认标记  0-无需确认，1-未确认，2-已确认")
    private Integer confirmSign;

    @Schema(description = "确认数量")
    private BigDecimal confirmQty;

    @Schema(description = "来源 0-手工单，1-需求单，2-配转采")
    private Integer billSource;

    @Schema(description = "来源单据类型")
    private String srcBillType;

    @Schema(description = "来源单号")
    private String srcBillNo;

    @Schema(description = "来源单单内序号")
    private Long srcInsideId;

    @Schema(description = "需求批次")
    private String purchBatchNo;

    @Schema(description = "门店经营模式 1:直营，2:加盟")
    private Integer deptOperateMode;

    @Schema(description = "单据类别 -1:采退，1:采购")
    private Integer billDirection;

    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称")
    private String orderAttributeName;

    @Schema(description = "状态 99-待转单， 0-草稿，1-待审核，2-已审核，3-收货中，4-已完成，5-已过期，6-已作废")
    private Integer status;

    @Schema(description = "送货方式 0-到店，1-到客户")
    private Integer sendMode;

    @Schema(description = "送货日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate deliverDate;

    @Schema(description = "有效日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate validityDate;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "联系人")
    private String contactMan;

    @Schema(description = "联系地址")
    private String contactAddr;

    @Schema(description = "联系电话")
    private String contactTel;

    @Schema(description = "采购订单备注")
    private String purchRemark;

    @Schema(description = "退货原因")
    private String refundReason;

    @Schema(description = "退货原因描述")
    private String refundReasonDesc;

    @Schema(description = "来源单据备注")
    private String srcRemark;

    @Schema(description = "采购计划单号")
    private String planBillNo;

    @Schema(description = "商品品项数")
    private Integer totalSkuCount;

    @Schema(description = "采购总数量")
    private BigDecimal totalQty;

    @Schema(description = "采购总金额")
    private BigDecimal totalTaxMoney;

    @Schema(description = "采购总税金")
    private BigDecimal totalTax;

    @Schema(description = "租户打印次数")
    private Integer printCount;

    @Schema(description = "供应商打印次数")
    private Integer suppPrintCount;

    @Schema(description = "已读标记 0-否，未读  1-是，已读")
    private Integer readSign;

    @Schema(description = "已读时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime readTime;

    @Schema(description = "审核人工号")
    private String auditCode;

    @Schema(description = "审核人名称")
    private String auditName;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime auditTime;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "作废人工号")
    private String cancelManCode;

    @Schema(description = "作废人名称")
    private String cancelManName;

    @Schema(description = "作废时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime cancelTime;

    @Schema(description = "作废备注")
    private String cancelRemark;

    @Schema(description = "是否直流订单 0-非直流 1-直流")
    private Integer directSign;

    @Schema(description = "是否配转采0-否 1-是")
    private Integer transferPurchSign;

    @Schema(description = "确认送货标记 0-无需确认，1-未确认，2-已确认")
    private Integer confirmDeliverSign;

    @Schema(description = "确认时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime confirmDeliverTime;

    @Schema(description = "发货标记 0-未发货，1-已发货")
    private Integer shipSign;

    @Schema(description = "发货时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime shipTime;

    @Schema(description = "预约标记 0-未预约，1-已预约")
    private Integer appointmentSign;

    @Schema(description = "来源配送单号")
    private String srcDeliveryBillNo;

    /** 关联单单内序号 */
    @Schema(description = "来源配送单单内序号")
    private Long srcDeliveryInsideId;

    @Schema(description = "租户号")
    private Long tenantId;
    @Schema(description = "创建人ssoId")
    private String createUid;
    @Schema(description = "创建人编码")
    private String createCode;
    @Schema(description = "创建人名称")
    private String createName;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @Schema(description = "修改人ssoId")
    private Long updateUid;
    @Schema(description = "修改人工号")
    private String updateCode;
    @Schema(description = "修改人姓名")
    private String updateName;
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    public String getDeliveryInsideIdKey() {
        return  deliveryBillNo + "-" +deliveryInsideId ;
    }

    @Schema(description = "直流配送订单号")
    private String deliveryBillNo;

    @Schema(description = "直流配送订单行号")
    private Long deliveryInsideId;

    @Schema(description = "直流配送入货部门编码")
    private String inDeptCode;

    @Schema(description = "直流配送入货部门名称")
    private String inDeptName;

    @Schema(description = "直流配送配送订货包装率")
    private BigDecimal deliveryOrderUnitRate;

    @Schema(description = "直流配送配送单价")
    private BigDecimal deliveryPrice;

    @Schema(description = "直流配送配送整件数量")
    private BigDecimal deliveryWholeQty;

    @Schema(description = "直流配送配送零头数量")
    private BigDecimal deliveryOddQty;

    @Schema(description = "直流配送配送数量")
    private BigDecimal deliveryQty;

    @Schema(description = "直流配送配送金额")
    private BigDecimal deliveryTaxMoney;

    @Schema(description = "直流配送配送税金")
    private BigDecimal deliveryTax;

    public String getPurchInsideIdKey() {
        return  billNo + "-" +insideId ;
    }
}
