package com.meta.supplychain.convert.wds;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.req.CreateWdRefundAcceptDetailReq;
import com.meta.supplychain.entity.dto.wds.req.CreateWdRefundAcceptReq;
import com.meta.supplychain.entity.dto.wds.resp.QueryWdRefundAcceptBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.QueryWdRefundAcceptBillResp;
import com.meta.supplychain.entity.dto.wds.resp.QueryWdRefundAcceptExcelView;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RefundAcceptConvert extends StandardEnumConvert {

    RefundAcceptConvert INSTANCE = Mappers.getMapper(RefundAcceptConvert.class);

    // WdRefundAcceptBillPO 转 QueryWdRefundAcceptBillResp
    QueryWdRefundAcceptBillResp convertToQueryWdRefundAcceptBillResp(WdRefundAcceptBillPO wdRefundAcceptBillPO);

    List<QueryWdRefundAcceptBillResp> convertToQueryWdRefundAcceptBillRespList(List<WdRefundAcceptBillPO> wdRefundAcceptBillPOList);


    // WdRefundAcceptBatchDetailPO 转 QueryWdRefundAcceptBatchDetailResp
    @Mapping(source = "acceptPrice", target = "acceptTaxPrice")
    @Mapping(source = "acceptMoney", target = "acceptTaxMoney")
    QueryWdRefundAcceptBatchDetailResp convertToQueryWdRefundAcceptBatchDetailResp(WdRefundAcceptBatchDetailPO wdRefundAcceptBatchDetailPO);

    List<QueryWdRefundAcceptBatchDetailResp> convertToQueryWdRefundAcceptBatchDetailRespList(List<WdRefundAcceptBatchDetailPO> wdRefundAcceptBatchDetailPOList);


    // QueryWdRefundAcceptBillResp 转 QueryWdRefundAcceptExcelView
    @Mapping(source = "submitTime", target = "submitTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "cancelTime", target = "cancelTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "createTime", target = "createTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "wh", expression = "java(convertManCodeName(result.getInDeptCode(), result.getWhName()))")
    @Mapping(target = "inDept", expression = "java(convertManCodeName(result.getWhCode(), result.getInDeptName()))")
    @Mapping(target = "statusDesc", expression = "java(convertToDesc(\"RefundAcceptBillStatusEnum\", result.getStatus()))")
    @Mapping(target = "totalAcceptTaxMoney", expression = "java(formatMoney(result.getTotalAcceptTaxMoney()))")
    @Mapping(target = "totalAcceptTax", expression = "java(formatMoney(result.getTotalAcceptTax()))")
    QueryWdRefundAcceptExcelView convertToQueryWdRefundAcceptExcelView(QueryWdRefundAcceptBillResp result);



    List<QueryWdRefundAcceptExcelView> convertToQueryWdRefundAcceptExcelViewList(List<QueryWdRefundAcceptBillResp> queryWdRefundAcceptBillRespList);

    // CreateWdRefundAcceptReq 转 WdRefundAcceptBillPO
    WdRefundAcceptBillPO convertToWdRefundAcceptBillPO(CreateWdRefundAcceptReq createWdRefundAcceptReq);

    // CreateWdRefundAcceptDetailReq 转 WdRefundAcceptBatchDetailPO
    @Mapping(target = "acceptPrice", source = "acceptTaxPrice")
    @Mapping(target = "acceptMoney", source = "acceptTaxMoney")
    WdRefundAcceptBatchDetailPO convertToWdRefundAcceptBatchDetailPO(CreateWdRefundAcceptDetailReq createWdRefundAcceptDetailReq);

    List<WdRefundAcceptBatchDetailPO> convertToWdRefundAcceptBatchDetailPOList(List<CreateWdRefundAcceptDetailReq> createWdRefundAcceptDetailReqList);
}
