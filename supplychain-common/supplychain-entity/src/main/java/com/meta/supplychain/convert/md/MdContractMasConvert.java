package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.contract.MdContractDTO;
import com.meta.supplychain.entity.dto.md.contract.MdContractDetailsDTO;
import com.meta.supplychain.entity.po.md.MdContractMasPO;
import com.meta.supplychain.entity.dto.md.view.MdContractView;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.md.MdContractOperateModeEnum;
import com.meta.supplychain.enums.md.MdContractSignModeEnum;
import com.meta.supplychain.enums.md.MdContractStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdContractMasConvert {
    MdContractMasConvert INSTANCE = Mappers.getMapper(MdContractMasConvert.class);

    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "signMode", target = "signModeDesc", qualifiedByName = "signModeToDesc")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    MdContractDTO convertPo2Dto(MdContractMasPO mdContractMasPO);

    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "signMode", target = "signModeDesc")
    @Mapping(source = "status", target = "statusDesc")
    MdContractDetailsDTO convertPo2MdContractDetailsDTO(MdContractMasPO mdContractMasPO);

    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "signMode", target = "signModeDesc", qualifiedByName = "signModeToDesc")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    List<MdContractDTO> convertPo2DtoList(List<MdContractMasPO> poList);

    @Mapping(source = "operateMode", target = "operateModeDesc", qualifiedByName = "operateModeToDesc")
    @Mapping(source = "signMode", target = "signModeDesc", qualifiedByName = "signModeToDesc")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    MdContractView convertDto2View(MdContractDTO mdContractMasPO);

    @Named("operateModeToDesc")
    default String operateModeToDesc(String code) {
        if (code == null) {
            return "";
        }
        MdContractOperateModeEnum enumValue = StandardEnum.codeOf(MdContractOperateModeEnum.class, code);
        return enumValue != null ? enumValue.getDesc() : code;
    }

    @Named("signModeToDesc")
    default String signModeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        MdContractSignModeEnum enumValue = StandardEnum.codeOf(MdContractSignModeEnum.class, code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }

    @Named("statusToDesc")
    default String statusToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        MdContractStatusEnum enumValue = StandardEnum.codeOf(MdContractStatusEnum.class, code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }
}
