package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.commonstatus.MdCommonStatusProcessDTO;
import com.meta.supplychain.entity.po.md.MdCommonStatusProcessPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MdCommonStatusProcessConvert {
    MdCommonStatusProcessConvert INSTANCE = Mappers.getMapper(MdCommonStatusProcessConvert.class);

    MdCommonStatusProcessPO convertDto2Po(MdCommonStatusProcessDTO mdCommonStatusProcessDTO);

    List<MdCommonStatusProcessDTO> convertListPo2Dto(List<MdCommonStatusProcessPO> list);
}
