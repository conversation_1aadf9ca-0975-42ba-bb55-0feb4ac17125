package com.meta.supplychain.entity.dto.franline.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 加盟额度调整入参
 * <AUTHOR> cat
 * @date 2024/11/1 14:48
 */
@Getter
@Setter
@Builder
public class FranLineAdjustReq {

    /**
     * 业务类型
     */
    private Integer type;
    private Long timeStamp;
    /**
     * 加盟店编码
     */
    private String storeCode;

    /**
     * 拨出门店编码(拨出门店是加盟店时必填)
     */
    private String outStoreCode;
    private String sign;
    /**
     * 系统回滚标志 1回滚 0正常
     */
    private Long rollBack;
    private String billNumber;
    private String appCode;

    /**
     * 创建人编码
     */
    private String createCode;
    /**
     * 创建人姓名
     */
    private String createName;

    private String remark;
    /**
     * 业务发生金额(元)  加盟额度精确到分
     */
    private Double amount;

    /**
     * 流水订单号(支持多订单号, 用,隔开)
     */
    private List<OrderNumber> orderNumberList;

    @Getter
    @Setter
    @Builder
    public static class OrderNumber{
        /**
         * 流水订单号
         */
        private String orderNumber;

        /**
         * 订单发货金额
         */
        private Double amount;
    }
}
