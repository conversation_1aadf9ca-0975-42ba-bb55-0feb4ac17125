package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.*;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商预约单关联采购订单表
 * <AUTHOR>
 * @date 2025-4-20 20:49:06
 */
@TableName(value ="pms_appointment_bill_purch")
@Getter
@Setter
@ToString
public class PmsAppointmentBillPurchPO extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 预约单据号
     */
    private String appointmentBillNo;

    /**
     * 采购订单号
     */
    private String purchBillNo;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 单据类别（1-采购，-1-采退）
     */
    private Integer billDirection;

    /**
     * 是否直流订单	0-非直流 1-直流
     */
    private Integer directSign;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 送货日期
     */
    private LocalDate deliverDate;

    /**
     * 有效日期
     */
    private LocalDate validityDate;

    /**
     * 来源	0-手工单，1-需求单，2-配转采
     */
    private Integer billSource;

    /**
     * 来源单号
     */
    private String refBillNo;

    /**
     * 来源单据备注
     */
    private String refRemark;

    /**
     * 采购订单备注
     */
    private String purchRemark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 订货属性编码
     */
    private String orderAttributeCode;

    /**
     * 订货属性名称
     */
    private String orderAttributeName;

    /**
     * 需求批次
     */
    private String purchBatchNo;

    /**
     * 退货原因
     */
    private String refundReason;

    /**
     * 采购整件总数量
     */
    private BigDecimal totalWholeQty;

    /**
     * 采购零头总数量
     */
    private BigDecimal totalOddQty;

    /**
     * 采购总数量
     */
    private BigDecimal totalQty;

    /**
     * 可约整件总数量
     */
    private BigDecimal totalCanAppointmentWholeQty;

    /**
     * 可约零头总数量
     */
    private BigDecimal totalCanAppointmentOddQty;

    /**
     * 可约总数量
     */
    private BigDecimal totalCanAppointmentQty;

    /**
     * 本次预约总件数
     */
    private BigDecimal totalAppointmentWholeQty;

    /**
     * 本次预约零头总数量
     */
    private BigDecimal totalAppointmentOddQty;

    /**
     * 本次预约总数量
     */
    private BigDecimal totalAppointmentQty;

    /** 逻辑删除状态 1:删除 0:正常 */
    @TableLogic
    private Integer delFlag;

    private Long tenantId;
} 