package com.meta.supplychain.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.serializes.StandardEnumJsonSerializer;

import java.lang.annotation.*;

/**
 * 枚举序列化注解
 * 标注在字段上，将字段值转换为对应枚举的描述
 * 
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@JacksonAnnotationsInside
@JsonSerialize(using = StandardEnumJsonSerializer.class)
public @interface EnumSerializer {
    
    /**
     * 枚举类类型
     * @return 枚举类类型
     */
    Class<? extends StandardEnum> value();
} 