package com.meta.supplychain.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日期时间格式注解，用于指定LocalDateTime & LocalDate的序列化和反序列化格式
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LocalDatetimePattern {
    /**
     * 日期时间格式
     */
    String value();
}