package com.meta.supplychain.enums.md;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商启用状态枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "供应商启用状态", code = "mstSupplierStatusEnum")
public enum MstSupplierStatusEnum implements VerifiableEnum<Integer> {
    DISABLED(0, "停用"),
    ENABLED(1, "启用");

    private final Integer code;
    private final String desc;
}
