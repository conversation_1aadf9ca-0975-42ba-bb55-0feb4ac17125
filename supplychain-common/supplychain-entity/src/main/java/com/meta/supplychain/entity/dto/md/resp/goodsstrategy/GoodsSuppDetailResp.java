package com.meta.supplychain.entity.dto.md.resp.goodsstrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSuppDetailResp {

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "合同补签序号")
    private String contractSerial;

    @Schema(description = "手工合同编号")
    private String manualContractNo;

    @Schema(description = "经营方式, J经销 D代销 L联营 Z租赁")
    private String operateMode;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "是否主供应商,0否,1是")
    private Integer mainSupplierMode;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门类型")
    private Integer deptType;
}
