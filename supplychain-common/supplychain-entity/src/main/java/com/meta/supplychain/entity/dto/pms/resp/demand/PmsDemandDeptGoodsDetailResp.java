package com.meta.supplychain.entity.dto.pms.resp.demand;

import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryShipperReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDetailSourceRefReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPurchShipperReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDemandDeptGoodsDetailResp {

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "上一级(pms_demand_goods_detail)单内序号")
    private Long pinsideId;
    @Schema(description = "需求单号")
    private Long billNo;
    @Schema(description = "来源单据送货方式 0-到店，1-到客户")
    private Integer sendMode;

    @Schema(description = "采购批次")
    private String purchBatchNo;


    @Schema(description = "响应整件数量")
    private BigDecimal responseWholeQty;
    @Schema(description = "直流标识")
    private Integer directSign;
    @Schema(description = "订货部门可用库存")
    private BigDecimal orderAtpQty;
    @Schema(description = "需求金额")
    private BigDecimal demandMoney;
    @Schema(description = "响应金额")
    private BigDecimal responseMoney;
    @Schema(description = "订货部门库存")
    private BigDecimal orderStockQty;
    @Schema(description = "需求整件数量")
    private BigDecimal demandWholeQty;

    @Schema(description = "需求金额税金")
    private BigDecimal demandTax;
    @Schema(description = "响应金额税金")
    private BigDecimal responseTax;
    @Schema(description = "门店经营模式 1 直营 2 加盟")
    private Integer deptOperateMode;
    @Schema(description = "来源类型 1.门店要货(订货申请) 2.主动配货(主派)")
    private Integer type;
    @Schema(description = "需求数量")
    private BigDecimal demandQty;
    @Schema(description = "响应零头数量")
    private BigDecimal responseOddQty;
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "订货部门编码")
    private String orderDeptCode;
    @Schema(description = "订货部门名称")
    private String orderDeptName;
    @Schema(description = "订货部门类型(1:门店2:配送)")
    private Integer orderDeptType;
    @Schema(description = "需求零头数量")
    private BigDecimal demandOddQty;
    @Schema(description = "响应数量")
    private BigDecimal responseQty;
    @Schema(description = "出货途径")
    private Integer shippingWay;
    @Schema(description = "响应失败原因")
    private String responseFailReason;
    @Schema(description = "0失败 1成功")
    private Integer status;

    @Schema(description = "商品类型,0主品,1附赠赠品")
    private Integer goodsType;

    @Schema(description = "转单标识,0普通, 1配转采,DemandConvertlEnum")
    private Integer convertFlag;

    @Schema(description = "商品编码")
    private String skuCode;

    @Schema(description = "商品名称")
    private String skuName;


    @Schema(description = "销售单价")
    private BigDecimal salePrice;

    @Schema(description = "流转途径编码")
    private String circulationModeCode;

    @Schema(description = "经营状态编码")
    private String workStateCode;

    @Schema(description = "订货包装率")
    private BigDecimal orderUnitRate;

    @Schema(description = "出货途径列表json,前端使用")
    private String shippingWayJson;

    @Schema(description = "前端json字段，服务端无业务逻辑")
    private String frontendJson;

    @Schema(description = "需求来源关联")
    List<PmsDemandDetailSourceRefResp> demandDetailSourceList = new ArrayList<>();

    @Schema(description = "出货方-供应商")
    List<PmsDemandPurchShipperResp> demandPurchShipperList = new ArrayList<>();

    @Schema(description = "出货方-配送中心")
    List<PmsDemandDeliveryShipperResp> demandDeliveryShipperList = new ArrayList<>();
}
