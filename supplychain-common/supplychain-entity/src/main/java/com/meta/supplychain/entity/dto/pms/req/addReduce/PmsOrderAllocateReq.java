package com.meta.supplychain.entity.dto.pms.req.addReduce;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订货申请单追加追减订单分配")
public class PmsOrderAllocateReq {

    @Schema(description = "配送部门编码")
    private String distCode;

    /**
     * 追加追减规则 1.优先调整现有订单  2. 追加生成新订单
     */
    @Schema(description = "配送部门编码")
    private Integer addReduceRule;

    /** 采购订单有效日期 */
    @Schema(description = "采购订单有效日期")
    private LocalDate purchValidityDate;

    /** 采购订单送货日期 */
    @Schema(description = "采购订单送货日期")
    private LocalDate purchDeliverDate;

    /** 配送订单有效日期 */
    @Schema(description = "配送订单有效日期")
    private LocalDate deliverValidityDate;

    /** 配送订单送货日期 */
    @Schema(description = "配送订单送货日期")
    private LocalDate deliverDeliverDate;

    @Schema(description = "追加追减备注")
    private String addReduceRemark;

    @Schema(description = "追加追减明细")
    List<PmsOrderAllocateDetail> details;

}
