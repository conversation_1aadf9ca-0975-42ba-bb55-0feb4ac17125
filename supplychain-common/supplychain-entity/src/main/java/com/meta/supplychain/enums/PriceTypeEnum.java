package com.meta.supplychain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品类型（1：单规格 2：多规格 3：组合商品）
 *
 * <AUTHOR> cat
 * @date 2024/5/16 15:23
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum  {

    ACCEPT_REFUND(1, "验收退补价格"),
    CONTRACT_PURCH_PRICE(2, "合同商品进价"),
    CONTRACT_SPECIAL_PRICE(3, "合同商品特供价"),
    PROMOTION_PURCH_PRICE(4, "采购促销特供价"),
    PURCHASE_PRICE(5, "档案进价"),
    DIST_FIXED_PRICE(6, "部门配送价(定价)"),
    DIST_MARKUP_PRICE(7, "部门配送价(加价率)"),
    DIST_DEDUCTION_RATE_PRICE(8, "部门配送价(倒扣率)"),
    DISPATCH_PRICE(9, "档案配送价"),
    DEPT_PURCH_PRICE(10, "部门商品进价"),
    ;
    private Integer code;

    private String desc;

}
