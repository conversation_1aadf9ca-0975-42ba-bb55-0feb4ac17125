package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierCategoryPageQueryReq;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;

public interface IMstSupplierCategoryRepositoryService extends IService<MstSupplierCategoryPO> {

    /**
     * 分页查询供应商分类
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<MstSupplierCategoryPO> pageQuerySupplierCategory(Page<MstSupplierCategoryPO> page, MstSupplierCategoryPageQueryReq request);
}
