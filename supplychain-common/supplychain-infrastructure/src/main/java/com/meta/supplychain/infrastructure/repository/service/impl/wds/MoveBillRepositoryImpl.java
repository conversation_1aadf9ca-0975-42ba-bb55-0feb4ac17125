package com.meta.supplychain.infrastructure.repository.service.impl.wds;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.wds.req.QueryMoveBillReq;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.infrastructure.repository.mapper.wds.MoveBillMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveLocBatchDetailRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@AllArgsConstructor
@Service
public class MoveBillRepositoryImpl extends ServiceImpl<MoveBillMapper, MoveBillPO> implements IMoveBillRepository {
    @Resource
    private IMoveLocBatchDetailRepository moveLocBatchDetailRepository;

    @Override
    public Page<MoveBillPO> listPage(QueryMoveBillReq query) {
        return this.baseMapper.listPage(new Page<>(query.getCurrent(), query.getPageSize()),query);
    }

    @Override
    public MoveBillPO queryMoveBillPOWithValid(String moveBillNo) {
        if (StringUtils.isEmpty(moveBillNo)){
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.WD_BIZ_ERROR_008P001);
        }
        MoveBillPO moveBillPO= queryMoveBillPO(moveBillNo);
        if (moveBillPO == null){
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.WD_BIZ_ERROR_008B001);
        }
        return moveBillPO;
    }

    @Override
    public MoveBillPO queryMoveBillPO(String moveBillNo) {
        LambdaQueryWrapper<MoveBillPO> wrapperQuery
                = Wrappers.lambdaQuery(MoveBillPO.class).eq(MoveBillPO::getBillNo, moveBillNo);
        return this.getOne(wrapperQuery);
    }

    @Override
    public boolean updateStatus(String billNo, Integer status, Integer statusOld) {
        return this.lambdaUpdate().eq(MoveBillPO::getBillNo, billNo).eq(MoveBillPO::getStatus, statusOld).set(MoveBillPO::getStatus, status).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMoveBill(MoveBillPO moveBill, List<MoveLocBatchDetailPO> moveLocBatchDetailList) {
        moveLocBatchDetailRepository.saveBatch(moveLocBatchDetailList);
        save(moveBill);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMoveBillWithDel(MoveBillPO moveBill, List<MoveLocBatchDetailPO> moveLocBatchDetailList) {
        LambdaQueryWrapper<MoveLocBatchDetailPO> delDetailWrapper = Wrappers.lambdaQuery(MoveLocBatchDetailPO.class).eq(MoveLocBatchDetailPO::getBillNo, moveBill.getBillNo());
        moveLocBatchDetailRepository.getBaseMapper().delete(delDetailWrapper);
        moveLocBatchDetailRepository.saveBatch(moveLocBatchDetailList);
        updateById(moveBill);
    }
}
