package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meta.supplychain.entity.dto.pms.req.appointment.DockTimeStatsQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPreSubmitStatsDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.DockTimeStatsDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商预约单主表Mapper
 * <AUTHOR>
 * @date 2025-4-20 20:45:32
 */
public interface PmsAppointmentBillMapper extends BaseMapper<PmsAppointmentBillPO> {
    
    /**
     * 多条件查询预约单列表（包含统计信息）
     * @param page 分页对象
     * @param queryDTO 查询条件DTO
     * @return 预约单列表（包含统计信息）
     */
    IPage<PmsAppointmentBillStatsDTO> selectAppointmentBillWithStats(IPage<PmsAppointmentBillStatsDTO> page, @Param("queryDTO") PmsAppointmentBillQueryDTO queryDTO);

    /**
     * 预约单详细查询
     * @param page 分页对象
     * @param queryDTO 查询条件DTO
     * @return 预约单详细信息列表
     */
    IPage<PmsAppointmentDetailDTO> selectAppointmentBillDetails(IPage<PmsAppointmentDetailDTO> page, @Param("queryDTO") PmsAppointmentDetailQueryDTO queryDTO);

    /**
     * 查询停靠点时间统计
     * @param queryDTO 查询条件DTO
     * @return 停靠点时间统计列表
     */
    List<DockTimeStatsDTO> selectDockTimeStats(@Param("queryDTO") DockTimeStatsQueryDTO queryDTO);

    @Delete("DELETE FROM pms_appointment_bill WHERE bill_no = #{billNo};" +
            "DELETE FROM pms_appointment_bill_goods WHERE appointment_bill_no = #{billNo};" +
            "DELETE FROM pms_appointment_bill_purch WHERE appointment_bill_no = #{billNo};")
    void purgeAppointmentBill(@Param("billNo") String billNo);


    PmsAppointmentBillPreSubmitStatsDTO selectAppointmentBillStats(@Param("billNo") String billNo);
} 