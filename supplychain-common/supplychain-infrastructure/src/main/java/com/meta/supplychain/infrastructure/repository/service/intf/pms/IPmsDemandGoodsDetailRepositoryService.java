package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.meta.supplychain.entity.po.pms.PmsDemandGoodsDetailPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandGoodsDetailMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_goods_detail(需求商品明细表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandGoodsDetailRepositoryService extends IService<PmsDemandGoodsDetailPO> {
    PmsDemandGoodsDetailMapper getPmsDemandGoodsDetailMapper();
}
