package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandBillPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandBillMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_bill(订货申请单主表)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandBillRepositoryServiceImpl extends ServiceImpl<PmsDemandBillMapper, PmsDemandBillPO>
    implements IPmsDemandBillRepositoryService {

    @Autowired
    private PmsDemandBillMapper pmsDemandBillMapper;

    @Override
    public PmsDemandBillMapper getPmsDemandBillMapper() {
        return pmsDemandBillMapper;
    }
}




