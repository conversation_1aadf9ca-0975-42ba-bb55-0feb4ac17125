package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandDeptGoodsDetailPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDeptGoodsDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeptGoodsDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_dept_goods_detail(采购订单部门分配表)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandDeptGoodsDetailRepositoryServiceImpl extends ServiceImpl<PmsDemandDeptGoodsDetailMapper, PmsDemandDeptGoodsDetailPO>
    implements IPmsDemandDeptGoodsDetailRepositoryService {

    @Autowired
    private PmsDemandDeptGoodsDetailMapper pmsDemandDeptGoodsDetailMapper;

    @Override
    public PmsDemandDeptGoodsDetailMapper getPmsDemandDeptGoodsDetailMapper() {
        return pmsDemandDeptGoodsDetailMapper;
    }
}




