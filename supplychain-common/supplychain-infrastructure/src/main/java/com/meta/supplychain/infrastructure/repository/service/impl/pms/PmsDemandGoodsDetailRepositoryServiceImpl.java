package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandGoodsDetailPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandGoodsDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandGoodsDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_goods_detail(需求商品明细表)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandGoodsDetailRepositoryServiceImpl extends ServiceImpl<PmsDemandGoodsDetailMapper, PmsDemandGoodsDetailPO>
    implements IPmsDemandGoodsDetailRepositoryService {

    @Autowired
    private PmsDemandGoodsDetailMapper pmsDemandGoodsDetailMapper;

    @Override
    public PmsDemandGoodsDetailMapper getPmsDemandGoodsDetailMapper() {
        return pmsDemandGoodsDetailMapper;
    }
}




