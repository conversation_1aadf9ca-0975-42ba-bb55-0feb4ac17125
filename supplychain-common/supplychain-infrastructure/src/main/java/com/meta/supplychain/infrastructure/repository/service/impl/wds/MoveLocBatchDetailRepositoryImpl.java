package com.meta.supplychain.infrastructure.repository.service.impl.wds;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;
import com.meta.supplychain.infrastructure.repository.mapper.wds.MoveLocBatchDetailMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IMoveLocBatchDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class MoveLocBatchDetailRepositoryImpl extends ServiceImpl<MoveLocBatchDetailMapper, MoveLocBatchDetailPO> implements IMoveLocBatchDetailRepository {


}
