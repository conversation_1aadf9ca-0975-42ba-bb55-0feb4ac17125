package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 供应商RepositoryService实现类
 */
@Service
public class MstSupplierRepositoryServiceImpl extends ServiceImpl<MstSupplierMapper, MstSupplierPO> implements IMstSupplierRepositoryService {
}
