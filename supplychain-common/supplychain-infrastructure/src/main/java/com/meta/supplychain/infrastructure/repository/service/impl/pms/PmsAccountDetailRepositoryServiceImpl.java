package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsAccountBillDetailPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAccountBillDetailMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountDetailRepositoryService;

import org.springframework.stereotype.Service;

@Service
public class PmsAccountDetailRepositoryServiceImpl extends ServiceImpl<PmsAccountBillDetailMapper, PmsAccountBillDetailPO>
        implements PmsAccountDetailRepositoryService {

}