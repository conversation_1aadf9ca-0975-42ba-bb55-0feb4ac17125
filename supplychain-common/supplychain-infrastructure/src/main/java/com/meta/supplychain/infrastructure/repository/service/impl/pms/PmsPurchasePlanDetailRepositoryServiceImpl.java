package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPurchasePlanDetailMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchasePlanDetailRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class PmsPurchasePlanDetailRepositoryServiceImpl extends ServiceImpl<PmsPurchasePlanDetailMapper, PmsPurchasePlanDetailPO>
        implements PmsPurchasePlanDetailRepositoryService {

    @Autowired
    private PmsPurchasePlanDetailMapper pmsPurchasePlanDetailMapper;

    @Override
    public Integer updateDetailNum(Long id, BigDecimal updateQty) {
        return pmsPurchasePlanDetailMapper.updateDetailNum(id, updateQty);
    }

    @Override
    public IPage<PmsPurchasePlanDetailPO> pageDetailList(QueryPurchasePlanBillReq queryPurchasePlanBillReq) {
        IPage<PmsPurchasePlanDetailPO> pageList =
                pmsPurchasePlanDetailMapper.pageDetailList(new Page<>(queryPurchasePlanBillReq.getCurrent(), queryPurchasePlanBillReq.getPageSize()), queryPurchasePlanBillReq);
        return pageList;
    }
}
