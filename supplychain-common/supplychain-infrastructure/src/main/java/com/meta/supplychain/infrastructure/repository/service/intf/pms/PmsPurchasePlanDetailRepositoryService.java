package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【pms_purch_bill(采购订单主表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface PmsPurchasePlanDetailRepositoryService extends IService<PmsPurchasePlanDetailPO> {

    Integer updateDetailNum(Long id, BigDecimal updateQty);

    IPage<PmsPurchasePlanDetailPO> pageDetailList(QueryPurchasePlanBillReq queryPurchasePlanBillReq);

}
