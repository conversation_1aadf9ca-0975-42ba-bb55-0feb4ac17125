package com.meta.supplychain.infrastructure.config;

import cn.linkkids.framework.business.gull.language.manager.ErrorMessageManager;
import cn.linkkids.framework.croods.common.logger.Logs;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 多语言上报hook
 */
@Component
public class LangDataUpdateHook implements ApplicationRunner {

    @Resource
    private ErrorMessageManager errorMessageManager;

    @Resource
    private ThreadPoolTaskExecutor cachedThreadPool;

    @Override
    public void run(ApplicationArguments args) {
        cachedThreadPool.submit(() -> {
            Logs.info("开始上报多语言错误信息");
            try {
                errorMessageManager.pushErrorMessage(false);
            }catch (Exception e){
                Logs.error("多语言错误信息上报失败",e);
            }
            Logs.info("上报多语言错误信息结束");
        });
    }
}
