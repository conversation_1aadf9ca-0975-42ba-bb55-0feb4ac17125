package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandPurchShipperPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandPurchShipperRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandPurchShipperMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_purch_shipper(采购供应商出货方)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandPurchShipperRepositoryServiceImpl extends ServiceImpl<PmsDemandPurchShipperMapper, PmsDemandPurchShipperPO>
    implements IPmsDemandPurchShipperRepositoryService {

    @Autowired
    private PmsDemandPurchShipperMapper pmsDemandPurchShipperMapper;

    @Override
    public PmsDemandPurchShipperMapper getPmsDemandPurchShipperMapper() {
        return pmsDemandPurchShipperMapper;
    }
}




