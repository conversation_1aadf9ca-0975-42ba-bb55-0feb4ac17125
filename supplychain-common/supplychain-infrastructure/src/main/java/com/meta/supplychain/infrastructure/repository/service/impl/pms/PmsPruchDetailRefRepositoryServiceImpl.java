package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPruchDetailRefMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsPruchDetailRefRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/24 10:41
 **/
@Service
public class PmsPruchDetailRefRepositoryServiceImpl extends ServiceImpl<PmsPruchDetailRefMapper, PmsPruchDetailRefPO>
        implements IPmsPruchDetailRefRepositoryService {
    @Autowired
    private PmsPruchDetailRefMapper pmsPruchDetailRefMapper;

    @Override
    public PmsPruchDetailRefMapper getPmsPruchDetailRefMapper() {
        return pmsPruchDetailRefMapper;
    }

    @Override
    public List<PmsPruchDetailRefPO> getRefBySrcBillNo(List<String> srcBillNos) {
        return baseMapper.getRefBySrcBillNo(srcBillNos);
    }

    @Override
    public List<PmsPruchDetailRefPO> getRefByBillNo(String billNo) {
        return baseMapper.getRefByBillNo(billNo);
    }
}