package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PmsPruchDetailRefMapper  extends BaseMapper<PmsPruchDetailRefPO> {
    /**
     * 根据关联单号查询关联信息
     */
    List<PmsPruchDetailRefPO> getRefBySrcBillNo(@Param("srcBillNos") List<String> srcBillNos);

    /**
     * 根据采购订单号查询关联信息
     */
    List<PmsPruchDetailRefPO> getRefByBillNo(@Param("billNo") String billNo);


}