package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.meta.supplychain.entity.po.pms.PmsDemandPurchShipperPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandPurchShipperMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_purch_shipper(采购供应商出货方)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandPurchShipperRepositoryService extends IService<PmsDemandPurchShipperPO> {
    PmsDemandPurchShipperMapper getPmsDemandPurchShipperMapper();
}
