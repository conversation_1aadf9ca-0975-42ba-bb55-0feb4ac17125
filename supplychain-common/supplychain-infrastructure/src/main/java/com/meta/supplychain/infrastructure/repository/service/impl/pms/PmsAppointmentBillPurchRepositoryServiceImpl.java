package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPurchPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAppointmentBillPurchMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsAppointmentBillPurchRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 供应商预约单关联采购订单表Repository实现类
 *
 * <AUTHOR>
 * @date 2025/04/20 21:00
 **/
@Service
public class PmsAppointmentBillPurchRepositoryServiceImpl extends ServiceImpl<PmsAppointmentBillPurchMapper, PmsAppointmentBillPurchPO>
        implements IPmsAppointmentBillPurchRepositoryService {
} 