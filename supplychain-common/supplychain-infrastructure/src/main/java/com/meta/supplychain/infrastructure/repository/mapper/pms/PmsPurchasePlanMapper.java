package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchaseBillReq;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanNumResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefinePO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PmsPurchasePlanMapper extends BaseMapper<PmsPurchasePlanBillPO> {

    PmsPurchasePlanNumResp queryListNum(@Param("params") QueryPurchasePlanBillReq queryPurchasePlanBillReq);

    Integer addPrintCount(@Param("billNoList") List<String> billNoList);

    IPage<PmsPurchasePlanBillPO> pageList(Page<PmsPurchasePlanBillPO> page, @Param("params") QueryPurchasePlanBillReq queryPurchasePlanBillReq);

}
