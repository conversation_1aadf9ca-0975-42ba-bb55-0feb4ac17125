package com.meta.supplychain.infrastructure.repository.service.intf.wds;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.wds.req.QueryWdRefundAcceptReq;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBillPO;

public interface IRefundAcceptBillRepository extends IService<WdRefundAcceptBillPO> {


    IPage<WdRefundAcceptBillPO> queryRefundAcceptList(IPage<WdRefundAcceptBillPO> page, QueryWdRefundAcceptReq query);


    WdRefundAcceptBillPO getByBillNo(String billNo);
}
