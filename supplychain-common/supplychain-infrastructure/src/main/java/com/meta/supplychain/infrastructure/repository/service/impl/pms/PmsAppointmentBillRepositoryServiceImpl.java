package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.pms.req.appointment.DockTimeStatsQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPreSubmitStatsDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.DockTimeStatsDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAppointmentBillMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsAppointmentBillRepositoryService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商预约单主表Repository实现类
 *
 * <AUTHOR>
 * @date 2025/04/20 21:00
 **/
@Service
public class PmsAppointmentBillRepositoryServiceImpl extends ServiceImpl<PmsAppointmentBillMapper, PmsAppointmentBillPO>
        implements IPmsAppointmentBillRepositoryService {

    @Override
    public IPage<PmsAppointmentBillStatsDTO> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO) {
        return baseMapper.selectAppointmentBillWithStats(new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize()), queryDTO);
    }

    @Override
    public IPage<PmsAppointmentDetailDTO> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO) {
        return baseMapper.selectAppointmentBillDetails(new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize()), queryDTO);
    }

    @Override
    public List<DockTimeStatsDTO> selectDockTimeStats(@Param("queryDTO") DockTimeStatsQueryDTO queryDTO) {
        return baseMapper.selectDockTimeStats(queryDTO);
    }

    @Override
    public void purgeAppointmentBill(String billNo) {
        baseMapper.purgeAppointmentBill(billNo);
    }

    @Override
    public PmsAppointmentBillPreSubmitStatsDTO selectAppointmentBillStats(@Param("billNo") String billNo) {
        return baseMapper.selectAppointmentBillStats(billNo);
    }
} 