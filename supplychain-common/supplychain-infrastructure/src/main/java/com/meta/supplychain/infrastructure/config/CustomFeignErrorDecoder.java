package com.meta.supplychain.infrastructure.config;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import feign.Response;
import feign.codec.ErrorDecoder;

import java.net.ConnectException;
import java.net.SocketTimeoutException;

public class CustomFeignErrorDecoder implements ErrorDecoder {
    @Override
    public Exception decode(String methodKey, Response response) {
        Logs.error("feign接口异常methodKey:{},response:{}",methodKey, response);
        if (response.status() == 504) {
            return new SocketTimeoutException("Feign 504 call timeout");
        } else if (response.status() == 503) {
            return new ConnectException("Feign service 503 unavailable");
        } else if (response.status() >= 400 && response.status() < 500) {
            BizExceptions.throwWithMsg(response.status()+" "+response.reason());
        }
        return new RuntimeException("Unknown error: " + response.status()+" "+response.reason());
    }
}
