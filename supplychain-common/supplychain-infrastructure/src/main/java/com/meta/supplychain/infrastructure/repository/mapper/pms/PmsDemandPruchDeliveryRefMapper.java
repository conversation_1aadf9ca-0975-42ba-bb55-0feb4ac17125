package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.entity.po.pms.PmsDemandPruchDeliveryRefPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PmsDemandPruchDeliveryRefMapper  extends BaseMapper<PmsDemandPruchDeliveryRefPO> {

    /**
     * 查询需求来源详细信息
     * 
     * @param refBillNo 关联单号
     * @param refInsideId 关联行号
     * @return 需求来源详细信息
     */
    PmsDemandSourceDetailDTO selectDemandSourceDetail(@Param("refBillNo") String refBillNo, 
                                                     @Param("refInsideId") Long refInsideId);

    void updateBatch(@Param("list") List<PmsDemandPruchDeliveryRefPO> list);
}