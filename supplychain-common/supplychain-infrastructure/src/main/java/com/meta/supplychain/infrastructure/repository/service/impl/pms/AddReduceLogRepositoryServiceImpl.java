package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.AddReduceLogPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.AddReduceLogMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.AddReduceLogRepositoryService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【add_reduce_log】的数据库操作Service实现
* @createDate 2025-05-23 10:00:28
*/
@Service
public class AddReduceLogRepositoryServiceImpl extends ServiceImpl<AddReduceLogMapper, AddReduceLogPO>
    implements AddReduceLogRepositoryService {

}




