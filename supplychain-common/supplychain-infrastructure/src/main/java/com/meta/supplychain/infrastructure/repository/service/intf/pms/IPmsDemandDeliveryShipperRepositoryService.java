package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.meta.supplychain.entity.po.pms.PmsDemandDeliveryShipperPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryShipperMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_transfer_shipper(配送出货方)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandDeliveryShipperRepositoryService extends IService<PmsDemandDeliveryShipperPO> {
    PmsDemandDeliveryShipperMapper getPmsDemandDeliveryShipperMapper();
}
