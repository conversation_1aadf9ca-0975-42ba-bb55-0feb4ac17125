package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsAccountBillPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAccountBillMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountBillRepositoryService;
import org.springframework.stereotype.Service;

@Service
public class PmsAccountBillRepositoryServiceImpl extends ServiceImpl<PmsAccountBillMapper, PmsAccountBillPO>
    implements PmsAccountBillRepositoryService {

    @Override
    public PmsAccountBillPO getAccountByBillNo(String srcBillNo) {
        LambdaQueryWrapper<PmsAccountBillPO> queryWrapper = Wrappers.lambdaQuery(PmsAccountBillPO.class);
        queryWrapper.eq(PmsAccountBillPO::getSrcBillNo, srcBillNo);
        return baseMapper.selectOne(queryWrapper);
    }
}




