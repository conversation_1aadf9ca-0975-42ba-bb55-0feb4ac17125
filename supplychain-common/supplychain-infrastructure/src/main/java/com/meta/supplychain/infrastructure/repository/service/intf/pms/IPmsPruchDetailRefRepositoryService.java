package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPruchDetailRefMapper;

import java.util.List;

public interface IPmsPruchDetailRefRepositoryService  extends IService<PmsPruchDetailRefPO> {
    PmsPruchDetailRefMapper getPmsPruchDetailRefMapper();

    /**
     * 配送订单号查询对应关联采购订单关系
     */
    List<PmsPruchDetailRefPO> getRefBySrcBillNo(List<String> srcBillNos);

    /**
     * 根据采购订单号查询关联关系
     */
    List<PmsPruchDetailRefPO> getRefByBillNo(String billNo);
}
