package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.pms.resp.appointment.PmsAppointmentBillGoodsQueryResultDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillGoodsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商预约单关联商品明细表Repository Service接口
 * <AUTHOR>
 * @date 2025-4-20 20:50:57
 */
public interface IPmsAppointmentBillGoodsRepositoryService extends IService<PmsAppointmentBillGoodsPO> {
    List<PmsAppointmentBillGoodsQueryResultDTO> selectAppointmentBillGoodsByPurchBillNo(@Param("purchBillNo") String purchBillNo);
}