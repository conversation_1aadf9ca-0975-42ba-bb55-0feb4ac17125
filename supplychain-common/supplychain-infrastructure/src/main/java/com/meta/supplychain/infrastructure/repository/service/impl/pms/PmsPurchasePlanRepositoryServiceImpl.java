package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPurchasePlanMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchasePlanRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PmsPurchasePlanRepositoryServiceImpl extends ServiceImpl<PmsPurchasePlanMapper, PmsPurchasePlanBillPO>
        implements PmsPurchasePlanRepositoryService {

    @Autowired
    PmsPurchasePlanMapper pmsPurchasePlanMapper;

    @Override
    public PmsPurchasePlanMapper getPmsPurchasePlanMapper() {
        return pmsPurchasePlanMapper;
    }
}
