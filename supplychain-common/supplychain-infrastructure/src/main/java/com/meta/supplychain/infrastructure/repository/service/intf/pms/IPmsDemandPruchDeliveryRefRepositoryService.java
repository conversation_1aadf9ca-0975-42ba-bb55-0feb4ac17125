package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPruchDeliveryRefReq;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandPruchDeliveryRefResp;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.entity.po.pms.PmsDemandPruchDeliveryRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandPruchDeliveryRefMapper;

import java.util.List;

public interface IPmsDemandPruchDeliveryRefRepositoryService extends IService<PmsDemandPruchDeliveryRefPO> {
    PmsDemandPruchDeliveryRefMapper getPmsDemandPruchDeliveryRefMapper();

    List<PmsDemandPruchDeliveryRefResp> listPmsDemandPruchDeliveryByDemandBillNo(PmsDemandPruchDeliveryRefReq pmsDemandPruchDeliveryRefReq);

    /**
     * 查询需求来源详细信息
     * 
     * @param refBillNo 关联单号
     * @param refInsideId 关联行号
     * @return 需求来源详细信息
     */
    PmsDemandSourceDetailDTO getDemandSourceDetail(String refBillNo, Long refInsideId);
}
