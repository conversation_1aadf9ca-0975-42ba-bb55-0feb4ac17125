package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchasePlanBillReq;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefinePO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface PmsPurchasePlanDetailMapper extends BaseMapper<PmsPurchasePlanDetailPO> {

    Integer updateDetailNum(@Param("id") Long id, @Param("updateQty") BigDecimal updateQty);

    IPage<PmsPurchasePlanDetailPO> pageDetailList(Page<MdContractGoodsDefinePO> page, @Param("params") QueryPurchasePlanBillReq queryPurchasePlanBillReq);

}
