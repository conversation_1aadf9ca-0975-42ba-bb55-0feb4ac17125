package com.meta.supplychain.infrastructure.repository.service.impl.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.infrastructure.repository.mapper.common.BillAdjustLogMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BillAdjustLogRepositoryServiceImpl extends ServiceImpl<BillAdjustLogMapper, BillAdjustLogPO>
        implements IBillAdjustLogRepositoryService {
    @Override
    public List<BillAdjustLogPO> getAdjustLog(String billNo) {
        LambdaQueryWrapper<BillAdjustLogPO> queryWrapper = Wrappers.lambdaQuery(BillAdjustLogPO.class);
        queryWrapper.eq(BillAdjustLogPO::getRefBillNo, billNo);
        queryWrapper.orderByDesc(BillAdjustLogPO::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }

}
