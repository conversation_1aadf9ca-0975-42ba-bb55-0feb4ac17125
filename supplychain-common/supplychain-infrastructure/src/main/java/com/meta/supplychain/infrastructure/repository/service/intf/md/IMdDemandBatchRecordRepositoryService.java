package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.md.MdDemandBatchRecordPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MdDemandBatchRecordMapper;

import java.util.List;

public interface IMdDemandBatchRecordRepositoryService extends IService<MdDemandBatchRecordPO> {
    MdDemandBatchRecordMapper getMdDemandBatchRecordMapper();

    List<MdDemandBatchRecordPO> getRecordsByBatchNos(List<String> batchNos);

}
