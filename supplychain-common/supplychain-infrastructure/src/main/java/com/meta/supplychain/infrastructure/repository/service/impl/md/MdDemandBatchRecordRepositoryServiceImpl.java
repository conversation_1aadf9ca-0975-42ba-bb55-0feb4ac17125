package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MdDemandBatchRecordPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MdDemandBatchRecordMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandBatchRecordRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/02 16:56
 **/
@Service
public class MdDemandBatchRecordRepositoryServiceImpl extends ServiceImpl<MdDemandBatchRecordMapper, MdDemandBatchRecordPO>
        implements IMdDemandBatchRecordRepositoryService {
    @Autowired
    private MdDemandBatchRecordMapper mdDemandBatchRecordMapper;

    @Override
    public MdDemandBatchRecordMapper getMdDemandBatchRecordMapper() {
        return mdDemandBatchRecordMapper;
    }

    @Override
    public List<MdDemandBatchRecordPO> getRecordsByBatchNos(List<String> batchNos) {
        LambdaQueryWrapper<MdDemandBatchRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MdDemandBatchRecordPO::getPurchBatchNo, batchNos);
        return mdDemandBatchRecordMapper.selectList(queryWrapper);
    }
}
