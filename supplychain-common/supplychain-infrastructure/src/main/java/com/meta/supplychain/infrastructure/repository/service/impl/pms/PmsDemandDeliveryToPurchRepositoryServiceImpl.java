package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandDeliveryToPurchPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchMapper;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchRefMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDeliveryToPurchRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/19 11:45
 **/
@Service
public class PmsDemandDeliveryToPurchRepositoryServiceImpl  extends ServiceImpl<PmsDemandDeliveryToPurchMapper, PmsDemandDeliveryToPurchPO>
        implements IPmsDemandDeliveryToPurchRepositoryService {
    @Autowired
    private PmsDemandDeliveryToPurchMapper pmsDemandDeliveryToPurchMapper;

    @Override
    public PmsDemandDeliveryToPurchMapper getPmsDemandDeliveryToPurchMapper() {
        return pmsDemandDeliveryToPurchMapper;
    }
}
