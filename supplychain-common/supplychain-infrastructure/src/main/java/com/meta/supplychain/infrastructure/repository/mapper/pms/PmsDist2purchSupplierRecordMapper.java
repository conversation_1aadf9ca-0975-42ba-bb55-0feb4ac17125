package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meta.supplychain.entity.po.pms.PmsDist2purchSupplierRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PmsDist2purchSupplierRecordMapper  extends BaseMapper<PmsDist2purchSupplierRecordPO> {
    List<PmsDist2purchSupplierRecordPO> list4LastSupplier(@Param("list") List<PmsDist2purchSupplierRecordPO> list);

    List<PmsDist2purchSupplierRecordPO> listDist2PurchSupplier(@Param("list") List<PmsDist2purchSupplierRecordPO> list);

    int clearLastSign(@Param("list") List<PmsDist2purchSupplierRecordPO> list);

    int modifyLastSign(@Param("list") List<PmsDist2purchSupplierRecordPO> list);
}