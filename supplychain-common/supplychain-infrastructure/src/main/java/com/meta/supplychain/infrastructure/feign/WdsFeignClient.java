package com.meta.supplychain.infrastructure.feign;

import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillBatchOptReq;
import com.meta.supplychain.entity.dto.wds.resp.WdBatchOptResp;
import com.meta.supplychain.infrastructure.config.CommonFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * wds http调用
 */
@FeignClient(name = "WdsFeignClient", url = "${feign.host.commonHost:''}", path = "/supplychain-wds-inner", configuration = CommonFeignConfig.class)
public interface WdsFeignClient {

    @PostMapping("/delivery/api/batchAudit")
    @MethodLog("批量审核配送订单")
    WdBatchOptResp batchAudit(WdDeliveryBillBatchOptReq batchOptReq);

}
