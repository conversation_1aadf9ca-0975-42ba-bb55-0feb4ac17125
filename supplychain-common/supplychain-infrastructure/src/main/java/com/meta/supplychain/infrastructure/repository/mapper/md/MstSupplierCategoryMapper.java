package com.meta.supplychain.infrastructure.repository.mapper.md;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierCategoryPageQueryReq;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商分类Mapper接口
 */
@Mapper
public interface MstSupplierCategoryMapper extends BaseMapper<MstSupplierCategoryPO> {

    /**
     * 分页查询供应商分类
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<MstSupplierCategoryPO> pageQuerySupplierCategory(Page<MstSupplierCategoryPO> page, @Param("request") MstSupplierCategoryPageQueryReq request);
}
