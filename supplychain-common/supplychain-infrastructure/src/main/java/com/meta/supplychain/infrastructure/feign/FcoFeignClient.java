package com.meta.supplychain.infrastructure.feign;

import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import com.meta.supplychain.entity.dto.fco.req.StockRebateReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 财务退补
 */
@FeignClient(name = "FcoFeignClient", url = "${feign.host.commonHost}")
public interface FcoFeignClient {
    /**
     * 库存降价补偿生成退补单据
     */
    @PostMapping("/financial-cloud-order/api/rebate/addStockRebate")
    @MethodLog(level = MethodLog.Level.INFO, value = "库存降价补偿生成退补单据")
    void addStockRebate(@RequestBody StockRebateReq req);
}
