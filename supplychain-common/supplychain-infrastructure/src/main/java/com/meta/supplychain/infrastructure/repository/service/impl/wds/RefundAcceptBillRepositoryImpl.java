package com.meta.supplychain.infrastructure.repository.service.impl.wds;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.wds.req.QueryWdRefundAcceptReq;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBillPO;
import com.meta.supplychain.infrastructure.repository.mapper.wds.RefundAcceptHdrMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IRefundAcceptBillRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class RefundAcceptBillRepositoryImpl extends ServiceImpl<RefundAcceptHdrMapper, WdRefundAcceptBillPO> implements IRefundAcceptBillRepository {


    @Override
    public IPage<WdRefundAcceptBillPO> queryRefundAcceptList(IPage<WdRefundAcceptBillPO> page, QueryWdRefundAcceptReq query) {
        return baseMapper.queryRefundAcceptList(page, query);
    }

    @Override
    public WdRefundAcceptBillPO getByBillNo(String billNo) {
        LambdaQueryWrapper<WdRefundAcceptBillPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WdRefundAcceptBillPO::getBillNo, billNo);
        return this.getOne(queryWrapper);
    }
}
