package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsDemandDeliveryToPurchPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchMapper;

public interface IPmsDemandDeliveryToPurchRepositoryService extends IService<PmsDemandDeliveryToPurchPO> {
    PmsDemandDeliveryToPurchMapper getPmsDemandDeliveryToPurchMapper();
}
