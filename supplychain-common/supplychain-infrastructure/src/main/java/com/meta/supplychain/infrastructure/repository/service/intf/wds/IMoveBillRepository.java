package com.meta.supplychain.infrastructure.repository.service.intf.wds;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.wds.req.QueryMoveBillReq;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO;

import java.util.List;

public interface IMoveBillRepository extends IService<MoveBillPO> {

    /**
     *
     * @param queryDto
     * @return
     */
    Page<MoveBillPO> listPage(QueryMoveBillReq queryDto);

    /**
     * 查询移库单 查不到会报错
     * @param moveBillNo 移库单号
     * @return
     */
    MoveBillPO queryMoveBillPOWithValid(String moveBillNo);

    /**
     * 查询移库单
     * @param moveBillNo 移库单号
     * @return
     */
    MoveBillPO queryMoveBillPO(String moveBillNo);

    /**
     * 更新移库单状态
     * @param billNo 移库单号
     * @param status 状态
     * @param statusOld 旧状态
     * @return
     */
    boolean updateStatus(String billNo, Integer status, Integer statusOld);
    /**
     * 保存发货单 新增
     * @param moveBill
     * @param moveLocBatchDetailList
     * @return
     */
    void saveMoveBill(MoveBillPO moveBill, List<MoveLocBatchDetailPO> moveLocBatchDetailList);

    /**
     * 保存发货单 先删后插
     * @param moveBill
     * @param moveLocBatchDetailList
     * @return
     */
    void saveMoveBillWithDel(MoveBillPO moveBill, List<MoveLocBatchDetailPO> moveLocBatchDetailList);

}
