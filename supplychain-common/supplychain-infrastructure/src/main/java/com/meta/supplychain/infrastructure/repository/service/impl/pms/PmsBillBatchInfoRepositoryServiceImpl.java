package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsBillBatchInfoPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsBillBatchInfoRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsBillBatchInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_bill_batch_info(批次效期表)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsBillBatchInfoRepositoryServiceImpl extends ServiceImpl<PmsBillBatchInfoMapper, PmsBillBatchInfoPO>
    implements PmsBillBatchInfoRepositoryService {

}




