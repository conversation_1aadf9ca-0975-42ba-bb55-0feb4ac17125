package com.meta.supplychain.infrastructure.repository.mapper.wds;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.wds.req.QueryMoveBillReq;
import com.meta.supplychain.entity.po.wds.MoveBillPO;
import org.apache.ibatis.annotations.Param;


/**
* <AUTHOR>
* @description 针对表【wd_move_bill(移位单主表)】的数据库操作Mapper
* @createDate 2025-05-22 14:09:12
* @Entity generator.domain.MoveBillPO
*/
public interface MoveBillMapper extends BaseMapper<MoveBillPO> {

    /**
     * 分页查询列表数据
     */
    Page<MoveBillPO> listPage(Page<MoveBillPO> page, @Param("query") QueryMoveBillReq query);
}




