package com.meta.supplychain.infrastructure.repository.service.impl.wds;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.wds.WdShipAcceptSumPO;
import com.meta.supplychain.infrastructure.repository.mapper.wds.WdShipAcceptSumMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptSumRepository;
import org.springframework.stereotype.Service;

/**
 * 配送验收汇总表 Repository 实现类
 *
 * <AUTHOR> generator
 */
@Service
public class WdShipAcceptSumRepositoryImpl extends ServiceImpl<WdShipAcceptSumMapper, WdShipAcceptSumPO> implements IWdShipAcceptSumRepository {

}