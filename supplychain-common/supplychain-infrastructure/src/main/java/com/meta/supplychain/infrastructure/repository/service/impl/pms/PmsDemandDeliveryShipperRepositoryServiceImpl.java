package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandDeliveryShipperPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDeliveryShipperRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryShipperMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_transfer_shipper(配送出货方)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandDeliveryShipperRepositoryServiceImpl extends ServiceImpl<PmsDemandDeliveryShipperMapper, PmsDemandDeliveryShipperPO>
    implements IPmsDemandDeliveryShipperRepositoryService {

    @Autowired
    private PmsDemandDeliveryShipperMapper pmsDemandDeliveryShipperMapper;
    @Override
    public PmsDemandDeliveryShipperMapper getPmsDemandDeliveryShipperMapper() {
        return pmsDemandDeliveryShipperMapper;
    }
}




