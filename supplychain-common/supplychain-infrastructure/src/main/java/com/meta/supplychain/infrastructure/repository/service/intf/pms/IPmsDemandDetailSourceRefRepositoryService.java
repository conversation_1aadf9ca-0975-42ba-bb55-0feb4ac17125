package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsDemandDetailSourceRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDetailSourceRefMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_detail_source(需求来源关联表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandDetailSourceRefRepositoryService extends IService<PmsDemandDetailSourceRefPO> {
    PmsDemandDetailSourceRefMapper getPmsDemandDetailSourceRefMapper();
}
