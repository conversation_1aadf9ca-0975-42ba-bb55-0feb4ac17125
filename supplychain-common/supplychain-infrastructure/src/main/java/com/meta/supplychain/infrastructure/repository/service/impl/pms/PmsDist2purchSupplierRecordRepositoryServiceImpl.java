package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDist2purchSupplierRecordPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDist2purchSupplierRecordMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDist2purchSupplierRecordRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/06/01 23:22
 **/
@Service
public class PmsDist2purchSupplierRecordRepositoryServiceImpl extends ServiceImpl<PmsDist2purchSupplierRecordMapper, PmsDist2purchSupplierRecordPO>
        implements IPmsDist2purchSupplierRecordRepositoryService {
    @Autowired
    private PmsDist2purchSupplierRecordMapper pmsDist2purchSupplierRecordMapper;

    @Override
    public PmsDist2purchSupplierRecordMapper getPmsDist2purchSupplierRecordMapper() {
        return pmsDist2purchSupplierRecordMapper;
    }
}
