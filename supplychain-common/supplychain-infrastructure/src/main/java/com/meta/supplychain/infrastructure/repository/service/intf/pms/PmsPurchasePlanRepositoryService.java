package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPurchasePlanMapper;

/**
* <AUTHOR>
* @description 针对表【pms_purch_bill(采购订单主表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface PmsPurchasePlanRepositoryService extends IService<PmsPurchasePlanBillPO> {

    PmsPurchasePlanMapper getPmsPurchasePlanMapper();
}
