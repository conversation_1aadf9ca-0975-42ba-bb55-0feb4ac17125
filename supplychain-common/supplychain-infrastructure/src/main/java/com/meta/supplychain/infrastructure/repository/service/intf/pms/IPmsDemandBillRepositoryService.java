package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.meta.supplychain.entity.po.pms.PmsDemandBillPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandBillMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_bill(订货申请单主表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandBillRepositoryService extends IService<PmsDemandBillPO> {
    PmsDemandBillMapper getPmsDemandBillMapper();
}
