package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.meta.supplychain.entity.po.pms.PmsDemandDeptGoodsDetailPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeptGoodsDetailMapper;

/**
* <AUTHOR>
* @description 针对表【pms_demand_dept_goods_detail(采购订单部门分配表)】的数据库操作Service
* @createDate 2025-03-31 19:12:33
*/
public interface IPmsDemandDeptGoodsDetailRepositoryService extends IService<PmsDemandDeptGoodsDetailPO> {
    PmsDemandDeptGoodsDetailMapper getPmsDemandDeptGoodsDetailMapper();
}
