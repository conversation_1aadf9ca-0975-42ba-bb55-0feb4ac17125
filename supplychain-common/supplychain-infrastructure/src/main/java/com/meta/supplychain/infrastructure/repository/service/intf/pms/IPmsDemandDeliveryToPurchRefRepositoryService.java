package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsDemandDeliveryToPurchRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchRefMapper;

public interface IPmsDemandDeliveryToPurchRefRepositoryService extends IService<PmsDemandDeliveryToPurchRefPO> {
    PmsDemandDeliveryToPurchRefMapper getPmsDemandDeliveryToPurchRefMapper();
}
