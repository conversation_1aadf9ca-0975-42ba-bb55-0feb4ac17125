package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBankAccountPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBankAccountMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBankAccountRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 商家银行账户RepositoryService实现类
 */
@Service
public class MstSupplierBankAccountRepositoryServiceImpl extends ServiceImpl<MstSupplierBankAccountMapper, MstSupplierBankAccountPO> implements IMstSupplierBankAccountRepositoryService {
}
