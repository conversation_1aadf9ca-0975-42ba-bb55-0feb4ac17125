package com.meta.supplychain.infrastructure.repository.service.impl.wds;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBatchDetailPO;
import com.meta.supplychain.infrastructure.repository.mapper.wds.RefundAcceptBatchDetailMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IRefundAcceptBatchDetailRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@AllArgsConstructor
@Service
public class RefundAcceptBatchDetailRepositoryImpl extends ServiceImpl<RefundAcceptBatchDetailMapper, WdRefundAcceptBatchDetailPO> implements IRefundAcceptBatchDetailRepository {


    @Override
    public boolean deleteByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return false;
        }
        LambdaQueryWrapper<WdRefundAcceptBatchDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WdRefundAcceptBatchDetailPO::getBillNo, billNo);
        return this.remove(queryWrapper);
    }

    @Override
    public List<WdRefundAcceptBatchDetailPO> queryByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WdRefundAcceptBatchDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WdRefundAcceptBatchDetailPO::getBillNo, billNo);
        return this.list(queryWrapper);
    }
}
