package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.pms.PmsDemandDetailSourceRefPO;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDetailSourceRefRepositoryService;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDetailSourceRefMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【pms_demand_detail_source(需求来源关联表)】的数据库操作Service实现
* @createDate 2025-03-31 19:12:33
*/
@Service
public class PmsDemandDetailSourceRefRepositoryServiceImpl extends ServiceImpl<PmsDemandDetailSourceRefMapper, PmsDemandDetailSourceRefPO>
    implements IPmsDemandDetailSourceRefRepositoryService {

    @Autowired
    private PmsDemandDetailSourceRefMapper pmsDemandDetailSourceRefMapper;
    @Override
    public PmsDemandDetailSourceRefMapper getPmsDemandDetailSourceRefMapper() {
        return pmsDemandDetailSourceRefMapper;
    }
}




