package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.pms.resp.appointment.PmsAppointmentBillGoodsQueryResultDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillGoodsPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAppointmentBillGoodsMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsAppointmentBillGoodsRepositoryService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商预约单关联商品明细表Repository实现类
 *
 * <AUTHOR>
 * @date 2025/04/20 21:00
 **/
@Service
public class PmsAppointmentBillGoodsRepositoryServiceImpl extends ServiceImpl<PmsAppointmentBillGoodsMapper, PmsAppointmentBillGoodsPO>
        implements IPmsAppointmentBillGoodsRepositoryService {

    @Override
    public List<PmsAppointmentBillGoodsQueryResultDTO> selectAppointmentBillGoodsByPurchBillNo(@Param("purchBillNo") String purchBillNo) {
        return baseMapper.selectAppointmentBillGoodsByPurchBillNo(purchBillNo);
    }
} 