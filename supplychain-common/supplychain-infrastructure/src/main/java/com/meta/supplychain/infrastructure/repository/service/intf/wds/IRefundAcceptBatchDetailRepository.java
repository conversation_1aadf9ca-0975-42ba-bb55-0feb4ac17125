package com.meta.supplychain.infrastructure.repository.service.intf.wds;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBatchDetailPO;

import java.util.List;

public interface IRefundAcceptBatchDetailRepository extends IService<WdRefundAcceptBatchDetailPO> {


    List<WdRefundAcceptBatchDetailPO> queryByBillNo(String billNo);

    boolean deleteByBillNo(String billNo);
}
