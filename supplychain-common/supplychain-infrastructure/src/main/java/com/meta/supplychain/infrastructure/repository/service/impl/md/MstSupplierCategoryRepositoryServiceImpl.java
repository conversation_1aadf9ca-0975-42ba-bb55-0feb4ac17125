package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierCategoryMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 供应商分类RepositoryService实现类
 */
@Service
public class MstSupplierCategoryRepositoryServiceImpl extends ServiceImpl<MstSupplierCategoryMapper, MstSupplierCategoryPO> implements IMstSupplierCategoryRepositoryService {
}
