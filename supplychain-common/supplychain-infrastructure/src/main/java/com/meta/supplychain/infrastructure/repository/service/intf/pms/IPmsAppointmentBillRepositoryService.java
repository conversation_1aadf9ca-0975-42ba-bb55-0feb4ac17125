package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.pms.req.appointment.DockTimeStatsQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillPreSubmitStatsDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentBillQueryDTO;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsAppointmentDetailQueryDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.appointment.DockTimeStatsDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IPmsAppointmentBillRepositoryService extends IService<PmsAppointmentBillPO> {
    
    /**
     * 数据查询：多条件查询预约单列表（包含统计信息）
     * @param queryDTO 查询条件DTO
     * @return 预约单列表（包含统计信息）
     */
    IPage<PmsAppointmentBillStatsDTO> selectAppointmentBillWithStats(PmsAppointmentBillQueryDTO queryDTO);

    /**
     * 数据查询：预约单详细查询
     * @param queryDTO 查询条件DTO
     * @return 预约单详细信息列表
     */
    IPage<PmsAppointmentDetailDTO> selectAppointmentBillDetails(PmsAppointmentDetailQueryDTO queryDTO);

    List<DockTimeStatsDTO> selectDockTimeStats(@Param("queryDTO") DockTimeStatsQueryDTO queryDTO);

    void purgeAppointmentBill(String billNo);

    PmsAppointmentBillPreSubmitStatsDTO selectAppointmentBillStats(@Param("billNo") String billNo);
}