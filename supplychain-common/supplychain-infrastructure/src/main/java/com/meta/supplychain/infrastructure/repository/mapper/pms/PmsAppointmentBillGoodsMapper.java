package com.meta.supplychain.infrastructure.repository.mapper.pms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meta.supplychain.entity.dto.pms.resp.appointment.PmsAppointmentBillGoodsQueryResultDTO;
import com.meta.supplychain.entity.po.pms.PmsAppointmentBillGoodsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商预约单关联商品明细表Mapper
 * <AUTHOR>
 * @date 2025-4-20 20:50:57
 */
public interface PmsAppointmentBillGoodsMapper extends BaseMapper<PmsAppointmentBillGoodsPO> {

    /**
     * 根据采购订单号查询预约单商品信息
     * @param purchBillNo 采购订单号
     * @return 预约单商品查询结果列表
     */
    List<PmsAppointmentBillGoodsQueryResultDTO> selectAppointmentBillGoodsByPurchBillNo(@Param("purchBillNo") String purchBillNo);

}