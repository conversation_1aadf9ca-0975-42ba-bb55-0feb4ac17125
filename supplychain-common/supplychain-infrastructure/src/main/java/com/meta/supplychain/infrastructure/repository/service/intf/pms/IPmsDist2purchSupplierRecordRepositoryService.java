package com.meta.supplychain.infrastructure.repository.service.intf.pms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.pms.PmsDist2purchSupplierRecordPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDist2purchSupplierRecordMapper;

public interface IPmsDist2purchSupplierRecordRepositoryService  extends IService<PmsDist2purchSupplierRecordPO> {
    PmsDist2purchSupplierRecordMapper getPmsDist2purchSupplierRecordMapper();
}
