<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MdDeliveryDockStrategyMapper">

    <!-- 查询停靠点限制规则 -->
    <select id="selectDeliveryDockLimitRules" resultType="com.meta.supplychain.entity.dto.md.resp.deliveryappointment.MdDeliveryDockLimitVO">
        SELECT
            s.bill_no,
            s.dept_code,
            s.dock_code,
            s.dock_name,
            s.dock_type,
            s.constraint_rule,
            r.inside_id,
            r.start_time,
            r.end_time,
            r.monday,
            r.tuesday,
            r.wednesday,
            r.thursday,
            r.friday,
            r.saturday,
            r.sunday
        FROM 
            md_delivery_dock_limit_rule r
            LEFT JOIN md_delivery_dock_strategy s ON s.dock_code = r.dock_code
        <where>
            r.del_flag = 0
            AND status = 1
            <if test="queryReq.timeInsideId != null">
                AND r.inside_id = #{queryReq.timeInsideId}
            </if>
            <if test="queryReq.deptCodeList != null and queryReq.deptCodeList.size() > 0">
                AND s.dept_code IN
                <foreach collection="queryReq.deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
            <if test="queryReq.dockCode != null and queryReq.dockCode != ''">
                AND s.dock_code = #{queryReq.dockCode}
            </if>
            <if test="queryReq.dockTypeList != null and queryReq.dockTypeList.size() > 0">
                AND s.dock_type IN
                <foreach collection="queryReq.dockTypeList" item="dockType" open="(" separator="," close=")">
                    #{dockType}
                </foreach>
            </if>
            <if test="queryReq.constraintRule != null">
                AND s.constraint_rule = #{queryReq.constraintRule}
            </if>
        </where>
    </select>

</mapper> 