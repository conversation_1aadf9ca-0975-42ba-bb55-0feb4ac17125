<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MdDemandBatchCateGoodsMapper" >

    <select id="listExistGoodsInfoByTimeSegmentAndWhCode"
            resultType="com.meta.supplychain.entity.dto.md.demandbatchstraegy.ExistGoodsInfoQueryDTO">
        SELECT DISTINCT
            g.wh_code,
            t.start_time,
            t.end_time,
            g.type,
            g.code
        FROM
            md_demand_batch_cate_goods g
            INNER JOIN md_demand_batch_time_segment t ON g.batch_time_segment_id = t.id
        where
          g.del_flag = 0
          AND t.del_flag = 0
          AND (g.wh_code, t.start_time, t.end_time, g.type, g.code) IN
          <foreach collection="queryList" item="qo" separator="," open="(" close=")">
              (#{qo.whCode}, #{qo.startTime}, #{qo.endTime}, #{qo.type}, #{qo.code})
          </foreach>
    </select>
</mapper>