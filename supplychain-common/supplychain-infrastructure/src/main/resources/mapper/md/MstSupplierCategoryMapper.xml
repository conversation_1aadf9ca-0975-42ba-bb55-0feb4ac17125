<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierCategoryMapper">

    <!-- 分页查询供应商分类 -->
    <select id="pageQuerySupplierCategory" resultType="com.meta.supplychain.entity.po.md.MstSupplierCategoryPO">
        SELECT
            id,
            cate_code,
            cate_name,
            type,
            parent_code,
            status,
            final_flag,
            del_flag,
            tenant_id,
            create_time,
            create_code,
            create_name,
            create_uid,
            update_time,
            update_code,
            update_name,
            update_uid
        FROM mst_supplier_category
        <where>
            del_flag = 0
            <if test="request.type != null">
                AND type = #{request.type}
            </if>
            <if test="request.parentCode != null and request.parentCode != ''">
                AND parent_code = #{request.parentCode}
            </if>
            <if test="request.status != null">
                AND status = #{request.status}
            </if>
            <if test="request.finalFlag != null">
                AND final_flag = #{request.finalFlag}
            </if>
            <if test="request.rowInfo != null and request.rowInfo != ''">
                AND (cate_code LIKE CONCAT('%', #{request.rowInfo}, '%')
                     OR cate_name LIKE CONCAT('%', #{request.rowInfo}, '%'))
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
