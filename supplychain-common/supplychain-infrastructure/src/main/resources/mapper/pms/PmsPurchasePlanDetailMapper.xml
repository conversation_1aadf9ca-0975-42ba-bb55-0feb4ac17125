<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPurchasePlanDetailMapper">
    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
        <result column="dept_code" property="deptCode" jdbcType="VARCHAR" />
        <result column="dept_name" property="deptName" jdbcType="VARCHAR" />
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR" />
        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
        <result column="inside_id" property="insideId" jdbcType="BIGINT" />
        <result column="sku_type" property="skuType" jdbcType="TINYINT" />
        <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
        <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
        <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
        <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
        <result column="brand_code" property="brandCode" jdbcType="VARCHAR" />
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        <result column="basic_unit" property="basicUnit" jdbcType="VARCHAR" />
        <result column="package_unit" property="packageUnit" jdbcType="VARCHAR" />
        <result column="sku_model" property="skuModel" jdbcType="VARCHAR" />
        <result column="sale_mode" property="saleMode" jdbcType="TINYINT" />
        <result column="input_tax_rate" property="inputTaxRate" jdbcType="DECIMAL" />
        <result column="output_tax_rate" property="outputTaxRate" jdbcType="DECIMAL" />
        <result column="uom_attr" property="uomAttr" jdbcType="TINYINT" />
        <result column="unit_rate" property="unitRate" jdbcType="DECIMAL" />
        <result column="purch_unit_rate" property="purchUnitRate" jdbcType="DECIMAL" />
        <result column="promote_period_price" property="promotePeriodPrice" jdbcType="DECIMAL" />
        <result column="promote_activity_code" property="promoteActivityCode" jdbcType="VARCHAR" />
        <result column="promote_activity_name" property="promoteActivityName" jdbcType="VARCHAR" />
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
        <result column="contract_special_price" property="contractSpecialPrice" jdbcType="DECIMAL" />
        <result column="contract_price" property="contractPrice" jdbcType="DECIMAL" />
        <result column="contract_max_price" property="contractMaxPrice" jdbcType="DECIMAL" />
        <result column="last_purch_price" property="lastPurchPrice" jdbcType="DECIMAL" />
        <result column="dept_goods_price" property="deptGoodsPrice" jdbcType="DECIMAL" />
        <result column="sku_purch_price" property="skuPurchPrice" jdbcType="DECIMAL" />
        <result column="purch_price" property="purchPrice" jdbcType="DECIMAL" />
        <result column="stock_qty" property="stockQty" jdbcType="DECIMAL" />
        <result column="atp_qty" property="atpQty" jdbcType="DECIMAL" />
        <result column="whole_qty" property="wholeQty" jdbcType="DECIMAL" />
        <result column="odd_qty" property="oddQty" jdbcType="DECIMAL" />
        <result column="purch_qty" property="purchQty" jdbcType="DECIMAL" />
        <result column="purch_money" property="purchMoney" jdbcType="DECIMAL" />
        <result column="purch_tax" property="purchTax" jdbcType="DECIMAL" />
        <result column="plan_req_qty" property="planReqQty" jdbcType="DECIMAL" />
        <result column="period_flag" property="periodFlag" jdbcType="TINYINT" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
        <result column="create_code" property="createCode" jdbcType="VARCHAR" />
        <result column="create_uid" property="createUid" jdbcType="BIGINT" />
        <result column="create_name" property="createName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_uid" property="updateUid" jdbcType="BIGINT" />
        <result column="update_name" property="updateName" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="update_code" property="updateCode" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List">
        detail.id, detail.bill_no, detail.dept_code, detail.dept_name, detail.supplier_code, detail.supplier_name,
         detail.inside_id, detail.sku_type, detail.sku_code, detail.sku_name, detail.barcode, detail.goods_no,
         detail.category_code, detail.category_name, detail.brand_code, detail.brand_name, detail.basic_unit,
         detail.package_unit, detail.sku_model, detail.sale_mode, detail.input_tax_rate, detail.output_tax_rate,
         detail.uom_attr, detail.unit_rate, detail.purch_unit_rate, detail.promote_period_price,
         detail.promote_activity_code, detail.promote_activity_name, detail.contract_no, detail.contract_special_price,
         detail.contract_price, detail.contract_max_price, detail.last_purch_price, detail.dept_goods_price,
         detail.sku_purch_price, detail.purch_price, detail.stock_qty, detail.atp_qty, detail.whole_qty,
         detail.odd_qty, detail.purch_qty, detail.purch_money, detail.purch_tax, detail.plan_req_qty,
         detail.period_flag, detail.remark, detail.tenant_id, detail.create_code, detail.create_uid,
         detail.create_name, detail.create_time, detail.update_uid, detail.update_name, detail.update_time,
         detail.update_code, detail.del_flag
    </sql>

    <sql id="List_If_Condition">
        <if test="params.billNoKey != null and params.billNoKey != ''">
            and bill.bill_no like concat('%',#{params.billNoKey},'%')
        </if>
        <if test="params.supplierCodeList != null and params.supplierCodeList.size() > 0">
            and bill.supplier_code in
            <foreach item="item" collection="params.supplierCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="params.deptCodeList != null and params.deptCodeList.size() > 0">
            and bill.dept_code in
            <foreach item="item" collection="params.deptCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="params.statusList != null and params.statusList.size() > 0">
            and bill.status in
            <foreach item="item" collection="params.statusList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <!-- 创建时间 单边查询 -->
        <if test="params.createTimeStart != null">
            AND bill.create_time &gt;= #{params.createTimeStart}
        </if>
        <if test="params.createTimeEnd != null">
            AND bill.create_time &lt;= #{params.createTimeEnd}
        </if>

        <!-- 审核时间 单边查询 -->
        <if test="params.auditTimeStart != null">
            AND bill.audit_time &gt;= #{params.auditTimeStart}
        </if>
        <if test="params.auditTimeEnd != null">
            AND bill.audit_time &lt;= #{params.auditTimeEnd}
        </if>

        <!-- 取消时间 单边查询 -->
        <if test="params.cancelTimeStart != null">
            AND bill.cancel_time &gt;= #{params.cancelTimeStart}
        </if>
        <if test="params.cancelTimeEnd != null">
            AND bill.cancel_time &lt;= #{params.cancelTimeEnd}
        </if>
        <if test="params.createCode != null and params.createCode != ''">
            and (bill.create_code like concat('%', #{params.createCode}, '%')
            or bill.create_name like concat('%', #{params.createCode}, '%'))
        </if>
        <if test="params.auditCode != null and params.auditCode != ''">
            and (bill.audit_code like concat('%', #{params.auditCode}, '%')
            or bill.audit_name like concat('%', #{params.auditCode}, '%'))
        </if>
        <if test="params.planRemark != null and params.planRemark != ''">
            and bill.plan_remark like concat('%',#{params.planRemark},'%')
        </if>
        <if test="params.printFlag != null">
            <choose>
                <when test="params.printFlag == 0">
                    and bill.print_count = 0
                </when>
                <when test="params.printFlag == 1">
                    and bill.print_count > 0
                </when>
            </choose>
        </if>
        <if test="params.skuList != null or params.categoryList != null or params.brandList != null">
            <!-- 子查询筛选 -->
            AND EXISTS (
            SELECT 1
            FROM pms_purch_plan_detail detail
            WHERE bill.bill_no = detail.bill_no
            AND (
            <if test="params.skuList != null and params.skuList.size() > 0">
                detail.sku_code in
                <foreach item="item" collection="params.skuList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="params.categoryList != null and params.categoryList.size() > 0">
                detail.category_code in
                <foreach item="item" collection="params.categoryList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="params.brandList != null and params.brandList.size() > 0">
                detail.brand_code in
                <foreach item="item" collection="params.brandList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            )
            )
        </if>
    </sql>

    <update id="updateDetailNum">
        update pms_purch_plan_detail set
            plan_req_qty = plan_req_qty + #{updateQty}
        where id = #{id}
        and plan_req_qty + #{updateQty} >= 0
    </update>
    <select id="pageDetailList" resultType="com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO">
        select
            <include refid="Base_Column_List"/>
        from pms_purch_plan_detail bill
        left join pms_purchase_plan_detail detail on bill.bill_no = detail.bill_no
        where 1=1
        <include refid="List_If_Condition"/>
        order by bill.create_time desc
    </select>
</mapper>
