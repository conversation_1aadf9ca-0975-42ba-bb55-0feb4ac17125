<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchMapper" >
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsDemandDeliveryToPurchPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
    <result column="inside_id" property="insideId" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="goods_type" property="goodsType" jdbcType="TINYINT" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="sku_model" property="skuModel" jdbcType="VARCHAR" />
    <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="category_code_all" property="categoryCodeAll" jdbcType="VARCHAR" />
    <result column="barcode" property="barcode" jdbcType="VARCHAR" />
    <result column="unit" property="unit" jdbcType="VARCHAR" />
    <result column="package_unit" property="packageUnit" jdbcType="VARCHAR" />
    <result column="input_tax_rate" property="inputTaxRate" jdbcType="DECIMAL" />
    <result column="output_tax_rate" property="outputTaxRate" jdbcType="DECIMAL" />
    <result column="dist_dept_stock_real_qty" property="distDeptStockRealQty" jdbcType="DECIMAL" />
    <result column="dist_dept_stock_atp_qty" property="distDeptStockAtpQty" jdbcType="DECIMAL" />
    <result column="dock_code" property="dockCode" jdbcType="VARCHAR" />
    <result column="dock_name" property="dockName" jdbcType="VARCHAR" />
    <result column="purch_batch_no" property="purchBatchNo" jdbcType="VARCHAR" />
    <result column="send_mode" property="sendMode" jdbcType="TINYINT" />
    <result column="delivery_qty" property="deliveryQty" jdbcType="DECIMAL" />
    <result column="delivery_unit_rate" property="deliveryUnitRate" jdbcType="DECIMAL" />
    <result column="dtp_whole_qty" property="dtpWholeQty" jdbcType="DECIMAL" />
    <result column="dtp_odd_qty" property="dtpOddQty" jdbcType="DECIMAL" />
    <result column="dtp_qty" property="dtpQty" jdbcType="DECIMAL" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="BIGINT" />
    <result column="create_code" property="createCode" jdbcType="VARCHAR" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="BIGINT" />
    <result column="update_code" property="updateCode" jdbcType="VARCHAR" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
    <result column="dist_dept_code" property="distDeptCode" jdbcType="VARCHAR" />
    <result column="dist_dept_name" property="distDeptName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bill_no, inside_id, tenant_id, type, status, goods_type, sku_code, sku_name, 
    sku_model, goods_no, category_code, category_name, category_code_all, barcode, unit, 
    package_unit, input_tax_rate, output_tax_rate, dist_dept_stock_real_qty, dist_dept_stock_atp_qty, 
    dock_code, dock_name, purch_batch_no, send_mode, delivery_qty, delivery_unit_rate, 
    dtp_whole_qty, dtp_odd_qty, dtp_qty, create_uid, create_code, create_name, create_time, 
    update_uid, update_code, update_name, update_time, del_flag,reason,dist_dept_code,dist_dept_name
  </sql>
</mapper>