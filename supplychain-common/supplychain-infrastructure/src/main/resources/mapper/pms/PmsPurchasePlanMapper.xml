<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPurchasePlanMapper">

    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="dept_code" property="deptCode" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR"/>
        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="total_sku_count" property="totalSkuCount" jdbcType="INTEGER"/>
        <result column="total_qty" property="totalQty" jdbcType="DECIMAL"/>
        <result column="total_tax_money" property="totalTaxMoney" jdbcType="DECIMAL"/>
        <result column="total_tax" property="totalTax" jdbcType="DECIMAL"/>
        <result column="validity_date" property="validityDate" jdbcType="DATE"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="plan_remark" property="planRemark" jdbcType="VARCHAR"/>
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR"/>
        <result column="cancel_remark" property="cancelRemark" jdbcType="VARCHAR"/>
        <result column="print_count" property="printCount" jdbcType="INTEGER"/>
        <result column="attachment_url" property="attachmentUrl" jdbcType="VARCHAR"/>
        <result column="audit_code" property="auditCode" jdbcType="VARCHAR"/>
        <result column="audit_name" property="auditName" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="cancel_man_code" property="cancelManCode" jdbcType="VARCHAR"/>
        <result column="cancel_man_name" property="cancelManName" jdbcType="VARCHAR"/>
        <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_code" property="createCode" jdbcType="VARCHAR"/>
        <result column="create_uid" property="createUid" jdbcType="BIGINT"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_code" property="updateCode" jdbcType="VARCHAR"/>
        <result column="update_uid" property="updateUid" jdbcType="BIGINT"/>
        <result column="update_name" property="updateName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        bill.id,bill.bill_no,bill.dept_code,bill.dept_name,bill.supplier_code,bill.supplier_name,
        bill.contract_no,bill.total_sku_count,bill.total_qty,bill.total_tax_money,bill.total_tax,
        bill.validity_date,bill.status,bill.plan_remark,bill.audit_remark,bill.cancel_remark,
        bill.print_count,bill.attachment_url,
        bill.audit_code,bill.audit_name,bill.audit_time,bill.cancel_man_code,bill.cancel_man_name,
        bill.cancel_time,bill.del_flag,bill.create_code,bill.create_uid,bill.create_name,bill.create_time,
        bill.update_code,bill.update_uid,bill.update_name,bill.update_time,bill.tenant_id
    </sql>

    <sql id="List_If_Condition">
        <if test="params.billNoKey != null and params.billNoKey != ''">
            and bill.bill_no like concat('%',#{params.billNoKey},'%')
        </if>
        <if test="params.supplierCodeList != null and params.supplierCodeList.size() > 0">
            and bill.supplier_code in
            <foreach item="item" collection="params.supplierCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="params.deptCodeList != null and params.deptCodeList.size() > 0">
            and bill.dept_code in
            <foreach item="item" collection="params.deptCodeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="params.statusList != null and params.statusList.size() > 0">
            and bill.status in
            <foreach item="item" collection="params.statusList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <!-- 创建时间 单边查询 -->
        <if test="params.createTimeStart != null">
            AND bill.create_time &gt;= #{params.createTimeStart}
        </if>
        <if test="params.createTimeEnd != null">
            AND bill.create_time &lt;= #{params.createTimeEnd}
        </if>

        <!-- 审核时间 单边查询 -->
        <if test="params.auditTimeStart != null">
            AND bill.audit_time &gt;= #{params.auditTimeStart}
        </if>
        <if test="params.auditTimeEnd != null">
            AND bill.audit_time &lt;= #{params.auditTimeEnd}
        </if>

        <!-- 取消时间 单边查询 -->
        <if test="params.cancelTimeStart != null">
            AND bill.cancel_time &gt;= #{params.cancelTimeStart}
        </if>
        <if test="params.cancelTimeEnd != null">
            AND bill.cancel_time &lt;= #{params.cancelTimeEnd}
        </if>
        <if test="params.createCode != null and params.createCode != ''">
            and (bill.create_code like concat('%', #{params.createCode}, '%')
            or bill.create_name like concat('%', #{params.createCode}, '%'))
        </if>
        <if test="params.auditCode != null and params.auditCode != ''">
            and (bill.audit_code like concat('%', #{params.auditCode}, '%')
            or bill.audit_name like concat('%', #{params.auditCode}, '%'))
        </if>
        <if test="params.planRemark != null and params.planRemark != ''">
            and bill.plan_remark like concat('%',#{params.planRemark},'%')
        </if>
        <if test="params.printFlag != null">
            <choose>
                <when test="params.printFlag == 0">
                    and bill.print_count = 0
                </when>
                <when test="params.printFlag == 1">
                    and bill.print_count > 0
                </when>
            </choose>
        </if>
        <if test="params.skuList != null or params.categoryList != null or params.brandList != null">
            <!-- 子查询筛选 -->
            AND EXISTS (
            SELECT 1
            FROM pms_purch_plan_detail detail
            WHERE bill.bill_no = detail.bill_no
            AND (
                <if test="params.skuList != null and params.skuList.size() > 0">
                    detail.sku_code in
                    <foreach item="item" collection="params.skuList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="params.categoryList != null and params.categoryList.size() > 0">
                    detail.category_code in
                    <foreach item="item" collection="params.categoryList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="params.brandList != null and params.brandList.size() > 0">
                    detail.brand_code in
                    <foreach item="item" collection="params.brandList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                )
            )
        </if>
        <choose>
            <when test="params.orderStr != null and params.orderStr != ''">
                order by bill.${params.orderStr}
            </when>
            <otherwise>
                order by bill.create_time desc
            </otherwise>
        </choose>
    </sql>

    <update id="addPrintCount">
        update pms_purch_plan_bill
        set print_count = print_count + 1
        where bill_no in
        <foreach item="item" collection="billNoList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="queryListNum"
            resultType="com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanNumResp">
        select
        count(case when bill.status = 0 then 1 end) as draftNum,
        count(case when bill.status = 1 then 1 end) as pendingAuditNum,
        count(case when bill.status = 2 then 1 end) as auditedNum,
        count(case when bill.status = 3 then 1 end) as partAcceptNum,
        count(case when bill.status = 4 then 1 end) as finishedNum,
        count(case when bill.status = 5 then 1 end) as expiredNum,
        count(case when bill.status = 6 then 1 end) as cancelledNum
        from pms_purch_plan_bill bill
        where 1=1
        <include refid="List_If_Condition"/>
    </select>
    <select id="pageList" resultType="com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO">
        select
            <include refid="Base_Column_List"/>
        from pms_purch_plan_bill bill
        where 1=1
        <include refid="List_If_Condition"/>
    </select>
</mapper>
