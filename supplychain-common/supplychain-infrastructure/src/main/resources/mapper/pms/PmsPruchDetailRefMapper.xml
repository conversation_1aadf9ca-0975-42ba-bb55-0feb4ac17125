<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsPruchDetailRefMapper" >
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
    <result column="inside_id" property="insideId" jdbcType="BIGINT" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="purch_qty" property="purchQty" jdbcType="DECIMAL" />
    <result column="bill_source" property="billSource" jdbcType="TINYINT" />
    <result column="src_bill_no" property="srcBillNo" jdbcType="VARCHAR" />
    <result column="src_inside_id" property="srcInsideId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, tenant_id, bill_no, inside_id, sku_code, sku_name, purch_qty, bill_source, src_bill_no, 
    src_inside_id, create_time, update_time
  </sql>

  <select id="getRefBySrcBillNo" resultType="com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO">
    select
    <include refid="Base_Column_List"/>
    from  pms_purch_detail_ref
    where
    src_bill_no in
    <foreach collection="srcBillNos" separator="," close=")" open="(" item="item">
      #{item}
    </foreach>
  </select>

  <select id="getRefByBillNo" resultType="com.meta.supplychain.entity.po.pms.PmsPruchDetailRefPO">
    select
    <include refid="Base_Column_List"/>
    from  pms_purch_detail_ref
    where bill_no = #{billNo}
  </select>

</mapper>