<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandPruchDeliveryRefMapper" >
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsDemandPruchDeliveryRefPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
    <result column="delivery_shipper_inside_id" property="deliveryShipperInsideId" jdbcType="BIGINT" />
    <result column="purch_shipper_inside_id" property="purchShipperInsideId" jdbcType="BIGINT" />
    <result column="dept_goods_inside_id" property="deptGoodsInsideId" jdbcType="BIGINT" />
    <result column="goods_inside_id" property="goodsInsideId" jdbcType="BIGINT" />
    <result column="ref_bill_no" property="refBillNo" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    <result column="create_uid" property="createUid" jdbcType="BIGINT" />
    <result column="create_code" property="createCode" jdbcType="VARCHAR" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="BIGINT" />
    <result column="update_code" property="updateCode" jdbcType="VARCHAR" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
    <result column="ref_inside_id" property="refInsideId" jdbcType="BIGINT" />
    <result column="qty" property="qty" jdbcType="DECIMAL" />
    <result column="apply_inside_id" property="applyInsideId" jdbcType="BIGINT" />
    <result column="apply_bill_no" property="applyBillNo" jdbcType="VARCHAR" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="transfer_sign" property="transferSign" jdbcType="TINYINT" />
    <result column="transfer_status" property="transferStatus" jdbcType="TINYINT" />
    <result column="transfer_msg" property="transferMsg" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bill_no, delivery_shipper_inside_id, purch_shipper_inside_id, ref_bill_no, type, 
    tenant_id, create_uid, create_code, create_name, create_time, update_uid, update_code, 
    update_name, update_time, del_flag,dept_goods_inside_id,ref_inside_id,qty,apply_bill_no,apply_inside_id,goods_inside_id,sku_code,
    transfer_sign,transfer_status,transfer_msg
  </sql>
  
  <!-- 查询需求来源详细信息 -->
  <select id="selectDemandSourceDetail" resultType="com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO">
    SELECT 
      d.type,
      d.apply_bill_no,
      d.src_inside_id,
      d.remark,
      d.goods_remark,
      d.attribute_code,
      d.attribute_name,
      d.src_shipping_way,
      d.src_shipper_name,
      d.src_shipper_code,
      d.src_manage_category_code,
      d.src_manage_category_name,
      d.src_customer_code,
      d.src_customer_name,
      d.src_demand_unit_rate,
      d.src_demand_whole_qty,
      d.src_demand_odd_qty,
      d.src_demand_qty,
      d.response_unit_rate,
      d.response_whole_qty,
      d.response_odd_qty,
      d.response_qty,
      d.purch_batch_no
    FROM pms_demand_purch_delivery_ref p
    LEFT JOIN pms_demand_detail_source_ref d ON p.bill_no = d.demand_bill_no 
      AND p.apply_bill_no = d.apply_bill_no 
      AND p.apply_inside_id = d.src_inside_id
    WHERE p.ref_bill_no = #{refBillNo}
      AND p.ref_inside_id = #{refInsideId}
    LIMIT 1
  </select>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      UPDATE pms_demand_purch_delivery_ref
      SET transfer_sign=#{item.transferSign},transfer_status=#{item.transferStatus},transfer_msg=#{item.transferMsg}
      WHERE bill_no = #{item.billNo} and ref_bill_no=#{item.refBillNo}
    </foreach>
  </update>
</mapper>