<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandDeliveryToPurchRefMapper" >
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.PmsDemandDeliveryToPurchRefPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
    <result column="delivery_to_purch_inside_id" property="deliveryToPurchInsideId" jdbcType="BIGINT" />
    <result column="dept_goods_inside_id" property="deptGoodsInsideId" jdbcType="BIGINT" />
    <result column="delivery_shipper_inside_id" property="deliveryShipperInsideId" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    <result column="create_uid" property="createUid" jdbcType="BIGINT" />
    <result column="create_code" property="createCode" jdbcType="VARCHAR" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="BIGINT" />
    <result column="update_code" property="updateCode" jdbcType="VARCHAR" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bill_no, delivery_to_purch_inside_id, dept_goods_inside_id, delivery_shipper_inside_id, 
    tenant_id, create_uid, create_code, create_name, create_time, update_uid, update_code, 
    update_name, update_time, del_flag
  </sql>

</mapper>