<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.AddReduceLogMapper">

    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.pms.AddReduceLogPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="billType" column="bill_type" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
            <result property="insideId" column="inside_id" jdbcType="BIGINT"/>
            <result property="purchUnitRate" column="purch_unit_rate" jdbcType="DECIMAL"/>
            <result property="orderQty" column="order_qty" jdbcType="DECIMAL"/>
            <result property="adjustedQty" column="adjusted_qty" jdbcType="DECIMAL"/>
            <result property="expectAdjustQty" column="expect_adjust_qty" jdbcType="DECIMAL"/>
            <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
            <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
            <result property="goodsType" column="goods_type" jdbcType="TINYINT"/>
            <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
            <result property="skuName" column="sku_name" jdbcType="VARCHAR"/>
            <result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
            <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
            <result property="basicUnit" column="basic_unit" jdbcType="VARCHAR"/>
            <result property="unitRate" column="unit_rate" jdbcType="DECIMAL"/>
            <result property="wholeUnit" column="whole_unit" jdbcType="VARCHAR"/>
            <result property="skuModel" column="sku_model" jdbcType="VARCHAR"/>
            <result property="inputTaxRate" column="input_tax_rate" jdbcType="DECIMAL"/>
            <result property="outputTaxRate" column="output_tax_rate" jdbcType="DECIMAL"/>
            <result property="directSign" column="direct_sign" jdbcType="TINYINT"/>
            <result property="addReduceQty" column="add_reduce_qty" jdbcType="DECIMAL"/>
            <result property="adjustQty" column="adjust_qty" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_type,audit_time,
        bill_no,inside_id,purch_unit_rate,
        order_qty,adjusted_qty,expect_adjust_qty,
        dept_code,dept_name,goods_type,
        sku_code,sku_name,goods_no,
        barcode,basic_unit,unit_rate,
        whole_unit,sku_model,input_tax_rate,
        output_tax_rate,direct_sign,add_reduce_qty,
        adjust_qty
    </sql>
</mapper>
