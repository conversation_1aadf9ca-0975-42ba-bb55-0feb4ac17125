<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAccountBillDetailMapper">
    <sql id="Bill_Column_List">
        a.id,a.tenant_id,a.bill_no,a.dc_code,a.dc_name,a.inside_id,a.sku_type,a.sku_code,a.sku_name,
        a.barcode,a.goods_no,a.category_code,a.category_name,a.brand_code,a.brand_name,a.basic_unit,
        a.package_unit,a.sku_model,a.sale_mode,a.input_tax_rate,a.output_tax_rate,a.uom_attr,
        a.period_flag,a.acc_qty,a.acc_price,a.acc_money,a.acc_tax,a.sale_price,a.sale_money,
        a.src_bill_type,a.src_bill_no,a.src_inside_id,a.create_uid,a.create_code,a.create_name,
        a.create_time,a.update_uid,a.update_code,a.update_name,a.update_time,a.del_flag
    </sql>

</mapper>
