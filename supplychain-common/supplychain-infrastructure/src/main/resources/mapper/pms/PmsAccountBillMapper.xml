<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.pms.PmsAccountBillMapper">

    <sql id="Base_Column_List">
        a.id,a.tenant_id,a.bill_no,a.src_bill_no,a.dc_code,a.dc_name,a.dc_acc_code,a.acc_dept_code,a.acc_dept_name,
        a.bill_type,a.bill_direction,a.redeploy_type,a.supplier_code,a.supplier_name,a.contract_no,a.repair_sign_id,
        a.in_dept_code,a.in_dept_name,a.in_acc_code,a.out_dept_code,a.out_dept_name,a.out_acc_code,a.acc_date,a.remark,
        a.create_uid,a.create_code,a.create_name,a.create_time,a.update_uid,a.update_code,a.update_name,a.update_time,a.del_flag
    </sql>

</mapper>
