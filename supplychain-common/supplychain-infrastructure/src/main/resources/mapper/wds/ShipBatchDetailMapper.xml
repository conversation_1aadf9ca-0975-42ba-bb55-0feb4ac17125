<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.ShipBatchDetailMapper">

    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.wds.ShipBatchDetailPO">
            <id property="id" column="id" />
            <result property="whCode" column="wh_code" />
            <result property="whName" column="wh_name" />
            <result property="billNo" column="bill_no" />
            <result property="billDirection" column="bill_direction" />
            <result property="billType" column="bill_type" />
            <result property="billSource" column="bill_source" />
            <result property="insideId" column="inside_id" />
            <result property="mainInsideId" column="main_inside_id" />
            <result property="skuType" column="sku_type" />
            <result property="skuCode" column="sku_code" />
            <result property="skuName" column="sku_name" />
            <result property="barcode" column="barcode" />
            <result property="goodsNo" column="goods_no" />
            <result property="skuModel" column="sku_model" />
            <result property="saleModel" column="sale_model" />
            <result property="basicUnit" column="basic_unit" />
            <result property="packageUnit" column="package_unit" />
            <result property="unitRate" column="unit_rate" />
            <result property="packageRate" column="package_rate" />
            <result property="orderUnitRate" column="order_unit_rate" />
            <result property="inputTaxRate" column="input_tax_rate" />
            <result property="outputTaxRate" column="output_tax_rate" />
            <result property="brandCode" column="brand_code" />
            <result property="brandName" column="brand_name" />
            <result property="categoryCode" column="category_code" />
            <result property="categoryName" column="category_name" />
            <result property="salePrice" column="sale_price" />
            <result property="saleMoney" column="sale_money" />
            <result property="shipQty" column="ship_qty" />
            <result property="shipPrice" column="ship_price" />
            <result property="shipTaxMoney" column="ship_tax_money" />
            <result property="shipTax" column="ship_tax" />
            <result property="deptDistPrice" column="dept_dist_price" />
            <result property="skuDistPrice" column="sku_dist_price" />
            <result property="deptCostPrice" column="dept_cost_price" />
            <result property="lastPurchPrice" column="last_purch_price" />
            <result property="deptSkuPurchPrice" column="dept_sku_purch_price" />
            <result property="promotePeriodPrice" column="promote_period_price" />
            <result property="promoteActivityCode" column="promote_activity_code" />
            <result property="promoteActivityName" column="promote_activity_name" />
            <result property="skuPurchPrice" column="sku_purch_price" />
            <result property="markupRate" column="markup_rate" />
            <result property="remark" column="remark" />
            <result property="periodFlag" column="period_flag" />
            <result property="periodBatchNo" column="period_batch_no" />
            <result property="productDate" column="product_date" />
            <result property="expireDate" column="expire_date" />
            <result property="periodBarcode" column="period_barcode" />
            <result property="locationCode" column="location_code" />
            <result property="locationName" column="location_name" />
            <result property="reversalBillSign" column="reversal_bill_sign" />
            <result property="reversalFlag" column="reversal_flag" />
            <result property="reversalDate" column="reversal_date" />
            <result property="reversalBillNo" column="reversal_bill_no" />
            <result property="reversalInsideId" column="reversal_inside_id" />
            <result property="wholeQty" column="whole_qty" />
            <result property="oddQty" column="odd_qty" />
            <result property="costTaxPrice" column="cost_tax_price" />
            <result property="costPrice" column="cost_price" />
            <result property="costTaxMoney" column="cost_tax_money" />
            <result property="costTax" column="cost_tax" />
            <result property="boxCode" column="box_code" />
            <result property="acceptSign" column="accept_sign" />
            <result property="acceptQty" column="accept_qty" />
            <result property="acceptTaxPrice" column="accept_tax_price" />
            <result property="acceptTaxMoney" column="accept_tax_money" />
            <result property="acceptTax" column="accept_tax" />
            <result property="srcBillType" column="src_bill_type" />
            <result property="srcBillNo" column="src_bill_no" />
            <result property="srcInsideId" column="src_inside_id" />
            <result property="createUid" column="create_uid" />
            <result property="createCode" column="create_code" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="updateUid" column="update_uid" />
            <result property="updateCode" column="update_code" />
            <result property="updateName" column="update_name" />
            <result property="updateTime" column="update_time" />
            <result property="tenantId" column="tenant_id" />
            <result property="delFlag" column="del_flag" />
    </resultMap>

    <sql id="Base_Column_List">
        id,wh_code,wh_name,bill_no,bill_direction,bill_type,
        bill_source,inside_id,main_inside_id,sku_type,sku_code,
        sku_name,barcode,goods_no,sku_model,sale_model,uom_attr,
        basic_unit,package_unit,unit_rate,package_rate,order_unit_rate,
        input_tax_rate,output_tax_rate,brand_code,brand_name,category_code,
        category_name,sale_price,sale_money,ship_qty,ship_price,
        ship_tax_money,ship_tax,dept_dist_price,sku_dist_price,dept_cost_price,
        last_purch_price,dept_sku_purch_price,promote_period_price,promote_activity_code,promote_activity_name,
        sku_purch_price,markup_rate,remark,period_flag,period_batch_no,
        product_date,expire_date,period_barcode,location_code,location_name,
        reversal_bill_sign,reversal_flag,reversal_date,reversal_bill_no,reversal_inside_id,
        whole_qty,odd_qty,cost_tax_price,cost_price,cost_tax_money,
        cost_tax,box_code,accept_sign,accept_qty,accept_tax_price,
        accept_tax_money,accept_tax,src_bill_type,src_bill_no,src_inside_id,
        create_uid,create_code,create_name,create_time,update_uid,
        update_code,update_name,update_time,tenant_id,del_flag
    </sql>
    <!--生成批次插入语句 -->
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into wd_ship_batch_detail (
        wh_code,wh_name,bill_no,bill_direction,bill_type,
        bill_source,inside_id,main_inside_id,sku_type,sku_code,
        sku_name,barcode,goods_no,sku_model,sale_model,uom_attr,
        basic_unit,package_unit,unit_rate,package_rate,order_unit_rate,
        input_tax_rate,output_tax_rate,brand_code,brand_name,category_code,
        category_name,sale_price,sale_money,ship_qty,ship_price,
        ship_tax_money,ship_tax,dept_dist_price,sku_dist_price,dept_cost_price,
        last_purch_price,dept_sku_purch_price,promote_period_price,promote_activity_code,promote_activity_name,
        sku_purch_price,markup_rate,ship_price_model,remark,period_flag,period_batch_no,
        product_date,expire_date,period_barcode,location_code,location_name,
        whole_qty,odd_qty,src_bill_type,src_bill_no,src_inside_id
        )
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
             #{item.whCode,jdbcType=VARCHAR},
             #{item.whName,jdbcType=VARCHAR},
             #{item.billNo,jdbcType=VARCHAR},
             #{item.billDirection,jdbcType=INTEGER},
             #{item.billType,jdbcType=INTEGER},
             #{item.billSource,jdbcType=INTEGER},
             #{item.insideId,jdbcType=BIGINT},
             #{item.mainInsideId,jdbcType=BIGINT},
             #{item.skuType,jdbcType=INTEGER},
             #{item.skuCode,jdbcType=VARCHAR},
             #{item.skuName,jdbcType=VARCHAR},
             #{item.barcode,jdbcType=VARCHAR},
             #{item.goodsNo,jdbcType=VARCHAR},
             #{item.skuModel,jdbcType=VARCHAR},
             #{item.saleModel,jdbcType=INTEGER},
             #{item.uomAttr,jdbcType=INTEGER},
             #{item.basicUnit,jdbcType=VARCHAR},
             #{item.packageUnit,jdbcType=VARCHAR},
             #{item.unitRate,jdbcType=DECIMAL},
             #{item.packageRate,jdbcType=DECIMAL},
             #{item.orderUnitRate,jdbcType=DECIMAL},
             #{item.inputTaxRate,jdbcType=DECIMAL},
             #{item.outputTaxRate,jdbcType=DECIMAL},
             #{item.brandCode,jdbcType=VARCHAR},
             #{item.brandName,jdbcType=VARCHAR},
             #{item.categoryCode,jdbcType=VARCHAR},
             #{item.categoryName,jdbcType=VARCHAR},
             #{item.salePrice,jdbcType=DECIMAL},
             #{item.saleMoney,jdbcType=DECIMAL},
             #{item.shipQty,jdbcType=DECIMAL},
             #{item.shipPrice,jdbcType=DECIMAL},
             #{item.shipTaxMoney,jdbcType=DECIMAL},
             #{item.shipTax,jdbcType=DECIMAL},
             #{item.deptDistPrice,jdbcType=DECIMAL},
             #{item.skuDistPrice,jdbcType=DECIMAL},
             #{item.deptCostPrice,jdbcType=DECIMAL},
             #{item.lastPurchPrice,jdbcType=DECIMAL},
             #{item.deptSkuPurchPrice,jdbcType=DECIMAL},
             #{item.promotePeriodPrice,jdbcType=DECIMAL},
             #{item.promoteActivityCode,jdbcType=VARCHAR},
             #{item.promoteActivityName,jdbcType=VARCHAR},
             #{item.skuPurchPrice,jdbcType=DECIMAL},
             #{item.markupRate,jdbcType=DECIMAL},
             #{item.shipPriceModel,jdbcType=INTEGER},
             #{item.remark,jdbcType=VARCHAR},
             #{item.periodFlag,jdbcType=INTEGER},
             #{item.periodBatchNo,jdbcType=VARCHAR},
             #{item.productDate,jdbcType=TIMESTAMP},
             #{item.expireDate,jdbcType=TIMESTAMP},
             #{item.periodBarcode,jdbcType=VARCHAR},
             #{item.locationCode,jdbcType=VARCHAR},
             #{item.locationName,jdbcType=VARCHAR},
             #{item.wholeQty,jdbcType=DECIMAL},
             #{item.oddQty,jdbcType=DECIMAL},
             #{item.srcBillType,jdbcType=VARCHAR},
             #{item.srcBillNo,jdbcType=VARCHAR},
             #{item.srcInsideId,jdbcType=BIGINT}
            )
                 </foreach>
    </insert>

</mapper>
