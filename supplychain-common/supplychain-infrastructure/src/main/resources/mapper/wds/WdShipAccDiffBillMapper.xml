<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.WdShipAccDiffBillMapper">



    <select id="queryDiffPage" resultType="com.meta.supplychain.entity.po.wds.WdShipAccDiffBillPO">
        SELECT
        a.*
        FROM wd_ship_acc_diff_bill a
        <if test="query.skuKeyWord!=null and query.skuKeyWord != ''">
            LEFT JOIN wd_ship_acc_diff_batch_detail  b ON a.bill_no = b.bill_no
        </if>
        <where>
            <if test="query.deptCode != null and query.deptCode != ''">
                AND a.dept_code = #{query.deptCode}
            </if>
            <if test="query.whCode != null and query.whCode != ''">
                AND a.wh_code = #{query.whCode}
            </if>
            <if test="query.whCodeList!=null and query.whCodeList.size() >0 ">
                and a.wh_code in
                <foreach collection="query.whCodeList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.status != null">
                AND a.status = #{query.status}
            </if>

            <if test="query.timeType!=null ">
                    <if test="query.timeType == 0 ">
                        <if test="query.beginTime!=null and query.beginTime!='' ">
                            and  a.create_time &gt;= #{query.beginTime}
                        </if>
                        <if test="query.endTime!=null and query.endTime!=''">
                            and  a.create_time &lt;= #{query.endTime}
                        </if>
                    </if>
                    <if test="query.timeType == 1 ">
                        <if test="query.beginTime!=null and query.beginTime!='' ">
                            and  a.approve_time &gt;= #{query.beginTime}
                        </if>
                        <if test="query.endTime!=null and query.endTime!=''">
                            and  a.approve_time &lt;= #{query.endTime}
                        </if>
                    </if>
                    <if test="query.timeType == 2 ">
                        <if test="query.beginTime!=null and query.beginTime!='' ">
                            and  a.ship_time &gt;= #{query.beginTime}
                        </if>
                        <if test="query.endTime!=null and query.endTime!=''">
                            and  a.ship_time &lt;= #{query.endTime}
                        </if>
                    </if>

            </if>

            <if test="query.statusList!=null and query.statusList.size() >0 ">
                and a.status in
                <foreach collection="query.statusList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.billNo != null and query.billNo != ''">
                AND a.bill_no = #{query.billNo}
            </if>
            <if test="query.shipBillNo != null and query.shipBillNo != ''">
                AND a.ship_bill_no = #{query.shipBillNo}
            </if>
            <if test="query.deptCodeList!=null and query.deptCodeList.size() >0 ">
                and a.dept_code in
                <foreach collection="query.deptCodeList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.acceptManKey!=null and query.acceptManKey != ''">
                and (
            a.create_code like concat('%',#{query.acceptManKey},'%')
                or
                a.create_name like concat('%',#{query.acceptManKey},'%')
                )
            </if>
            <if test="query.approveManKey!=null and query.approveManKey != ''">
                and (
            a.approve_man_code like concat('%',#{query.approveManKey},'%')
                or
                a.approve_man_name like concat('%',#{query.approveManKey},'%')
                )
            </if>

            <if test="query.remark!=null and query.remark != ''">
                and  a.remark like concat('%',#{query.remark},'%')
            </if>

            <if test="query.accRemark!=null and query.accRemark != ''">
                and  a.acc_remark like concat('%',#{query.accRemark},'%')
            </if>

            <if test="query.skuKeyWord!=null and query.skuKeyWord != ''">
                and  (
                b.sku_code like concat('%',#{query.skuKeyWord},'%')
                or b.sku_name like concat('%',#{query.skuKeyWord},'%')
                or b.barcode like concat('%',#{query.skuKeyWord},'%')
                or b.goods_no like concat('%',#{query.skuKeyWord},'%')
                )
            </if>
        </where>
        <if test="query.skuKeyWord!=null and query.skuKeyWord != ''">
            group by a.bill_no
        </if>
        ORDER BY a.create_time DESC
    </select>

</mapper>