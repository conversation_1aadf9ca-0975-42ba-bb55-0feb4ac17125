<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.WdDeliveryDetailPickRefMapper">


    <sql id="Base_Column_List">
        id, wh_code, wh_name, bill_no, in_dept_code, in_dept_name, inside_id, sku_type, 
        sku_code, sku_name, delivery_qty, ship_bill_no, ship_inside_id, ship_qty, 
        accept_bill_no, accept_inside_id, accept_qty, create_uid, create_code, create_name, 
        create_time, update_uid, update_code, update_name, update_time, tenant_id, del_flag
    </sql>

    <update id="batchUpdatePickQty">
        UPDATE wd_delivery_bill_detail_pick_ref
        <trim prefix="SET pick_qty = CASE id" suffix="END" suffixOverrides=",">
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.pickQty}
            </foreach>
        </trim>
        ,update_time=now()
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <select id="getShipDeliveryDetail" resultType="com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPickRefPO">
        select a.bill_no, a.inside_id,a.dist_qty,a.pick_qty,b.status
        from wd_delivery_bill_detail_pick_ref a
                 left join wd_pick_bill b on a.pick_bill_no = b.bill_no
        where
           a.bill_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>