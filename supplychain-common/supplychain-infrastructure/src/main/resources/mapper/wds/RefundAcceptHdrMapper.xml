<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.RefundAcceptHdrMapper">



    <select id="queryRefundAcceptList" resultType="com.meta.supplychain.entity.po.wds.WdRefundAcceptBillPO">
        SELECT bill.*
        FROM wd_refund_accept_bill bill
        <if test="query.skuKeyWord != null and query.skuKeyWord != ''">
            left join wd_refund_accept_batch_detail detail on bill.bill_no = detail.bill_no
        </if>
        <where>
            <!-- 配送中心编码 -->
            <if test="query.whCode != null and query.whCode != ''">
                AND bill.wh_code = #{query.whCode}
            </if>

            <if test="query.billType != null">
                AND bill.bill_type = #{query.billType}
            </if>

            <!-- 配送中心编码列表 -->
            <if test="query.whCodeList != null and !query.whCodeList.isEmpty()">
                AND bill.wh_code IN
                <foreach collection="query.whCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 时间类型对应的日期字段 -->
            <choose>
                <when test="query.timeType == 0">
                    <!-- 制单日期 -->
                    <if test="query.beginTime != null and query.beginTime != ''">
                        AND bill.create_time >=  CONCAT(#{query.beginTime},  ' 00:00:00')
                    </if>
                    <if test="query.endTime != null and query.endTime != ''">
                        AND bill.create_time &lt;=  CONCAT(#{query.endTime},  ' 23:59:59')
                    </if>
                </when>
                <when test="query.timeType == 1">
                    <!-- 收货日期 -->
                    <if test="query.beginTime != null and query.beginTime != ''">
                        AND bill.submit_time >=  CONCAT(#{query.beginTime}, ' 00:00:00')
                    </if>
                    <if test="query.endTime != null and query.endTime != ''">
                        AND bill.submit_time &lt;=  CONCAT(#{query.endTime},  ' 23:59:59')
                    </if>
                </when>
            </choose>

            <!-- 单据状态 -->
            <if test="query.status != null">
                AND bill.status = #{query.status}
            </if>

            <!-- 单据状态列表 -->
            <if test="query.statusList != null and !query.statusList.isEmpty()">
                AND bill.status IN
                <foreach collection="query.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 退货收货单号 -->
            <if test="query.billNo != null and query.billNo != ''">
                AND bill.bill_no LIKE CONCAT('%', #{query.billNo}, '%')
            </if>

            <!-- 关联配送单号 -->
            <if test="query.deliveryBillNo != null and query.deliveryBillNo != ''">
                AND bill.delivery_bill_no LIKE CONCAT('%', #{query.deliveryBillNo}, '%')
            </if>

            <!-- 退货方部门编码 -->
            <if test="query.inDeptCode != null and query.inDeptCode != ''">
                AND bill.in_dept_code = #{query.inDeptCode}
            </if>

            <!-- 退货方部门编码列表 -->
            <if test="query.inDeptCodeList != null and !query.inDeptCodeList.isEmpty()">
                AND bill.in_dept_code IN
                <foreach collection="query.inDeptCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 提交人关键字（模糊匹配） -->
            <if test="query.submitManKeyWord != null and query.submitManKeyWord != ''">
                AND (bill.submit_man_code LIKE CONCAT('%', #{query.submitManKeyWord}, '%')
                OR bill.submit_man_name LIKE CONCAT('%', #{query.submitManKeyWord}, '%'))
            </if>

            <!-- 商品关键字（模糊匹配） -->
            <if test="query.skuKeyWord != null and query.skuKeyWord != ''">
                AND (detail.sku_code LIKE CONCAT('%', #{query.skuKeyWord}, '%')
                OR detail.sku_name LIKE CONCAT('%', #{query.skuKeyWord}, '%')
                OR detail.barcode LIKE CONCAT('%', #{query.skuKeyWord}, '%')
                OR detail.goods_no LIKE CONCAT('%', #{query.skuKeyWord}, '%'))
            </if>

            <!-- 备注信息模糊匹配 -->
            <if test="query.remark != null and query.remark != ''">
                AND bill.remark LIKE CONCAT('%', #{query.remark}, '%')
            </if>
        </where>
        order by bill.create_time desc
    </select>
</mapper>
