<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.MoveLocBatchDetailMapper">

    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.wds.MoveLocBatchDetailPO">
            <id property="id" column="id" />
            <result property="whCode" column="wh_code" />
            <result property="whName" column="wh_name" />
            <result property="billNo" column="bill_no" />
            <result property="insideId" column="inside_id" />
            <result property="skuCode" column="sku_code" />
            <result property="skuName" column="sku_name" />
            <result property="barcode" column="barcode" />
            <result property="goodsNo" column="goods_no" />
            <result property="unit" column="unit" />
            <result property="uomAttr" column="uom_attr" />
            <result property="skuModel" column="sku_model" />
            <result property="basicUnit" column="basic_unit" />
            <result property="packageUnit" column="package_unit" />
            <result property="unitRate" column="unit_rate" />
            <result property="packageRate" column="package_rate" />
            <result property="periodFlag" column="period_flag" />
            <result property="outLocationCode" column="out_location_code" />
            <result property="outLocationName" column="out_location_name" />
            <result property="inLocationCode" column="in_location_code" />
            <result property="inLocationName" column="in_location_name" />
            <result property="wholeQty" column="whole_qty" />
            <result property="oddQty" column="odd_qty" />
            <result property="moveQty" column="move_qty" />
            <result property="periodBatchNo" column="period_batch_no" />
            <result property="productDate" column="product_date" />
            <result property="expireDate" column="expire_date" />
            <result property="periodBarcode" column="period_barcode" />
            <result property="remark" column="remark" />
            <result property="srcBillType" column="src_bill_type" />
            <result property="srcBillNo" column="src_bill_no" />
            <result property="srcInsideId" column="src_inside_id" />
            <result property="createUid" column="create_uid" />
            <result property="createCode" column="create_code" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="updateUid" column="update_uid" />
            <result property="updateCode" column="update_code" />
            <result property="updateName" column="update_name" />
            <result property="updateTime" column="update_time" />
            <result property="tenantId" column="tenant_id" />
            <result property="delFlag" column="del_flag" />
    </resultMap>

    <sql id="Base_Column_List">
        id,wh_code,wh_name,bill_no,inside_id,sku_code,
        sku_name,barcode,goods_no,unit,uom_attr,
        sku_model,basic_unit,package_unit,unit_rate,package_rate,
        period_flag,out_location_code,out_location_name,in_location_code,in_location_name,
        whole_qty,odd_qty,move_qty,period_batch_no,product_date,
        expire_date,period_barcode,remark,src_bill_type,src_bill_no,
        src_inside_id,create_uid,create_code,create_name,create_time,
        update_uid,update_code,update_name,update_time,tenant_id,
        del_flag
    </sql>
</mapper>
