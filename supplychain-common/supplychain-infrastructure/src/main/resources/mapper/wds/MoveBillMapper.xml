<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.wds.MoveBillMapper">

    <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.wds.MoveBillPO">
            <id property="id" column="id" />
            <result property="whCode" column="wh_code" />
            <result property="whName" column="wh_name" />
            <result property="billNo" column="bill_no" />
            <result property="remark" column="remark" />
            <result property="moveType" column="move_type" />
            <result property="srcBillNo" column="src_bill_no" />
            <result property="totalMoveQty" column="total_move_qty" />
            <result property="status" column="status" />
            <result property="cancelTime" column="cancel_time" />
            <result property="cancelManCode" column="cancel_man_code" />
            <result property="cancelManName" column="cancel_man_name" />
            <result property="confirmTime" column="confirm_time" />
            <result property="confirmManCode" column="confirm_man_code" />
            <result property="confirmManName" column="confirm_man_name" />
            <result property="createUid" column="create_uid" />
            <result property="createCode" column="create_code" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="updateUid" column="update_uid" />
            <result property="updateCode" column="update_code" />
            <result property="updateName" column="update_name" />
            <result property="updateTime" column="update_time" />
            <result property="tenantId" column="tenant_id" />
            <result property="delFlag" column="del_flag" />
    </resultMap>

    <sql id="Base_Column_List">
        bill.id,bill.wh_code,bill.wh_name,bill.bill_no,bill.remark,bill.move_type,
        bill.out_location_code,bill.out_location_name,bill.in_location_code,bill.in_location_name,
        bill.src_bill_no,bill.total_move_qty,bill.status,bill.cancel_time,bill.cancel_man_code,
        bill.cancel_man_name,bill.confirm_time,bill.confirm_man_code,bill.confirm_man_name,bill.create_uid,bill.create_code,bill.create_name,
        bill.create_time,bill.update_uid,bill.update_code,bill.update_name,bill.update_time,
        bill.tenant_id,bill.del_flag
    </sql>

    <select id="listPage"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wd_move_bill bill
        <where>
            bill.del_flag = 0
            <if test="query.whCode != null and query.whCode != ''">
                and bill.wh_code = #{query.whCode,jdbcType=VARCHAR}
            </if>
            <if test="query.whCodeList!=null and query.whCodeList.size() >0 ">
                and bill.wh_code in
                <foreach collection="query.whCodeList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.billNo != null and query.billNo != ''">
                and bill.bill_no = #{query.billNo,jdbcType=VARCHAR}
            </if>
            <if test="query.skuSearchKey != null and query.skuSearchKey != ''">
                and EXISTS (
                    SELECT 1
                    FROM wd_move_loc_batch_detail dtl
                    WHERE bill.bill_no = dtl.bill_no and bill.del_flag = dtl.del_flag
                    and  (
                    dtl.sku_code = #{query.skuSearchKey}
                    or dtl.sku_name like concat('%',#{query.skuSearchKey},'%')
                    or dtl.barcode = #{query.skuSearchKey}
                    or dtl.goods_no = #{query.skuSearchKey}
                    )
                )
            </if>
            <if test="query.remark != null and query.remark != ''">
                and bill.remark LIKE concat('%', #{query.remark,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.status != null">
                AND bill.status = #{query.status}
            </if>
            <if test="query.statusList!=null and query.statusList.size() >0 ">
                and bill.status in
                <foreach collection="query.statusList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.moveType != null">
                AND bill.move_type = #{query.moveType}
            </if>
            <if test="query.timeType == 0">
                <if test="query.startTime != null and query.startTime != ''">
                    AND bill.create_time >= #{query.startTime}
                </if>
                <if test="query.endTime != null and query.endTime != ''">
                    AND bill.create_time <![CDATA[<=]]> #{query.endTime}
                </if>
            </if>
            <if test="query.timeType == 1">
                <if test="query.startTime != null and query.startTime != ''">
                    AND bill.confirm_time >= #{query.startTime}
                </if>
                <if test="query.endTime != null and query.endTime != ''">
                    AND bill.confirm_time <![CDATA[<=]]> #{query.endTime}
                </if>
            </if>
            <if test="query.createManSearch != null and query.createManSearch != ''">
                and (
                bill.create_code LIKE concat('%', #{query.createManSearch,jdbcType=VARCHAR}, '%')
                or bill.create_name LIKE concat('%', #{query.createManSearch,jdbcType=VARCHAR}, '%')
                )
            </if>
            ORDER BY bill.create_time DESC
        </where>
    </select>
</mapper>
