<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>supplychain-common</artifactId>
        <groupId>com.meta.supplychain.common</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.meta.supplychain.common.component</groupId>
    <artifactId>supplychain-component</artifactId>

    <name>supplychain-component</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.infrastructure</groupId>
            <artifactId>supplychain-infrastructure</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.util</groupId>
            <artifactId>supplychain-util</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-lock</artifactId>
        </dependency>
    </dependencies>
</project>
