package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierCategoryPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierCategoryDTO;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 供应商分类Domain Service测试类
 */
@ExtendWith(MockitoExtension.class)
class MdSupplierCategoryDomainServiceImplTest {

    @Mock
    private IMstSupplierCategoryRepositoryService repositoryService;

    @InjectMocks
    private MdSupplierCategoryDomainServiceImpl domainService;

    private MstSupplierCategoryDTO testDTO;
    private MstSupplierCategoryPO testPO;

    @BeforeEach
    void setUp() {
        testDTO = new MstSupplierCategoryDTO();
        testDTO.setId(1L);
        testDTO.setCode("TEST001");
        testDTO.setName("测试分类");
        testDTO.setType(1);
        testDTO.setFinalFlag(1);

        testPO = new MstSupplierCategoryPO();
        testPO.setId(1L);
        testPO.setCateCode("TEST001");
        testPO.setCateName("测试分类");
        testPO.setType(1);
        testPO.setFinalFlag(1);
    }

    @Test
    void testCreateSupplierCategory() {
        // 测试新增（ID为null）
        MstSupplierCategoryDTO newDTO = new MstSupplierCategoryDTO();
        newDTO.setCode("NEW001");
        newDTO.setName("新分类");
        newDTO.setType(1);
        newDTO.setFinalFlag(1);

        MstSupplierCategoryPO savedPO = new MstSupplierCategoryPO();
        savedPO.setId(2L);
        savedPO.setCateCode("NEW001");

        when(repositoryService.save(any(MstSupplierCategoryPO.class))).thenAnswer(invocation -> {
            MstSupplierCategoryPO po = invocation.getArgument(0);
            po.setId(2L);
            return true;
        });

        Long result = domainService.createOrUpdateSupplierCategory(newDTO);

        assertNotNull(result);
        verify(repositoryService).save(any(MstSupplierCategoryPO.class));
        verify(repositoryService, never()).updateById(any(MstSupplierCategoryPO.class));
    }

    @Test
    void testUpdateSupplierCategory() {
        // 测试更新（ID不为null）
        when(repositoryService.updateById(any(MstSupplierCategoryPO.class))).thenReturn(true);

        Long result = domainService.createOrUpdateSupplierCategory(testDTO);

        assertEquals(testDTO.getId(), result);
        verify(repositoryService).updateById(any(MstSupplierCategoryPO.class));
        verify(repositoryService, never()).save(any(MstSupplierCategoryPO.class));
    }

    @Test
    void testDeleteSupplierCategoryByCode() {
        when(repositoryService.remove(any())).thenReturn(true);

        assertDoesNotThrow(() -> domainService.deleteSupplierCategoryByCode("TEST001"));

        verify(repositoryService).remove(any());
    }

    @Test
    void testPageQuerySupplierCategory() {
        // 准备测试数据
        MstSupplierCategoryPageQueryReq request = new MstSupplierCategoryPageQueryReq();
        request.setCurrent(1L);
        request.setPageSize(10L);
        request.setType(1);
        request.setRowInfo("测试");

        List<MstSupplierCategoryPO> records = Arrays.asList(testPO);
        Page<MstSupplierCategoryPO> mockPage = new Page<>(1, 10);
        mockPage.setRecords(records);
        mockPage.setTotal(1);

        when(repositoryService.pageQuerySupplierCategory(any(Page.class), any(MstSupplierCategoryPageQueryReq.class)))
                .thenReturn(mockPage);

        PageResult<MstSupplierCategoryDTO> result = domainService.pageQuerySupplierCategory(request);

        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRows().size());
        assertEquals("TEST001", result.getRows().get(0).getCode());
        assertEquals("测试分类", result.getRows().get(0).getName());

        verify(repositoryService).pageQuerySupplierCategory(any(Page.class), any(MstSupplierCategoryPageQueryReq.class));
    }
}
