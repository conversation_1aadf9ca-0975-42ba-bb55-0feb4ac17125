package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.meta.supplychain.common.component.domain.md.intf.IMdOrderStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizDeptRuleService;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.req.QueryUpDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryUpDeptListResp;
import com.meta.supplychain.entity.dto.md.base.DeptInfo;
import com.meta.supplychain.entity.dto.md.component.deptrule.*;
import com.meta.supplychain.entity.dto.md.orderstrategy.MdOrderStrategyQueryDTO;
import com.meta.supplychain.entity.dto.md.req.order.MdOrderStrategyTypeCodeQuery;
import com.meta.supplychain.entity.dto.md.resp.order.MdOrderStrategyDTO;
import com.meta.supplychain.entity.dto.pms.apply.CheckFranchiseDTO;
import com.meta.supplychain.entity.po.md.MdOrderStrategyPO;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.MdDeptTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdOrderStrategyRepositoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务部门规则控制器
 *
 * <AUTHOR>
 * @date 2025/04/18 17:17
 **/
@Service
@Slf4j
public class SupplychainBizDeptRuleServiceImpl implements ISupplychainBizDeptRuleService {
//    @Override
//    public List<DockDTO> listDock(DockQueryDTO dockQueryDTO) {
//        return null;
//    }
//
//    @Override
//    public List<DockUseInfoDTO> listDockUseInfo(DockUseQueryDTO dockUseQueryDTO) {
//        return null;
//    }

    @Autowired
    private IMdOrderStrategyRepositoryService mdOrderStrategyRepositoryService;

    @Autowired
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    /**
     * 校验部门匹配的订单订货策略时间是否允许要货
     * resp： 可以要货的部门
     *
     * @param deptCodes     部门
     * @param localTime 时间
     */
    @Override
    public List<String> checkOrderStrategyTime(List<String> deptCodes, LocalTime localTime) {

        //按部门分组 将每个部门内店组群优先级排序好
        Map<String, List<DeptInfo>> deptGoodsMap = new HashMap<>();
        for (String deptCode : deptCodes) {
            deptGoodsMap.put(deptCode, Lists.newArrayList(new DeptInfo(deptCode, MdDeptTypeEnum.DEPT.getCode())));
        }
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(deptCodes)
                .build();
        QueryBatchDeptListResp queryBatchDeptListResp = baseDataSystemFeignClient.queryUpDeptListBatch(req);
        if (queryBatchDeptListResp != null && CollectionUtils.isNotEmpty(queryBatchDeptListResp.getRows())) {
            for (QueryBatchDeptListResp.Rows row : queryBatchDeptListResp.getRows()) {
                List<DeptInfo> sortedDeptCodeList
                        = row.getDeptGroupList().stream().sorted(Comparator.comparing(QueryBatchDeptListResp.DeptGroup::getLevel))
                        .map(QueryBatchDeptListResp.DeptGroup::getCode).map(code -> new DeptInfo(code, MdDeptTypeEnum.DEPT_GROUP.getCode())).collect(Collectors.toList());
                deptGoodsMap.getOrDefault(row.getCode(), Lists.newArrayList()).addAll(sortedDeptCodeList);
            }
        }

        // 1. 查询部门订单订货策略
        List<MdOrderStrategyTypeCodeQuery> typeCodeList = new ArrayList<>();
        for (List<DeptInfo> value : deptGoodsMap.values()) {
            for (DeptInfo deptInfo : value) {
                typeCodeList.add(MdOrderStrategyTypeCodeQuery.builder()
                        .deptType(deptInfo.getDeptType())
                        .deptCode(deptInfo.getDeptCode())
                        .build());
            }
        }
        List<MdOrderStrategyPO> stOrderStrategies = mdOrderStrategyRepositoryService.listByDeptTypeAndCodeList(typeCodeList);
        Map<String, MdOrderStrategyPO> strategyPOMap = stOrderStrategies.stream().collect(Collectors.toMap(MdOrderStrategyPO::getDeptCode, Function.identity()));
        Map<String, MdOrderStrategyPO> stOrderStrategyMap = new HashMap<>();
        for (Map.Entry<String, List<DeptInfo>> entry : deptGoodsMap.entrySet()) {
            //按优先级筛选出第一个符合的策略
            Optional<MdOrderStrategyPO> first = entry.getValue().stream().map(deptInfo -> {
                        if (strategyPOMap.containsKey(deptInfo.getDeptCode())
                                && strategyPOMap.get(deptInfo.getDeptCode()).getDeptType().equals(deptInfo.getDeptType())) {
                            return strategyPOMap.get(deptInfo.getDeptCode());
                        } else {
                            return null;
                        }
                    }).filter(Objects::nonNull).findFirst();
            first.ifPresent(mdOrderStrategyPO -> stOrderStrategyMap.put(entry.getKey(), mdOrderStrategyPO));
        }

        for (Map.Entry<String, MdOrderStrategyPO> strategyPOEntry : stOrderStrategyMap.entrySet()) {
            MdOrderStrategyPO orderStrategyPO = strategyPOEntry.getValue();
            if(!(localTime.isAfter(orderStrategyPO.getStartTime()) && localTime.isBefore(orderStrategyPO.getEndTime()))) {
                log.warn("部门{}的订单订货策略时间超出阈值", strategyPOEntry.getKey());
                deptCodes.remove(strategyPOEntry.getKey());
            }
        }

        return deptCodes;
    }

    /**
     * 校验加盟额度
     *
     * @param checkFranchiseDTOS 部门金额列表
     * @return 通过校验的部门
     */
    @Override
    public List<String> checkFranchise(List<CheckFranchiseDTO> checkFranchiseDTOS) {
        return checkFranchiseDTOS.stream().map(CheckFranchiseDTO::getDeptCode).collect(Collectors.toList());
    }
}
