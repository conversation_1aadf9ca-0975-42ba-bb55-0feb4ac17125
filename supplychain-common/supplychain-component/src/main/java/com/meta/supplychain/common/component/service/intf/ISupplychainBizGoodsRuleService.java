package com.meta.supplychain.common.component.service.intf;

import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSimpleInfo;
import com.meta.supplychain.entity.dto.goods.resp.ManageAndCirculationResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsStrategyResp;
import com.meta.supplychain.entity.dto.md.req.DemandGoodsStrategyAggreReq;
import com.meta.supplychain.entity.dto.md.resp.DemandGoodsStrategyAggreResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.*;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;

import java.util.List;
import java.util.Map;

/**
 * 业务商品及部门商品规则控制器
 */
public interface ISupplychainBizGoodsRuleService {

    /**
     * 配送与采购场景商品公共判断
     * 1.商品在目录：商品字段inCatalog为1
     * 2.进项税率不能为空（可以为0）：商品字段isInputRate
     * 3.商品档案进价为空不允许录入：商品字段purchPrice
     * 4.不允许录入/导入经营方式=联营、租赁、AB生鲜、生鲜归集码
     * 商品字段：operationModel =》 [2, 8, 10, 12]存在这个数组集合不合规
     * 5.类别码商品不允许录入：商品字段categoryCodeFlag不为1
     * 6.商品订货策略如果配置中央控制不允许录入
     * 7.经营状态：订货活退货分场景
     */
    List<ApplyGoodsDTO> checkGoodsBasic(List<GoodsQueryResp> goodsQueryRespList);

    ApplyGoodsDTO checkGoodsBasic(GoodsQueryResp goodsQueryResp);

    /**
     * 采购的商品判断，需聚合 checkApplyGoods()
     * 1.商品必须是合同品
     * 2.判断是否有第一供应商
     * 3.判断流转途径
     *
     * 订货/退货申请
     */
    List<ApplyGoodsDTO> checkApplyGoods(GoodsQueryDTO goodsQueryDTO);

    /**
     * 配送的商品判断，需聚合 checkApplyGoods()
     * 1.流转途径
     * 2.订货的情况下选择订货属性为追加或追减的时候
     * 按照商品+出货方+到店日期（到店日期）匹配存在的需求批次如果不存在需求批次，则不允许配送
     *
     * 订货/退货申请
     */
//    List<ApplyGoodsDTO> checkApplyGoods4Delivery(GoodsQueryDTO goodsQueryDTO);

    /**
     * 查询及校验商品经营状态(进货/退货、出货/退货)
     *
     * 经营状态公共逻辑 订/退货申请、需求单、采购计划单、采购/退订单、配送订单、门店调拨
     *
     * 查询及校验商品流转途径(允许门店调拨、销售、配送送货/退货、供应商送货/退货、直流、直流供应商)
     *
     * 流转途径公共逻辑 订/退货申请、需求单、配转采、采购计划单、采购/退订单、配送订单、门店调拨
     */
    List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO);

    ManageAndCirculationDTO getManageAndCirculation();

    ManageAndCirculationResp getWorkAndCirculation();

    /**
     * 查询商品列表
     * @param goodsQueryDTO
     * @return
     */
    List<GoodsQueryResp> listGoodsInfo(GoodsQueryDTO goodsQueryDTO);

    /**
     * 根据skuCode列表，批量查询商品信息
     * @param skuCodeList 商品编码列表
     * @return 商品信息Map key：skuCode value：商品信息
     */
    Map<String, GoodsSimpleInfo> getSkuSimpleInfoMap(List<String> skuCodeList);

    /**
     * 需求批次策略-时段:生成采购批次，返回采购批次信息
     * 需求批次策略-日期，本次不做
     * 订货申请、需求单
     */
    void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO);

    /**
     * 判断商品是否允许追加追减
     * 订货申请
     */
    List<GoodsAddReduceDTO> checkGoodsAddReduce(GoodsAddReduceQueryDTO goodsAddReduceQuery);

    /**
     * 供应商档案-是否直流合单
     *
     * 需求单、追加追减
     */
    List<MstSupplierVO> listSupplier(SupplierQueryDTO supplierQuery);

    /**
     * 批量获取采购价
     *
     * 订货申请、需求单、配转采、采购计划、配转采、采购验收/采购退货、配送订单、配送发货、波次管理
     */
    List<DeptGoodsPurchPriceDTO> listDeptGoodsPurchPrice(DeptGoodsPurchPriceQueryDTO deptGoodsPriceQuery);

    /**
     * 批量获取配送价(需要返回快照价格信息)
     *
     * 订货申请、需求单、配转采、采购计划、配转采、采购验收/采购退货、配送订单、配送发货、波次管理
     */
    List<DeptGoodsDeliveryPriceDTO> listDeptGoodsDeliveryPrice(DeptGoodsDeliveryPriceQueryDTO deptGoodsPriceQuery);

    /**
     * 查询合同、部门、商品
     *
     * 订货/退货申请、需求单、配送转采购、采购计划单、采购/退订单、采购验收/退货
     */
    List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO contractGoodsDeptQuery);

    /**
     * 查询合同商品下生效数据
     * @param contractGoodsDeptQuery
     * @return
     */
    List<ContractGoods4DeptResultDTO> queryContractGoods4Dept(ContractGoodsQueryDTO contractGoodsDeptQuery);

    /**
     * 查询合同商品下生效数据
     * @param checkDTO
     * @return
     */
    List<ContractGoodsDeptCheckResultDTO> checkContracGoods(ContractGoodsDeptCheckDTO checkDTO);

    /**
     * 查询商品库存
     * @param stockQuery
     * @return
     */
    List<GoodsStockDTO> listGoodsStock(StockQueryDTO stockQuery);

    /**
     * 查询商品与订单订货策略
     * @param req
     * @return
     */
    RequireGoodsResp getGoodsStrategy(RequireGoodsReq req);

    GoodsStrategyResp getPurchGoodsStrategy(GoodsStrageReq req);

    List<GoodsStrategyResp> getBatchGoodsStrategy(List<GoodsStrageReq> req);

    /**
     * 查询主供应商
     * @param mainSupplierInfoQuery
     * @return 合同商品
     */
    List<RequireGoodsResp.RequireSupplier> getMainSupplierInfo(MainSupplierInfoQueryDTO mainSupplierInfoQuery);

    List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoList(QueryGoodsPriceInfoReq queryGoodsPriceInfoReq);

    List<GoodsDeliverPriceInfoBatchResp> goodsDeliverPriceInfoListBatch(QueryGoodsPriceInfoBatchReq queryGoodsPriceInfoReq);

    List<GoodsPurchPriceInfoResp> goodsPurchPriceInfoList(QueryGoodsPurchPriceInfoReq queryGoodsAttrReqParams);

    /**
     * 查询商品供应商
     * @param queryGoodsSuppReq
     * @return
     */
    Result<List<GoodsSuppResp>> queryGoodsSupp(QueryGoodsSuppReq queryGoodsSuppReq);

    /**
     * 根据商品品类解析品类全路径
     *  1.查询商品品类等级系统参数
     *  2.解析商品品类全路径
     * @param categoryCodeList 商品品类结合
     * @return 品类全路径
     */
    List<CategoryCodeAll> getCategoryCodeAll(List<String> categoryCodeList);

    DemandGoodsStrategyAggreResp getDemandGoodsStrategyAggre(DemandGoodsStrategyAggreReq demandGoodsStrategyAggreReq);
}
