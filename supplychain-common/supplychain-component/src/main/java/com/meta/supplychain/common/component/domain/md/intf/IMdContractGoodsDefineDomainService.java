package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractGoodsDefineDTO;
import com.meta.supplychain.entity.dto.md.req.contractdef.*;
import com.metadata.idaas.client.model.LoginUserDTO;

import java.util.List;

public interface IMdContractGoodsDefineDomainService {

    /**
     * 保存合同商品定义
     * @param mdCgoodsDefinition
     * @return
     */
    MdContractGoodsDefineDTO save(MdContractDefSaveReq mdCgoodsDefinition);

    /**
     * 分页查询合同商品定义
     * @param param
     * @return
     */
    PageResult<MdContractGoodsDefineDTO> pageList(MdContractDefPageListReq param);

    /**
     * 合同商品定义列表提交审核
     * @param param
     */
    void toApprove(MdContractDefToApproveReq param);

    /**
     * 合同商品定义作废
     * @param param
     */
    void cancel(MdContractDefCancelReq param);

    /**
     * 合同商品定义审核
     * @param param
     */
    void approve(MdContractDefApproveReq param, LoginUserDTO loginUser);

    /**
     * 合同商品定义详情
     * @param param
     * @return
     */
    MdContractGoodsDefineDTO getDetail(MdContractDefDetailReq param);

    /**
     * 执行合同商品定义
     * @param billNo
     */
    void executeBill(String billNo);

    /**
     * 获取定时执行的合同商品定义
     */
    List<MdContractGoodsDefineDTO> queryDefineList();

}