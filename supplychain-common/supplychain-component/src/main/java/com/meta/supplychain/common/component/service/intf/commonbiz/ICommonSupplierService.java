package com.meta.supplychain.common.component.service.intf.commonbiz;

import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import java.util.List;
import java.util.Map;

public interface ICommonSupplierService {
    MstSupplierVO queryByCode(String code);

    List<MstSupplierVO> querySupplierList(List<String> codeList);

    Map<String,MstSupplierVO> querySupplierMap(List<String> codeList);
}
