package com.meta.supplychain.common.component.service.impl.commonbiz;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.google.common.collect.Lists;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.entity.dto.goods.req.*;
import com.meta.supplychain.entity.dto.goods.resp.*;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.infrastructure.feign.GoodsFeignClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/11 21:49
 **/
@Component
public class CommonGoodsServiceImpl implements ICommonGoodsService {

    @Resource
    private GoodsFeignClient goodsFeignClient;

    @Override
    public List<GoodsQueryResp> getGoodsInfo(GoodsQueryReq goodsQueryReq) {
        List<GoodsQueryResp> goodsInfo = new ArrayList<>();
        try{
            goodsInfo = goodsFeignClient.getGoodsInfo(goodsQueryReq);
            return goodsInfo;
        }catch (Exception e){
            e.printStackTrace();
            Logs.error("CommonGoodsServiceImpl.getGoodsInfo.error.",e);
            BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCMD999B013.getCode(),MdErrorCodeEnum.SCMD999B013.getErrorMsg() + "replenishmentList");
            return goodsInfo;
        }
    }

    @Override
    public List<GoodsCategoryQueryResp> getGoodsCategory(GoodsCategoryQueryReq categoryQueryReq) {
        List<GoodsCategoryQueryResp> categoryQueryResps = goodsFeignClient.getGoodsCategory(categoryQueryReq);
        return categoryQueryResps;
    }

    @Override
    public ManageAndCirculationResp getManageAndCircul(ManageAndCirculationReq goodsQueryReq) {
        ManageAndCirculationResp manageAndCircul = goodsFeignClient.getManageAndCircul(goodsQueryReq);
        return manageAndCircul;
    }

    @Override
    public List<GoodsSimpleInfo> skuSimpleList(String skuCodes, String attributeNameFlag) {
        List<GoodsSimpleInfo> goodsSimpleInfos = goodsFeignClient.skuSimpleList(skuCodes, attributeNameFlag);
        return goodsSimpleInfos;
    }

    @Override
    public List<GoodsSimpleInfo> skuSimpleList(List<String> skuCodes, String attributeNameFlag) {
        List<GoodsSimpleInfo> goodsSimpleInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuCodes)) {
            return goodsSimpleInfos;
        }
        if (skuCodes.size() <= 100) {
            String skuString = String.join(",", skuCodes);
            goodsSimpleInfos.addAll(goodsFeignClient.skuSimpleList(skuString, attributeNameFlag));
        } else {
            for (int i = 0; i < skuCodes.size(); i += 100) {
                List<String> subList = skuCodes.subList(i, Math.min(i + 100, skuCodes.size()));
                String skuString = String.join(",", subList);
                goodsSimpleInfos.addAll(goodsFeignClient.skuSimpleList(skuString, attributeNameFlag));
            }
        }
        return goodsSimpleInfos;
    }

    @Override
    public GoodsSimpleInfoResp simpleSearchQuery(GoodsSearchQueryReq param) {
        try {
            return goodsFeignClient.simpleSearchQuery(param).getRows();
        } catch (Exception e) {
            Logs.error("CommonGoodsServiceImpl.simpleSearchQuery.error.",e);
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P022);
        }
        return null;
    }

    @Override
    public void updatePurchPrice(UpdatePurchPriceReq updatePurchPriceReq) {
        goodsFeignClient.updatePurchPrice(updatePurchPriceReq);
    }

    @Override
    public List<GoodsQuerySearchResp> getGoodsInfoSearch(GoodsQuerySearchReq goodsQueryReq) {
        return goodsFeignClient.getGoodsInfoSearch(goodsQueryReq);
    }

    @Override
    public List<GoodsCategoryResp> getGoodsCategoryBatch(GoodsCategoryBatch goodsCategoryBatch) {
        return goodsFeignClient.getGoodsCategoryBatch(goodsCategoryBatch);
    }
}
