package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import com.meta.supplychain.common.component.domain.md.intf.IMdApplyOrderAttributeDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizBillRuleService;
import com.meta.supplychain.entity.dto.md.component.bizrule.*;
import com.meta.supplychain.entity.dto.md.req.orderattribute.QueryAttributeReq;
import com.meta.supplychain.entity.dto.replenishment.req.*;
import com.meta.supplychain.entity.dto.replenishment.resp.BatchGenerateBillNoResp;
import com.meta.supplychain.entity.dto.replenishment.resp.GenerateBillNoResp;
import com.meta.supplychain.entity.dto.replenishment.resp.TranscodingStrategyResp;
import com.meta.supplychain.entity.po.md.MdApplyOrderAttributePO;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.infrastructure.feign.ReplenishmentAdminFeignClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务单据规则控制器
 *
 * <AUTHOR>
 * @date 2025/04/18 17:16
 **/
@Service
public class SupplychainBizBillRuleServiceImpl implements ISupplychainBizBillRuleService {

    @Autowired
    private ReplenishmentAdminFeignClient replenishmentAdminFeignClient;

    @Autowired
    private IMdApplyOrderAttributeDomainService mdApplyOrderAttributeDomainService;

    @Override
    public String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode) {
        QueryBillNoRuleReq queryBillNoRuleReq = new QueryBillNoRuleReq();
        queryBillNoRuleReq.setDeptCode(deptCode);
        queryBillNoRuleReq.setBillType(mdBillNoBillTypeEnum.getCode());
        GenerateBillNoResp generateBillNoResp = replenishmentAdminFeignClient.generateBillNo(queryBillNoRuleReq);

        String billNo = generateBillNoResp.getBillNumber();

        return billNo;
    }

    @Override
    public List<BatchGenerateBillNoResp> getBatchBillNo(List<BatchGenerateBillNoDTO> list) {
        QueryBatchBillNoRuleReq req = new QueryBatchBillNoRuleReq();
        List<QueryBillNoRule> queryBillNoRuleList = new ArrayList<>();
        req.setList(queryBillNoRuleList);
        for (BatchGenerateBillNoDTO batchGenerateBillNoDTO : list) {
            QueryBillNoRule queryBillNoRule = new QueryBillNoRule();
            queryBillNoRule.setBillType(batchGenerateBillNoDTO.getMdBillNoBillTypeEnum().getCode());
            queryBillNoRule.setDeptCode(batchGenerateBillNoDTO.getDeptCode());
            queryBillNoRule.setUnique(batchGenerateBillNoDTO.getUnique());
            queryBillNoRuleList.add(queryBillNoRule);
        }
        return replenishmentAdminFeignClient.generateBatchBillNo(req);
    }

    @Override
    public List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        QueryAttributeReq req = generateQueryAttributeReq(orderAttrQueryDTO);
        req.setApplyCate(0);


        List<MdApplyOrderAttributePO> mdApplyOrderAttributePOS = mdApplyOrderAttributeDomainService.listApplyOrder(req);
        List<OrderAttrDTO> copy = CglibCopier.copy(mdApplyOrderAttributePOS, OrderAttrDTO.class);

        return copy;
    }

    private QueryAttributeReq generateQueryAttributeReq(OrderAttrQueryDTO orderAttrQueryDTO){
        QueryAttributeReq req = new QueryAttributeReq();
        //失效的订退货属性也要查询，以防规则失效，但是业务还在用
//        req.setStatus(1);
        if(StringUtils.isNotEmpty(orderAttrQueryDTO.getAttributeCode())){
            List<String> attributeCodes = new ArrayList<>();
            attributeCodes.add(orderAttrQueryDTO.getAttributeCode());
            req.setAttributeCodes(attributeCodes);
        }
        return req;
    }

    @Override
    public List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        QueryAttributeReq req = generateQueryAttributeReq(orderAttrQueryDTO);
        req.setApplyCate(1);

        List<MdApplyOrderAttributePO> mdApplyOrderAttributePOS = mdApplyOrderAttributeDomainService.listApplyOrder(req);
        List<OrderReturnAttrDTO> copy = CglibCopier.copy(mdApplyOrderAttributePOS, OrderReturnAttrDTO.class);

        return copy;
    }

    @Override
    public DemandProcessStrategyAutoDTO getAutoDemandProcessStrategy(DemandProcessStrategyAutoQueryDTO demandProcessStrategyAutoQueryDTO) {
        return null;
    }

    @Override
    public DemandProcessStrategyManualDTO getManualDemandProcessStrategy(DemandProcessStrategyManualQueryDTO demandProcessStrategyManualQueryDTO) {
        return null;
    }

    /**
     * 获取转码策略
     *
     * @param skuCodeList
     * @param isRmGoods
     * @return
     */
    @Override
    public List<TranscodingStrategyResp> getStrategyListFromGoodsCode(List<String> skuCodeList, boolean isRmGoods) {
        TranscodingStrategyByGoodsReq req = new TranscodingStrategyByGoodsReq();
        req.setSkuCodeList(skuCodeList);
        req.setRmGoods(isRmGoods);
        return replenishmentAdminFeignClient.getStrategyListFromGoodsCode(req);
    }

    /**
     * 新增转码单
     *
     * @param req
     * @return
     */
    @Override
    public String saveTscBill(TranscodingBillReq req) {
        return replenishmentAdminFeignClient.saveTscBill(req);
    }

    /**
     * 冲红转码单
     *
     * @param req
     * @return
     */
    @Override
    public String reverseTscBill(TranscodingReq req) {
        return replenishmentAdminFeignClient.reverseTscBill(req);
    }
}
