package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.EventChannelEnum;
import com.meta.supplychain.infrastructure.kafka.KafkaProvider;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RefreshScope
public class BillEventServiceFactory {
    @Resource
    private KafkaProvider<Object> kafkaProvider;

    private static final Map<String,IOrderEventService> BILL_EVENT_MAP = new HashMap<>();

    public BillEventServiceFactory(List<IOrderEventService> orderEventServices){
        orderEventServices.forEach(item->BILL_EVENT_MAP.put(item.supportType(),item));
    }

    public IOrderEventService getOrDefault(String supportType){
        String supportTypeDefault = String.join(SysConstants.COLON_DELIMITER, EventChannelEnum.CHAIN.getCode(), BillActionTypeEnum.SupportSign.DEFAULT.getSign());
        return BILL_EVENT_MAP.getOrDefault(supportType,BILL_EVENT_MAP.get(supportTypeDefault));
    }

    /**
     *
     * @param billNo 单据号
     * @param billActionType 操作类型
     * @param obj 单据对象
     */
    @Retryable(
            value = {Exception.class},         // 对哪些异常进行重试
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 1000)  // 每次重试间隔 1 秒
    )
    public void publishEvent(String billNo,BillActionTypeEnum billActionType,Object obj){
        EventMsgBaseDTO<Object> msgBaseDTO = new EventMsgBaseDTO<>();
        msgBaseDTO.setBillNo(billNo);
        msgBaseDTO.setData(obj);
        msgBaseDTO.setBillActionType(billActionType.getCode());
        kafkaProvider.sendEventMessage(msgBaseDTO, SysConstants.SUPPLYCHAIN_BILL_TOPIC);
    }
}