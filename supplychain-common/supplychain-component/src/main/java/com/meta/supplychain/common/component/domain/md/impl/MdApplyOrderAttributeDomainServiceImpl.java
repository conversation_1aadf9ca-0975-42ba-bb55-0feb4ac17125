package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdApplyOrderAttributeDomainService;
import com.meta.supplychain.convert.md.ApplyOrderAttributeCopier;
import com.meta.supplychain.entity.dto.md.orderattribute.ApplyOrderAttributeInfo;
import com.meta.supplychain.entity.dto.md.req.orderattribute.QueryAttributeDetailReq;
import com.meta.supplychain.entity.dto.md.req.orderattribute.QueryAttributeReq;
import com.meta.supplychain.entity.dto.md.req.orderattribute.UpdateAttributeReq;
import com.meta.supplychain.entity.po.md.MdApplyOrderAttributePO;
import com.meta.supplychain.enums.OrderAttributeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdApplyOrderAttributeRepositoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// 补货管理旧逻辑迁移
@Service
public class MdApplyOrderAttributeDomainServiceImpl implements IMdApplyOrderAttributeDomainService {

    @Autowired
    private IMdApplyOrderAttributeRepositoryService stApplyOrderAttributeService;

    private final ApplyOrderAttributeCopier applyOrderAttributeCopier = ApplyOrderAttributeCopier.INSTANCE;

    @Override
    public void addOrUpdateAttribute(ApplyOrderAttributeInfo applyOrderAttributeInfo) {
        MdApplyOrderAttributePO stApplyOrderAttribute = applyOrderAttributeCopier.convertAttribute(applyOrderAttributeInfo);
        if(applyOrderAttributeInfo.getId() != null) {
            stApplyOrderAttributeService.updateById(stApplyOrderAttribute);
        } else {
            LambdaQueryWrapper<MdApplyOrderAttributePO> wrapper = new LambdaQueryWrapper<>();
            if(OrderAttributeEnum.ZHENG_CHANG.getCode().equals(applyOrderAttributeInfo.getAttributeCode())) {
                wrapper.eq(MdApplyOrderAttributePO::getApplyCate, applyOrderAttributeInfo.getApplyCate());
            }
            wrapper.eq(MdApplyOrderAttributePO::getAttributeCode, applyOrderAttributeInfo.getAttributeCode());
            MdApplyOrderAttributePO applyOrderAttribute = stApplyOrderAttributeService.getBaseMapper().selectOne(wrapper);
            if(applyOrderAttribute != null) {
                BizExceptions.throwWithMsg("订退货属性属性编码重复");
            } else {
                stApplyOrderAttribute.setStatus(YesOrNoEnum.YES.getCode());
                stApplyOrderAttributeService.save(stApplyOrderAttribute);
            }
        }
    }

    @Override
    public void stopAttribute(UpdateAttributeReq req) {
        MdApplyOrderAttributePO stApplyOrderAttribute = new MdApplyOrderAttributePO();
        stApplyOrderAttribute.setId(req.getId());
        stApplyOrderAttribute.setStatus(req.getStatus());
        stApplyOrderAttributeService.updateById(stApplyOrderAttribute);
    }

    @Override
    public Page<ApplyOrderAttributeInfo> getApplyOrderList(QueryAttributeReq req) {
        LambdaQueryWrapper<MdApplyOrderAttributePO> wrapper = new LambdaQueryWrapper<>();
        if(req.getApplyCate() != null) {
            wrapper.eq(MdApplyOrderAttributePO::getApplyCate, req.getApplyCate());
        }
        if(req.getStatus() != null) {
            wrapper.eq(MdApplyOrderAttributePO::getStatus, req.getStatus());
        }
        if(CollectionUtils.isNotEmpty(req.getAttributeCodes())) {
            wrapper.in(MdApplyOrderAttributePO::getAttributeCode, req.getAttributeCodes());
        }
        wrapper.orderByAsc(MdApplyOrderAttributePO::getAttributeCode);
        Page<MdApplyOrderAttributePO> page = new Page<>(req.getCurrent(), req.getPageSize());
        Page<MdApplyOrderAttributePO> stApplyOrderAttributes = stApplyOrderAttributeService.getBaseMapper().selectPage(page, wrapper);
        Page result = CglibCopier.copy(stApplyOrderAttributes, Page.class);
        if(stApplyOrderAttributes.getSize() > 0) {
            List<ApplyOrderAttributeInfo> applyOrderAttributeInfos = applyOrderAttributeCopier.copyAttributeList2Resp(stApplyOrderAttributes.getRecords());
            Integer count = 0;
            for (ApplyOrderAttributeInfo applyOrderAttributeInfo : applyOrderAttributeInfos) {
                if(OrderAttributeEnum.ZHENG_CHANG.getCode().equals(applyOrderAttributeInfo.getAttributeCode())) {
                    count ++;
                }
                if(count > 1) {
                    applyOrderAttributeInfos.remove(applyOrderAttributeInfo);
                    break;
                }
            }
            result.setRecords(applyOrderAttributeInfos);
        }

        result.setTotal(stApplyOrderAttributes.getTotal());

        return result;
    }

    @Override
    public List<MdApplyOrderAttributePO> listApplyOrder(QueryAttributeReq req){
        LambdaQueryWrapper<MdApplyOrderAttributePO> wrapper = new LambdaQueryWrapper<>();
        if(req.getApplyCate() != null) {
            wrapper.eq(MdApplyOrderAttributePO::getApplyCate, req.getApplyCate());
        }
        if(req.getStatus() != null) {
            wrapper.eq(MdApplyOrderAttributePO::getStatus, req.getStatus());
        }
        if(CollectionUtils.isNotEmpty(req.getAttributeCodes())) {
            wrapper.in(MdApplyOrderAttributePO::getAttributeCode, req.getAttributeCodes());
        }
        wrapper.orderByAsc(MdApplyOrderAttributePO::getAttributeCode);

        List<MdApplyOrderAttributePO> stApplyOrderAttributes = stApplyOrderAttributeService.getBaseMapper().selectList(wrapper);

        return stApplyOrderAttributes;
    }

    @Override
    public ApplyOrderAttributeInfo getApplyDetail(QueryAttributeDetailReq req) {
        MdApplyOrderAttributePO stApplyOrderAttribute = stApplyOrderAttributeService.getBaseMapper().selectById(req.getId());
        return applyOrderAttributeCopier.copyAttribute2Resp(stApplyOrderAttribute);

    }
}
