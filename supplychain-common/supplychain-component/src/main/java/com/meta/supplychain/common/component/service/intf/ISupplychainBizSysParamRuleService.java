package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.enums.StandardEnum;

import java.math.BigDecimal;

public interface ISupplychainBizSysParamRuleService {
    boolean isEnable(StandardEnum StandardEnum);
    
    boolean isEnable(StandardEnum StandardEnum, String deptCode);
    
    String getValue(StandardEnum StandardEnum);

    String getValue(StandardEnum StandardEnum, String deptCode);

    Integer getIntValue(StandardEnum StandardEnum);

    Integer getIntValue(StandardEnum StandardEnum, String deptCode);

    Long getLongValue(StandardEnum StandardEnum);

    Long getLongValue(StandardEnum StandardEnum, String deptCode);

    BigDecimal getBigDecimalValue(StandardEnum StandardEnum);

    BigDecimal getBigDecimalValue(StandardEnum StandardEnum, String deptCode);
}
