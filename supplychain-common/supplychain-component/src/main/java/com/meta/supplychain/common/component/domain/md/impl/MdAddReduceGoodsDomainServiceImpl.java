package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meta.supplychain.common.component.domain.md.intf.IMdAddReduceGoodsDomainService;
import com.meta.supplychain.convert.md.MdAddReduceGoodsConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptImportDTO;
import com.meta.supplychain.entity.dto.md.req.addreducegoods.*;
import com.meta.supplychain.entity.dto.md.resp.addreducegoods.CheckMdAddReduceGoodsResp;
import com.meta.supplychain.entity.po.md.MdAddReduceGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdAddReduceGoodsPO;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.MdAddReduceGoodsDeptTypeEnum;
import com.meta.supplychain.enums.md.MdContractGoodsDeptTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdAddReduceGoodsDeptRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdAddReduceGoodsRepositoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:43
 **/
@Service
public class MdAddReduceGoodsDomainServiceImpl implements IMdAddReduceGoodsDomainService {

    @Autowired
    private IMdAddReduceGoodsRepositoryService mdAddReduceGoodsRepositoryService;

    @Autowired
    private IMdAddReduceGoodsDeptRepositoryService mdAddReduceGoodsDeptRepositoryService;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void save(CreateMdAddReduceReq param) {
        Set<String> skuCodeSet = param.getSkuList().stream().map(CreateMdAddReduceGoodsReq::getSkuCode).collect(Collectors.toSet());

        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .in(MdAddReduceGoodsPO::getSkuCode, skuCodeSet);
        List<MdAddReduceGoodsPO> dbList = mdAddReduceGoodsRepositoryService.getBaseMapper().selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(dbList)) {
            Set<String> skuSet = dbList.stream().map(MdAddReduceGoodsPO::getSkuCode).collect(Collectors.toSet());
            String msg = String.format(MdErrorCodeEnum.SCWDS011P002.getErrorMsg(), skuSet);
            BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCWDS011P002.getErrorCode(), msg);
        }

        if (param.getDeptScope().equals(2) && CollectionUtils.isEmpty(param.getDeptList())) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS011P004);
        }

        // 组装商品数据
        List<MdAddReduceGoodsPO> saveList = Lists.newArrayList();
        param.getSkuList().forEach(item -> {
            MdAddReduceGoodsPO mdAddReduceGoodsPO = new MdAddReduceGoodsPO();
            mdAddReduceGoodsPO.setSkuCode(item.getSkuCode());
            mdAddReduceGoodsPO.setSkuName(item.getSkuName());
            mdAddReduceGoodsPO.setBarcode(item.getBarcode());
            mdAddReduceGoodsPO.setIsAllowMultiBill(param.getIsAllowMultiBill());
            mdAddReduceGoodsPO.setIsAllowReduceBill(param.getIsAllowReduceBill());
            if (param.getDeptScope().equals(1)) {
                mdAddReduceGoodsPO.setDeptScope(1);
            } else {
                mdAddReduceGoodsPO.setDeptScope(2);
            }
            saveList.add(mdAddReduceGoodsPO);
        });
        // 保存商品数据
        mdAddReduceGoodsRepositoryService.saveBatch(saveList);

        // 获取id值
        List<MdAddReduceGoodsPO> oldDbList = mdAddReduceGoodsRepositoryService.getBaseMapper().selectList(queryWrapper);

        if (param.getDeptScope().equals(2)) {
            // 组装商品部门数据
            List<MdAddReduceGoodsDeptPO> deptSaveList = Lists.newArrayList();
            param.getDeptList().forEach(item -> {
                oldDbList.forEach(save -> {
                    MdAddReduceGoodsDeptPO mdAddReduceGoodsDeptPO = new MdAddReduceGoodsDeptPO();
                    mdAddReduceGoodsDeptPO.setAddReduceGoodsId(save.getId());
                    mdAddReduceGoodsDeptPO.setDeptType(item.getDeptType());
                    mdAddReduceGoodsDeptPO.setCode(item.getCode());
                    mdAddReduceGoodsDeptPO.setName(item.getName());
                    deptSaveList.add(mdAddReduceGoodsDeptPO);
                });
            });
            // 保存商品部门数据
            mdAddReduceGoodsDeptRepositoryService.saveBatch(deptSaveList);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void update(UpdateMdAddReduceReq param) {
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .eq(MdAddReduceGoodsPO::getSkuCode, param.getSkuCode());
        MdAddReduceGoodsPO dbPo = mdAddReduceGoodsRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == dbPo) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS011P003);
        }

        if (param.getDeptScope().equals(2) && CollectionUtils.isEmpty(param.getDeptList())) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS011P004);
        }

        // 组装商品数据
        dbPo.setIsAllowMultiBill(param.getIsAllowMultiBill());
        dbPo.setIsAllowReduceBill(param.getIsAllowReduceBill());
        if (param.getDeptScope().equals(1)) {
            dbPo.setDeptScope(1);
        } else {
            dbPo.setDeptScope(2);
        }
        dbPo.setUpdateCode(null);
        dbPo.setUpdateName(null);
        dbPo.setUpdateUid(null);
        dbPo.setUpdateTime(null);
        // 保存商品数据
        mdAddReduceGoodsRepositoryService.updateById(dbPo);

        // 组装商品部门数据
        List<MdAddReduceGoodsDeptPO> deptSaveList = Lists.newArrayList();
        if (param.getDeptScope().equals(2)) {
            param.getDeptList().forEach(item -> {
                MdAddReduceGoodsDeptPO mdAddReduceGoodsDeptPO = new MdAddReduceGoodsDeptPO();
                mdAddReduceGoodsDeptPO.setAddReduceGoodsId(dbPo.getId());
                mdAddReduceGoodsDeptPO.setDeptType(item.getDeptType());
                mdAddReduceGoodsDeptPO.setCode(item.getCode());
                mdAddReduceGoodsDeptPO.setName(item.getName());
                deptSaveList.add(mdAddReduceGoodsDeptPO);
            });
        }

        // 保存商品部门数据
        LambdaQueryWrapper<MdAddReduceGoodsDeptPO> deleteWrapper = new LambdaQueryWrapper<MdAddReduceGoodsDeptPO>()
                .eq(MdAddReduceGoodsDeptPO::getAddReduceGoodsId, dbPo.getId());
        mdAddReduceGoodsDeptRepositoryService.remove(deleteWrapper);
        mdAddReduceGoodsDeptRepositoryService.saveBatch(deptSaveList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void delete(IdMdAddReduceGoodsReq param) {
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .eq(MdAddReduceGoodsPO::getId, param.getId());
        MdAddReduceGoodsPO dbPo = mdAddReduceGoodsRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == dbPo) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS011P006);
        }

        mdAddReduceGoodsRepositoryService.removeById(dbPo.getId());
        LambdaQueryWrapper<MdAddReduceGoodsDeptPO> deleteWrapper = new LambdaQueryWrapper<MdAddReduceGoodsDeptPO>()
                .eq(MdAddReduceGoodsDeptPO::getAddReduceGoodsId, dbPo.getId());
        mdAddReduceGoodsDeptRepositoryService.remove(deleteWrapper);
    }

    @Override
    public PageResult<MdAddReduceGoodsDTO> pageList(QueryMdAddReduceGoodsReq param) {
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .eq(null != param.getIsAllowReduceBill(), MdAddReduceGoodsPO::getIsAllowReduceBill, param.getIsAllowReduceBill())
                .eq(null != param.getIsAllowMultiBill(), MdAddReduceGoodsPO::getIsAllowMultiBill, param.getIsAllowMultiBill());
        if (StringUtils.isNotEmpty(param.getKeyWord())) {
            queryWrapper.or().and(wrapper -> wrapper
                    .like(MdAddReduceGoodsPO::getSkuName, param.getKeyWord())
                    .or()
                    .like(MdAddReduceGoodsPO::getSkuCode, param.getKeyWord())
                    .or()
                    .like(MdAddReduceGoodsPO::getBarcode, param.getKeyWord()));
        }
        queryWrapper.orderByDesc(MdAddReduceGoodsPO::getUpdateTime).orderByDesc(MdAddReduceGoodsPO::getSkuCode);
        Page<MdAddReduceGoodsPO> poPage = mdAddReduceGoodsRepositoryService.getBaseMapper().selectPage(new Page<>(param.getCurrent(), param.getPageSize()), queryWrapper);
        List<MdAddReduceGoodsDTO> dtoList = poPage.getRecords().stream().map(MdAddReduceGoodsConvert.INSTANCE::convertPo2Dto).collect(Collectors.toList());
        return new PageResult<>(poPage.getTotal(), dtoList);
    }

    @Override
    public List<MdAddReduceGoodsDTO> queryList(List<String> skuCodeList, List<String> barCodeList) {
        List<MdAddReduceGoodsDTO> returnList = Lists.newArrayList();
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .in(CollectionUtils.isNotEmpty(skuCodeList), MdAddReduceGoodsPO::getSkuCode, skuCodeList)
                .in(CollectionUtils.isNotEmpty(barCodeList), MdAddReduceGoodsPO::getBarcode, barCodeList);
        List<MdAddReduceGoodsPO> mdAddReduceGoodsPOS = mdAddReduceGoodsRepositoryService.getBaseMapper().selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(mdAddReduceGoodsPOS)) {
            Set<Long> ids = mdAddReduceGoodsPOS.stream().map(MdAddReduceGoodsPO::getId).collect(Collectors.toSet());
            LambdaQueryWrapper<MdAddReduceGoodsDeptPO> detailWrapper = new LambdaQueryWrapper<MdAddReduceGoodsDeptPO>()
                    .in(MdAddReduceGoodsDeptPO::getAddReduceGoodsId, ids);
            List<MdAddReduceGoodsDeptPO> deptPOList = mdAddReduceGoodsDeptRepositoryService.getBaseMapper().selectList(detailWrapper);
            Map<Long, List<MdAddReduceGoodsDeptPO>> detailMap = deptPOList.stream().collect(Collectors.groupingBy(MdAddReduceGoodsDeptPO::getAddReduceGoodsId));
            mdAddReduceGoodsPOS.forEach(item -> {
                MdAddReduceGoodsDTO mdAddReduceGoodsDTO = MdAddReduceGoodsConvert.INSTANCE.convertPo2Dto(item);
                List<MdAddReduceGoodsDeptPO> deptPos = detailMap.get(mdAddReduceGoodsDTO.getId());
                if (CollectionUtils.isNotEmpty(deptPos)) {
                    List<MdAddReduceGoodsDeptDTO> deptDTOS = deptPos.stream().map(MdAddReduceGoodsConvert.INSTANCE::convertDeptPo2Dto).collect(Collectors.toList());
                    mdAddReduceGoodsDTO.setDetailList(deptDTOS);
                }
                returnList.add(mdAddReduceGoodsDTO);
            });
        }
        return returnList;
    }

    @Override
    public MdAddReduceGoodsDTO detail(IdMdAddReduceGoodsReq param) {
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .eq(MdAddReduceGoodsPO::getId, param.getId());
        MdAddReduceGoodsPO dbPo = mdAddReduceGoodsRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == dbPo) {
            return null;
        }

        MdAddReduceGoodsDTO mdAddReduceGoodsDTO = MdAddReduceGoodsConvert.INSTANCE.convertPo2Dto(dbPo);
        LambdaQueryWrapper<MdAddReduceGoodsDeptPO> queryDeptWrapper = new LambdaQueryWrapper<MdAddReduceGoodsDeptPO>()
                .eq(MdAddReduceGoodsDeptPO::getAddReduceGoodsId, param.getId());
        List<MdAddReduceGoodsDeptPO> detailList = mdAddReduceGoodsDeptRepositoryService.getBaseMapper().selectList(queryDeptWrapper);
        mdAddReduceGoodsDTO.setDetailList(detailList.stream().map(MdAddReduceGoodsConvert.INSTANCE::convertDeptPo2Dto).collect(Collectors.toList()));
        return mdAddReduceGoodsDTO;
    }

    @Override
    public List<CheckMdAddReduceGoodsResp> checkAddReduceGoods(CheckMdAddReduceReq param) {
        List<CheckMdAddReduceGoodsResp> resultList = Lists.newArrayList();
        if (param.getSkuList().size() > 100) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS011P003);
        }

        // 查询所有符合条件的商品数据
        Set<String> skuCodeSet = param.getSkuList().stream().map(item -> item.getSkuCode()).collect(Collectors.toSet());
        LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<MdAddReduceGoodsPO>()
                .in(MdAddReduceGoodsPO::getSkuCode, skuCodeSet);
        List<MdAddReduceGoodsPO> dbPo = mdAddReduceGoodsRepositoryService.getBaseMapper().selectList(queryWrapper);
        Map<String, MdAddReduceGoodsPO> dbMap = dbPo.stream()
                .collect(Collectors.toMap(MdAddReduceGoodsPO::getSkuCode, item -> item, (item1, item2) -> item1));

        // 查询商品部门数据
        List<Long> dbIds = dbPo.stream().map(MdAddReduceGoodsPO::getId).collect(Collectors.toList());
        Map<Long, List<MdAddReduceGoodsDeptPO>> detailMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(dbIds)) {
            LambdaQueryWrapper<MdAddReduceGoodsDeptPO> queryDeptWrapper = new LambdaQueryWrapper<MdAddReduceGoodsDeptPO>()
                    .in(MdAddReduceGoodsDeptPO::getAddReduceGoodsId, dbIds);
            List<MdAddReduceGoodsDeptPO> detailList = mdAddReduceGoodsDeptRepositoryService.getBaseMapper().selectList(queryDeptWrapper);
            detailMap.putAll(detailList.stream().collect(Collectors.groupingBy(MdAddReduceGoodsDeptPO::getAddReduceGoodsId)));
        }

        // 校验数据
        for (CheckMdAddReduceReq.CheckMdAddReduceGoodsReq sku : param.getSkuList()) {
            CheckMdAddReduceGoodsResp checkMdAddReduceGoodsResp = new CheckMdAddReduceGoodsResp();
            resultList.add(checkMdAddReduceGoodsResp);
            checkMdAddReduceGoodsResp.setSkuCode(sku.getSkuCode());
            checkMdAddReduceGoodsResp.setSuccess(true);
            //未设置的商品默认追加追减都允许
            if (!dbMap.containsKey(sku.getSkuCode())) {
                if (sku.getIsAllowReduceBill() != null && sku.getIsAllowReduceBill() == 0) {
                    checkMdAddReduceGoodsResp.setSuccess(false);
                    checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P011.getErrorMsg());
                    continue;
                }
                if (sku.getIsAllowMultiBill() != null && sku.getIsAllowMultiBill() == 0) {
                    checkMdAddReduceGoodsResp.setSuccess(false);
                    checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P010.getErrorMsg());
                    continue;
                }
                continue;
            }

            MdAddReduceGoodsPO goodsPO = dbMap.get(sku.getSkuCode());
            if (CollectionUtils.isNotEmpty(sku.getDeptCodeList())) {
                if (!detailMap.containsKey(goodsPO.getId())) {
                    if (sku.getIsAllowReduceBill() != null && sku.getIsAllowReduceBill() == 0) {
                        checkMdAddReduceGoodsResp.setSuccess(false);
                        checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P011.getErrorMsg());
                        continue;
                    }
                    if (sku.getIsAllowMultiBill() != null && sku.getIsAllowMultiBill() == 0) {
                        checkMdAddReduceGoodsResp.setSuccess(false);
                        checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P010.getErrorMsg());
                        continue;
                    }
                }

                List<MdAddReduceGoodsDeptPO> deptPOList = detailMap.get(goodsPO.getId());
                //未设置的商品部门默认追加追减都允许
                for (String deptCode : sku.getDeptCodeList()) {
                    Optional<MdAddReduceGoodsDeptPO> deptFirst = deptPOList.stream()
                            .filter(item -> item.getCode().equals(deptCode) && item.getDeptType().equals(MdAddReduceGoodsDeptTypeEnum.DEPT.getCode()))
                            .findFirst();
                    if (!deptFirst.isPresent()) {
                        if (sku.getIsAllowReduceBill() != null && sku.getIsAllowReduceBill() == 0) {
                            checkMdAddReduceGoodsResp.setSuccess(false);
                            checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P011.getErrorMsg());
                            break;
                        }
                        if (sku.getIsAllowMultiBill() != null && sku.getIsAllowMultiBill() == 0) {
                            checkMdAddReduceGoodsResp.setSuccess(false);
                            checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P010.getErrorMsg());
                            break;
                        }
                    } else {
                        // 判断追加追减是否一致
                        if (sku.getIsAllowReduceBill() != null && sku.getIsAllowReduceBill() != goodsPO.getIsAllowReduceBill()) {
                            checkMdAddReduceGoodsResp.setSuccess(false);
                            checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P011.getErrorMsg());
                            break;
                        }
                        if (sku.getIsAllowMultiBill() != null && sku.getIsAllowMultiBill() != goodsPO.getIsAllowMultiBill()) {
                            checkMdAddReduceGoodsResp.setSuccess(false);
                            checkMdAddReduceGoodsResp.setFailReason(MdErrorCodeEnum.SCWDS011P010.getErrorMsg());
                            break;
                        }
                    }
                }
            }

        }
        return resultList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void batchSave(List<MdAddReduceGoodsDeptImportDTO> saveList, OpInfo operatorInfo) {
        Logs.info("开始保存追加追减商品部门信息");
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<MdAddReduceGoodsPO> poList = Lists.newArrayList();
            List<MdAddReduceGoodsPO> updateList = Lists.newArrayList();
            List<MdAddReduceGoodsDeptPO> saveDeptList = Lists.newArrayList();
            Map<String, List<MdAddReduceGoodsDeptImportDTO>> dtoMap = saveList.stream().collect(Collectors.groupingBy(MdAddReduceGoodsDeptImportDTO::getSkuCode));
            LambdaQueryWrapper<MdAddReduceGoodsPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(MdAddReduceGoodsPO::getSkuCode, dtoMap.keySet());
            List<MdAddReduceGoodsPO> dbList = mdAddReduceGoodsRepositoryService.list(queryWrapper);
            Map<String, Long> ids = dbList.stream().collect(Collectors.toMap(MdAddReduceGoodsPO::getSkuCode, MdAddReduceGoodsPO::getId));

            dtoMap.forEach((skuCode, dtoList) -> {
                MdAddReduceGoodsPO mdAddReduceGoodsPO = new MdAddReduceGoodsPO();
                mdAddReduceGoodsPO.setSkuCode(skuCode);
                mdAddReduceGoodsPO.setSkuName(dtoList.get(0).getSkuName());
                mdAddReduceGoodsPO.setBarcode(dtoList.get(0).getBarcode());
                mdAddReduceGoodsPO.setIsAllowMultiBill(dtoList.get(0).getIsAllowMultiBill());
                mdAddReduceGoodsPO.setIsAllowReduceBill(dtoList.get(0).getIsAllowReduceBill());
                mdAddReduceGoodsPO.setDeptScope(dtoList.get(0).getDeptScope());
                mdAddReduceGoodsPO.setCreateUid(Long.valueOf(operatorInfo.getUserId()));
                mdAddReduceGoodsPO.setCreateCode(operatorInfo.getOperatorCode());
                mdAddReduceGoodsPO.setCreateName(operatorInfo.getOperatorName());
                mdAddReduceGoodsPO.setCreateTime(LocalDateTime.now());
                mdAddReduceGoodsPO.setUpdateUid(Long.valueOf(operatorInfo.getUserId()));
                mdAddReduceGoodsPO.setUpdateCode(operatorInfo.getOperatorCode());
                mdAddReduceGoodsPO.setUpdateName(operatorInfo.getOperatorName());
                mdAddReduceGoodsPO.setUpdateTime(LocalDateTime.now());
                if  (!ids.containsKey(skuCode)) {
                    poList.add(mdAddReduceGoodsPO);
                } else {
                    updateList.add(mdAddReduceGoodsPO);
                }
            });
            // 先保存商品
            if (CollectionUtils.isNotEmpty(poList)) {
                mdAddReduceGoodsRepositoryService.saveBatch(poList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                mdAddReduceGoodsRepositoryService.updateBatchById(updateList);
            }

            dbList = mdAddReduceGoodsRepositoryService.list(queryWrapper);
            Map<String, Long> newIds = dbList.stream().collect(Collectors.toMap(MdAddReduceGoodsPO::getSkuCode, MdAddReduceGoodsPO::getId));

            dtoMap.forEach((skuCode, dtoList) -> {
                for (MdAddReduceGoodsDeptImportDTO dto : dtoList) {
                    if (dto.getDeptScope().equals(2)) {
                        MdAddReduceGoodsDeptPO mdAddReduceGoodsDeptPO = new MdAddReduceGoodsDeptPO();
                        mdAddReduceGoodsDeptPO.setAddReduceGoodsId(newIds.get(skuCode));
                        mdAddReduceGoodsDeptPO.setDeptType(dto.getDeptType());
                        mdAddReduceGoodsDeptPO.setCode(dto.getCode());
                        mdAddReduceGoodsDeptPO.setName(dto.getName());
                        mdAddReduceGoodsDeptPO.setCreateUid(Long.valueOf(operatorInfo.getUserId()));
                        mdAddReduceGoodsDeptPO.setCreateCode(operatorInfo.getOperatorCode());
                        mdAddReduceGoodsDeptPO.setCreateName(operatorInfo.getOperatorName());
                        mdAddReduceGoodsDeptPO.setCreateTime(LocalDateTime.now());
                        mdAddReduceGoodsDeptPO.setUpdateUid(Long.valueOf(operatorInfo.getUserId()));
                        mdAddReduceGoodsDeptPO.setUpdateCode(operatorInfo.getOperatorCode());
                        mdAddReduceGoodsDeptPO.setUpdateName(operatorInfo.getOperatorName());
                        mdAddReduceGoodsDeptPO.setUpdateTime(LocalDateTime.now());
                        saveDeptList.add(mdAddReduceGoodsDeptPO);
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(saveDeptList)) {
                mdAddReduceGoodsDeptRepositoryService.saveBatch(saveDeptList);
            }
        }
    }
}
