package com.meta.supplychain.common.component.domain.md.intf;


import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoods4DeptResultDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsQueryDTO;
import com.meta.supplychain.entity.dto.md.contract.*;
import com.meta.supplychain.entity.dto.md.req.contract.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsSuppResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsSuppReq;
import com.meta.supplychain.entity.po.md.MdContractMasPO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface IMdContractMasDomainService {
    PageResult<MdContractDTO> pageList(MdContractPageListReq param);

    List<MdContractDTO> queryList(MdContractQueryListReq param);

    List<MdContractDTO> list(MdContractPageListReq param);

    Integer countMdContract(MdContractQueryCntDTO mdContractQueryCntDTO);

    List<MdContractMasPO> listMdContract(MdContractQueryCntDTO mdContractQueryCntDTO);

    MdContractDetailsDTO getDetailById(MdContractGetDetailsReq param);

    MdContractMasPO basicAddSave(MdContractBasicSaveReq param);

    MdContractMasPO basicModifySave(MdContractBasicSaveReq param);

    List<MdContractScopeDTO> listMdContractScopeDTO(MdContractGetDetailsReq param, MdContractDetailsDTO details);

    Boolean saveContractScopeList(MdContractBasicSaveReq param,MdContractMasPO mdContractMasPO);

    PageResult<MdContractDTO> extensionPageList(MdContractPageListReq param);

    Result<Void> extensionHandle(List<MdContractExtensionHandleReq> param);

    Result<MdContractDetailsDTO> getDetails(MdContractGetDetailsReq param);

    Result<Void> toApprove(MdContractToApproveReq param);

    Result<Void> approve(MdContractApproveReq param);

    Result<Void> cancel(MdContractCancelReq param);

    Result<Void> stop(MdContractStopReq param);

    Result<Void> supplementSave(MdContractSupplementSaveReq param);

    Result<Void> supplementToApprove(MdContractSupplementToApproveReq param);

    Result<Void> skuSave(MdContractSkuSaveReq param);

    Result<MdContractRenewalStartDate> getContractRenewalStartDate(MdContractRenewalStartDateReq param);

    Result<MdContractBasicDTO> basicSave(MdContractBasicSaveReq param);

    Result<Void> execContractJob(String tenantIds,String contractNo);

    List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO param);

    List<ContractGoodsDeptDTO> listContractGoods(ContractGoodsDeptQueryDTO param);

    List<ContractGoods4DeptResultDTO> queryContractGoods4Dept(ContractGoodsQueryDTO param);

    List<ContractGoods4DeptResultDTO> queryContractGoods4DeptCancel(ContractGoodsQueryDTO param);

    List<String> listTenant();

    List<GoodsSuppResp> queryGoodsSupp(QueryGoodsSuppReq queryGoodsSuppReq);
}