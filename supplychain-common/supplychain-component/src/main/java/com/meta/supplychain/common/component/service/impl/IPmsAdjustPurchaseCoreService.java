package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.entity.dto.bds.resp.SupplierByCodeResp;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjust4AsReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjustReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchaseAdjustDTO;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.ChangeTypeEnum;
import com.meta.supplychain.enums.DeptOperateModeEnum;
import com.meta.supplychain.enums.SettleModeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.util.BaseStoreUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class IPmsAdjustPurchaseCoreService {
    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final BaseStoreUtil baseStoreUtil;;
    private final CommonFranchiseService commonFranchiseService;;;

    /**
     * 组装采购订单调整参数
     */
    public PurchaseBillAdjustReq assembleAdjustReq(PurchaseBillAdjust4AsReq asReq, Map<Long, PmsPurchaseBillDetailPO> detailCollect){
        List<PurchaseBillAdjustReq.GoodsInfo> detailList = asReq.getDetailList().stream().map(item->{
            //按insideId分组，只有一条
            PmsPurchaseBillDetailPO detailPO = detailCollect.get(item);
            PurchaseBillAdjustReq.GoodsInfo goodsInfo = PurchaseBillAdjustReq.GoodsInfo.builder()
                    .insideId(item.getInsideId())
                    .skuCode(detailPO.getSkuCode())
                    .skuType(detailPO.getSkuType())
                    .skuName(detailPO.getSkuName())
                    .barcode(detailPO.getBarcode())
                    .purchMoneyBefore(detailPO.getPurchMoney())
                    .purchQtyBefore(detailPO.getPurchQty())
                    .build();
            BigDecimal purchQtyAfter = detailPO.getPurchQty().add(item.getAdjustQty());
            if(purchQtyAfter.compareTo(BigDecimal.ZERO) < 0){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_003_B024, new Object[]{detailPO.getSkuCode()});
            }
            BigDecimal wholeQtyAfter = purchQtyAfter.divide(detailPO.getPurchUnitRate(), 0, RoundingMode.HALF_UP);
            BigDecimal oddQtyAfter = purchQtyAfter.subtract(wholeQtyAfter.multiply(detailPO.getPurchUnitRate()));
            BigDecimal purchMoney = purchQtyAfter.multiply(detailPO.getPurchPrice());
            BigDecimal purchTax = purchMoney.divide(detailPO.getInputTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            goodsInfo.setPurchMoney(purchMoney);
            goodsInfo.setPurchTax(purchTax);
            goodsInfo.setPurchQty(purchQtyAfter);
            goodsInfo.setWholeQty(wholeQtyAfter);
            goodsInfo.setOddQty(oddQtyAfter);
            return goodsInfo;
        }).collect(Collectors.toList());

        PurchaseBillAdjustReq adjustReq = PurchaseBillAdjustReq.builder()
                .billNo(asReq.getBillNo())
                .adjustBillNo(asReq.getAdjustBillNo())
                .billSource(1)
                .deliverDate(asReq.getDeliverDate())
                .validityDate(asReq.getValidityDate())
                .adjustRemark(asReq.getAdjustRemark())
                .detailList(detailList)
                .build();
        return adjustReq;
    }

    /**
     * 组装&校验 调整数据
     * @return
     */
    public List<BillAdjustLogPO> assembleChangeInfo(PmsPurchaseAdjustDTO purchaseAdjustDTO,ChangeTypeEnum changeTypeEnum){
        PurchaseBillAdjustReq req = purchaseAdjustDTO.getAdjustReq();
        PmsPurchaseOrderPO oldBill = purchaseAdjustDTO.getOldBill();
        PmsPurchaseOrderPO newBill = purchaseAdjustDTO.getNewBill();
        Map<Long, PmsPurchaseBillDetailPO> detailCollect = purchaseAdjustDTO.getExistDetailMap();
        List<PmsPurchaseBillDetailPO> afterBillDetail = new ArrayList<>();
        List<PmsPurchasePlanDetailPO> planDetailList = new ArrayList<>();
        List<PmsPurchaseBillDetailPO> changeBillDetail = new ArrayList<>();
        List<BillAdjustLogPO> logList = new ArrayList<>();
        BillAdjustLogPO billAdjustLog = BillAdjustLogPO.builder()
                .billNo(purchaseAdjustDTO.getAdjustBillNo())
                .refBillNo(req.getBillNo())
                .billType(ChangeTypeEnum.PURCHASE.getCode())
                .billSource(changeTypeEnum.getCode())
                .remark(req.getAdjustRemark())
                .adjustTarget(0)
                .build();
        //组装单据维度变化 联系人 联系地址 联系电话 备注 退货原因 送货方式
        newBill.setBillNo(oldBill.getBillNo());
        assembleBillFieldInfo(oldBill.getContactMan(),req.getContactMan(),billAdjustLog,logList, PmsAdjustNameEnum.CONTACT_MAN.getDesc());
        assembleBillFieldInfo(oldBill.getContactAddr(),req.getContactAddr(),billAdjustLog,logList,PmsAdjustNameEnum.CONTACT_ADDR.getDesc());
        assembleBillFieldInfo(oldBill.getContactTel(),req.getContactTel(),billAdjustLog,logList,PmsAdjustNameEnum.CONTACT_TEL.getDesc());
        assembleBillFieldInfo(oldBill.getPurchRemark(),req.getPurchRemark(),billAdjustLog,logList,PmsAdjustNameEnum.PURCH_REMARK.getDesc());
        if(Objects.nonNull(req.getContactMan()))  { newBill.setContactMan(req.getContactMan()); }
        if(Objects.nonNull(req.getContactAddr())) { newBill.setContactAddr(req.getContactAddr()); }
        if(Objects.nonNull(req.getContactTel()))  { newBill.setContactTel(req.getContactTel()); }
        if(Objects.nonNull(req.getPurchRemark())) { newBill.setPurchRemark(req.getPurchRemark()); }
        if(Objects.nonNull(req.getRefundReason())){
            assembleBillFieldInfo(oldBill.getRefundReasonDesc(),req.getRefundReasonDesc(),billAdjustLog,logList,PmsAdjustNameEnum.REFUND_REASON.getDesc());
            newBill.setRefundReason(req.getRefundReason());
            newBill.setRefundReasonDesc(req.getRefundReasonDesc());
        }
        if(Objects.nonNull(req.getSendMode())){
            assembleBillFieldInfo(PmsSendModEnum.getEnumByCode(oldBill.getSendMode()).getName(),
                    PmsSendModEnum.getEnumByCode(req.getSendMode()).getName(),billAdjustLog,logList,PmsAdjustNameEnum.SEND_MODE.getDesc());
            newBill.setSendMode(req.getSendMode());
        }
        LocalDate currentDate = LocalDate.now();
        //送货日期
        if(Objects.nonNull(req.getDeliverDate())){
            if (req.getDeliverDate().isBefore(currentDate)) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P004);
            }
            assembleBillFieldInfo(oldBill.getDeliverDate().toString(),req.getDeliverDate().toString(),billAdjustLog,logList,PmsAdjustNameEnum.DELIVER_DATE.getDesc());
            newBill.setDeliverDate(req.getDeliverDate());
        }
        //有效日期
        if(Objects.nonNull(req.getValidityDate())){
            if (req.getValidityDate().isBefore(currentDate)) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P005);
            }
            assembleBillFieldInfo(oldBill.getValidityDate().toString(),req.getValidityDate().toString(),billAdjustLog,logList,PmsAdjustNameEnum.VALIDITY_DATE.getDesc());
            newBill.setValidityDate(req.getValidityDate());
        }

        //原单总采购数量 总采购金额 总采购税金
        req.setTotalQty(oldBill.getTotalQty());
        req.setTotalTaxMoney(oldBill.getTotalTaxMoney());
        req.setTotalTax(oldBill.getTotalTax());
        //组装单据维度变化 采购价格 采购数量 整件数量 零头数量 采购金额 采购税金
        //数量 价格联动金额变化,金额一定会变 只需要记录采购价格或者采购数量是否变更
        req.getDetailList().stream().forEach(item ->{
            PmsPurchaseBillDetailPO originalDetail = detailCollect.get(item.getInsideId());
            BigDecimal qtyDifference = BigDecimal.ZERO;
            //校验金额，数量阀值
            checkThreshold(oldBill.getBillDirection(),oldBill.getDeptCode(),item.getPurchQty(),item.getPurchMoney(),item.getSkuCode());

            PmsPurchaseBillDetailPO billDetailPO = PmsPurchaseBillDetailPO.builder()
                    .billNo(oldBill.getBillNo())
                    .insideId(item.getInsideId())
                    .skuCode(item.getSkuCode()).build();
            BillAdjustLogPO detailLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            detailLog.setAdjustTarget(1);
            detailLog.setRefInsideId(item.getInsideId());
            detailLog.setRefSkuCode(item.getSkuCode());
            detailLog.setRefSkuName(item.getSkuName());
            detailLog.setRefBarcode(item.getBarcode());
            //采购价格
            if(Objects.nonNull(item.getPurchPrice()) && item.getPurchPrice().compareTo(item.getPurchPriceBefore()) != 0){
                assembleBillFieldInfo(item.getPurchPriceBefore().stripTrailingZeros().toPlainString(),
                        item.getPurchPrice().stripTrailingZeros().toPlainString(),detailLog, logList,PmsAdjustNameEnum.PURCH_PRICE.getDesc());
                billDetailPO.setPurchPrice(item.getPurchPrice());
            }
            //采购数量
            if(Objects.nonNull(item.getPurchQty()) && item.getPurchQty().compareTo(item.getPurchQtyBefore()) != 0){
                req.setIsChangeQty(true);
                assembleBillFieldInfo(item.getPurchQtyBefore().stripTrailingZeros().toPlainString(),
                        item.getPurchQty().stripTrailingZeros().toPlainString(),detailLog,logList,PmsAdjustNameEnum.PURCH_QTY.getDesc());
                billDetailPO.setPurchQty(item.getPurchQty());
                billDetailPO.setWholeQty(item.getWholeQty());
                billDetailPO.setOddQty(item.getOddQty());
                //计算差值
                qtyDifference = item.getPurchQty().subtract(item.getPurchQtyBefore());

                //处理记录数量发生值，用于上报库存调增调减
                PmsPurchaseBillDetailPO changeDetail = detailCollect.get(item.getInsideId());
                changeDetail.setPurchQty(qtyDifference);//记录发生值-差值
                changeBillDetail.add(changeDetail);

                if(StringUtils.hasText(oldBill.getPlanBillNo())){
                    //组装采购计划单更新剩余可采信息
                    PmsPurchasePlanDetailPO planDetail = new PmsPurchasePlanDetailPO();
                    planDetail.setBillNo(oldBill.getPlanBillNo());
                    planDetail.setSkuCode(item.getSkuCode());
                    planDetail.setSkuType(changeDetail.getSkuType());
                    planDetail.setPlanReqQty(qtyDifference);
                    planDetailList.add(planDetail);
                }
            }
            billDetailPO.setPurchMoney(item.getPurchMoney());
            billDetailPO.setPurchTax(item.getPurchTax());
            afterBillDetail.add(billDetailPO);

            req.setTotalQty(req.getTotalQty().add(qtyDifference));

            if(Objects.nonNull(item.getPurchMoney())){
                BigDecimal moneyDifference = item.getPurchMoney().subtract(originalDetail.getPurchMoney());
                req.setTotalTaxMoney(req.getTotalTaxMoney().add(moneyDifference));
            }
            if(Objects.nonNull(item.getPurchTax())){
                BigDecimal taxDifference = item.getPurchTax().subtract(originalDetail.getPurchTax());
                req.setTotalTax(req.getTotalTax().add(taxDifference));
            }
        });

        //重新计算单据维度采购金额  采购数量的汇总变化
        if(CollectionUtils.isNotEmpty(req.getDetailList())){
            if(req.getIsChangeQty()){
                newBill.setTotalQty(req.getTotalQty());
            }
            newBill.setTotalTaxMoney(req.getTotalTaxMoney());
            newBill.setTotalTax(req.getTotalTax());
            //加盟店 校验额度变化值
            BigDecimal amount = newBill.getTotalTaxMoney().subtract(oldBill.getTotalTaxMoney());
            purchCheckFranLine(oldBill,amount);
        }

        purchaseAdjustDTO.setAfterBillDetail(afterBillDetail);
        purchaseAdjustDTO.setPlanDetailList(planDetailList);
        purchaseAdjustDTO.setChangeBillDetail(changeBillDetail);
        return logList;
    }


    /**
     * 处理单据维度变更字段
     **/
    private void assembleBillFieldInfo(String fieldBefore,String fieldAfter,BillAdjustLogPO billAdjustLog,
                                       List<BillAdjustLogPO> logList,String desc){
        if(Objects.nonNull(fieldAfter)){
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustName(desc);
            copyLog.setBeforeValue(Objects.nonNull(fieldBefore)?fieldBefore:"");
            copyLog.setAfterValue(fieldAfter);
            logList.add(copyLog);
        }
    }

    /**
     * 校验采购订单
     * 金额异常阀值 数量异常阀值
     */
    private void checkThreshold(Integer billDirection, String deptCode,BigDecimal amount, BigDecimal money,String skuCode){
        BigDecimal maxAmount = PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)
                ? supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PURCHASE_MAX_AMOUNT, deptCode)
                : supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PURCHASE_REFUND_MAX_AMOUNT, deptCode);
        BigDecimal maxMoney = PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)
                ? supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PURCHASE_MAX_MONEY, deptCode)
                : supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PURCHASE_REFUND_MAX_MONEY, deptCode);

        if (Objects.nonNull(maxAmount) && Objects.nonNull(amount) && amount.compareTo(maxAmount) > 0) {
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_000_P007.getCode(),"商品："+skuCode+PmsErrorCodeEnum.SC_PMS_000_P007.getDesc());
        }
        if (Objects.nonNull(maxMoney) && Objects.nonNull(money) && money.compareTo(maxMoney) > 0) {
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_000_P008.getCode(),"商品："+skuCode+PmsErrorCodeEnum.SC_PMS_000_P008.getDesc());
        }
    }

    /**
     * 采购订单校验加盟额度
     * @param purchasePO
     */
    public void purchCheckFranLine(PmsPurchaseOrderPO purchasePO,BigDecimal totalTaxMoney){
        SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchasePO.getSupplierCode());
        Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(purchasePO.getDeptOperateMode())
                && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())
                && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(purchasePO.getBillSource())
                && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());
        if(isJM){
            commonFranchiseService.checkFranLine(purchasePO.getDeptCode(),totalTaxMoney);
        }
    }
}
