package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.exception.BaseErrorCodeEnum;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.trace.util.TraceIds;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.meta.supplychain.common.component.service.intf.IOrderEventService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.entity.base.EventMsgBaseDTO;
import com.meta.supplychain.entity.po.md.MdBillEventLogPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.EventChannelEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdBillEventLogRepositoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Service
public abstract  class AbstractChainOrderEventServiceImpl implements IOrderEventService {

    private final Gson gson = new Gson();
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IMdBillEventLogRepositoryService mdBillEventLogRepositoryService;

    @Override
    public EventChannelEnum channel() {
        return EventChannelEnum.CHAIN;
    }

    public <T> void logPersistence(boolean isSuccess, EventMsgBaseDTO<T> reqEventMsg, Object res) {
        //根据事件类型获取单据类型枚举
        BillActionTypeEnum billActionTypeEnum = StandardEnum.codeOf(BillActionTypeEnum.class, reqEventMsg.getBillActionType());
        String supportType = String.join(SysConstants.COLON_DELIMITER, channel().getCode(), billActionTypeEnum.getSupportSign().getSign());
        MdBillEventLogPO billEventLogDb = getEventLog(reqEventMsg.getBillActionType(), reqEventMsg.getBillNo());
        String errorReason = gson.toJson(res);
        try {
            if (Objects.isNull(billEventLogDb)){
                MdBillEventLogPO billEventLog = new MdBillEventLogPO();
                billEventLog.setBillType(billActionTypeEnum.getBillType());
                billEventLog.setBillTypeDesc(billActionTypeEnum.getBillTypeDesc());
                billEventLog.setBillNo(reqEventMsg.getBillNo());
                billEventLog.setBillContent(objectMapper.writeValueAsString(reqEventMsg));
                billEventLog.setActionType(billActionTypeEnum.getActionType());
                billEventLog.setEventType(billActionTypeEnum.getCode());
                billEventLog.setEventDesc(billActionTypeEnum.getDesc());
                billEventLog.setChannel(channel().getCode());
                billEventLog.setSupportType(supportType);
                billEventLog.setRetryCount(0);
                billEventLog.setIsSuccess(isSuccess?1:0);
                billEventLog.setRespData(gson.toJson(res));
                billEventLog.setErrorReason(isSuccess ? "" : StringUtils.abbreviate(errorReason, 200));
                billEventLog.setTraceId(TraceIds.get());
                mdBillEventLogRepositoryService.saveOrUpdate(billEventLog);
            } else {
                //Success 0:失败，1:成功，2:补偿成功
                if (billEventLogDb.getIsSuccess()!=0){
                    Logs.info("单据【{}】事件【{}】已经处理成功，不处理", reqEventMsg.getBillNo(), reqEventMsg.getBillActionType());
                    return;
                }
                billEventLogDb.setErrorReason(isSuccess ? "" : StringUtils.abbreviate(errorReason, 200));
                billEventLogDb.setRetryCount(Optional.ofNullable(billEventLogDb.getRetryCount()).orElse(0) + 1);
                billEventLogDb.setRetryTime(LocalDateTime.now());
                billEventLogDb.setIsSuccess(isSuccess?2:0);
                billEventLogDb.setTraceId(TraceIds.get());
                mdBillEventLogRepositoryService.updateById(billEventLogDb);
            }
        } catch (Exception e) {
            Logs.error("单据【{}】事件【{}】日志记录异常：", reqEventMsg.getBillNo(), reqEventMsg.getBillActionType(), e);
        }
    }

    //参数转换
    public <T> EventMsgBaseDTO<T> convertParam(String data, Class<T> clazz) {
        try {
            return objectMapper.readValue(data, objectMapper.getTypeFactory().constructParametricType(EventMsgBaseDTO.class, clazz));
        } catch (JsonProcessingException e) {
            Logs.error("convertParam error:", e);
            throw new BizException(BaseErrorCodeEnum.ARGUMENT_PARSE_ERROR);
        }
    }

    //根据日志记录 判断是否幂等
    //已处理返回true
    public <T> boolean isIdempotent(EventMsgBaseDTO<T> reqEventMsg) {
        MdBillEventLogPO billEventLogDb = getEventLog(reqEventMsg.getBillActionType(), reqEventMsg.getBillNo());
        return Objects.nonNull(billEventLogDb) && !Objects.equals(billEventLogDb.getIsSuccess(), YesOrNoEnum.NO.getCode());
    }


    private MdBillEventLogPO getEventLog(String billActionType,String billNo) {
        //根据事件类型获取单据类型枚举
        BillActionTypeEnum billActionTypeEnum = StandardEnum.codeOf(BillActionTypeEnum.class, billActionType);
        String supportType = String.join(SysConstants.COLON_DELIMITER, channel().getCode(), billActionTypeEnum.getSupportSign().getSign());
        LambdaQueryWrapper<MdBillEventLogPO> wrapperQuery
                = Wrappers.lambdaQuery(MdBillEventLogPO.class).eq(MdBillEventLogPO::getBillNo, billNo)
                .eq(MdBillEventLogPO::getBillType, billActionTypeEnum.getBillType())
                .eq(MdBillEventLogPO::getActionType, billActionTypeEnum.getActionType())
                .eq(MdBillEventLogPO::getEventType,billActionTypeEnum.getCode())
                .eq(MdBillEventLogPO::getChannel, channel().getCode())
                .eq(MdBillEventLogPO::getSupportType, supportType);
        return mdBillEventLogRepositoryService.getOne(wrapperQuery);
    }


}
