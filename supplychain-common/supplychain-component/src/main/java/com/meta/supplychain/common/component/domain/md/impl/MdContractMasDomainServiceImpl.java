package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdContractMasDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.common.component.service.intf.commonbiz.IMdCommonStatusProcessService;
import com.meta.supplychain.convert.md.MdContractGoodsDeptConvert;
import com.meta.supplychain.convert.md.MdContractMasConvert;
import com.meta.supplychain.convert.md.MdContractScopeConvert;
import com.meta.supplychain.entity.dto.bds.req.*;
import com.meta.supplychain.entity.dto.bds.resp.*;
import com.meta.supplychain.entity.dto.goods.req.GoodsSearchQueryReq;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSearchInfo;
import com.meta.supplychain.entity.dto.md.commonstatus.MdCommonStatusProcessDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.contract.*;
import com.meta.supplychain.entity.dto.md.req.contract.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsSuppDetailResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsSuppResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsSuppReq;
import com.meta.supplychain.entity.po.md.MdContractGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdContractMasPO;
import com.meta.supplychain.entity.po.md.MdContractScopePO;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.enums.pms.ApplyCateEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractGoodsDeptRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractMasRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractScopeRepositoryService;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.NumberUtil;
import com.metadata.idaas.client.cons.ClientIdentCons;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:48
 **/
@Service
public class MdContractMasDomainServiceImpl implements IMdContractMasDomainService {
    @Autowired
    private IMdContractMasRepositoryService mdContractRepositoryService;

    @Autowired
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Autowired
    private IMdContractScopeRepositoryService mdContractScopeRepositoryService;

    @Autowired
    private IMdContractGoodsDeptRepositoryService mdContractGoodsDeptRepositoryService;

    @Autowired
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Autowired
    private IMdCommonStatusProcessService mdCommonStatusProcessService;

    @Resource
    private ICommonGoodsService commonGoodsService;

    @Resource
    private DataSourceTransactionManager dstManager;

    @Override
    public PageResult<MdContractDTO> pageList(MdContractPageListReq param) {
        if(null == param.getCurrent()){
            param.setCurrent(1L);
        }
        if(null == param.getPageSize()){
            param.setPageSize(10L);
        }
        param.setCurrentSize((param.getCurrent() -1) * param.getPageSize());
        Integer count = mdContractRepositoryService.getMdContractMasMapper().pageCount(param);
        if(Objects.equals(0,count)){
            return PageResult.ofEmpty();
        }
        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().pageList(param);

        List<MdContractDTO> mdContractDTOS = MdContractMasConvert.INSTANCE.convertPo2DtoList(mdContractMasPOS);
        return PageResult.of(count,mdContractDTOS);

//        IPage<MdContractMasPO> pageResult = mdContractRepositoryService.getMdContractMasMapper().selectPage(page, mdContractPOLambdaQueryWrapper);
//
//        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
//            return PageResult.ofEmpty();
//        }
//
//        IPage<MdContractDTO> convert = pageResult.convert(MdContractMasConvert.INSTANCE::convertPo2Dto);
//
//        return PageResult.of(pageResult.getTotal() ,convert.getRecords());
    }

    @Override
    public List<MdContractDTO> queryList(MdContractQueryListReq param) {
        LambdaQueryWrapper<MdContractMasPO> queryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(CollectionUtils.isNotEmpty(param.getSupplierCodeList()), MdContractMasPO::getSupplierCode, param.getSupplierCodeList())
                .in(CollectionUtils.isNotEmpty(param.getStatusList()), MdContractMasPO::getStatus, param.getStatusList())
                .in(CollectionUtils.isNotEmpty(param.getOperateModeList()), MdContractMasPO::getOperateMode, param.getOperateModeList())
                .in(CollectionUtils.isNotEmpty(param.getContractNoList()), MdContractMasPO::getContractNo, param.getContractNoList())
                .le(StringUtils.isNotEmpty(param.getStartDate()), MdContractMasPO::getStartDate, param.getStartDate())
                .ge(StringUtils.isNotEmpty(param.getEndDate()), MdContractMasPO::getEndDate, param.getEndDate())
                .orderByDesc(MdContractMasPO::getId);
        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(queryWrapper);

        // 查询满足条件合同中最大合同序号
        List<MdContractDTO> resultList = null;
        if (param.getMaxSerialFlag()) {
            Map<String, MdContractMasPO> resultMap = mdContractMasPOS.stream()
                    .collect(Collectors.toMap(MdContractMasPO::getContractNo,
                            Function.identity(),
                            BinaryOperator.maxBy(Comparator.comparing(MdContractMasPO::getContractSerial))));
            resultList = resultMap.values().stream()
                    .map(MdContractMasConvert.INSTANCE::convertPo2Dto)
                    .collect(Collectors.toList());
        } else {
            resultList = MdContractMasConvert.INSTANCE.convertPo2DtoList(mdContractMasPOS);
        }

        if (CollectionUtils.isEmpty(resultList)) {
            return Lists.newArrayList();
        }
        // 查询合同范围
        Set<String> contractSet = resultList.stream().map(MdContractDTO::getContractNo).collect(Collectors.toSet());
        LambdaQueryWrapper<MdContractScopePO> queryScopeWrapper = new LambdaQueryWrapper<MdContractScopePO>()
                .in(MdContractScopePO::getContractNo, contractSet);

        List<MdContractScopePO> mdContractScopePOList = mdContractScopeRepositoryService.getMdContractScopeMapper().selectList(queryScopeWrapper);
        if(CollectionUtils.isNotEmpty(mdContractScopePOList)){
            List<MdContractScopeDTO> mdContractScopeDTOList = MdContractScopeConvert.INSTANCE.convertPo2DtoList(mdContractScopePOList);
            resultList.forEach(item -> {
                List<MdContractScopeDTO> scopeList = mdContractScopeDTOList.stream()
                        .filter(obj -> obj.getContractNo().equals(item.getContractNo())
                                && obj.getContractSerial().equals(item.getContractSerial()))
                        .collect(Collectors.toList());
                item.setContractScopeList(scopeList);
            });
        }

        return resultList;
    }

    @Override
    public List<MdContractDTO> list(MdContractPageListReq param) {
        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(CollectionUtils.isNotEmpty(param.getContractNoList()), MdContractMasPO::getContractNo, param.getContractNoList())
                .orderByDesc(MdContractMasPO::getId);

        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
        List<MdContractDTO> mdContractDTOS = MdContractMasConvert.INSTANCE.convertPo2DtoList(mdContractMasPOS);
        return mdContractDTOS;
    }

    @Override
    public Integer countMdContract(MdContractQueryCntDTO mdContractQueryCntDTO) {
        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(CollectionUtils.isNotEmpty(mdContractQueryCntDTO.getStatusList()), MdContractMasPO::getStatus, mdContractQueryCntDTO.getStatusList())
                .eq(StringUtils.isNotEmpty(mdContractQueryCntDTO.getContractNo()),MdContractMasPO::getContractNo,mdContractQueryCntDTO.getContractNo());

        return mdContractRepositoryService.getMdContractMasMapper().selectCount(mdContractPOLambdaQueryWrapper);

    }

    @Override
    public List<MdContractMasPO> listMdContract(MdContractQueryCntDTO mdContractQueryCntDTO) {
        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(CollectionUtils.isNotEmpty(mdContractQueryCntDTO.getStatusList()), MdContractMasPO::getStatus, mdContractQueryCntDTO.getStatusList())
                .in(CollectionUtils.isNotEmpty(mdContractQueryCntDTO.getSignModeList()), MdContractMasPO::getSignMode, mdContractQueryCntDTO.getSignModeList())
                .eq(StringUtils.isNotEmpty(mdContractQueryCntDTO.getContractNo()),MdContractMasPO::getContractNo,mdContractQueryCntDTO.getContractNo())
                .eq(StringUtils.isNotEmpty(mdContractQueryCntDTO.getContractSerial()),MdContractMasPO::getContractSerial,mdContractQueryCntDTO.getContractSerial())
                ;

        return mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
    }

    @Override
    public MdContractDetailsDTO getDetailById(MdContractGetDetailsReq param) {
        MdContractMasPO mdContractMasPO = mdContractRepositoryService.getMdContractMasMapper().selectById(param.getId());
        if(null == mdContractMasPO){
            return null;
        }
        MdContractDetailsDTO mdContractDetailsDTO = MdContractMasConvert.INSTANCE.convertPo2MdContractDetailsDTO(mdContractMasPO);

        return mdContractDetailsDTO;
    }


    @Override
    public MdContractMasPO basicAddSave(MdContractBasicSaveReq param) {
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        if(1 == param.getOpType()){
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.CONTRACT,"");
            mdContractMasPO.setContractNo(billNo);
            param.setContractNo(billNo);
            if(StringUtils.isEmpty(param.getManualContractNo())){
                mdContractMasPO.setManualContractNo(billNo);
            }
            else{
                mdContractMasPO.setManualContractNo(param.getManualContractNo());
            }
        }
        else{
            mdContractMasPO.setContractNo(param.getContractNo());
            mdContractMasPO.setManualContractNo(param.getManualContractNo());
        }
        mdContractMasPO.setSupplierCode(param.getSupplierCode());
        mdContractMasPO.setSupplierName(param.getSupplierName());
        mdContractMasPO.setOperateMode(param.getOperateMode());
        mdContractMasPO.setStartDate(param.getStartDate());
        mdContractMasPO.setEndDate(param.getEndDate());
        mdContractMasPO.setBuyerId(param.getBuyerId());
        mdContractMasPO.setBuyerCode(param.getBuyerCode());
        mdContractMasPO.setBuyerName(param.getBuyerName());
        mdContractMasPO.setSignDate(param.getSignDate());
        mdContractMasPO.setSignMode(param.getSignMode());
        mdContractMasPO.setDelayAdvanceDays(param.getDelayAdvanceDays());
        mdContractMasPO.setRemark(param.getRemark());
        mdContractMasPO.setAttachmentUrl(param.getAttachmentUrl());
        if(null != param.getSupplementaryTerms()){
            mdContractMasPO.setSupplementaryTerms(param.getSupplementaryTerms());
        }

        MdContractMasPO mdContractMasPOParam = new MdContractMasPO();
        mdContractMasPOParam.setContractNo(mdContractMasPO.getContractNo());
        mdContractMasPOParam.setTenantId(TenantContext.getLong());
        MdContractMasPO resultMdContractMasPO = mdContractRepositoryService.getMdContractMasMapper().selectMaxContractSerial(mdContractMasPOParam);
        if(null == resultMdContractMasPO){
            mdContractMasPO.setContractSerial("1");
        }
        else{
            String contractSerial = resultMdContractMasPO.getContractSerial();
            String serial = Integer.valueOf(contractSerial) + 1 + "";
            mdContractMasPO.setContractSerial(serial);
        }

        mdContractRepositoryService.getMdContractMasMapper().insert(mdContractMasPO);

        return mdContractMasPO;
    }

    @Override
    public MdContractMasPO basicModifySave(MdContractBasicSaveReq param) {
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        //对于新签合同，草稿、待提交状态可编辑，除合同id外，其他字段均可修改。
        //对于非新签合同，草稿、待提交状态可编辑，除合同id和手工合同号外，其余字段均可修改
        if(MdContractSignModeEnum.XQ1.equals(param.getSignMode())){
            mdContractMasPO.setManualContractNo(param.getManualContractNo());
        }
        mdContractMasPO.setSupplierCode(param.getSupplierCode());
        mdContractMasPO.setSupplierName(param.getSupplierName());
        mdContractMasPO.setOperateMode(param.getOperateMode());
        mdContractMasPO.setStartDate(param.getStartDate());
        mdContractMasPO.setEndDate(param.getEndDate());
        mdContractMasPO.setBuyerId(param.getBuyerId());
        mdContractMasPO.setBuyerCode(param.getBuyerCode());
        mdContractMasPO.setBuyerName(param.getBuyerName());
        mdContractMasPO.setSignDate(param.getSignDate());
        mdContractMasPO.setSignMode(param.getSignMode());
        mdContractMasPO.setDelayAdvanceDays(param.getDelayAdvanceDays());
        mdContractMasPO.setId(param.getId());
        mdContractMasPO.setRemark(param.getRemark());
        mdContractMasPO.setAttachmentUrl(param.getAttachmentUrl());
        if(null != param.getSupplementaryTerms()){
            mdContractMasPO.setSupplementaryTerms(param.getSupplementaryTerms());
        }


        mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

        return mdContractRepositoryService.getById(param.getId());

    }

    @Override
    public List<MdContractScopeDTO> listMdContractScopeDTO(MdContractGetDetailsReq param, MdContractDetailsDTO details) {
        LambdaQueryWrapper<MdContractScopePO> queryWrapper = new LambdaQueryWrapper<MdContractScopePO>()
                .eq(MdContractScopePO::getContractNo, details.getContractNo())
                .eq(MdContractScopePO::getContractSerial, details.getContractSerial())
                ;

        List<MdContractScopePO> mdContractScopePOList = mdContractScopeRepositoryService.getMdContractScopeMapper().selectList(queryWrapper);
        if(CollectionUtils.isEmpty(mdContractScopePOList)){
            return new ArrayList<>();
        }
        List<MdContractScopeDTO> mdContractScopeDTOList = MdContractScopeConvert.INSTANCE.convertPo2DtoList(mdContractScopePOList);
        return mdContractScopeDTOList;
    }

    @Override
    public Boolean saveContractScopeList(MdContractBasicSaveReq param,MdContractMasPO mdContractMasPO) {
        //批量新增
        List<MdContractScopePO> mdContractScopePOList = new ArrayList<>();
        for(MdContractScopeReq mdContractScopeReq : param.getScopeList()){
            MdContractScopePO mdContractScopePO = new MdContractScopePO();
            mdContractScopePO.setContractNo(param.getContractNo());
            mdContractScopePO.setContractSerial(param.getContractSerial());

            mdContractScopePO.setScopeType(mdContractScopeReq.getScopeType());
            mdContractScopePO.setScopeCode(mdContractScopeReq.getScopeCode());
            mdContractScopePO.setScopeName(mdContractScopeReq.getScopeName());

            mdContractScopePOList.add(mdContractScopePO);
        }

        MdContractScopePO mdContractScopePO = new MdContractScopePO();
        mdContractScopePO.setContractNo(mdContractMasPO.getContractNo());
        mdContractScopePO.setContractSerial(mdContractMasPO.getContractSerial());
        mdContractScopeRepositoryService.getMdContractScopeMapper().delScope(mdContractScopePO);
        if(CollectionUtils.isNotEmpty(mdContractScopePOList)){

            if(CollectionUtils.isNotEmpty(mdContractScopePOList)){
                boolean b = mdContractScopeRepositoryService.saveBatch(mdContractScopePOList);

                return b;
            }
        }


        return true;
    }

    private Boolean skuDeptSave(MdContractSkuSaveReq param) {
        List<MdContractGoodsDeptPO> list = new ArrayList<>();
        for(MdContractGoodsReq goods : param.getGoodsList()){
            List<MdContractGoodsDeptPO> mdContractGoodsDeptPOList = MdContractGoodsDeptConvert.INSTANCE.convertReq2poList(goods.getDeptGoodsList());
            list.addAll(mdContractGoodsDeptPOList);
        }

        List<List<MdContractGoodsDeptPO>> partitionList = ListUtils.partition(list, 1000);
        for(List<MdContractGoodsDeptPO> mdContractGoodsDeptPOList : partitionList){
            boolean b = mdContractGoodsDeptRepositoryService.saveBatch(mdContractGoodsDeptPOList);
            if(!b){
                return b;
            }
        }
        return true;
    }

    @Override
    public PageResult<MdContractDTO> extensionPageList(MdContractPageListReq param) {
//        筛选项和列表字段同合同管理，展示的合同序号范围为
//        1、若合同号存在待审核/审核驳回的延期合同，则展示该版本合同；
//        2、其他合同号
//        a)若有生效中合同，则展示该生效中合同序号展示
//        b)若无生效中合同，则展示当前时间点最近一份 已过期/已终止的合同序号展示
        Integer cnt = mdContractRepositoryService.getMdContractMasMapper().extensionCount(param);
        if(null == cnt || 0 == cnt){
            return PageResult.ofEmpty();
        }
        List<MdContractMasPO> mdContractMasPOList = mdContractRepositoryService.getMdContractMasMapper().extensionPageList(param);
        List<MdContractDTO> mdContractDTOList = MdContractMasConvert.INSTANCE.convertPo2DtoList(mdContractMasPOList);

        List<String> contractNoList = new ArrayList<>();
        for (MdContractDTO mdContractDTO : mdContractDTOList) {
            contractNoList.add(mdContractDTO.getContractNo());
        }

        List<Integer> contractStatusList = new ArrayList<>();
        contractStatusList.add(MdContractStatusEnum.STATUS_5.getCode());
        contractStatusList.add(MdContractStatusEnum.STATUS_4.getCode());
        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(MdContractMasPO::getContractNo, contractNoList)
                .in(MdContractMasPO::getStatus,contractStatusList)
                .orderByDesc(MdContractMasPO::getContractNo,MdContractMasPO::getContractSerial);

        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);

        Map<String,MdContractMasPO> mdContractMasPOMap = new HashMap<>();

        for(MdContractMasPO mdContractMasPO : mdContractMasPOS){

            if(mdContractMasPOMap.containsKey(mdContractMasPO.getContractNo())){
                if(NumberUtil.getInteger(mdContractMasPO.getContractSerial()) > NumberUtil.getInteger(mdContractMasPOMap.get(mdContractMasPO.getContractNo()).getContractSerial())){
                    mdContractMasPOMap.put(mdContractMasPO.getContractNo(),mdContractMasPO);
                }
            }
            else{
                mdContractMasPOMap.put(mdContractMasPO.getContractNo(),mdContractMasPO);
            }
        }

        for (MdContractDTO mdContractDTO : mdContractDTOList) {
            if(mdContractMasPOMap.containsKey(mdContractDTO.getContractNo())){
                MdContractMasPO mdContractMasPO = mdContractMasPOMap.get(mdContractDTO.getContractNo());

                mdContractDTO.setMaxSerialStartDate(mdContractMasPO.getStartDate().toLocalDate());
                mdContractDTO.setMaxSerialEndDate(mdContractMasPO.getEndDate().toLocalDate());
            }
            else{
                mdContractDTO.setMaxSerialStartDate(mdContractDTO.getStartDate());
                mdContractDTO.setMaxSerialEndDate(mdContractDTO.getEndDate());
            }
        }

        return PageResult.of(cnt,mdContractDTOList);
    }

    @Override
    public Result<Void> extensionHandle(List<MdContractExtensionHandleReq> param) {
//        1.若当前合同存在已延期且待审核的合同序号，则弹框提示 XXX,XXX合同已存在延期待审核的延期合同，无法创建（可驳回重新提交）
//        2.若当前合同存在已延期且待生效的合同序号，则弹框提示 XXX,XXX合同已存在延期待生效的延期合同，无法创建（可修改待生效延期合同的结束时间）
//        3.合同延期时校验该该合同的延期提前天数，若延期时距离合同结束时间大于延期提前天数，则提示该合同未到可延期时间（XXXXX后可延期），无法延期
        List<String> contractNoList = new ArrayList<>();
        Map<String,MdContractExtensionHandleReq> map = new HashMap<>();

        for(MdContractExtensionHandleReq req : param){
            contractNoList.add(req.getContractNo());
        }

        List<Integer> contractStatusList = new ArrayList<>();
        contractStatusList.add(MdContractStatusEnum.STATUS_5.getCode());
        contractStatusList.add(MdContractStatusEnum.STATUS_2.getCode());

        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(MdContractMasPO::getContractNo, contractNoList)
                .eq(MdContractMasPO::getSignMode,MdContractSignModeEnum.YQ4.getCode())
                .in(MdContractMasPO::getStatus,contractStatusList)
                .orderByDesc(MdContractMasPO::getId);

        //1.若当前合同存在已延期且待审核的合同序号，则弹框提示 XXX,XXX合同已存在延期待审核的延期合同，无法创建（可驳回重新提交）
        //2.若当前合同存在已延期且待生效的合同序号，则弹框提示 XXX,XXX合同已存在延期待生效的延期合同，无法创建（可修改待生效延期合同的结束时间）
        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
        if(CollectionUtils.isNotEmpty(mdContractMasPOS)){
            //已延期且待审核的合同
            HashSet<String> setStatus2 = new HashSet<>();
            //已延期且待生效的合同
            HashSet<String> setStatus5 = new HashSet<>();
            for(MdContractMasPO mdContractMasPO : mdContractMasPOS){
                if(mdContractMasPO.getStatus().equals(MdContractStatusEnum.STATUS_2.getCode())){
                    setStatus2.add(mdContractMasPO.getContractNo());
                }else if(mdContractMasPO.getStatus().equals(MdContractStatusEnum.STATUS_5.getCode())){
                    setStatus5.add(mdContractMasPO.getContractNo());
                }
            }

            String msg = "";
            if(setStatus2.size() > 0){
                String result = setStatus2.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(", "));
                msg += result + MdErrorCodeEnum.SCMD001B020.getDesc();
            }

            if(setStatus5.size() > 0){
                String result = setStatus5.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(", "));
                msg += result + MdErrorCodeEnum.SCMD001B021.getDesc();
            }

            if(StringUtils.isNotEmpty(msg)){
                return Results.of(MdErrorCodeEnum.SCMD001B020.getCode(),msg,false);
            }
        }

        contractStatusList = new ArrayList<>();
        contractStatusList.add(MdContractStatusEnum.STATUS_5.getCode());
        contractStatusList.add(MdContractStatusEnum.STATUS_4.getCode());
        contractStatusList.add(MdContractStatusEnum.STATUS_7.getCode());
        contractStatusList.add(MdContractStatusEnum.STATUS_8.getCode());
        mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                .in(MdContractMasPO::getContractNo, contractNoList)
                .in(MdContractMasPO::getStatus,contractStatusList)
                .orderByDesc(MdContractMasPO::getContractNo,MdContractMasPO::getContractSerial);
        mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);


        Map<String,MdContractMasPO> mdContractMasPOMap = new HashMap<>();

        for(MdContractMasPO mdContractMasPO : mdContractMasPOS){

            if(mdContractMasPOMap.containsKey(mdContractMasPO.getContractNo())){
                if(NumberUtil.getInteger(mdContractMasPO.getContractSerial()) > NumberUtil.getInteger(mdContractMasPOMap.get(mdContractMasPO.getContractNo()).getContractSerial())){
                    mdContractMasPOMap.put(mdContractMasPO.getContractNo(),mdContractMasPO);
                }
            }
            else{
                mdContractMasPOMap.put(mdContractMasPO.getContractNo(),mdContractMasPO);
            }
        }


        //执行合同延期. 合同中费用条款不做自动延期处理
        //md_contract_mas、md_contract_scope、结算条款

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            LoginUserDTO operator = ClientIdentUtil.getLoginUser();

            LocalDateTime createTime = LocalDateTime.now();
            String createCode = "";
            Long createUid = 0L;
            String createName = "";
            if(null != operator){
                createCode = operator.getCode();
                createUid = operator.getUid();
                createName = operator.getName();
            }

            for(MdContractExtensionHandleReq req : param){
                //获取序号
                MdContractMasPO mdContractMasPOParam = new MdContractMasPO();
                mdContractMasPOParam.setContractNo(req.getContractNo());
                mdContractMasPOParam.setTenantId(TenantContext.getLong());
                MdContractMasPO resultMdContractMasPO = mdContractRepositoryService.getMdContractMasMapper().selectMaxContractSerial(mdContractMasPOParam);
                String contractSerial = resultMdContractMasPO.getContractSerial();
                String serial = Integer.valueOf(contractSerial) + 1 + "";

                MdContractMasPO mdContractMasPO = new MdContractMasPO();
                mdContractMasPO.setContractNo(req.getContractNo());
                mdContractMasPO.setContractSerial(serial);
                mdContractMasPO.setCreateTime(createTime);
                mdContractMasPO.setCreateUid(createUid);
                mdContractMasPO.setCreateCode(createCode);
                mdContractMasPO.setCreateName(createName);
                mdContractMasPO.setSignMode(MdContractSignModeEnum.YQ4.getCode());
                mdContractMasPO.setId(req.getId());
                mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_2.getCode());
                mdContractMasPO.setEndDate(DateUtil.date2LocalDateTime(req.getEndDate() + " 23:59:59"));
                int masCnt = mdContractRepositoryService.getMdContractMasMapper().extensionContract(mdContractMasPO);

                MdContractScopePO mdContractScopePO = new MdContractScopePO();
                mdContractScopePO.setContractNo(req.getContractNo());

                mdContractScopePO.setCreateTime(createTime);
                mdContractScopePO.setCreateUid(createUid);
                mdContractScopePO.setCreateCode(createCode);
                mdContractScopePO.setCreateName(createName);
                MdContractMasPO mdContractMasPOReq = mdContractMasPOMap.get(req.getContractNo());
                if(null == mdContractMasPOReq){
                    BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCMD001B023.getCode(),req.getContractNo() + MdErrorCodeEnum.SCMD001B023.getDesc());
                }
                else{
                    mdContractScopePO.setId(mdContractMasPOReq.getId());
                    mdContractScopePO.setNewContractSerial(serial);
                    mdContractScopePO.setContractSerial(mdContractMasPOReq.getContractSerial());
                }
                int scopeCnt = mdContractScopeRepositoryService.getMdContractScopeMapper().extensionContract(mdContractScopePO);

                if(1 != masCnt && 0 == scopeCnt){
                    BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCMD001B023.getCode(),req.getContractNo() + MdErrorCodeEnum.SCMD001B023.getDesc());
                }


                //TODO 结算条款待产品需求完善后修改
            }
            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.extensionHandle.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B024,false);
        }

        return Results.ofSuccess();
    }

    @Override
    public Result<MdContractDetailsDTO> getDetails(MdContractGetDetailsReq param) {
        // 存在id则用id查 不存在用合同号查询
        MdContractDetailsDTO details = null;
        if (null != param.getId()) {
            details = getDetailById(param);
        }
        if (StringUtils.isNotEmpty(param.getContractNo())) {
            MdContractMasPO mdContractMasPO = new MdContractMasPO();
            mdContractMasPO.setContractNo(param.getContractNo());
            mdContractMasPO.setTenantId(Long.valueOf(TenantContext.get()));
            MdContractMasPO resultMdContractMasPO = mdContractRepositoryService.getMdContractMasMapper().selectMaxContractSerial(mdContractMasPO);
            details = MdContractMasConvert.INSTANCE.convertPo2MdContractDetailsDTO(resultMdContractMasPO);
        }
        if(null == details){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        List<MdContractScopeDTO> mdContractScopeDTOList = listMdContractScopeDTO(param,details);
        details.setContractScopeList(mdContractScopeDTOList);

        return Results.ofSuccess(details);
    }

    private void statusTransition(MdCommonStatusProcessDTO mdCommonStatusProcessDTO) {
        //上报状态变更记录
        mdCommonStatusProcessService.uploadCommonStatusProcess(mdCommonStatusProcessDTO);
    }

    /**
     * 列表页提交审核
     * @param param
     * @return
     */
    @Override
    public Result<Void> toApprove(MdContractToApproveReq param) {
        MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
        mdContractGetDetailsReq.setId(param.getId());
        MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
        if(null == detail){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!MdContractStatusEnum.STATUS_1.getCode().equals(detail.getStatus()) &&
                !MdContractStatusEnum.STATUS_3.getCode().equals(detail.getStatus())){
            return Results.of(MdErrorCodeEnum.SCMD001B025,false);
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            handleToApprove(detail);

            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.toApprove.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B006,false);
        }

        return Results.ofSuccess();
    }

    private void handleToApprove(MdContractDetailsDTO detail) {
        //修改状态
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        mdContractMasPO.setId(detail.getId());
        mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_2.getCode());

        int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

        //上报记录
        if(cnt > 0){

            MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, MdContractStatusEnum.STATUS_2.getCode(),MdContractStatusEnum.STATUS_2.getDesc());
            statusTransition(mdCommonStatusProcessDTO);
        }
    }

    private MdCommonStatusProcessDTO assembleParam(MdContractDetailsDTO detail, Integer mdContractStatus,String remark) {
        MdCommonStatusProcessDTO mdCommonStatusProcessDTO = new MdCommonStatusProcessDTO();

        mdCommonStatusProcessDTO.setModuleCode(MdCommonStatusModuleEnum.MD);
        mdCommonStatusProcessDTO.setModuleName(MdCommonStatusModuleEnum.MD.getDesc());

        mdCommonStatusProcessDTO.setBizId(detail.getId());
        mdCommonStatusProcessDTO.setBizCode(detail.getContractNo());
        mdCommonStatusProcessDTO.setBizType(MdCommonStatusBizTypeEnum.CONTRACT);

        mdCommonStatusProcessDTO.setStatusCode(mdContractStatus + "");
        mdCommonStatusProcessDTO.setStatusName(MdContractStatusEnum.ofCodeToDesc(mdContractStatus));
        mdCommonStatusProcessDTO.setRemark(remark);
        return mdCommonStatusProcessDTO;
    }

    /**
     * 审核
     * @param param
     * @return
     */
    @Override
    public Result<Void> approve(MdContractApproveReq param) {
        MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
        mdContractGetDetailsReq.setId(param.getId());
        MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
        if(null == detail){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!MdContractStatusEnum.STATUS_2.getCode().equals(detail.getStatus())){
            return Results.of(MdErrorCodeEnum.SCMD001B003,false);
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            MdContractMasPO mdContractMasPO = new MdContractMasPO();
            LoginUserDTO operator = ClientIdentUtil.getLoginUser();
            if(null != operator){
                mdContractMasPO.setAuditUid(operator.getUid());
                mdContractMasPO.setAuditTime(LocalDateTime.now());
                mdContractMasPO.setAuditCode(operator.getCode());
                mdContractMasPO.setAuditName(operator.getName());
            }

            if(param.getAuditStatus().equals(MdContractAuditStatusEnum.STATUS_1.getCode())){
                LocalDate now = LocalDate.now();
                //判断合同是否生效
                if((detail.getStartDate().isBefore(now) || detail.getStartDate().isEqual(now))
                        && (detail.getEndDate().isAfter(now) || detail.getEndDate().isEqual(now))){
                    mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_4.getCode());
                    mdContractMasPO.setRealEffectiveDate(LocalDateTime.now());

//                    //续签合同处理逻辑
//                    if(detail.getSignMode().equals(MdContractSignModeEnum.XQ3.getCode())){
//                        //若续签合同晚于延期合同审核，则续签合同审核通过后，续签合同生效，延期合同失效
//                        //若续签合同早于延期合同审核，延期合同审核后无任何变化
//                        //相当于续签和延期合同 生效重叠时，优先以续签合同为准
//                        //失效延期合同
//                        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
//                                .eq(MdContractMasPO::getContractNo, detail.getContractNo())
//                                .eq(MdContractMasPO::getSignMode,MdContractSignModeEnum.YQ4.getCode())
//                                .eq(MdContractMasPO::getStatus,MdContractStatusEnum.STATUS_4.getCode())
//                                .orderByDesc(MdContractMasPO::getId);
//
//                        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
//                        for(MdContractMasPO mdContractMas : mdContractMasPOS){
//                            mdContractMas.setStatus(MdContractStatusEnum.STATUS_6.getCode());
//                            execExpireContract(mdContractMas);
//                        }
//                    }
//                    else if(detail.getSignMode().equals(MdContractSignModeEnum.BQ2.getCode())
//                            || detail.getSignMode().equals(MdContractSignModeEnum.YQ4.getCode())){
//                        LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
//                                .eq(MdContractMasPO::getContractNo, detail.getContractNo())
//                                .eq(MdContractMasPO::getStatus,MdContractStatusEnum.STATUS_4.getCode())
//                                .orderByDesc(MdContractMasPO::getId);
//
//                        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
//                        for(MdContractMasPO mdContractMas : mdContractMasPOS){
//                            mdContractMas.setStatus(MdContractStatusEnum.STATUS_6.getCode());
//                            execExpireContract(mdContractMas);
//                        }
//                    }

                    List<Integer> statusList = new ArrayList<>();
                    statusList.add(MdContractStatusEnum.STATUS_4.getCode());
                    statusList.add(MdContractStatusEnum.STATUS_5.getCode());

                    //待生效、生效 合同改为失效
                    LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                            .eq(MdContractMasPO::getContractNo, detail.getContractNo())
                            .in(MdContractMasPO::getStatus,statusList)
                            .orderByDesc(MdContractMasPO::getId);

                    List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
                    for(MdContractMasPO mdContractMas : mdContractMasPOS){
                        mdContractMas.setStatus(MdContractStatusEnum.STATUS_6.getCode());
                        execExpireContract(mdContractMas);
                    }
                }
                else{
                    //后续需要定时任务处理
                    mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_5.getCode());
                }

            }
            else{
                mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_3.getCode());
            }

            //修改状态
            mdContractMasPO.setId(param.getId());

            mdContractMasPO.setAuditRemark(param.getAuditRemark());
            mdContractMasPO.setAuditStatus(param.getAuditStatus());

            int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

            //上报记录
            if(cnt > 0){
                MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, mdContractMasPO.getStatus() ,param.getAuditRemark());

                statusTransition(mdCommonStatusProcessDTO);
            }

            dstManager.commit(transaction);

        }catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.approve.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B007,false);
        }

        return Results.ofSuccess();
    }

    /**
     * 作废
     * @param param
     * @return
     */
    @Override
    public Result<Void> cancel(MdContractCancelReq param) {
        MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
        mdContractGetDetailsReq.setId(param.getId());
        MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
        if(null == detail){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!MdContractStatusEnum.STATUS_1.getCode().equals(detail.getStatus())
                && !MdContractStatusEnum.STATUS_3.getCode().equals(detail.getStatus())
                && !MdContractStatusEnum.STATUS_5.getCode().equals(detail.getStatus())){
            return Results.of(MdErrorCodeEnum.SCMD001B004,false);
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            //修改状态
            MdContractMasPO mdContractMasPO = new MdContractMasPO();
            mdContractMasPO.setId(param.getId());
            mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_9.getCode());
            mdContractMasPO.setRealExpireDate(LocalDateTime.now());

            int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

            //上报记录
            if(cnt > 0){

                MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, MdContractStatusEnum.STATUS_9.getCode() ,"");

                statusTransition(mdCommonStatusProcessDTO);
            }

            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.cancel.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B008,false);
        }

        return Results.ofSuccess();
    }

    /**
     * 终止
     * @param param
     * @return
     */
    @Override
    public Result<Void> stop(MdContractStopReq param) {
        MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
        mdContractGetDetailsReq.setId(param.getId());
        MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
        if(null == detail){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!MdContractStatusEnum.STATUS_4.getCode().equals(detail.getStatus())){
            return Results.of(MdErrorCodeEnum.SCMD001B005,false);
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {

            MdContractMasPO mdContractMasPO = new MdContractMasPO();
            mdContractMasPO.setId(param.getId());
            mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_7.getCode());
            mdContractMasPO.setRealExpireDate(LocalDateTime.now());

            int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

            //上报记录
            if(cnt > 0){

                MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, MdContractStatusEnum.STATUS_7.getCode() ,"");

                statusTransition(mdCommonStatusProcessDTO);
            }

            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.stop.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B009,false);
        }

        return Results.ofSuccess();
    }

    /**
     * 补充条款，暂存
     * @param param
     * @return
     */
    @Override
    public Result<Void> supplementSave(MdContractSupplementSaveReq param) {
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        mdContractMasPO.setId(param.getId());
        mdContractMasPO.setSupplementaryTerms(param.getSupplementaryTerms());

        int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

        if(0 == cnt){
            return Results.of(MdErrorCodeEnum.SCMD001B010,false);
        }
        return Results.ofSuccess();
    }

    @Override
    public Result<Void> supplementToApprove(MdContractSupplementToApproveReq param) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            //修改补充条款
            MdContractMasPO mdContractMasPO = new MdContractMasPO();
            mdContractMasPO.setId(param.getId());
            mdContractMasPO.setSupplementaryTerms(param.getSupplementaryTerms());

            int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

            //更改状态
            MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
            mdContractGetDetailsReq.setId(param.getId());
            MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
            handleToApprove(detail);


            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.supplementToApprove.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B012,false);
        }

        return Results.ofSuccess();
    }

    @Override
    public Result<Void> skuSave(MdContractSkuSaveReq param) {
        MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
        mdContractGetDetailsReq.setId(param.getId());
        MdContractDetailsDTO detail = getDetailById(mdContractGetDetailsReq);
        if(null == detail){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!detail.getContractNo().equals(param.getContractNo())){
            return Results.of(MdErrorCodeEnum.SCMD001B001,false);
        }

        if(!MdContractStatusEnum.STATUS_1.getCode().equals(detail.getStatus())){
            return Results.of(MdErrorCodeEnum.SCMD001B002,false);
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            //保存商品部门信息
            Boolean b2 = skuDeptSave(param);
            if(!b2){
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCMD001B015);
            }

            dstManager.commit(transaction);
        } catch (BizException e) {
            Logs.error("MdContractApplicationServiceImpl.skuSave.BizException", e);
            dstManager.rollback(transaction);
            return Results.of(e.getError(),false);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.skuSave.Exception", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B013,false);
        }

        return Results.ofSuccess();
    }

    /**
     * 获取续签合同的开始时间
     * 开始时间：默认为主合同（新签、补签、续签）下所有生效中、待生效合同（已审核且未终止的合同） 最大结束时间+1。 若无生效中或待生效合同，则取所有合同中最大结束时间加一。
     * @param param
     * @return
     */
    @Override
    public Result<MdContractRenewalStartDate> getContractRenewalStartDate(MdContractRenewalStartDateReq param) {
        MdContractRenewalStartDate mdContractRenewalStartDate = new MdContractRenewalStartDate();

        MdContractQueryCntDTO mdContractQueryCntDTO = new MdContractQueryCntDTO();
        mdContractQueryCntDTO.setContractNo(param.getContractNo());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(MdContractStatusEnum.STATUS_4.getCode());
        statusList.add(MdContractStatusEnum.STATUS_5.getCode());

        mdContractQueryCntDTO.setStatusList(statusList);

        List<Integer> signModeList = new ArrayList<>();
        signModeList.add(MdContractSignModeEnum.XQ1.getCode());
        signModeList.add(MdContractSignModeEnum.BQ2.getCode());
        signModeList.add(MdContractSignModeEnum.XQ3.getCode());

        mdContractQueryCntDTO.setSignModeList(signModeList);

        List<MdContractMasPO> mdContractMasPOList = listMdContract(mdContractQueryCntDTO);
        LocalDateTime startDateTime = LocalDateTime.now();
        //若没有,就取合同序号结束时间+1
        if(CollectionUtils.isEmpty(mdContractMasPOList)){
            mdContractQueryCntDTO.setStatusList(null);
            mdContractMasPOList = listMdContract(mdContractQueryCntDTO);
            if(CollectionUtils.isEmpty(mdContractMasPOList)){
                mdContractQueryCntDTO = new MdContractQueryCntDTO();
                mdContractQueryCntDTO.setContractNo(param.getContractNo());
                mdContractQueryCntDTO.setContractSerial(param.getContractSerial());
                mdContractMasPOList = listMdContract(mdContractQueryCntDTO);
                if(CollectionUtils.isEmpty(mdContractMasPOList)){
                    mdContractRenewalStartDate.setStartDate(startDateTime);
                    return Results.ofSuccess(mdContractRenewalStartDate);
                }
                else{
                    startDateTime = mdContractMasPOList.get(0).getStartDate().plus(1, ChronoUnit.DAYS);
                    mdContractRenewalStartDate.setStartDate(startDateTime);
                    return Results.ofSuccess(mdContractRenewalStartDate);
                }
            }
            else{
                mdContractRenewalStartDate = getMdContractRenewalStartDate(mdContractMasPOList);
                return Results.ofSuccess(mdContractRenewalStartDate);
            }

        }
        else{
            mdContractRenewalStartDate = getMdContractRenewalStartDate(mdContractMasPOList);
            return Results.ofSuccess(mdContractRenewalStartDate);
        }
    }

    private MdContractRenewalStartDate getMdContractRenewalStartDate(List<MdContractMasPO> mdContractMasPOList){
        MdContractRenewalStartDate mdContractRenewalStartDate = new MdContractRenewalStartDate();

        //开始时间：默认为主合同下所有生效中、待生效合同（已审核且未终止的合同） 最大结束时间+1
        LocalDateTime startDateTime = mdContractMasPOList.get(0).getEndDate();
        for(MdContractMasPO mdContractMasPO : mdContractMasPOList){
            if(startDateTime.isBefore(mdContractMasPO.getEndDate())){
                startDateTime = mdContractMasPO.getEndDate();
            }
        }

        mdContractRenewalStartDate.setStartDate(startDateTime);

        return mdContractRenewalStartDate;
    }

    /**
     * 合同基本信息暂存
     * @param param
     * @return
     */
    @Override
    public Result<MdContractBasicDTO> basicSave(MdContractBasicSaveReq param) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);
        //暂存操作类型,1:新增暂存,2修改暂存,3补签修改暂存,4续签修改暂存,5补签新增暂存,6续签新增暂存

        MdContractQueryCntDTO mdContractQueryCntDTO = new MdContractQueryCntDTO();
        mdContractQueryCntDTO.setContractNo(param.getContractNo());
        //补签校验
        //补签：主合同对应签订序号状态为生效中的合同可补签，若当前主合同无生效中序号合同，则已终止、已过期合同可发起补签。主合同若有已发起的草稿、待提交、审核中合同时，则不能在发起补签。
        if(MdContractOpTypeEnum.STATUS_5.getCode().equals(param.getOpType())){
            List<Integer> statusList = new ArrayList<>();
            statusList.add(MdContractStatusEnum.STATUS_1.getCode());
            statusList.add(MdContractStatusEnum.STATUS_2.getCode());
            statusList.add(MdContractStatusEnum.STATUS_3.getCode());

            mdContractQueryCntDTO.setStatusList(statusList);
            Integer cnt = countMdContract(mdContractQueryCntDTO);
            if(cnt > 0){
                return Results.of(MdErrorCodeEnum.SCMD001B016,false);
            }
        }

        //续签校验
        //续签：同补签，主合同对应签订序号状态为生效中的合同可续签，若当前主合同无生效中序号合同，则已终止、已过期合同可发起续签。主合同若有已发起的草稿、待提交、审核中、待生效合同时，则不能在发起续签。
        if(MdContractOpTypeEnum.STATUS_6.getCode().equals(param.getOpType())){
            List<Integer> statusList = new ArrayList<>();
            statusList.add(MdContractStatusEnum.STATUS_1.getCode());
            statusList.add(MdContractStatusEnum.STATUS_2.getCode());
            statusList.add(MdContractStatusEnum.STATUS_3.getCode());
            statusList.add(MdContractStatusEnum.STATUS_5.getCode());

            mdContractQueryCntDTO.setStatusList(statusList);
            Integer cnt = countMdContract(mdContractQueryCntDTO);
            if(cnt > 0){
                return Results.of(MdErrorCodeEnum.SCMD001B017,false);
            }
        }

        //编辑校验 草稿、待提交状态可编辑
        if(!(MdContractOpTypeEnum.STATUS_1.getCode().equals(param.getOpType())
                || MdContractOpTypeEnum.STATUS_5.getCode().equals(param.getOpType())
                || MdContractOpTypeEnum.STATUS_6.getCode().equals(param.getOpType()))){
            MdContractGetDetailsReq mdContractGetDetailsReq = new MdContractGetDetailsReq();
            mdContractGetDetailsReq.setId(param.getId());
            MdContractDetailsDTO details = getDetailById(mdContractGetDetailsReq);
            if(null != details){
                if(!(MdContractStatusEnum.STATUS_1.getCode().equals(details.getStatus())
                        || MdContractStatusEnum.STATUS_3.getCode().equals(details.getStatus()))){
                    return Results.of(MdErrorCodeEnum.SCMD001B018,false);
                }
            }

        }


        MdContractBasicDTO mdContractBasicDTO = new MdContractBasicDTO();
        try {
            MdContractMasPO mdContractMasPO;
            //新增或者修改合同主信息
            if(MdContractOpTypeEnum.STATUS_1.getCode().equals(param.getOpType())
                    || MdContractOpTypeEnum.STATUS_5.getCode().equals(param.getOpType())
                    || MdContractOpTypeEnum.STATUS_6.getCode().equals(param.getOpType())){
                mdContractMasPO = basicAddSave(param);
                param.setId(mdContractMasPO.getId());
            }
            else{
                mdContractMasPO = basicModifySave(param);
            }

            mdContractBasicDTO.setContractNo(mdContractMasPO.getContractNo());
            mdContractBasicDTO.setContractSerial(mdContractMasPO.getContractSerial());
            mdContractBasicDTO.setId(mdContractMasPO.getId());
            mdContractBasicDTO.setManualContractNo(mdContractMasPO.getManualContractNo());

            param.setContractSerial(mdContractMasPO.getContractSerial());

            //修改合同范围
            Boolean aBoolean = saveContractScopeList(param,mdContractMasPO);
            if(!aBoolean){
                throw new BizException(MdErrorCodeEnum.SCMD001B009);
            }

            dstManager.commit(transaction);
        } catch (Exception e) {
            Logs.error("MdContractApplicationServiceImpl.basicSave.error", e);
            dstManager.rollback(transaction);
            return Results.of(MdErrorCodeEnum.SCMD001B011,false);
        }

        return Results.ofSuccess(mdContractBasicDTO);
    }

    /**
     * 定时任务执行合同,入参可为空
     * @param tenantIds
     * @param contractNo
     * @return
     */
    @Override
    public Result<Void> execContractJob(String tenantIds, String contractNo) {
        List<String> tenantIdList = new ArrayList<>();

        if(StringUtils.isNotEmpty(tenantIds)){
            tenantIdList = Arrays.asList(tenantIds.split(","));
        }
        else{
            //查询所有的租户，然后按照租户遍历合同任务
            tenantIdList = mdContractRepositoryService.getMdContractMasMapper().listTenant();
        }

        for(String tenantId : tenantIdList){
            try{
                Logs.info("MdContractMasDomainServiceImpl.execContractJob.tenantId" + tenantId);

                TenantContext.clear();
                TenantContext.set(tenantId);

                LoginUserDTO loginUser = new LoginUserDTO();
                loginUser.setName("system");
                loginUser.setCode("system");
                loginUser.setUid(0L);
                String KEY_LOGIN_USER = "_login_user_idaas";
                ThreadLocals.setValue(ClientIdentCons.KEY_LOGIN_USER, loginUser);

                //已过结束时间的，失效合同
                expireContract(contractNo);
                //生效
                effectiveContract(contractNo);
            }catch (Exception e){
                Logs.error("MdContractMasDomainServiceImpl.execContractJob.error:" + tenantId ,e);
                e.printStackTrace();
            }
        }
        return Results.ofSuccess();
    }

    /**
     * 生效合同
     */
    private void effectiveContract(String contractNo){
//        若续签合同晚于延期合同审核，则续签合同审核通过后，续签合同生效，延期合同失效
//        若续签合同早于延期合同审核，延期合同审核后无任何变化
//        相当于续签和延期合同 生效重叠时，优先以续签合同为准
        MdContractMasPO mdContractMasPOParam = new MdContractMasPO();
        mdContractMasPOParam.setStatus(MdContractStatusEnum.STATUS_5.getCode());
        mdContractMasPOParam.setContractNo(contractNo);

        List<MdContractMasPO> mdContractMasPOList = mdContractRepositoryService.getMdContractMasMapper().listPendingEffectivenessContract(mdContractMasPOParam);

        for(MdContractMasPO mdContractMasPO : mdContractMasPOList){

            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus transaction = dstManager.getTransaction(def);

            try {
                //先将生效中的改为已失效
                LambdaQueryWrapper<MdContractMasPO> mdContractPOLambdaQueryWrapper = new LambdaQueryWrapper<MdContractMasPO>()
                        .eq(MdContractMasPO::getContractNo, mdContractMasPO.getContractNo())
                        .eq(MdContractMasPO::getStatus,MdContractStatusEnum.STATUS_4.getCode())
                        .orderByDesc(MdContractMasPO::getId);

                List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().selectList(mdContractPOLambdaQueryWrapper);
                for(MdContractMasPO mdContractMas : mdContractMasPOS){
                    mdContractMas.setStatus(MdContractStatusEnum.STATUS_6.getCode());
                    execExpireContract(mdContractMas);
                }

                mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_4.getCode());
                mdContractMasPO.setRealEffectiveDate(LocalDateTime.now());

                //生效合同
                int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

                if(cnt > 0){
                    //上报记录
                    MdContractDetailsDTO detail = new MdContractDetailsDTO();
                    detail.setId(mdContractMasPO.getId());
                    detail.setContractNo(mdContractMasPO.getContractNo());
                    MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, MdContractStatusEnum.STATUS_4.getCode(),MdContractStatusEnum.STATUS_4.getDesc());
                    statusTransition(mdCommonStatusProcessDTO);
                }
                Logs.info("MdContractApplicationServiceImpl.effectiveContract.合同生效:" + JSON.toJSONString(mdContractMasPO));
                dstManager.commit(transaction);
            } catch (Exception e) {
                Logs.error("MdContractApplicationServiceImpl.effectiveContract.error:" + JSON.toJSONString(mdContractMasPO), e);
                dstManager.rollback(transaction);
            }

        }
    }

    /**
     * 失效合同
     */
    private void expireContract(String contractNo){
        MdContractMasPO mdContractMasPOParam = new MdContractMasPO();

        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime endDate = LocalDateTime.of(currentDateTime.getYear(), currentDateTime.getMonth(), currentDateTime.getDayOfMonth()
                , 0, 0,0);

        mdContractMasPOParam.setEndDate(endDate);

        mdContractMasPOParam.setStatus(MdContractStatusEnum.STATUS_4.getCode());
        mdContractMasPOParam.setContractNo(contractNo);
        List<MdContractMasPO> mdContractMasPOS = mdContractRepositoryService.getMdContractMasMapper().listExpireContract(mdContractMasPOParam);

        for(MdContractMasPO mdContractMasPO : mdContractMasPOS){
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus transaction = dstManager.getTransaction(def);

            try {
                mdContractMasPO.setStatus(MdContractStatusEnum.STATUS_8.getCode());
                execExpireContract(mdContractMasPO);
                dstManager.commit(transaction);
            } catch (Exception e) {
                Logs.error("MdContractApplicationServiceImpl.expireContract.error:" + JSON.toJSONString(mdContractMasPO), e);
                dstManager.rollback(transaction);
            }
        }

    }

    private void execExpireContract(MdContractMasPO mdContractMasPO) {

        mdContractMasPO.setRealExpireDate(LocalDateTime.now());
        int cnt = mdContractRepositoryService.getMdContractMasMapper().updateById(mdContractMasPO);

        if(cnt > 0){
            //上报记录
            MdContractDetailsDTO detail = new MdContractDetailsDTO();
            detail.setId(mdContractMasPO.getId());
            detail.setContractNo(mdContractMasPO.getContractNo());
            MdCommonStatusProcessDTO mdCommonStatusProcessDTO = assembleParam(detail, MdContractStatusEnum.STATUS_8.getCode(),MdContractStatusEnum.STATUS_8.getDesc());
            statusTransition(mdCommonStatusProcessDTO);
        }
        Logs.info("MdContractApplicationServiceImpl.expireContract.合同失效:" + JSON.toJSONString(mdContractMasPO));

    }

    /**
     * 合同商品查询，本方法只支持单个部门
     * @return
     */
    @Override
    public List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO req) {
        //查询门店 + 商品  与 店组群 + 商品
        QueryUpDeptListReq queryUpDeptListReq = new QueryUpDeptListReq();
        queryUpDeptListReq.setDeptCode(req.getDeptCode());
        queryUpDeptListReq.setClassCode(GroupDeptEnum.CONTRACT_GROUP.getCode());
        queryUpDeptListReq.setOpenStatus(1);
        //查出当前部门下店组群 用于下面批量查询
        QueryUpDeptListResp resp = baseDataSystemFeignClient.queryUpDeptList(queryUpDeptListReq);
        List<String> codes = new ArrayList<>();
        if (resp != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(resp.getRows())) {
            codes = resp.getRows().stream().map(QueryUpDeptListResp.UpDeptInfo::getCode).collect(Collectors.toList());
        }

        ListContractGoodsDeptParam4Db param = new ListContractGoodsDeptParam4Db();
        List<String> deptCodeList = new ArrayList<>();
        deptCodeList.add(req.getDeptCode());
        param.setDeptCodeList(deptCodeList);
        param.setSkuCodeList(req.getSkuCodeList());
        param.setSupplierCode(req.getSupplierCode());
        if(req.getMainSupplierMode() != null) {
            param.setMainSupplierMode(req.getMainSupplierMode());
        }
        param.setStoreGroupCodeList(codes);

        List<ContractGoodsDeptDTO> mdContractGoodsDeptPOList = mdContractGoodsDeptRepositoryService.getMdContractGoodsDeptMapper().listContractGoodsDept(param);

        Map<String, List<ContractGoodsDeptDTO>> deptGoodsMap = mdContractGoodsDeptPOList.parallelStream().collect(Collectors.groupingBy(ContractGoodsDeptDTO::getSkuCode));

        List<ContractGoodsDeptDTO> mdContractGoodsDeptPOResultList = new ArrayList<>();
        for (List<ContractGoodsDeptDTO> value : deptGoodsMap.values()) {
            //升序
            value.sort(Comparator.comparing(ContractGoodsDeptDTO::getContractNo));

            //降序 TODO 系统参数
//            value.sort(Comparator.comparing(ContractGoodsDeptDTO::getContractNo).reversed());

            //多供应商场景
            if(ApplyCateEnum.PURCH.getCode().equals(req.getApplyCate())
                    || ApplyCateEnum.DELIVERY_TO_PURCH.getCode().equals(req.getApplyCate())){
                Map<String,ContractGoodsDeptDTO> supplierDeptGoodsMap = new HashMap<>();
                for (ContractGoodsDeptDTO contractGoodsDeptDTO : value) {
                    if(supplierDeptGoodsMap.containsKey(contractGoodsDeptDTO.getSupplierCode())){
                        ContractGoodsDeptDTO contractGoodsDept = supplierDeptGoodsMap.get(contractGoodsDeptDTO.getSupplierCode());

                        //门店
                        if(ObjectUtils.notEqual(1,contractGoodsDept.getDeptType())){
                            continue;
                        }
                        else if(ObjectUtils.notEqual(1,contractGoodsDeptDTO.getDeptType())){
                            supplierDeptGoodsMap.put(contractGoodsDeptDTO.getSupplierCode(),contractGoodsDeptDTO);
                        }
                    }
                    else{
                        supplierDeptGoodsMap.put(contractGoodsDeptDTO.getSupplierCode(),contractGoodsDeptDTO);
                    }
                }
                mdContractGoodsDeptPOResultList.addAll(supplierDeptGoodsMap.values());
            }
            else{
                ContractGoodsDeptDTO contractGoodsDeptDTOResult = value.get(0);

                for (ContractGoodsDeptDTO contractGoodsDeptDTO : value) {
                    //门店
                    if(ObjectUtils.notEqual(1,contractGoodsDeptDTO.getDeptType())){
                        contractGoodsDeptDTOResult = contractGoodsDeptDTO;
                        continue;
                    }
                    if(ObjectUtils.notEqual(0,contractGoodsDeptDTO.getMainSupplierMode())){
                        contractGoodsDeptDTOResult = contractGoodsDeptDTO;
                    }
                }
                mdContractGoodsDeptPOResultList.add(contractGoodsDeptDTOResult);
            }

        }

        return mdContractGoodsDeptPOResultList;
    }

    @Override
    public List<ContractGoodsDeptDTO> listContractGoods(ContractGoodsDeptQueryDTO param) {
        // 查询所有符合条件的对照数据
        LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, param.getContractNo());
        queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, param.getSupplierCode());
        queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, param.getSkuCodeList());
        List<MdContractGoodsDeptPO> goodsDeptPOS = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        return goodsDeptPOS.stream().map(MdContractGoodsDeptConvert.INSTANCE::convertPo2DTO).collect(Collectors.toList());
    }

    @Override
    public List<ContractGoods4DeptResultDTO> queryContractGoods4Dept(ContractGoodsQueryDTO param) {
        if (CollectionUtils.isEmpty(param.getSkuList())) {
            return Lists.newArrayList();
        }
        if (param.getSkuList().size() > 100) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P017);
        }
        // 查询所有符合条件的对照数据
        Set<String> skuCodeSet = param.getSkuList().stream()
                .map(ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO::getSkuCode).collect(Collectors.toSet());
        LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, param.getContractNo());
        queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, param.getSupplierCode());
        queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, skuCodeSet);
        List<MdContractGoodsDeptPO> goodsDeptPOS = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        Map<String, List<MdContractGoodsDeptPO>> dbContractGoodsMap = goodsDeptPOS.stream().collect(Collectors.groupingBy(MdContractGoodsDeptPO::getSkuCode));

        // 获取不存在的数据 部门再查一次上级店组群 店组群直接组装商品信息
        Set<String> deptSet = Sets.newHashSet();
        Set<String> groupSet = Sets.newHashSet();
        param.getSkuList().forEach(item -> {
            // 遍历部门
            if (CollectionUtils.isNotEmpty(item.getDeptCodeList())) {
                deptSet.addAll(item.getDeptCodeList());
            }
            // 遍历店组群
            if (CollectionUtils.isNotEmpty(item.getGroupCodeList())) {
                groupSet.addAll(item.getGroupCodeList());
            }
        });
        if (CollectionUtils.isEmpty(deptSet) && CollectionUtils.isEmpty(groupSet)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P018);
        }

        // 上级店组群信息
        Map<String, QueryBatchDeptListResp.Rows> upDeptMap = Maps.newHashMap();

        // 部门与店组群信息
        Map<String, String> deptMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(deptSet)) {
            // 获取部门上级店组群
            List<QueryBatchDeptListResp.Rows> groupList = Lists.newArrayList();
            try {
                QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                        .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                        .deptCodeList(new ArrayList<>(deptSet))
                        .build();
                groupList.addAll(baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows());
            } catch (Exception e) {
                Logs.error("获取部门上级店组群失败", e);
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
            }

            upDeptMap = groupList.stream()
                    .collect(Collectors.toMap(QueryBatchDeptListResp.Rows::getCode, obj -> obj));

            // 获取所有部门
            StoreDetailListResp distDeptListResp = null;
            try {
                QueryDeptListReq distDeptListReq = QueryDeptListReq.builder()
                        .deptCodeList(new ArrayList<>(deptSet))
                        .pageSize(Integer.MAX_VALUE)
                        .build();
                distDeptListResp = baseDataSystemFeignClient.queryDeptList(distDeptListReq);
            } catch (Exception e) {
                Logs.error("获取所有部门失败", e);
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
            }

            if (null != distDeptListResp && CollectionUtils.isNotEmpty(distDeptListResp.getRows())) {
                distDeptListResp.getRows().forEach(item -> {
                    deptMap.put(item.getCode(), item.getName());
                });
            }

            // 校验部门是否全部存在
            deptSet.forEach(item -> {
                if (!deptMap.containsKey(item)) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P019, new String[]{item});
                }
            });
        }

        // 获取所有店组群
        if (CollectionUtils.isNotEmpty(groupSet)) {
            try {
                QueryDeptGroupBatchReq deptListReq = QueryDeptGroupBatchReq.builder()
                        .codeList(new ArrayList<>(groupSet))
                        .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                        .build();
                QueryDeptGroupBatchResp queryGroupDeptListResp = baseDataSystemFeignClient.queryDeptGroupBatch(deptListReq);

                if (null != queryGroupDeptListResp && CollectionUtils.isNotEmpty(queryGroupDeptListResp.getDeptGroupList())) {
                    queryGroupDeptListResp.getDeptGroupList().forEach(item -> {
                        deptMap.put(item.getCode(), item.getName());
                    });
                }
            } catch (Exception e) {
                BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCWDS012P021.getErrorCode(), e.getMessage());
            }

            // 校验店组群是否全部存在
            groupSet.forEach(item -> {
                if (!deptMap.containsKey(item)) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P020, new String[]{item});
                }
            });
        }

        // 获取商品信息
        GoodsSearchQueryReq goodsParam = new GoodsSearchQueryReq();
        goodsParam.setSkuCodes(new ArrayList<>(skuCodeSet));
        goodsParam.setSource("supplychain-component");
        goodsParam.setCurrent(1L);
        goodsParam.setPageSize(100L);
        List<GoodsSearchInfo> goodsSearchInfoList = commonGoodsService.simpleSearchQuery(goodsParam).getRows();
        if (CollectionUtils.isEmpty(goodsSearchInfoList)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P009);
        }

        Map<String, GoodsSearchInfo> goodsMap = goodsSearchInfoList.stream().collect(Collectors.toMap(GoodsSearchInfo::getSkuCode, obj -> obj));

        // 遍历组装数据
        List<ContractGoods4DeptResultDTO> resultList = Lists.newArrayList();
        Map<String, QueryBatchDeptListResp.Rows> finalUpDeptMap = upDeptMap;
        param.getSkuList().forEach(item -> {
            GoodsSearchInfo goodsSimpleInfo = goodsMap.get(item.getSkuCode());
            if (null == goodsSimpleInfo) {
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P010, new String[]{item.getSkuCode()});
            }

            List<ContractGoods4DeptDTO> list = Lists.newArrayList();
            ContractGoods4DeptResultDTO contractGoods4DeptResultDTO = new ContractGoods4DeptResultDTO();
            contractGoods4DeptResultDTO.setSkuCode(item.getSkuCode());
            contractGoods4DeptResultDTO.setDetailList(list);
            resultList.add(contractGoods4DeptResultDTO);
            // 不存在数据则拼装商品信息
            if (dbContractGoodsMap.isEmpty() || !dbContractGoodsMap.containsKey(item.getSkuCode())) {
                // 组装店组群
                if (CollectionUtils.isNotEmpty(item.getGroupCodeList())) {
                    item.getGroupCodeList().forEach(obj -> {
                        list.add(buildContractGoods(goodsSimpleInfo, null, obj, deptMap.get(obj)));
                    });
                }

                // 组装部门
                if (CollectionUtils.isNotEmpty(item.getDeptCodeList())) {
                    item.getDeptCodeList().forEach(obj -> {
                        list.add(buildContractGoods(goodsSimpleInfo, obj, null, deptMap.get(obj)));
                    });
                }
            } else {
                // 存在数据则直接返回
                List<MdContractGoodsDeptPO> contractGoodsDeptPOS = dbContractGoodsMap.get(item.getSkuCode());
                Map<String, MdContractGoodsDeptPO> deptContractGoodsMap = contractGoodsDeptPOS.stream()
                        .collect(Collectors.toMap(MdContractGoodsDeptPO::getCode, obj -> obj));
                // 组装店组群
                if (CollectionUtils.isNotEmpty(item.getGroupCodeList())) {
                    item.getGroupCodeList().forEach(obj -> {
                        MdContractGoodsDeptPO mdContractGoodsDeptPO = deptContractGoodsMap.get(obj);
                        if (null != mdContractGoodsDeptPO) {
                            list.add(MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(mdContractGoodsDeptPO));
                        } else {
                            list.add(buildContractGoods(goodsSimpleInfo, null, obj, deptMap.get(obj)));
                        }
                    });
                }

                // 组装部门
                if (CollectionUtils.isNotEmpty(item.getDeptCodeList())) {
                    item.getDeptCodeList().forEach(obj -> {
                        MdContractGoodsDeptPO mdContractGoodsDeptPO = deptContractGoodsMap.get(obj);
                        if (null != mdContractGoodsDeptPO) {
                            list.add(MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(mdContractGoodsDeptPO));
                        } else {
                            // 判断上级店组群是否存在
                            if (finalUpDeptMap.containsKey(obj)) {
                                Optional<QueryBatchDeptListResp.DeptGroup> dept = finalUpDeptMap.get(obj).getDeptGroupList()
                                        .stream().max(Comparator.comparing(QueryBatchDeptListResp.DeptGroup::getLevel));
                                // 上级店组群存在且有数据
                                if (dept.isPresent() && deptContractGoodsMap.containsKey(dept.get().getCode())) {
                                    MdContractGoodsDeptPO groupPO = deptContractGoodsMap.get(dept.get().getCode());
                                    ContractGoods4DeptDTO contractGoodsDeptDTO = MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(groupPO);
                                    contractGoodsDeptDTO.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
                                    contractGoodsDeptDTO.setCode(obj);
                                    contractGoodsDeptDTO.setName(deptMap.get(obj));
                                    list.add(contractGoodsDeptDTO);
                                } else {
                                    list.add(buildContractGoods(goodsSimpleInfo, obj, null, deptMap.get(obj)));
                                }
                            } else {
                                list.add(buildContractGoods(goodsSimpleInfo, obj, null, deptMap.get(obj)));
                            }
                        }
                    });
                }
            }
        });
        return resultList;
    }

    @Override
    public List<ContractGoods4DeptResultDTO> queryContractGoods4DeptCancel(ContractGoodsQueryDTO param) {
        if (CollectionUtils.isEmpty(param.getSkuList())) {
            return Lists.newArrayList();
        }
        if (param.getSkuList().size() > 100) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCMD999B012);
        }

        List<ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO> skuList = param.getSkuList();

        // 查询所有符合条件的对照数据
        Set<String> skuCodeSet = param.getSkuList().stream()
                .map(ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO::getSkuCode).collect(Collectors.toSet());
        LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, param.getContractNo());
        queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, param.getSupplierCode());
        queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, skuCodeSet);
        List<MdContractGoodsDeptPO> goodsDeptPOS = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        Map<String, List<MdContractGoodsDeptPO>> dbContractGoodsMap = goodsDeptPOS.stream().collect(Collectors.groupingBy(MdContractGoodsDeptPO::getSkuCode));

        // 获取所有店组群数据
        Set<String> groupCodeSet = goodsDeptPOS.stream()
                .filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode()))
                .map(MdContractGoodsDeptPO::getCode)
                .collect(Collectors.toSet());

        Map<String, List<QueryGroupDeptListResp.Rows>> theDeptMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupCodeSet)) {
            List<QueryGroupDeptListResp.Rows> theDeptList = Lists.newArrayList();
            try {
                QueryGroupDeptListReq queryGroupDeptListReq = QueryGroupDeptListReq.builder()
                        .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                        .codeList(new ArrayList<>(groupCodeSet))
                        .build();
                theDeptList.addAll(baseDataSystemFeignClient.queryGroupDeptList(queryGroupDeptListReq).getRows());
            } catch (Exception e) {
                Logs.error("查询店组群列表接口失败", e);
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
            }

            theDeptMap = theDeptList.stream()
                    .collect(Collectors.groupingBy(QueryGroupDeptListResp.Rows::getGroupCode));
        }

        // 遍历组装数据
        List<ContractGoods4DeptResultDTO> resultList = Lists.newArrayList();
        Map<String, List<QueryGroupDeptListResp.Rows>> finalTheDeptMap = theDeptMap;
        dbContractGoodsMap.forEach((skuCode, list) -> {
            ContractGoods4DeptResultDTO contractGoods4DeptResultDTO = new ContractGoods4DeptResultDTO();
            List<ContractGoods4DeptDTO> dtoList = Lists.newArrayList();
            contractGoods4DeptResultDTO.setSkuCode(skuCode);
            Set<String> deptCodeSet = Sets.newHashSet();
            List<MdContractGoodsDeptPO> deptList = list.stream()
                    .filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT.getCode()))
                    .collect(Collectors.toList());
            List<MdContractGoodsDeptPO> groupList = list.stream()
                    .filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode()))
                    .collect(Collectors.toList());
            // 部门存在则直接复制数据
            if (CollectionUtils.isNotEmpty(deptList)) {
                deptList.forEach(item -> {
                    ContractGoods4DeptDTO dto = MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(item);
                    dtoList.add(dto);
                    deptCodeSet.add(item.getCode());
                });
            }

            // 店组群数据直接复制
            if (CollectionUtils.isNotEmpty(groupList)) {
                groupList.forEach(item -> {
                    ContractGoods4DeptDTO dto = MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(item);
                    dtoList.add(dto);

                    // 店组群下部门数据复制 处理过的跳过
                    if (finalTheDeptMap.containsKey(item.getCode())) {
                        List<QueryGroupDeptListResp.Rows> depts = finalTheDeptMap.get(item.getCode());
                        depts.forEach(dept -> {
                            if (!deptCodeSet.contains(dept.getDeptCode())) {
                                ContractGoods4DeptDTO deptDto = MdContractGoodsDeptConvert.INSTANCE.convertPo2DeptDTO(item);
                                deptDto.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
                                deptDto.setCode(dept.getDeptCode());
                                deptDto.setName(dept.getDeptName());
                                dtoList.add(deptDto);
                            }
                        });
                    }
                });
            }

            // 如果传入部门数据则进行过滤
            List<ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO> skuDtoList = skuList.stream()
                    .filter(item -> item.getSkuCode().equals(skuCode)).collect(Collectors.toList());
            Set<String> deptCode = skuDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO::getDeptCodeList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
            Set<String> groupCode = skuDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(ContractGoodsQueryDTO.ContractGoods4DeptQueryDTO::getGroupCodeList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(deptCode) || CollectionUtils.isNotEmpty(groupCode)) {
                List<ContractGoods4DeptDTO> detailList = Lists.newArrayList();
                detailList.addAll(dtoList.stream()
                        .filter(item -> (item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT.getCode()) && deptCode.contains(item.getCode()))
                                || (item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode()) && groupCode.contains(item.getCode())))
                        .collect(Collectors.toList()));
                contractGoods4DeptResultDTO.setDetailList(detailList);
            } else {
                contractGoods4DeptResultDTO.setDetailList(dtoList);
            }

            if (CollectionUtils.isNotEmpty(contractGoods4DeptResultDTO.getDetailList())) {
                resultList.add(contractGoods4DeptResultDTO);
            }
        });
        return resultList;
    }

    @Override
    public List<String> listTenant() {
        //查询所有的租户
        return mdContractRepositoryService.getMdContractMasMapper().listTenant();
    }

    @Override
    public List<GoodsSuppResp> queryGoodsSupp(QueryGoodsSuppReq queryGoodsSuppReq) {

        if (queryGoodsSuppReq.getSkuCodeList().size() > 100) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCMD999B012);
        }

        List<String> groupCodeList = Lists.newArrayList();
        Map<String, String> groupMap = Maps.newHashMap();
        // 获取上级店组群的数据
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                .deptCodeList(queryGoodsSuppReq.getDeptCodeList())
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();
        if (CollectionUtils.isNotEmpty(groupList)) {
            // 把店组群编码加入查询条件
            groupList.forEach(item -> {
                Optional<QueryBatchDeptListResp.DeptGroup> first = item.getDeptGroupList()
                        .stream().max(Comparator.comparing(QueryBatchDeptListResp.DeptGroup::getLevel));
                if (first.isPresent()) {
                    groupCodeList.add(first.get().getCode());
                    groupMap.put(item.getCode(), first.get().getCode());
                }
            });
        }
        groupCodeList.addAll(queryGoodsSuppReq.getDeptCodeList());

        // 获取包含店组群的合同商品数据
        List<GoodsSuppResp> resultList = Lists.newArrayList();
        QueryGoodsSuppReq query = new QueryGoodsSuppReq();
        query.setSkuCodeList(queryGoodsSuppReq.getSkuCodeList());
        query.setDeptCodeList(groupCodeList.stream().distinct().collect(Collectors.toList()));
        List<GoodsSuppDetailResp> dbDetailList =
                mdContractGoodsDeptRepositoryService.getMdContractGoodsDeptMapper().queryGoodsSupp(query);
        Map<String, List<GoodsSuppDetailResp>> dbDetailMap = dbDetailList.stream().collect(Collectors.groupingBy(GoodsSuppDetailResp::getSkuCode));

        // 处理数据
        for (String skuCode : queryGoodsSuppReq.getSkuCodeList()) {
            GoodsSuppResp goodsSuppResp = new GoodsSuppResp();
            List<GoodsSuppDetailResp> detailList = Lists.newArrayList();
            goodsSuppResp.setSkuCode(skuCode);
            goodsSuppResp.setSupplierList(detailList);
            resultList.add(goodsSuppResp);
            for (String deptCode : queryGoodsSuppReq.getDeptCodeList()) {
                if (dbDetailMap.containsKey(skuCode)) {
                    List<GoodsSuppDetailResp> dbSkuList = dbDetailMap.get(skuCode);
                    // 先取部门数据是否存在
                    List<GoodsSuppDetailResp> dbDeptList = dbSkuList.stream()
                            .filter(item -> item.getDeptCode().equals(deptCode) && item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT.getCode()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dbDeptList)) {
                        //获取主供应商
                        dbDeptList.stream().filter(item -> item.getMainSupplierMode().equals(1))
                                .forEach(item -> {
                                    detailList.add(item);
                                });
                        //获取次供应商
                        dbDeptList.stream().filter(item -> item.getMainSupplierMode().equals(0))
                                .forEach(item -> {
                                    detailList.add(item);
                                });
                    } else {
                        // 再取店组群数据是否存在
                        String groupCode = groupMap.get(deptCode);
                        if (StringUtils.isNotEmpty(groupCode)) {
                            List<GoodsSuppDetailResp> dbGroupList = dbSkuList.stream()
                                    .filter(item -> item.getDeptCode().equals(groupCode) && item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode()))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dbGroupList)) {
                                //获取主供应商
                                dbGroupList.stream().filter(item -> item.getMainSupplierMode().equals(1))
                                        .forEach(item -> {
                                            GoodsSuppDetailResp savePo = new GoodsSuppDetailResp();
                                            BeanUtils.copyProperties(item, savePo);
                                            savePo.setDeptCode(deptCode);
                                            savePo.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
                                            detailList.add(savePo);
                                        });
                                //获取次供应商
                                dbGroupList.stream().filter(item -> item.getMainSupplierMode().equals(0))
                                        .forEach(item -> {
                                            GoodsSuppDetailResp savePo = new GoodsSuppDetailResp();
                                            BeanUtils.copyProperties(item, savePo);
                                            savePo.setDeptCode(deptCode);
                                            savePo.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
                                            detailList.add(savePo);
                                        });
                            }
                        }
                    }

                }
            }
        }

        return resultList;
    }

    /**
     * 组装合同商品
     * @param goods
     * @param deptCode
     * @param groupCode
     * @return
     */
    private ContractGoods4DeptDTO buildContractGoods(GoodsSearchInfo goods, String deptCode, String groupCode, String name) {
        ContractGoods4DeptDTO contractGoods4DeptDTO = new ContractGoods4DeptDTO();
        if (null != deptCode) {
            contractGoods4DeptDTO.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
            contractGoods4DeptDTO.setCode(deptCode);
        }
        if (null != groupCode) {
            contractGoods4DeptDTO.setDeptType(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode());
            contractGoods4DeptDTO.setCode(groupCode);
        }
        contractGoods4DeptDTO.setName(name);
        contractGoods4DeptDTO.setSkuCode(goods.getSkuCode());
        contractGoods4DeptDTO.setSkuName(goods.getSkuName());
        contractGoods4DeptDTO.setBarcode(goods.getSkuBarCode());
        contractGoods4DeptDTO.setSkuNo(goods.getGoodsNo());
        contractGoods4DeptDTO.setSkuModel(goods.getSkuSpecModel());
        contractGoods4DeptDTO.setProducingArea(goods.getProducingArea());
        contractGoods4DeptDTO.setPurchPrice(StringUtils.isEmpty(goods.getPurchasePrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goods.getPurchasePrice()));
        contractGoods4DeptDTO.setSalePrice(StringUtils.isEmpty(goods.getReferPrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goods.getReferPrice()));

        contractGoods4DeptDTO.setPurchPriceMethod(1);
        contractGoods4DeptDTO.setPurchTaxPrice(StringUtils.isEmpty(goods.getPurchasePrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goods.getPurchasePrice()));
        return contractGoods4DeptDTO;
    }
}
