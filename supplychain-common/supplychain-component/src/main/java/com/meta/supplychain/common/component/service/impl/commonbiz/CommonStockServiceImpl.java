package com.meta.supplychain.common.component.service.impl.commonbiz;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.google.common.collect.Lists;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.entity.dto.md.component.goodsrule.StockQueryDTO;
import com.meta.supplychain.entity.dto.stock.LocItemDTO;
import com.meta.supplychain.entity.dto.stock.req.*;
import com.meta.supplychain.entity.dto.stock.resp.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.LocationContorlEnum;
import com.meta.supplychain.enums.LocationTypeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.goods.GoodsTypeEnum;
import com.meta.supplychain.infrastructure.feign.StockCostFeignClient;
import com.meta.supplychain.infrastructure.feign.StockTradeFeignClient;
import com.meta.supplychain.util.AlarmUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import feign.RetryableException;
import feign.codec.DecodeException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一库存处理实现
 */
@Component
public class CommonStockServiceImpl implements ICommonStockService {
    @Resource
    private StockTradeFeignClient stockTradeFeignClient;
    @Resource
    private StockCostFeignClient stockCostFeignClient;

    @Resource
    private BillEventServiceFactory billEventServiceFactory;

    @Resource
    private AlarmUtil alarmUtil;

    @Retryable(
            value = {
                    Exception.class,
            },        // 对哪些异常进行重试
            exclude = {
                    BizException.class, DecodeException.class
            },//排除业务异常
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 300)  // 每次重试间隔 300 毫秒
    )
    @Override
    public BatchRecordResp costStockExecute(BatchRecordReq batchRecordReq) {
        batchRecordReq.setTenantId(TenantContext.get());
        Logs.info("统一库存处理执行入参:{}", Jsons.toJson(batchRecordReq));
        try {
            return stockCostFeignClient.costStockExecute(batchRecordReq);
        } catch (RetryableException e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                alarmUtil.bizWorkWeChatAlarm("统一库存costStockExecute处理执行超时,单号:"+batchRecordReq.getBillNo()+",SocketTimeoutException："+e.getMessage());

                // 库存超时推送事件保存
                billEventServiceFactory.publishEvent(batchRecordReq.getBillNo(), BillActionTypeEnum.STOCK_EXECUTE_TIMEOUT, BatchStockReportReq.builder()
                        .uniqueId(batchRecordReq.getBillNo())
                        .executeDtoList(Collections.singletonList(batchRecordReq))
                        .build());
            }
            else{
                alarmUtil.bizWorkWeChatAlarm("统一库存costStockExecute处理执行报错,单号:"+batchRecordReq.getBillNo()+",RetryableException："+e.getMessage());
            }
            throw e;
        }
    }

    @Retryable(
            value = {
                    Exception.class,
            },        // 对哪些异常进行重试
            exclude = {
                    BizException.class, DecodeException.class
            },//排除业务异常
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 300)  // 每次重试间隔 300 毫秒
    )
    @Override
    public void costStockExecute(BatchStockReportReq batchRecord) {
        Logs.info("批量上报统一库存操作入参:{}", Jsons.toJson(batchRecord));
        try {
            stockCostFeignClient.batchExecute(batchRecord);
        } catch (RetryableException e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                alarmUtil.bizWorkWeChatAlarm("批量上报统一库存batchExecute处理执行超时,单号:"+batchRecord.getUniqueId()+",SocketTimeoutException："+e.getMessage());
                // 库存超时推送事件保存
                billEventServiceFactory.publishEvent(batchRecord.getUniqueId(), BillActionTypeEnum.STOCK_EXECUTE_TIMEOUT, batchRecord);
            }
            else{
                alarmUtil.bizWorkWeChatAlarm("批量上报统一库存batchExecute处理执行超时,单号:"+batchRecord.getUniqueId()+",RetryableException："+e.getMessage());
            }
            throw e;
        }
    }

    @Override
    public void costStockRollback(BatchRecordReq batchRecordReq) {
        if (Objects.isNull(batchRecordReq)) {
            return;
        }
        BatchReleaseAndRollBackReq batchReleaseAndRollBack = BatchReleaseAndRollBackReq.builder()
                .billNo(batchRecordReq.getBillNo())
                .billType(batchRecordReq.getBillType())
                .isSuccess(false)
                .build();
        stockCostFeignClient.costStockRollback(batchReleaseAndRollBack);
    }

    @Override
    public List<StockVO> getWhStockQquery(StockQueryWithDeptAndSkusDTO stockQueryWithDeptAndSkusDTO) {
        return stockTradeFeignClient.getWhStockQquery(stockQueryWithDeptAndSkusDTO);
    }

    @Override
    public LocStockResp locStockLock(LocStockReq locStockReq) {
        locStockReq.setSourceAppCode(SpringContextUtil.getApplicationName());
        return stockTradeFeignClient.locStockLock(locStockReq);
    }

    @Override
    public void locStockBatchRelease(List<LocReleaseDTO> batchReleaseReq) {
        stockTradeFeignClient.batchRelease(batchReleaseReq);
    }


    @Override
    public LocStockResp getSkuLocStockList(LocStockQueryReq locStockQueryReq) {
        return stockTradeFeignClient.getSkuLocStockList(locStockQueryReq);
    }
    @Retryable(
            value = {
                    Exception.class,
            },        // 对哪些异常进行重试
            exclude = {
                    BizException.class, DecodeException.class
            },//排除业务异常
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 1000)  // 每次重试间隔 1 秒
    )
    @Override
    public LocStockResp getSkuLocStockList(String whCode, List<String> skuCodeList, Integer directSign){
        List<LocItemDTO> locItemList = Lists.newArrayList();
        for (String skuCode : skuCodeList){
            locItemList.add(LocItemDTO.builder().skuCode(skuCode).build());
        }
        LocStockQueryReq locStockQueryReq = LocStockQueryReq.builder().deptCode(whCode).directFlag(directSign).skuList(locItemList).build();
        locStockQueryReq.setSourceAppCode(SpringContextUtil.getApplicationName());
        return stockTradeFeignClient.getSkuLocStockList(locStockQueryReq);
    }

    @Override
    public List<WhLocationResp> getSkuLocList(WhLocationReq locationReq) {
        return stockTradeFeignClient.getSkuLocList(locationReq);
    }

    @Retryable(
            value = {
                    Exception.class,
            },        // 对哪些异常进行重试
            exclude = {
                    BizException.class, DecodeException.class
            },//排除业务异常
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 1000)  // 每次重试间隔 1 秒
    )
    @Override
    public List<WhLocationResp> getSkuLocList(String whCode, List<String> skuCodeList, Integer directSign) {
        List<Integer> locationTypeList = Lists.newArrayList();
        List<Integer> excludeLocationControlList = Lists.newArrayList();
        if (Objects.equals(YesOrNoEnum.YES.getCode(), directSign)){
            //直流
            locationTypeList.add(LocationTypeEnum.DIRECT.getCode());
        } else if (Objects.equals(YesOrNoEnum.NO.getCode(), directSign)){
            //非直流 取储位控制<>冻结、不允许出库的正常储位(排除直流储位和退货储位，储位类型=直流、退货)
            excludeLocationControlList.add(LocationContorlEnum.FORBID_OUT.getCode());
            excludeLocationControlList.add(LocationContorlEnum.FROZEN.getCode());
            locationTypeList.add(LocationTypeEnum.PICK.getCode());
            locationTypeList.add(LocationTypeEnum.STOCK.getCode());
        }
        WhLocationReq locationReq = WhLocationReq.builder().statusList(Collections.singletonList(YesOrNoEnum.YES.getCode()))
                .locationTypeList(locationTypeList)
                .excludeLocationControlList(excludeLocationControlList)
                .deptCode(whCode)
                .skuCodeList(skuCodeList).build();
        locationReq.setSourceAppCode(SpringContextUtil.getApplicationName());
        return stockTradeFeignClient.getSkuLocList(locationReq);
    }

    @Retryable(
            value = {
                    Exception.class,
            },        // 对哪些异常进行重试
            exclude = {
                    BizException.class, DecodeException.class
            },//排除业务异常
            maxAttempts = 3,                  // 最多重试次数（含首次）
            backoff = @Backoff(delay = 1000)  // 每次重试间隔 1 秒
    )
    @Override
    public List<WhLocationResp> getAcceptSkuLocList(String deptCode, List<String> skuCodeList) {
        List<Integer> locationTypeList = Lists.newArrayList();
        List<Integer> excludeLocationControlList = Lists.newArrayList();
        //商品存在储位商品对照的正常(非退货储位、直流储位)  且 储位控制<>冻结、不允许入库
        excludeLocationControlList.add(LocationContorlEnum.FORBID_IN.getCode());
        excludeLocationControlList.add(LocationContorlEnum.FROZEN.getCode());
        locationTypeList.add(LocationTypeEnum.PICK.getCode());
        locationTypeList.add(LocationTypeEnum.STOCK.getCode());
        List<Integer> excludeLocationTypeList = Lists.newArrayList();
        excludeLocationTypeList.add(LocationTypeEnum.DIRECT.getCode());
        excludeLocationTypeList.add(LocationTypeEnum.REFUSE.getCode());
        WhLocationReq locationReq = WhLocationReq.builder().statusList(Collections.singletonList(YesOrNoEnum.YES.getCode()))
                .locationTypeList(locationTypeList)
                .excludeLocationControlList(excludeLocationControlList)
                .excludeLocationTypeList(excludeLocationTypeList)
                .deptCode(deptCode)
                .skuCodeList(skuCodeList).build();
        locationReq.setSourceAppCode(SpringContextUtil.getApplicationName());
        return stockTradeFeignClient.getSkuLocList(locationReq);
    }

    @Override
    public Map<String, List<StockVO>> getStockInfo(StockQueryDTO stockQuery) {
        List<String> skuCodeList = new ArrayList<>();
        for(StockQueryDTO.GoodsInfo goodsInfo : stockQuery.getGoodInfoList()){
            if(GoodsTypeEnum.COMB.getCode().equals(goodsInfo.getGoodsType())){
                continue;
            }
            skuCodeList.add(goodsInfo.getGoodsCode());
        }

        List<StockVO> stockList = new ArrayList<>();
        try{
            if(CollectionUtils.isNotEmpty(skuCodeList)){
                for(String deptCode : stockQuery.getStoreCodeList()){
                    StockQueryWithDeptAndSkusDTO req = new StockQueryWithDeptAndSkusDTO();
                    req.setDeptCode(deptCode);
                    req.setSkuCodeList(skuCodeList);
                    if(ObjectUtils.equals(1,stockQuery.getPeriodFlag())){
                        req.setNeedPeriodStk(true);
                    }

                    List<StockVO> whStockQquery = getWhStockQquery(req);
                    stockList.addAll(whStockQquery);
                }
            }
        }catch (Exception e){
            Logs.error("SupplychainBizGoodsRuleServiceImpl.getStockInfo.error",e);
        }

        //TODO 库存返回的deptCode为空，临时使用whCode处理
        Map<String, List<StockVO>> stockVOMap = stockList.stream().collect(Collectors.groupingBy(StockVO::getWhCode));

        return stockVOMap;
//        return stockList.stream().collect(Collectors.toMap(StockVO::getDeptCode, t -> t));

    }

    @Override
    public void locStockMove(LocStockMoveReq locStockMoveReq) {
        locStockMoveReq.setSourceAppCode(SpringContextUtil.getApplicationName());
        stockTradeFeignClient.locStockMove(locStockMoveReq);
    }

    @Override
    public List<StkWhResp> getBatchWhStockQquery(List<QueryStockSingleReq> req) {
        try {
            return stockTradeFeignClient.getBatchWhStockQquery(req);
        }catch (Exception e){
            Logs.error("SupplychainBizGoodsRuleServiceImpl.getBatchWhStockQquery.error",e);
            return new ArrayList<>();
        }
    }
}
