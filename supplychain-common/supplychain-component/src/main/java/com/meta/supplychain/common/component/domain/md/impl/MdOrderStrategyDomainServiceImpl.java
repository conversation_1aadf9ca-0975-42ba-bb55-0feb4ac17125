package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.convert.md.MdOrderStrategyConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.md.orderstrategy.MdOrderStrategyQueryDTO;
import com.meta.supplychain.entity.dto.md.req.order.MdOrderStrategyPageQueryReq;
import com.meta.supplychain.entity.dto.md.req.order.MdOrderStrategyTypeCodeQuery;
import com.meta.supplychain.entity.dto.md.resp.order.MdOrderStrategyDTO;
import com.meta.supplychain.entity.po.md.MdOrderStrategyPO;
import com.meta.supplychain.enums.md.MdDeptTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdOrderStrategyRepositoryService;
import com.meta.supplychain.common.component.domain.md.intf.IMdOrderStrategyDomainService;
import com.meta.supplychain.util.UserUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单订货策略
 * <AUTHOR>
 */
@Service
public class MdOrderStrategyDomainServiceImpl implements IMdOrderStrategyDomainService {
    
    @Resource
    private IMdOrderStrategyRepositoryService mdOrderStrategyRepositoryService;

    @Resource
    private UserUtil userUtil;
    
    @Override
    public PageResult<MdOrderStrategyDTO> pageQuery(MdOrderStrategyPageQueryReq request) {
        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();

        LambdaQueryWrapper<MdOrderStrategyPO> condition = Wrappers.lambdaQuery(MdOrderStrategyPO.class)
                .eq(request.getDeptType() != null, MdOrderStrategyPO::getDeptType, request.getDeptType())
                .eq(StringUtils.isNotBlank(request.getDeptCode()), MdOrderStrategyPO::getDeptCode, request.getDeptCode())
                .and(!deptOpInfo.getOriginDeptFlag(), innerCondition -> innerCondition
                        .apply(CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()) && CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), "1 = 0")
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getManageDeptCodeList())
                        .or()
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT_GROUP.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getGroupCodeList())
                )
                .orderByDesc(MdOrderStrategyPO::getUpdateTime);
        
        IPage<MdOrderStrategyDTO> pageResult = mdOrderStrategyRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), condition)
                .convert(MdOrderStrategyConvert.INSTANCE::po2dto);
        
        return new PageResult<>(pageResult.getTotal(), pageResult.getRecords());
    }
    
    @Override
    public MdOrderStrategyDTO listById(Long id) {
        if (id == null) {
            return null;
        }

        MdOrderStrategyPO orderStrategyPO = mdOrderStrategyRepositoryService.getById(id);
        if (orderStrategyPO == null) {
            return null;
        }

        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();
        if (deptOpInfo.getOriginDeptFlag()) {
            return MdOrderStrategyConvert.INSTANCE.po2dto(orderStrategyPO);
        }
        if (MdDeptTypeEnum.DEPT.verifyByCode(orderStrategyPO.getDeptType())
                && deptOpInfo.getManageDeptCodeList().contains(orderStrategyPO.getDeptCode())) {
            return MdOrderStrategyConvert.INSTANCE.po2dto(orderStrategyPO);
        }
        if (MdDeptTypeEnum.DEPT_GROUP.verifyByCode(orderStrategyPO.getDeptType())
                && deptOpInfo.getGroupCodeList().contains(orderStrategyPO.getDeptCode())) {
            return MdOrderStrategyConvert.INSTANCE.po2dto(orderStrategyPO);
        }
        
        return null;
    }
    
    @Override
    @Transactional
    public boolean removeByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();

        mdOrderStrategyRepositoryService.lambdaUpdate()
                .in(MdOrderStrategyPO::getId, ids)
                .and(!deptOpInfo.getOriginDeptFlag(), innerCondition -> innerCondition
                        .apply(CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()) && CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), "1 = 0")
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getManageDeptCodeList())
                        .or()
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT_GROUP.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getGroupCodeList())
                )
                .remove();

        return true;
    }
    
    @Override
    public boolean updateByDeptTypeAndDeptCode(MdOrderStrategyDTO dto) {
        if (dto == null || dto.getDeptType() == null || StringUtils.isBlank(dto.getDeptCode())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD004B001);
        }

        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();

        LambdaUpdateWrapper<MdOrderStrategyPO> updateWrapper = Wrappers.lambdaUpdate(MdOrderStrategyPO.class)
                .eq(MdOrderStrategyPO::getDeptType, dto.getDeptType())
                .eq(MdOrderStrategyPO::getDeptCode, dto.getDeptCode())
                .and(!deptOpInfo.getOriginDeptFlag(), innerCondition -> innerCondition
                        .apply(CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()) && CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), "1 = 0")
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getManageDeptCodeList())
                        .or()
                        .eq(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptType, MdDeptTypeEnum.DEPT_GROUP.getCode())
                        .in(!CollectionUtils.isEmpty(deptOpInfo.getGroupCodeList()), MdOrderStrategyPO::getDeptCode, deptOpInfo.getGroupCodeList())
                );

        updateWrapper
                .set(true, MdOrderStrategyPO::getWhCode, dto.getWhCode())
                .set(true, MdOrderStrategyPO::getWhName, dto.getWhName())
                .set(true, MdOrderStrategyPO::getStartTime, dto.getStartTime())
                .set(true, MdOrderStrategyPO::getEndTime, dto.getEndTime())
                .set(true, MdOrderStrategyPO::getSecondWhCode, dto.getSecondWhCode())
                .set(true, MdOrderStrategyPO::getSecondWhName, dto.getSecondWhName())
                .set(true, MdOrderStrategyPO::getEnableReturn, dto.getEnableReturn())
                .set(true, MdOrderStrategyPO::getEnableSecondWhReturn, dto.getEnableSecondWhReturn());

        // 更新操作人信息
        OperatorInfoHandler.fillUpdateInfo(updateWrapper);
        
        return mdOrderStrategyRepositoryService.update(updateWrapper);
    }
    
    @Override
    public boolean saveOrderStrategy(MdOrderStrategyDTO dto) {
        if (dto == null || dto.getDeptType() == null || StringUtils.isBlank(dto.getDeptCode())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD004B001);
        }
        if (StringUtils.isBlank(dto.getWhCode()) && (dto.getStartTime() == null || dto.getEndTime() == null)) {
            throw new ScBizException(MdErrorCodeEnum.SCMD004B002);
        }

        LambdaQueryWrapper<MdOrderStrategyPO> queryWrapper = Wrappers.lambdaQuery(MdOrderStrategyPO.class)
                .eq(MdOrderStrategyPO::getDeptType, dto.getDeptType())
                .eq(MdOrderStrategyPO::getDeptCode, dto.getDeptCode());

        long count = mdOrderStrategyRepositoryService.count(queryWrapper);
        
        if (count > 0) {
            return updateByDeptTypeAndDeptCode(dto);
        } else {
            MdOrderStrategyPO po = MdOrderStrategyConvert.INSTANCE.dto2po(dto);
            return mdOrderStrategyRepositoryService.save(po);
        }
    }

    @Override
    public void saveBatchOrderStrategy(List<MdOrderStrategyDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();

        dtoList.stream().filter(dto -> {
                    if (deptOpInfo.getOriginDeptFlag()) {
                        return true;
                    }
                    if (MdDeptTypeEnum.DEPT.verifyByCode(dto.getDeptType())
                            && deptOpInfo.getManageDeptCodeList().contains(dto.getDeptCode())) {
                        return true;
                    }
                    if (MdDeptTypeEnum.DEPT_GROUP.verifyByCode(dto.getDeptType())
                            && deptOpInfo.getGroupCodeList().contains(dto.getDeptCode())) {
                        return true;
                    }
                    return false;
                })
                .forEach(this::saveOrderStrategy);
    }
    
    @Override
    public List<MdOrderStrategyDTO> listByDeptTypeAndCodes(List<MdOrderStrategyTypeCodeQuery> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Collections.emptyList();
        }

        List<MdOrderStrategyPO> poList = mdOrderStrategyRepositoryService.listByDeptTypeAndCodeList(typeCodeList);
        
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        
        return poList.stream()
                .map(MdOrderStrategyConvert.INSTANCE::po2dto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MdOrderStrategyDTO> queryOrderStrategy(MdOrderStrategyQueryDTO dto) {
        LambdaQueryWrapper<MdOrderStrategyPO> queryWrapper = Wrappers.lambdaQuery(MdOrderStrategyPO.class)
                .eq(Objects.nonNull(dto.getDeptType()),MdOrderStrategyPO::getDeptType, dto.getDeptType())
                .in(MdOrderStrategyPO::getDeptCode, dto.getDeptCodeList());

        List<MdOrderStrategyPO> mdOrderStrategyPOS = mdOrderStrategyRepositoryService.getBaseMapper().selectList(queryWrapper);
        if(CollectionUtils.isEmpty(mdOrderStrategyPOS)) {
            return null;
        }

        return mdOrderStrategyPOS.stream()
                .map(MdOrderStrategyConvert.INSTANCE::po2dto)
                .collect(Collectors.toList());
    }
}
