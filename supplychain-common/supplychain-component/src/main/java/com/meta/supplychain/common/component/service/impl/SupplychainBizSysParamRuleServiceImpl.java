package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizSysParamRuleService;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.metaera.sysparam.client.SystemParamClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 系统参数工具类
 * 本类使用 com.meta.supplychain.common.component.service.impl.SupplychainControlEngineServiceImpl#getSysParamService()
 * <AUTHOR>
 * @date 2025/04/18 14:43
 **/
@Service
public class SupplychainBizSysParamRuleServiceImpl implements ISupplychainBizSysParamRuleService {
    @Resource
    private SystemParamClient systemParamClient;

    @Override
    public boolean isEnable(StandardEnum StandardEnum) {
        String value = getValue(StandardEnum);
        return YesOrNoEnum.YES.getCode().toString().equals(value);
    }

    @Override
    public boolean isEnable(StandardEnum StandardEnum, String deptCode) {
        String value = getValue(StandardEnum, deptCode);
        return YesOrNoEnum.YES.getCode().toString().equals(value);
    }

    @Override
    public String getValue(StandardEnum StandardEnum) {
        String value = systemParamClient.getParamValue((String) StandardEnum.getCode());
        Logs.info("获取系统参数 {} {} 的值为 {} ", StandardEnum.getDesc(), StandardEnum.getCode(), value);
        return value;
    }

    @Override
    public String getValue(StandardEnum StandardEnum, String deptCode) {
        String value = systemParamClient.getOrgParamValue((String) StandardEnum.getCode(), deptCode);
        Logs.info("获取部门 {} 系统参数 {} {} 的值为 {} ", deptCode, StandardEnum.getDesc(), StandardEnum.getCode(), value);
        if (!StringUtils.hasText(value)) {
            value = getValue(StandardEnum);
        }
        return value;
    }

    @Override
    public Integer getIntValue(StandardEnum StandardEnum) {
        String value = getValue(StandardEnum);
        if (StringUtils.hasText(value)) {
            return Integer.parseInt(value);
        }
        return null;
    }

    @Override
    public Integer getIntValue(StandardEnum StandardEnum, String deptCode) {
        String value = getValue(StandardEnum, deptCode);
        if (!StringUtils.hasText(value)) {
            return Integer.parseInt(getValue(StandardEnum));
        }
        return Integer.parseInt(value);
    }

    @Override
    public Long getLongValue(StandardEnum StandardEnum) {
        String value = getValue(StandardEnum);
        if (StringUtils.hasText(value)) {
            return Long.parseLong(value);
        }
        return null;
    }

    @Override
    public Long getLongValue(StandardEnum StandardEnum, String deptCode) {
        String value = getValue(StandardEnum, deptCode);
        if (!StringUtils.hasText(value)) {
            return Long.parseLong(getValue(StandardEnum));
        }
        return Long.parseLong(value);
    }

    @Override
    public BigDecimal getBigDecimalValue(StandardEnum StandardEnum) {
        Long value = getLongValue(StandardEnum);
        if (Objects.nonNull(value)) {
            return BigDecimal.valueOf(value);
        }
        return null;
    }

    @Override
    public BigDecimal getBigDecimalValue(StandardEnum StandardEnum, String deptCode) {
        Long value = getLongValue(StandardEnum, deptCode);
        if (Objects.nonNull(value)) {
            return BigDecimal.valueOf(value);
        }
        return null;
    }
}
