package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.entity.dto.md.component.bizrule.OrderAttrDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderAttrQueryDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderReturnAttrDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;

import java.util.List;

public interface ISupplychainWdsBizRuleEngineService {
    /**
     * 服务端获取单据号
     * @param mdBillNoBillTypeEnum
     * @return
     */
    String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode);

    /**
     * 查询订货属性
     * 配送订单
     */
    List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 查询退货属性
     * 配送订单
     */
    List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 查询及校验商品经营状态(进货/退货、出货/退货)
     *
     * 经营状态公共逻辑 订/退货申请、需求单、采购计划单、采购/退订单、配送订单、门店调拨
     *
     *
     * 查询及校验商品流转途径(允许门店调拨、销售、配送送货/退货、供应商送货/退货、直流、直流供应商)
     *
     * 流转途径公共逻辑 订/退货申请、需求单、配转采、采购计划单、采购/退订单、配送订单、门店调拨
     */
    List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO);

}
