package com.meta.supplychain.common.component.service.intf.commonbiz;

import com.meta.supplychain.entity.dto.goods.req.*;
import com.meta.supplychain.entity.dto.goods.resp.*;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

public interface ICommonGoodsService {
    /**
     *
     * 门店补货商品查询
     * @param goodsQueryReq
     * @return
     */
    List<GoodsQueryResp> getGoodsInfo(@RequestBody GoodsQueryReq goodsQueryReq);

    /**
     * 商品分类路径解析
     * @param categoryQueryReq
     * @return
     */
    List<GoodsCategoryQueryResp> getGoodsCategory(@RequestBody GoodsCategoryQueryReq categoryQueryReq);
    /**
     * 经营状态、流转途径
     *
     * @param goodsQueryReq
     * @return
     */
    ManageAndCirculationResp getManageAndCircul(@RequestBody ManageAndCirculationReq goodsQueryReq);

    /**
     * 批量获取商品sku信息
     *
     * @param skuCodes
     * @return
     */
    List<GoodsSimpleInfo> skuSimpleList(String skuCodes, String attributeNameFlag);

    /**
     * 批量获取商品sku信息
     *
     * @param skuCodes
     * @return
     */
    List<GoodsSimpleInfo> skuSimpleList(List<String> skuCodes, String attributeNameFlag);

    /**
     * 商品搜索(简化版)
     *
     * @param param
     * @return
     */
    GoodsSimpleInfoResp simpleSearchQuery(GoodsSearchQueryReq param);

    /**
     * 更新部门参考进价
     *
     * @param updatePurchPriceReq
     * @return
     */
    void updatePurchPrice(@RequestBody UpdatePurchPriceReq updatePurchPriceReq);

    /**
     * 商品查询
     * 门店+商品编码或条码或货号
     * @param goodsQueryReq
     * @return
     */
    List<GoodsQuerySearchResp> getGoodsInfoSearch(@RequestBody GoodsQuerySearchReq goodsQueryReq);
    /**
     * 批量查询商品关联品类-管理分类
     * @param goodsCategoryBatch
     * @return1
     */
    List<GoodsCategoryResp> getGoodsCategoryBatch(@RequestBody GoodsCategoryBatch goodsCategoryBatch);
}
