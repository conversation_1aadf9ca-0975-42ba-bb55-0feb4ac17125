package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.convert.md.MdGoodsStrategyConvert;
import com.meta.supplychain.entity.dto.bds.resp.DeptGroupTree;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.md.base.DeptInfo;
import com.meta.supplychain.entity.dto.md.goodsstrategy.GoodsStrategyDetailDTO;
import com.meta.supplychain.entity.dto.md.goodsstrategy.GoodsStrategyInfoDTO;
import com.meta.supplychain.entity.dto.md.goodsstrategy.ListGoodsStrategyDTO;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyLogResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyResp;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptOpLogPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyPO;
import com.meta.supplychain.enums.GoodsTypeEnum;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.MdGoodsStrategyOpTypeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdGoodsStrategyDeptOpLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdGoodsStrategyDeptRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdGoodsStrategyRepositoryService;
import com.meta.supplychain.common.component.domain.md.intf.IMdGoodsStrategyDomainService;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.local.DeptTypeEnum;
import com.meta.supplychain.util.local.LocalCacheManager;
import com.meta.supplychain.util.task.ScheduledTask;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:56
 **/
@Service
public class MdGoodsStrategyDomainServiceImpl implements IMdGoodsStrategyDomainService {
    @Autowired
    private IMdGoodsStrategyRepositoryService mdGoodsStrategyRepositoryService;

    @Autowired
    private IMdGoodsStrategyDeptRepositoryService mdGoodsStrategyDeptRepositoryService;

    @Autowired
    private IMdGoodsStrategyDeptOpLogRepositoryService mdGoodsStrategyDeptOpLogRepositoryService;

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private ScheduledTask scheduledTask;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGoodsStrategy(List<GoodsStrategyReq> req) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();

        String batch = DateUtil.getDateTimeFormate(LocalDateTime.now());

        for (GoodsStrategyReq goodsStrategyReq : req) {
            MdGoodsStrategyPO mdGoodsStrategyPO = MdGoodsStrategyConvert.INSTANCE.convertReqToPo(goodsStrategyReq);
            if(null != loginUser){
                mdGoodsStrategyPO.setCreateCode(loginUser.getCode());
                mdGoodsStrategyPO.setCreateName(loginUser.getName());
                mdGoodsStrategyPO.setCreateUid(loginUser.getUid());

                mdGoodsStrategyPO.setUpdateCode(loginUser.getCode());
                mdGoodsStrategyPO.setUpdateName(loginUser.getName());
                mdGoodsStrategyPO.setUpdateUid(loginUser.getUid());
            }

            List<MdGoodsStrategyDeptPO> mdGoodsStrategyDeptPOList = new ArrayList<>();
            List<MdGoodsStrategyDeptOpLogPO> mdGoodsStrategyOpLogList = new ArrayList<>();
            if (mdGoodsStrategyPO.getId() == null) {
                //用户操作新增
                QueryWrapper<MdGoodsStrategyPO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("goods_code", goodsStrategyReq.getGoodsCode());
                queryWrapper.eq("goods_type", goodsStrategyReq.getGoodsType());
                List<MdGoodsStrategyPO> mdGoodsStrategyPOList = mdGoodsStrategyRepositoryService.getBaseMapper().selectList(queryWrapper);

                if(null != mdGoodsStrategyPO.getWholeUnitRate()){
                    BigDecimal zero = new BigDecimal("0");
                    if(zero.equals(mdGoodsStrategyPO.getWholeUnitRate())){
                        mdGoodsStrategyPO.setWholeUnitRate(new BigDecimal("1"));
                    }
                }

                if (CollectionUtils.isNotEmpty(mdGoodsStrategyPOList)) {
                    //数据操作更新
                    Long id = mdGoodsStrategyPOList.get(0).getId();
                    QueryWrapper<MdGoodsStrategyDeptPO> deptQueryWrapper = new QueryWrapper();
                    deptQueryWrapper.eq("goods_strategy_id", id);
                    List<MdGoodsStrategyDeptPO> deptList = mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(deptQueryWrapper);
                    Map<String, MdGoodsStrategyDeptPO> strategyDeptMap = deptList.stream().collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptKey, Function.identity()));
                    mdGoodsStrategyPO.setId(id);

                    mdGoodsStrategyPO.setUpdateTime(LocalDateTime.now());

                    mdGoodsStrategyRepositoryService.updateById(mdGoodsStrategyPO);

                    List<MdGoodsStrategyDeptPO> mdGoodsStrategyPODepts =
                            MdGoodsStrategyConvert.INSTANCE.convertDeptReqToDeptPo(goodsStrategyReq.getGoodsDetails());

                    List<MdGoodsStrategyDeptPO> addmdGoodsStrategyDeptPOList = new ArrayList<>();
                    List<MdGoodsStrategyDeptPO> modifymdGoodsStrategyDeptPOList = new ArrayList<>();
                    List<MdGoodsStrategyDeptPO> delmdGoodsStrategyDeptPOList = new ArrayList<>();
                    List<Long> delIdList = new ArrayList<>();
                    for (MdGoodsStrategyDeptPO goodsDetail : mdGoodsStrategyPODepts) {
                        goodsDetail.setGoodsStrategyId(mdGoodsStrategyPO.getId());
                        goodsDetail.setGoodsType(goodsStrategyReq.getGoodsType());
                        goodsDetail.setGoodsCode(mdGoodsStrategyPO.getGoodsCode());

                        String deptKey = goodsDetail.getDeptCode()  + "-" + goodsDetail.getDeptType();


                        if(strategyDeptMap.containsKey(deptKey)){
                            if(null == strategyDeptMap.get(deptKey).getId()){
                                continue;
                            }
                            //不同删除后新增
                            if(!goodsDetail.toString().equals(strategyDeptMap.get(deptKey).toString())){
                                //删除历史
                                delmdGoodsStrategyDeptPOList.add(strategyDeptMap.get(deptKey));
                                delIdList.add(strategyDeptMap.get(deptKey).getId());
                                //删除后新增
                                modifymdGoodsStrategyDeptPOList.add(goodsDetail);
                            }
                        }
                        else{
                            addmdGoodsStrategyDeptPOList.add(goodsDetail);
                            strategyDeptMap.put(deptKey,goodsDetail);
                        }
                    }

                    if(CollectionUtils.isNotEmpty(modifymdGoodsStrategyDeptPOList)){
                        //删除明细再新增
                        mdGoodsStrategyDeptRepositoryService.getMdGoodsStrategyDeptMapper().delByIdList(delIdList);

                        mdGoodsStrategyDeptPOList.addAll(modifymdGoodsStrategyDeptPOList);
                        List<MdGoodsStrategyDeptOpLogPO> mdGoodsStrategyDeptOpLogPOS = generateStrategyLogs(modifymdGoodsStrategyDeptPOList, mdGoodsStrategyPO,
                                MdGoodsStrategyOpTypeEnum.UPDATE.getCode(), loginUser,batch);

                        mdGoodsStrategyOpLogList.addAll(mdGoodsStrategyDeptOpLogPOS);
                    }

                    if(CollectionUtils.isNotEmpty(addmdGoodsStrategyDeptPOList)){
                        mdGoodsStrategyDeptPOList.addAll(addmdGoodsStrategyDeptPOList);
                        List<MdGoodsStrategyDeptOpLogPO> mdGoodsStrategyDeptOpLogPOS = generateStrategyLogs(addmdGoodsStrategyDeptPOList, mdGoodsStrategyPO,
                                MdGoodsStrategyOpTypeEnum.CREATE.getCode(), loginUser,batch);

                        mdGoodsStrategyOpLogList.addAll(mdGoodsStrategyDeptOpLogPOS);
                    }

                } else {
                    // 数据操作新增
                    mdGoodsStrategyRepositoryService.save(mdGoodsStrategyPO);
                    List<MdGoodsStrategyDeptPO> mdGoodsStrategyPODepts =
                            MdGoodsStrategyConvert.INSTANCE.convertDeptReqToDeptPo(goodsStrategyReq.getGoodsDetails());
                    for (MdGoodsStrategyDeptPO goodsDetail : mdGoodsStrategyPODepts) {
                        goodsDetail.setGoodsStrategyId(mdGoodsStrategyPO.getId());
                        goodsDetail.setGoodsType(goodsStrategyReq.getGoodsType());
                        goodsDetail.setGoodsCode(mdGoodsStrategyPO.getGoodsCode());
                        mdGoodsStrategyDeptPOList.add(goodsDetail);
                    }
                    mdGoodsStrategyOpLogList = generateStrategyLogs(mdGoodsStrategyDeptPOList,mdGoodsStrategyPO,
                            MdGoodsStrategyOpTypeEnum.CREATE.getCode(), loginUser,batch);
                }
            } else {
                //用户更新
                //前端未传入哪些数据删除，只能先删除再新增
                mdGoodsStrategyPO.setUpdateTime(LocalDateTime.now());
                mdGoodsStrategyRepositoryService.updateById(mdGoodsStrategyPO);
                List<MdGoodsStrategyDeptPO> mdGoodsStrategyPODepts =
                        MdGoodsStrategyConvert.INSTANCE.convertDeptReqToDeptPo(goodsStrategyReq.getGoodsDetails());

                QueryWrapper<MdGoodsStrategyDeptPO> deptQueryWrapper = new QueryWrapper();
                deptQueryWrapper.eq("goods_strategy_id", mdGoodsStrategyPO.getId());
                List<MdGoodsStrategyDeptPO> oriStrategyDept = mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(deptQueryWrapper);

                List<Long> newIds = mdGoodsStrategyPODepts.stream().map(MdGoodsStrategyDeptPO::getId).distinct().collect(Collectors.toList());
                List<MdGoodsStrategyDeptPO> deleteStrategy = new ArrayList<>();
                if (!CollectionUtils.isEmpty(oriStrategyDept)) {
                    deleteStrategy = oriStrategyDept.stream().filter(item -> !newIds.contains(item.getId())).collect(Collectors.toList());
                }

                for (MdGoodsStrategyDeptPO goodsDetail : mdGoodsStrategyPODepts) {
                    goodsDetail.setGoodsStrategyId(mdGoodsStrategyPO.getId());
                    goodsDetail.setGoodsCode(mdGoodsStrategyPO.getGoodsCode());
                    goodsDetail.setGoodsType(mdGoodsStrategyPO.getGoodsType());
                    mdGoodsStrategyDeptPOList.add(goodsDetail);
                }

                List<Long> idList = new ArrayList<>();
                idList.add(mdGoodsStrategyPO.getId());
                mdGoodsStrategyDeptRepositoryService.getMdGoodsStrategyDeptMapper().delByGoodsStrategyIdList(idList);

                mdGoodsStrategyOpLogList = generateStrategyLogs(mdGoodsStrategyDeptPOList, mdGoodsStrategyPO,
                        MdGoodsStrategyOpTypeEnum.UPDATE.getCode(), loginUser,batch);
                mdGoodsStrategyOpLogList.addAll(generateStrategyLogs(deleteStrategy, mdGoodsStrategyPO,
                        MdGoodsStrategyOpTypeEnum.DELETE.getCode(), loginUser,batch));
            }
            mdGoodsStrategyDeptRepositoryService.saveBatch(mdGoodsStrategyDeptPOList);
            mdGoodsStrategyDeptOpLogRepositoryService.saveBatch(mdGoodsStrategyOpLogList);
        }
    }

    @Override
    public List<MdGoodsStrategyDeptOpLogPO> generateStrategyLogs(List<MdGoodsStrategyDeptPO> strategyDeptList,
                                                                  MdGoodsStrategyPO mdGoodsStrategyPO,
                                                                     Integer operateType, LoginUserDTO loginUser,String batch) {
        List<MdGoodsStrategyDeptOpLogPO> MdGoodsStrategyDeptOpLogPOLogs = MdGoodsStrategyConvert.INSTANCE.convertDept2LogList(strategyDeptList);
        for (MdGoodsStrategyDeptOpLogPO mdGoodsStrategyDeptOpLogPO : MdGoodsStrategyDeptOpLogPOLogs) {
            mdGoodsStrategyDeptOpLogPO.setId(null);
            mdGoodsStrategyDeptOpLogPO.setBatch(batch);
            if(null != loginUser){
                mdGoodsStrategyDeptOpLogPO.setCreateCode(loginUser.getCode());
                mdGoodsStrategyDeptOpLogPO.setCreateName(loginUser.getName());
                mdGoodsStrategyDeptOpLogPO.setCreateUid(loginUser.getUid());
            }

            mdGoodsStrategyDeptOpLogPO.setOpType(operateType);
            mdGoodsStrategyDeptOpLogPO.setSkuModel(mdGoodsStrategyPO.getSkuModel());
            mdGoodsStrategyDeptOpLogPO.setGoodsName(mdGoodsStrategyPO.getGoodsName());
            mdGoodsStrategyDeptOpLogPO.setBarcode(mdGoodsStrategyPO.getBarcode());
            mdGoodsStrategyDeptOpLogPO.setCreateTime(LocalDateTime.now());
            mdGoodsStrategyDeptOpLogPO.setUpdateTime(LocalDateTime.now());
        }
        return MdGoodsStrategyDeptOpLogPOLogs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteGoodsStrategy(List<Long> mdGoodsStrategyPOIdlIST) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        LambdaQueryWrapper<MdGoodsStrategyPO> strategyQueryWrapper = new LambdaQueryWrapper<>();
        strategyQueryWrapper.in(MdGoodsStrategyPO::getId, mdGoodsStrategyPOIdlIST);
        List<MdGoodsStrategyPO> mdGoodsStrategyPOList = mdGoodsStrategyRepositoryService.getBaseMapper().selectList(strategyQueryWrapper);

        String batch = DateUtil.getDateTimeFormate(LocalDateTime.now());

        //删除策略主表
        mdGoodsStrategyRepositoryService.getMdGoodsStrategyMapper().delByIdList(mdGoodsStrategyPOIdlIST);

        //查询部门商品表
        LambdaQueryWrapper<MdGoodsStrategyDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MdGoodsStrategyDeptPO::getGoodsStrategyId, mdGoodsStrategyPOIdlIST);

        List<MdGoodsStrategyDeptPO> mdGoodsStrategyDeptPOList = mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        Map<Long, List<MdGoodsStrategyDeptPO>> detailMap = mdGoodsStrategyDeptPOList.stream().collect(Collectors.groupingBy(MdGoodsStrategyDeptPO::getGoodsStrategyId));

        //批量删除部门商品明细
        mdGoodsStrategyDeptRepositoryService.getMdGoodsStrategyDeptMapper().delByGoodsStrategyIdList(mdGoodsStrategyPOIdlIST);

        for (MdGoodsStrategyPO mdGoodsStrategyPO : mdGoodsStrategyPOList) {
            mdGoodsStrategyDeptOpLogRepositoryService.saveBatch(
                    generateStrategyLogs(detailMap.get(mdGoodsStrategyPO.getId()),
                            mdGoodsStrategyPO,
                            MdGoodsStrategyOpTypeEnum.DELETE.getCode(), loginUser,batch));
        }
    }

    @Override
    public PageResult<GoodsStrategyLogResp> queryGoodsStrategyLogs(GoodsStrategyLogReq req) {
        LambdaQueryWrapper<MdGoodsStrategyDeptOpLogPO> queryWrapper = new LambdaQueryWrapper<>();
        Page<MdGoodsStrategyDeptOpLogPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        if(StringUtils.isNotEmpty(req.getDeptCode()) && req.getDeptType() != null){
            queryWrapper.eq(MdGoodsStrategyDeptOpLogPO::getDeptCode, req.getDeptCode());
            queryWrapper.eq(MdGoodsStrategyDeptOpLogPO::getDeptType, req.getDeptType());
        }
        if (req.getGoodsType() != null && StringUtils.isNotBlank(req.getGoodsCode())) {
            queryWrapper.eq(MdGoodsStrategyDeptOpLogPO::getGoodsType, req.getGoodsType());
            if (GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode().equals(req.getGoodsType())) {
                queryWrapper.and(w -> w.like(MdGoodsStrategyDeptOpLogPO::getGoodsCode, req.getGoodsCode()).or().like(MdGoodsStrategyDeptOpLogPO::getGoodsName, req.getGoodsCode()).or().like(MdGoodsStrategyDeptOpLogPO::getBarcode, req.getGoodsCode()));
            } else {
                queryWrapper.in(MdGoodsStrategyDeptOpLogPO::getGoodsCode, Arrays.asList(req.getGoodsCode().split(",")));
            }
        }
        if (StringUtils.isNotBlank(req.getStartTime())) {
            queryWrapper.ge(MdGoodsStrategyDeptOpLogPO::getCreateTime,req.getStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(req.getEndTime())) {
            queryWrapper.le(MdGoodsStrategyDeptOpLogPO::getCreateTime, req.getEndTime() + " 23:59:59");
        }
        if(req.getOpType() != null) {
            queryWrapper.eq(MdGoodsStrategyDeptOpLogPO::getOpType, req.getOpType());
        }
        if(StringUtils.isNotEmpty(req.getOperator())) {
            queryWrapper.and(wrapper -> wrapper.like(MdGoodsStrategyDeptOpLogPO::getCreateCode, req.getOperator()).or().like(MdGoodsStrategyDeptOpLogPO::getCreateName, req.getOperator()));
        }
        queryWrapper.orderByDesc(MdGoodsStrategyDeptOpLogPO::getCreateTime);
        Page<MdGoodsStrategyDeptOpLogPO> mdGoodsStrategyDeptOpLogPOPage = mdGoodsStrategyDeptOpLogRepositoryService.getBaseMapper().selectPage(page, queryWrapper);
        List<GoodsStrategyLogResp> goodsStrategyLogRespList = MdGoodsStrategyConvert.INSTANCE.convertLogPo2RespList(mdGoodsStrategyDeptOpLogPOPage.getRecords());

        return PageResult.of(mdGoodsStrategyDeptOpLogPOPage.getTotal(), goodsStrategyLogRespList);

    }

    @Override
    public PageResult<GoodsStrategyInfoDTO> queryExistingStrategyList(QueryExistingStrategyReq req) {
        Logs.info("查询已存在商品策略，租户：{}， 入参：{}", TenantContext.get(), JSON.toJSONString(req));
        if (LocalCacheManager.isEmptyDeptGroup()) {
            scheduledTask.refreshDeptGroupTree();
        }
        List<DeptInfo> deptInfos = new ArrayList<>();
        for (DeptInfo deptInfo : req.getDeptList()) {
            String classCode = DeptTypeEnum.STORE_GROUP.getCode().equals(deptInfo.getDeptType()) ? GroupDeptEnum.CONTROL_GROUP.getCode() : "";
            DeptGroupTree deptGroupTree = LocalCacheManager.getDeptGroupTree(deptInfo.getDeptCode(), deptInfo.getDeptType(), classCode);
            generateDeptInfo(deptGroupTree, deptInfos);
        }

        if(CollectionUtils.isEmpty(deptInfos)) {
            return PageResult.ofEmpty();
        }

        List<String> categoryCodes = req.getGoodsList().stream().filter(e -> GoodsTypeEnum.CATEGORY.getCode().equals(e.getGoodsType())).map(QueryExistingStrategyReq.GoodsInfo::getGoodsCode).collect(Collectors.toList());
        List<String> goodsCodes = req.getGoodsList().stream().filter(e -> GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode().equals(e.getGoodsType())).map(QueryExistingStrategyReq.GoodsInfo::getGoodsCode).collect(Collectors.toList());

        List<String> deptCodes = deptInfos.stream().filter(e -> DeptTypeEnum.SPECIFIC_DEPT.getCode().equals(e.getDeptType())).map(DeptInfo::getDeptCode).collect(Collectors.toList());
        List<String> groupCodes = deptInfos.stream().filter(e -> DeptTypeEnum.STORE_GROUP.getCode().equals(e.getDeptType())).map(DeptInfo::getDeptCode).collect(Collectors.toList());

        LambdaQueryWrapper<MdGoodsStrategyDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        if(CollectionUtils.isNotEmpty(deptCodes) && CollectionUtils.isNotEmpty(groupCodes)){
            queryWrapper.and(w -> w.and(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.SPECIFIC_DEPT.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, deptCodes))
                    .or(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.STORE_GROUP.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, groupCodes)));
        }else if(CollectionUtils.isEmpty(deptCodes)) {
            queryWrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.STORE_GROUP.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, groupCodes);
        } else {
            queryWrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.SPECIFIC_DEPT.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, deptCodes);
        }

        if(CollectionUtils.isNotEmpty(goodsCodes) && CollectionUtils.isNotEmpty(categoryCodes)){
            queryWrapper.and(w -> w.and(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getGoodsType, GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode()).in(MdGoodsStrategyDeptPO::getGoodsCode, goodsCodes))
                    .or(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getGoodsType, GoodsTypeEnum.CATEGORY.getCode()).in(MdGoodsStrategyDeptPO::getGoodsCode, categoryCodes)));
        }else if(CollectionUtils.isEmpty(categoryCodes)) {
            queryWrapper.eq(MdGoodsStrategyDeptPO::getGoodsType, GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode()).in(MdGoodsStrategyDeptPO::getGoodsCode, goodsCodes);
        } else {
            queryWrapper.eq(MdGoodsStrategyDeptPO::getGoodsType, GoodsTypeEnum.CATEGORY.getCode()).in(MdGoodsStrategyDeptPO::getGoodsCode, categoryCodes);
        }

        List<MdGoodsStrategyDeptPO> stCommodityStrategyDepts = mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        List<GoodsStrategyDetailDTO> commodityStrategyDeptDetails = MdGoodsStrategyConvert.INSTANCE.convertDeptPo2DetailList(stCommodityStrategyDepts);

        Map<String, List<GoodsStrategyDetailDTO>> deptDetailMap = commodityStrategyDeptDetails.stream().collect(Collectors.groupingBy(GoodsStrategyDetailDTO::getGoodsCode));
        List<GoodsStrategyInfoDTO> goodsStrategyInfoList = deptDetailMap.entrySet().stream().map(entry -> {
            GoodsStrategyInfoDTO goodsStrategyInfo = new GoodsStrategyInfoDTO();
            goodsStrategyInfo.setGoodsCode(entry.getKey());
            goodsStrategyInfo.setStrategyList(entry.getValue());
            return goodsStrategyInfo;
        }).collect(Collectors.toList());
        return  PageResult.ofRows(goodsStrategyInfoList);
    }
    private void generateDeptInfo(DeptGroupTree deptGroupTree, List<DeptInfo> deptInfos) {
        if (deptGroupTree != null) {
            deptInfos.add(new DeptInfo(deptGroupTree.getCode(), DeptTypeEnum.getByType(deptGroupTree.getType()).getCode()));
            if (CollectionUtils.isNotEmpty(deptGroupTree.getChild())) {
                for (DeptGroupTree groupTree : deptGroupTree.getChild()) {
                    generateDeptInfo(groupTree, deptInfos);
                }
            }
        }


    }

    @Override
    public PageResult<GoodsStrategyResp> queryGoodsStrategyList(QueryGoodsStrategyReq req) {
        OpInfo opInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();
        Page<MdGoodsStrategyPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        Page<MdGoodsStrategyPO> mdGoodsStrategyPage = null;
        if (!opInfo.getOriginDeptFlag() && CollectionUtils.isEmpty(opInfo.getManageDeptCodeList())) {
            BizExceptions.throwWithMsg("当前分管部门没有查询权限");
        }
        if ((!opInfo.getOriginDeptFlag() && CollectionUtils.isNotEmpty(opInfo.getManageDeptCodeList())) || (StringUtils.isNotBlank(req.getDeptCode()) && req.getDeptType() != null)) {
            GoodsStrategyDetailReq strategyReq = new GoodsStrategyDetailReq();
            if (StringUtils.isNotBlank(req.getDeptCode()) && req.getDeptType() != null) {
                if (opInfo.getOriginDeptFlag() || (CollectionUtils.isNotEmpty(opInfo.getManageDeptCodeList()) && opInfo.getManageDeptCodeList().contains(req.getDeptCode()))) {
                    strategyReq.setDeptCode(req.getDeptCode());
                    strategyReq.setDeptType(req.getDeptType());
                } else {
                    BizExceptions.throwWithMsg("当前分管部门没有查询权限");
                }
            } else {
                strategyReq.setDeptCodes(opInfo.getManageDeptCodeList());
                strategyReq.setGroupCodes(opInfo.getGroupCodeList());
            }
            if (req.getGoodsType() != null && StringUtils.isNotBlank(req.getGoodsCode())) {
                if (GoodsTypeEnum.CATEGORY.getCode().equals(req.getGoodsType())) {
                    strategyReq.setCategoryCodeList(Arrays.asList(req.getGoodsCode().split(",")));
                } else {
                    strategyReq.setGoodsCode(req.getGoodsCode());
                }
                strategyReq.setGoodsType(req.getGoodsType());
            }
            mdGoodsStrategyPage = mdGoodsStrategyRepositoryService.queryGoodsStrategyDetail4Page(strategyReq, page);
        } else {
            LambdaQueryWrapper<MdGoodsStrategyPO> wrapper = new LambdaQueryWrapper();
            if (req.getGoodsType() != null && StringUtils.isNotBlank(req.getGoodsCode())) {
                wrapper.eq(MdGoodsStrategyPO::getGoodsType, req.getGoodsType());
                if (GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode().equals(req.getGoodsType())) {
                    wrapper.and(w -> w.like(MdGoodsStrategyPO::getGoodsCode, req.getGoodsCode()).or().like(MdGoodsStrategyPO::getGoodsName, req.getGoodsCode()).or().like(MdGoodsStrategyPO::getBarcode, req.getGoodsCode()));
                } else {
                    wrapper.in(MdGoodsStrategyPO::getGoodsCode, Arrays.asList(req.getGoodsCode().split(",")));
                }
            }
            wrapper.orderByDesc(MdGoodsStrategyPO::getUpdateTime);

            mdGoodsStrategyPage = mdGoodsStrategyRepositoryService.getBaseMapper().selectPage(page, wrapper);
        }
        if(CollectionUtils.isEmpty(mdGoodsStrategyPage.getRecords())){
            return PageResult.ofEmpty();
        }

        List<GoodsStrategyResp> goodsStrategyRespResps = MdGoodsStrategyConvert.INSTANCE.convertGoodsStrategy2RespList(mdGoodsStrategyPage.getRecords());
        Page result = CglibCopier.copy(mdGoodsStrategyPage, Page.class);
        result.setTotal(mdGoodsStrategyPage.getTotal());
        result.setRecords(goodsStrategyRespResps);
        return PageResult.of(mdGoodsStrategyPage.getTotal(),goodsStrategyRespResps);
    }

    @Override
    public PageResult<GoodsStrategyResp> queryGoodsStrategyList4Role(QueryGoodsStrategy4RoleReq req) {
        OpInfo operatorInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();
        Page<MdGoodsStrategyPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        if (CollectionUtils.isEmpty(operatorInfo.getManageDeptCodeList())
                || (StringUtils.isNotEmpty(req.getDeptCode()) && !operatorInfo.getManageDeptCodeList().contains(req.getDeptCode()))) {
            return PageResult.ofEmpty();
        }

        GoodsStrategyDetailReq strategyReq = new GoodsStrategyDetailReq();
        if (StringUtils.isNotEmpty(req.getDeptCode()) && req.getDeptType() != null) {
            strategyReq.setDeptCode(req.getDeptCode());
            strategyReq.setDeptType(req.getDeptType());
        } else {
            strategyReq.setDeptCodes(operatorInfo.getManageDeptCodeList());
            strategyReq.setGroupCodes(operatorInfo.getGroupCodeList());
        }
        if (req.getGoodsType() != null && StringUtils.isNotBlank(req.getGoodsCode())) {
            if (GoodsTypeEnum.CATEGORY.getCode().equals(req.getGoodsType())) {
                strategyReq.setCategoryCodeList(Arrays.asList(req.getGoodsCode().split(",")));
            } else {
                strategyReq.setGoodsCode(req.getGoodsCode());
            }
            strategyReq.setGoodsType(req.getGoodsType());
        }
        Page<MdGoodsStrategyPO> mdGoodsStrategyPage = mdGoodsStrategyRepositoryService.queryGoodsStrategyDetail4RolePage(strategyReq, page);

        List<GoodsStrategyResp> goodsStrategyRespList = MdGoodsStrategyConvert.INSTANCE.convertGoodsStrategy2RespList(mdGoodsStrategyPage.getRecords());
        return PageResult.of(mdGoodsStrategyPage.getTotal(), goodsStrategyRespList);
    }

    @Override
    public Result<GoodsStrategyResp> queryGoodsStrategyDetailList(Long id) {
        OpInfo operatorInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();
        if (!operatorInfo.getOriginDeptFlag() && CollectionUtils.isEmpty(operatorInfo.getManageDeptCodeList())) {
            BizExceptions.throwWithMsg("当前分管部门没有查询权限");
        }
        LambdaQueryWrapper<MdGoodsStrategyDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdGoodsStrategyDeptPO::getGoodsStrategyId, id);
        if (!operatorInfo.getOriginDeptFlag()) {
            queryWrapper.and(w -> w.and(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.SPECIFIC_DEPT.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, operatorInfo.getManageDeptCodeList()))
                    .or(wrapper -> wrapper.eq(MdGoodsStrategyDeptPO::getDeptType, DeptTypeEnum.STORE_GROUP.getCode()).in(MdGoodsStrategyDeptPO::getDeptCode, operatorInfo.getGroupCodeList())));
        }

        MdGoodsStrategyPO mdGoodsStrategyPO = mdGoodsStrategyRepositoryService.getBaseMapper().selectById(id);
        GoodsStrategyResp goodsStrategyResp = MdGoodsStrategyConvert.INSTANCE.convertGoodsStrategy2Resp(mdGoodsStrategyPO);

        List<MdGoodsStrategyDeptPO> goodsStrategyDeptList = mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
        List<GoodsStrategyDetailDTO> goodsStrategyDeptDetailList = MdGoodsStrategyConvert.INSTANCE.convertDeptPo2DetailList(goodsStrategyDeptList);
        goodsStrategyResp.setGoodsStrategyDeptList(goodsStrategyDeptDetailList);
        return Results.ofSuccess(goodsStrategyResp);
    }

    @Override
    public List<MdGoodsStrategyDeptPO> listGoodsStrategy(ListGoodsStrategyDTO listGoodsStrategyDTO) {
        QueryWrapper<MdGoodsStrategyDeptPO> wrapper = new QueryWrapper<>();

        if (CollectionUtils.isNotEmpty(listGoodsStrategyDTO.getStoreGroupCodeList())) {
            wrapper.and(wra -> wra.and(w -> w.eq("dept_code", listGoodsStrategyDTO.getDeptCode()).eq("dept_type", DeptTypeEnum.SPECIFIC_DEPT.getCode()))
                    .or(w -> w.in("dept_code", listGoodsStrategyDTO.getStoreGroupCodeList()).eq("dept_type", DeptTypeEnum.STORE_GROUP.getCode())));
        } else {
            wrapper.eq("dept_code", listGoodsStrategyDTO.getDeptCode()).eq("dept_type", DeptTypeEnum.SPECIFIC_DEPT.getCode());
        }
        wrapper.in("goods_code", listGoodsStrategyDTO.getGoodsCodeList());
        wrapper.eq("goods_type", listGoodsStrategyDTO.getGoodsType());

        return mdGoodsStrategyDeptRepositoryService.getBaseMapper().selectList(wrapper);
    }
}

