package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.md.deliveryappointment.ListDeliveryAppointmentStrategyDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentCloseDateDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryAppointmentStrategyDTO;
import com.meta.supplychain.entity.dto.md.deliveryappointment.MdDeliveryDockStrategyDTO;
import com.meta.supplychain.entity.dto.md.req.delivery.MdDeliveryAppointmentCloseDateBatchCreateReq;
import com.meta.supplychain.entity.dto.md.req.delivery.MdDeliveryAppointmentStrategyPageQueryReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDeliveryDockStrategyQueryReq;
import com.meta.supplychain.entity.dto.md.resp.deliveryappointment.DeliveryAppointmentStrategyResultDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 供应商预约策略
 * <AUTHOR>
 */
public interface IMdDeliveryAppointmentStrategyDomainService {
    /**
     * 供应商预约策略单据查询
     */
    PageResult<MdDeliveryAppointmentStrategyDTO> pageQuery(MdDeliveryAppointmentStrategyPageQueryReq req);

    /**
     * 新增供应商预约策略
     */
    @Transactional(rollbackFor = Exception.class)
    String create(MdDeliveryAppointmentStrategyDTO dto);
    
    /**
     * 更新供应商预约策略
     * @param dto 预约策略DTO
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    boolean update(MdDeliveryAppointmentStrategyDTO dto);

    /**
     * 根据单号查询供应商预约策略详情
     */
    MdDeliveryAppointmentStrategyDTO getDetailByBillNo(String billNo);

    /**
     * 根据单号删除供应商预约策略
     */
    boolean deleteByBillNo(String billNo);

    /**
     * 更新供应商预约策略状态
     */
    boolean updateStatus(String billNo, Integer status);

    /**
     * 批量创建/更新停止预约时间段数据
     */
    @Transactional(rollbackFor = Exception.class)
    boolean batchCreateAppointmentCloseDate(MdDeliveryAppointmentCloseDateBatchCreateReq batchCreateReq);

    /**
     * 停止预约时间数据查询
     */
    List<MdDeliveryAppointmentCloseDateDTO> getAppointmentCloseDateList(String billNo);

    List<MdDeliveryAppointmentCloseDateDTO> getAppointmentCloseDateList(List<String> billNoList);

    /**
     * 查询停靠点策略列表
     * @param req 查询条件
     * @return 停靠点策略列表
     */
    List<MdDeliveryDockStrategyDTO> queryDockStrategyList(MdDeliveryDockStrategyQueryReq req);
    
    /**
     * 根据停靠点编码查询停靠点策略详情
     * @param dockCode 停靠点编码
     * @return 停靠点策略详情
     */
    MdDeliveryDockStrategyDTO getDockStrategyDetailByDockCode(String dockCode);
    
    /**
     * 新增或更新停靠点策略
     * @param dockStrategyDTO 停靠点策略DTO
     * @return 停靠点编码
     */
    @Transactional(rollbackFor = Exception.class)
    String saveOrUpdateDockStrategy(MdDeliveryDockStrategyDTO dockStrategyDTO);

    DeliveryAppointmentStrategyResultDTO listDeliveryAppointmentStrategy(ListDeliveryAppointmentStrategyDTO param);

    DeliveryAppointmentStrategyResultDTO handleDock(Map<String, QueryBatchDeptListResp.Rows> upDeptMap,
                                                    Map<String, List<String>> deptGoodsMap, Set<String> categoryCodeSet, Set<String> skuCodeSet, Set<String> deliveryDeptCodeSet);
}