package com.meta.supplychain.common.component.service.intf;

/**
 * 供应链控制引擎，有两个主要作用
 * 1.业务核心控制器：经营状态+流转路径+订货策略等各个业务场景的验证控制、输入输出的功能
 *   ISupplychainBizBillRuleService 单据维度的策略调用
 *   ISupplychainBizDeptRuleService 部门维度的策略调用
 *   ISupplychainBizGoodsRuleService 商品维度的策略调用
 *   ISupplychainBizSysParamRuleService 系统参数调用
 * 2.单据流转
 *   IPmsWorkflowManagerService 采购订货单据流转
 *   IWdsWorkflowManagerService 仓储配送单据流转
 */
public interface ISupplychainControlEngineService {
    IPmsWorkflowManagerService getPmsWorkflowManagerService();

    IWdsWorkflowManagerService getWdsWorkflowManagerService();

    ISupplychainBizBillRuleService getSupplychainBizBillRuleService();

    ISupplychainBizDeptRuleService getSupplychainBizDeptRuleService();

    ISupplychainBizGoodsRuleService getSupplychainBizGoodsRuleService();

    ISupplychainBizSysParamRuleService getSupplychainBizSysParamRuleService();

    ISupplychainPmsBizRuleEngineService getSupplychainPmsBizRuleEngineService();

    ISupplychainWdsBizRuleEngineService getSupplychainWdsBizRuleEngineService();

    IOrderEventService getOrderEventService(String supportType);
}
