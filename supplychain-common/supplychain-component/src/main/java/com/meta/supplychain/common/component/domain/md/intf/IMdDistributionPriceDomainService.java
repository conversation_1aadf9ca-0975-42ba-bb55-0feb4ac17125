package com.meta.supplychain.common.component.domain.md.intf;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.md.distprice.*;
import com.meta.supplychain.entity.dto.md.req.distprice.ExistDistPriceDataReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryValidDistPriceReq;
import com.meta.supplychain.entity.dto.md.resp.MdDistPriceResponseDTO;
import com.meta.supplychain.enums.md.MdDistPriceOrderStatusEnum;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 配送价格领域服务接口
 */
public interface IMdDistributionPriceDomainService {
    
    /**
     * 分页查询配送价格
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 配送价格分页结果
     */
    IPage<MdDistPriceResponseDTO> selectDistPricePage(Page<MdDistPriceResponseDTO> page, MdDistPriceQueryDTO queryDTO);

    /**
     * 分页查询配送价格单及其明细
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 配送价格单及明细分页结果
     */
    IPage<MdDistPriceBillWithDetailDTO> selectBillJoinDetailPage(Page<MdDistPriceBillWithDetailDTO> page, MdDistPriceBillQueryDTO queryDTO);

    /**
     * 批量保存配送价格商品信息
     *
     * @param distPriceBillDTO 配送价格单信息
     * @param goodsList 配送价格商品列表
     * @return Map包含错误信息 （map为空：操作成功；map不为空：操作失败；null：操作异常）
     */
    @Transactional
    Map<Integer, String> batchSaveDistPriceGoods(MdDistPriceBillDTO distPriceBillDTO, List<MdDistPriceGoodsDTO> goodsList);

    /**
     * 批量保存配送价格明细信息
     *
     * @param distPriceBillDTO 配送价格单信息
     * @param detailsList 配送价格明细列表
     * @return Map包含错误信息 （map为空：操作成功；map不为空：操作失败；null：操作异常）
     */
    @Transactional
    Map<Integer, String> batchSaveDistPriceDetail(MdDistPriceBillDTO distPriceBillDTO, List<MdDistPriceDetailDTO> detailsList);

    @Transactional
    String createDistPriceBill(MdDistPriceBillDTO billDTO);

    @Transactional
    String createAndSubmitDistPriceBill(MdDistPriceBillDTO billDTO);

    @Transactional
    void updateDistPriceBill(MdDistPriceBillDTO distPriceBillDTO);

    @Transactional
    void purgeDistPriceData(String distPriceBillNo);

    @Transactional
    void deleteDistPriceBill(String distPriceBillNo);

    boolean submit(MdDistPriceBillDTO distPriceBillDTO);

    boolean submit(String distPriceBillNo);

    boolean audit(String distPriceBillNo, MdDistPriceOrderStatusEnum statusEnum, String remark);

    boolean invalidated(String distPriceBillNo);

    boolean createNewDistPrice(String tenantId, String distBillNo);

    boolean invalidatePendingValidDistPrice(String tenantId, String distBillNo);

    @Transactional
    boolean validatePendingValidDistPrice(String tenantId, String distBillNo);

    List<MdDistPriceDTO> listExistDistPriceData(ExistDistPriceDataReq existDistPriceDataReq);

    MdDistPriceBillDTO queryBillDetail(String billNo);

    void handleTakeEffectTask(LocalDateTime time);

    List<MdDistPriceDTO> queryValidDistPrice(QueryValidDistPriceReq queryValidDistPriceReq);

    List<MdDistPriceDTO> queryValidDistPrice(QueryDistPriceReq req);
}