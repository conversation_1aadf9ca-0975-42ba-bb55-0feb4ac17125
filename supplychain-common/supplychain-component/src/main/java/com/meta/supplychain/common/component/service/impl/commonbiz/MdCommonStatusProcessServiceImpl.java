package com.meta.supplychain.common.component.service.impl.commonbiz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meta.supplychain.common.component.service.intf.commonbiz.IMdCommonStatusProcessService;
import com.meta.supplychain.convert.md.MdCommonStatusProcessConvert;
import com.meta.supplychain.entity.dto.md.commonstatus.MdCommonStatusProcessDTO;
import com.meta.supplychain.entity.po.md.MdCommonStatusProcessPO;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdCommonStatusProcessRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/07 19:16
 **/
@Service
public class MdCommonStatusProcessServiceImpl implements IMdCommonStatusProcessService {

    @Autowired
    private IMdCommonStatusProcessRepositoryService mdCommonStatusProcessRepositoryService;

    @Override
    public void uploadCommonStatusProcess(MdCommonStatusProcessDTO mdCommonStatusProcessDTO) {
        MdCommonStatusProcessPO mdCommonStatusProcessPO = MdCommonStatusProcessConvert.INSTANCE.convertDto2Po(mdCommonStatusProcessDTO);
        mdCommonStatusProcessRepositoryService.getMdCommonStatusProcessMapper().insert(mdCommonStatusProcessPO);
    }

    @Override
    public List<MdCommonStatusProcessDTO> listCommonStatusProcess(MdCommonStatusProcessDTO mdCommonStatusProcessDTO) {
        LambdaQueryWrapper<MdCommonStatusProcessPO> lambdaQueryWrapper = new LambdaQueryWrapper<MdCommonStatusProcessPO>();
        lambdaQueryWrapper.eq(MdCommonStatusProcessPO::getModuleCode,mdCommonStatusProcessDTO.getModuleCode().getCode());
        lambdaQueryWrapper.eq(MdCommonStatusProcessPO::getBizType,mdCommonStatusProcessDTO.getBizType().getCode());
        lambdaQueryWrapper.eq(MdCommonStatusProcessPO::getBizCode,mdCommonStatusProcessDTO.getBizCode());
        lambdaQueryWrapper.eq(null != mdCommonStatusProcessDTO.getBizId(),MdCommonStatusProcessPO::getBizId,mdCommonStatusProcessDTO.getBizId());
        lambdaQueryWrapper.orderByDesc(MdCommonStatusProcessPO::getId);
        List<MdCommonStatusProcessPO> mdCommonStatusProcessPOS = mdCommonStatusProcessRepositoryService.getMdCommonStatusProcessMapper().selectList(lambdaQueryWrapper);

        List<MdCommonStatusProcessDTO> mdCommonStatusProcessDTOS = MdCommonStatusProcessConvert.INSTANCE.convertListPo2Dto(mdCommonStatusProcessPOS);

        return mdCommonStatusProcessDTOS;
    }
}
