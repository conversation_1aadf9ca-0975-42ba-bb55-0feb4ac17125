package com.meta.supplychain.common.component.service.impl;

import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.ISupplychainPmsBizRuleEngineService;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.md.component.bizrule.*;
import com.meta.supplychain.entity.dto.md.component.deptrule.*;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsDeliverPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPriceInfoReq;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import com.meta.supplychain.entity.dto.replenishment.resp.BatchGenerateBillNoResp;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/21 15:56
 **/
@Service
public class SupplychainPmsBizRuleEngineServiceImpl implements ISupplychainPmsBizRuleEngineService {

    public ISupplychainControlEngineService getSupplychainControlEngineService(){
        ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
        return supplychainControlEngineService;
    }

    @Override
    public String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().getBillNo(mdBillNoBillTypeEnum,deptCode);
    }

    @Override
    public List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().listOrderAttr(orderAttrQueryDTO);
    }

    @Override
    public List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().listOrderReturnAttr(orderAttrQueryDTO);
    }

    @Override
    public DemandProcessStrategyAutoDTO getAutoDemandProcessStrategy(DemandProcessStrategyAutoQueryDTO demandProcessStrategyAutoQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().getAutoDemandProcessStrategy(demandProcessStrategyAutoQueryDTO);
    }

    @Override
    public DemandProcessStrategyManualDTO getManualDemandProcessStrategy(DemandProcessStrategyManualQueryDTO demandProcessStrategyManualQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().getManualDemandProcessStrategy(demandProcessStrategyManualQueryDTO);
    }

//    @Override
//    public List<DockDTO> listDock(DockQueryDTO dockQueryDTO) {
//        return getSupplychainControlEngineService().getSupplychainBizDeptRuleService().listDock(dockQueryDTO);
//    }
//
//    @Override
//    public List<DockUseInfoDTO> listDockUseInfo(DockUseQueryDTO dockUseQueryDTO) {
//        return getSupplychainControlEngineService().getSupplychainBizDeptRuleService().listDockUseInfo(dockUseQueryDTO);
//    }

    @Override
    public List<ApplyGoodsDTO> checkApplyGoods(GoodsQueryDTO goodsQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().checkApplyGoods(goodsQueryDTO);
    }

//    @Override
//    public List<ApplyGoodsDTO> checkApplyGoods4Delivery(GoodsQueryDTO goodsQueryDTO) {
//        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().checkApplyGoods4Delivery(goodsQueryDTO);
//    }

    @Override
    public List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO){
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().getGoodsManageAndCirculation(goodsQueryDTO);
    }


    @Override
    public void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO) {
        getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().generateProcurementBatch(generatePurchBatchDTO);
    }

    @Override
    public List<GoodsAddReduceDTO> checkGoodsAddReduce(GoodsAddReduceQueryDTO goodsAddReduceQuery) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().checkGoodsAddReduce(goodsAddReduceQuery);
    }

    @Override
    public List<MstSupplierVO> listSupplier(SupplierQueryDTO supplierQuery) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().listSupplier(supplierQuery);
    }


    @Override
    public List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO contractGoodsDeptQuery) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().listContractGoodsDept(contractGoodsDeptQuery);
    }

    @Override
    public List<GoodsQueryResp> listGoodsInfo(GoodsQueryDTO goodsQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().listGoodsInfo(goodsQueryDTO);
    }

    @Override
    public List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoList(QueryGoodsPriceInfoReq queryGoodsPriceInfoReq) {
        return Collections.emptyList();
    }

    @Override
    public List<BatchGenerateBillNoResp> getBatchBillNo(List<BatchGenerateBillNoDTO> list) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().getBatchBillNo(list);
    }

    @Override
    public GoodsStrategyResp getPurchGoodsStrategy(GoodsStrageReq req) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().getPurchGoodsStrategy(req);
    }

    @Override
    public ManageAndCirculationDTO getManageAndCirculation() {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().getManageAndCirculation();
    }

    @Override
    public List<ApplyGoodsDTO> checkGoodsBasic(List<GoodsQueryResp> goodsQueryRespList) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().checkGoodsBasic(goodsQueryRespList);
    }

    @Override
    public ApplyGoodsDTO checkGoodsBasic(GoodsQueryResp goodsQueryResp) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().checkGoodsBasic(goodsQueryResp);
    }

    @Override
    public List<CategoryCodeAll> getCategoryCodeAll(List<String> categoryCodeList) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().getCategoryCodeAll(categoryCodeList);
    }
}
