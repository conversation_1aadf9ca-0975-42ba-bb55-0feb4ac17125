package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierCategoryPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierCategoryDTO;

/**
 * 供应商分类相关业务逻辑接口
 */
public interface IMdSupplierCategoryDomainService {

    /**
     * 新增或更新供应商分类
     *
     * @param dto 供应商分类DTO
     * @return 当前数据的id
     */
    Long createOrUpdateSupplierCategory(MstSupplierCategoryDTO dto);

    /**
     * 根据分类编码删除供应商分类
     *
     * @param cateCode 分类编码
     */
    void deleteSupplierCategoryByCode(String cateCode);

    /**
     * 分页查询供应商分类
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    PageResult<MstSupplierCategoryDTO> pageQuerySupplierCategory(MstSupplierCategoryPageQueryReq request);
}
