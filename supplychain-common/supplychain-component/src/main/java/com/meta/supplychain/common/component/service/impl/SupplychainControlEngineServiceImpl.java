package com.meta.supplychain.common.component.service.impl;

import com.meta.supplychain.common.component.service.intf.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 供应链控制引擎，有两个主要作用
 * 1.业务核心控制器：经营状态+流转路径+订货策略等各个业务场景的验证控制、输入输出的功能
 * 2.单据流转
 */
@Service
public class SupplychainControlEngineServiceImpl implements ISupplychainControlEngineService {

    @Autowired
    private ISupplychainBizSysParamRuleService supplychainBizSysParamRuleService;

    @Autowired
    private ISupplychainBizBillRuleService supplychainBizBillRuleService;

    @Autowired
    private ISupplychainBizDeptRuleService supplychainBizDeptRuleService;

    @Autowired
    private ISupplychainBizGoodsRuleService supplychainBizGoodsRuleService;

    @Autowired
    private ISupplychainPmsBizRuleEngineService supplychainPmsBizRuleEngineService;

    @Autowired
    private ISupplychainWdsBizRuleEngineService supplychainWdsBizRuleEngineService;

    @Autowired
    private BillEventServiceFactory billEventServiceFactory;

    @Override
    public IPmsWorkflowManagerService getPmsWorkflowManagerService() {
        return null;
    }

    @Override
    public IWdsWorkflowManagerService getWdsWorkflowManagerService() {
        return null;
    }


    @Override
    public ISupplychainBizBillRuleService getSupplychainBizBillRuleService() {
        return supplychainBizBillRuleService;
    }

    @Override
    public ISupplychainBizDeptRuleService getSupplychainBizDeptRuleService() {
        return supplychainBizDeptRuleService;
    }

    @Override
    public ISupplychainBizGoodsRuleService getSupplychainBizGoodsRuleService() {
        return supplychainBizGoodsRuleService;
    }

    @Override
    public ISupplychainBizSysParamRuleService getSupplychainBizSysParamRuleService() {
        return supplychainBizSysParamRuleService;
    }

    @Override
    public ISupplychainPmsBizRuleEngineService getSupplychainPmsBizRuleEngineService() {
        return supplychainPmsBizRuleEngineService;
    }

    @Override
    public ISupplychainWdsBizRuleEngineService getSupplychainWdsBizRuleEngineService() {
        return supplychainWdsBizRuleEngineService;
    }

    public IOrderEventService getOrderEventService(String supportType) {
        return billEventServiceFactory.getOrDefault(supportType);
    }
}
