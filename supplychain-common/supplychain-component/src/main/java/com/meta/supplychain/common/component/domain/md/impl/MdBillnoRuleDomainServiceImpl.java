package com.meta.supplychain.common.component.domain.md.impl;

import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdBillnoRuleRepositoryService;
import com.meta.supplychain.common.component.domain.md.intf.IMdBillnoRuleDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 单号规则领域服务实现类
 *
 * <AUTHOR>
 * @date 2025/03/30 20:16
 **/
@Service
public class MdBillnoRuleDomainServiceImpl implements IMdBillnoRuleDomainService {
    @Autowired
    private IMdBillnoRuleRepositoryService mdBillnoRuleRepositoryService;
} 