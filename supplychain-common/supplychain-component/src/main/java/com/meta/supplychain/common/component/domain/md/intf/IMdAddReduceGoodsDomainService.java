package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDTO;
import com.meta.supplychain.entity.dto.md.addreducegoods.MdAddReduceGoodsDeptImportDTO;
import com.meta.supplychain.entity.dto.md.req.addreducegoods.*;
import com.meta.supplychain.entity.dto.md.resp.addreducegoods.CheckMdAddReduceGoodsResp;
import com.meta.supplychain.entity.po.md.MdAddReduceGoodsPO;
import com.metadata.idaas.client.model.LoginUserDTO;

import java.util.List;

public interface IMdAddReduceGoodsDomainService {

    void save(CreateMdAddReduceReq param);

    void update(UpdateMdAddReduceReq param);

    void delete(IdMdAddReduceGoodsReq param);

    PageResult<MdAddReduceGoodsDTO> pageList(QueryMdAddReduceGoodsReq param);

    List<MdAddReduceGoodsDTO> queryList(List<String> skuCodeList, List<String> barCodeList);

    MdAddReduceGoodsDTO detail(IdMdAddReduceGoodsReq param);

    List<CheckMdAddReduceGoodsResp> checkAddReduceGoods(CheckMdAddReduceReq param);

    void batchSave(List<MdAddReduceGoodsDeptImportDTO> saveList, OpInfo operatorInfo);

}