package com.meta.supplychain.common.component.domain.md.intf;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.md.orderattribute.ApplyOrderAttributeInfo;
import com.meta.supplychain.entity.dto.md.req.orderattribute.QueryAttributeDetailReq;
import com.meta.supplychain.entity.dto.md.req.orderattribute.QueryAttributeReq;
import com.meta.supplychain.entity.dto.md.req.orderattribute.UpdateAttributeReq;
import com.meta.supplychain.entity.po.md.MdApplyOrderAttributePO;

import java.util.List;

public interface IMdApplyOrderAttributeDomainService {
    void addOrUpdateAttribute(ApplyOrderAttributeInfo applyOrderAttributeInfo);

    void stopAttribute(UpdateAttributeReq req);

    Page<ApplyOrderAttributeInfo> getApplyOrderList(QueryAttributeReq req);

    ApplyOrderAttributeInfo getApplyDetail(QueryAttributeDetailReq req);

    List<MdApplyOrderAttributePO> listApplyOrder(QueryAttributeReq req);
}
