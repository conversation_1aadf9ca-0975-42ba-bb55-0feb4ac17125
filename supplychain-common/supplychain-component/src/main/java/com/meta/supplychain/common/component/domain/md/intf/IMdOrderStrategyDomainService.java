package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.orderstrategy.MdOrderStrategyQueryDTO;
import com.meta.supplychain.entity.dto.md.req.order.MdOrderStrategyPageQueryReq;
import com.meta.supplychain.entity.dto.md.req.order.MdOrderStrategyTypeCodeQuery;
import com.meta.supplychain.entity.dto.md.resp.order.MdOrderStrategyDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.Collection;
import java.util.List;

/**
 * 订单订货策略服务接口
 * <AUTHOR>
 */
@Tag(name = "订单订货策略接口", description = "订单订货策略相关接口")
public interface IMdOrderStrategyDomainService {
    
    /**
     * 分页查询订单订货策略
     *
     * @param request 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询订单订货策略", description = "根据条件分页查询订单订货策略")
    PageResult<MdOrderStrategyDTO> pageQuery(@Parameter(description = "查询条件") MdOrderStrategyPageQueryReq request);
    
    /**
     * 根据ID批量查询订单订货策略详情
     *
     * @param id ID
     * @return 订单策略详情列表
     */
    @Operation(summary = "批量查询订单订货策略详情", description = "根据ID集合批量查询订单订货策略详情")
    MdOrderStrategyDTO listById(@Parameter(description = "订单策略ID") Long id);
    
    /**
     * 根据ID批量删除订单订货策略
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Operation(summary = "批量删除订单订货策略", description = "根据ID集合批量删除订单订货策略")
    boolean removeByIds(@Parameter(description = "订单策略ID集合") Collection<Long> ids);
    
    /**
     * 根据部门类型和部门编码更新订单订货策略
     * 
     * @param dto 订单订货策略DTO
     * @return 是否更新成功
     */
    @Operation(summary = "更新订单订货策略", description = "根据部门类型和部门编码更新订单订货策略")
    boolean updateByDeptTypeAndDeptCode(@Parameter(description = "订单订货策略") MdOrderStrategyDTO dto);
    
    /**
     * 保存订单订货策略（存在则更新，不存在则新增）
     * 
     * @param dto 订单订货策略DTO
     * @return 是否保存成功
     */
    @Operation(summary = "保存订单订货策略", description = "根据部门类型和部门编码判断，存在则更新，不存在则新增")
    boolean saveOrderStrategy(@Parameter(description = "订单订货策略") MdOrderStrategyDTO dto);

    void saveBatchOrderStrategy(List<MdOrderStrategyDTO> dtoList);

    /**
     * 根据多个部门类型和部门编码组合查询订单订货策略
     * 
     * @param typeCodeList 部门类型和部门编码查询条件列表
     * @return 订单策略详情列表
     */
    @Operation(summary = "根据多个部门类型和部门编码查询", description = "根据多个部门类型和部门编码组合查询订单订货策略")
    List<MdOrderStrategyDTO> listByDeptTypeAndCodes(@Parameter(description = "部门类型和部门编码列表") List<MdOrderStrategyTypeCodeQuery> typeCodeList);

    /**
     * 组件查询订单订货策略，如果多条返回第一条
     * @param dto
     * @return
     */
    List<MdOrderStrategyDTO> queryOrderStrategy(MdOrderStrategyQueryDTO dto);
}
