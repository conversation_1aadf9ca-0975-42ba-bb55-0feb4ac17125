package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.entity.dto.md.component.bizrule.*;
import com.meta.supplychain.entity.dto.replenishment.req.TranscodingBillReq;
import com.meta.supplychain.entity.dto.replenishment.req.TranscodingReq;
import com.meta.supplychain.entity.dto.replenishment.resp.BatchGenerateBillNoResp;
import com.meta.supplychain.entity.dto.replenishment.resp.TranscodingStrategyResp;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;

import java.util.List;

/**
 * 业务单据规则控制器
 */
public interface ISupplychainBizBillRuleService {

    /**
     * 服务端获取单据号
     * @param mdBillNoBillTypeEnum
     * @return
     */
    String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode);

    List<BatchGenerateBillNoResp> getBatchBillNo(List<BatchGenerateBillNoDTO> list);
    /**
     * 查询订货属性
     * 订货申请/需求单/配送订单
     */
    List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 查询退货属性
     * 退货申请/需求单/配送订单
     */
    List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 订货申请提交自动生成需求单校验 及 转订单状态
     * 订货申请
     */
    DemandProcessStrategyAutoDTO getAutoDemandProcessStrategy(DemandProcessStrategyAutoQueryDTO demandProcessStrategyAutoQueryDTO);

    /**
     * 手工生成需求单转订单状态
     * 需求单
     */
    DemandProcessStrategyManualDTO getManualDemandProcessStrategy(DemandProcessStrategyManualQueryDTO demandProcessStrategyManualQueryDTO);

    /**
     * 获取转码策略
     * @param skuCodeList
     * @param isRmGoods
     * @return
     */
    List<TranscodingStrategyResp> getStrategyListFromGoodsCode(List<String> skuCodeList, boolean isRmGoods);
    /**
     * 新增转码单
     * @param req
     * @return
     */
    String saveTscBill(TranscodingBillReq req);
    /**
     * 冲红转码单
     * @param req
     * @return
     */
    String reverseTscBill(TranscodingReq req);
}
