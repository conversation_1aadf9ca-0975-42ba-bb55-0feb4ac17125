package com.meta.supplychain.common.component.service.intf.commonbiz;

import com.meta.supplychain.entity.dto.md.component.goodsrule.StockQueryDTO;
import com.meta.supplychain.entity.dto.stock.req.*;
import com.meta.supplychain.entity.dto.stock.resp.*;

import java.util.List;
import java.util.Map;

public interface ICommonStockService {

    /**
     *
     */
    BatchRecordResp costStockExecute(BatchRecordReq batchRecord);

    void costStockExecute(BatchStockReportReq batchRecord);


    /**
     */
    void costStockRollback(BatchRecordReq batchRecordReq);

    /**
     * 商品库存查询
     * @param stockQueryWithDeptAndSkusDTO
     * @return
     */
    List<StockVO> getWhStockQquery(StockQueryWithDeptAndSkusDTO stockQueryWithDeptAndSkusDTO);

    /**
     * 储位库存锁定/释放
     * @param locStockReq
     * @return
     */
    LocStockResp locStockLock(LocStockReq locStockReq);

    /**
     * 储位库存按单批量释放
     * @param batchReleaseReq
     * @return
     */
    void locStockBatchRelease(List<LocReleaseDTO> batchReleaseReq);

    /**
     * 查询商品可用储位库存
     * @param locStockQueryReq
     * @return
     */
    LocStockResp getSkuLocStockList(LocStockQueryReq locStockQueryReq);

    /**
     *
     * @param whCode 仓库编码
     * @param skuCodeList 商品编码列表
     * @param directSign 是否直流，0否1是 非必须
     * @return
     */
    LocStockResp getSkuLocStockList(String whCode, List<String> skuCodeList, Integer directSign);
    /**
     * 查询商品可用储位库存
     * @param locationReq
     * @return
     */
    List<WhLocationResp> getSkuLocList(WhLocationReq locationReq);

    /**
     * 查询商品可用储位信息
     * @param whCode 门店编码
     * @param skuCodeList 商品编码列表
     * @param directSign 是否直流，0否1是 非必须
     * @return
     */
    List<WhLocationResp> getSkuLocList(String whCode, List<String> skuCodeList, Integer directSign);

    /**
     * 获取部门可入库的仓库信息
     * @param deptCode 部门编码
     * @param skuCodeList 商品编码列表
     * @return
     */
    List<WhLocationResp> getAcceptSkuLocList(String deptCode, List<String> skuCodeList);
    /**
     * 查询商品库存信息
     * @param stockQuery
     * @return
     */
    Map<String, List<StockVO>> getStockInfo(StockQueryDTO stockQuery);

    /**
     * 商品移位库存处理
     * @param locStockMoveReq
     */
    void locStockMove(LocStockMoveReq locStockMoveReq);

    List<StkWhResp> getBatchWhStockQquery(List<QueryStockSingleReq> req);
}
