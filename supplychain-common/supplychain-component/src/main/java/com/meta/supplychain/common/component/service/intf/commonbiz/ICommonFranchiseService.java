package com.meta.supplychain.common.component.service.intf.commonbiz;

import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.franline.resp.FranLineAdjustResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ICommonFranchiseService {

    /**
     * 查询加盟店额度
     */
    public List<FranLineAdjustResp> queryFranLine(String deptCode);

    /**
     * 检查加盟店额度
     * @param deptMoneyMap 部门编码， 需要校验的金额
     */
    Map<String,String> checkFranLine(Map<String, BigDecimal>  deptMoneyMap);

    void checkFranLine(String deptCode, BigDecimal amount);
    /**
     * 检查加盟店额度
     * 校验失败抛异常
     * @param deptMoneyMap 部门编码， 需要校验的金额
     */
    void checkFranLineWithValid(Map<String,BigDecimal> deptMoneyMap);

    /**
     * 组装调整加盟额度入参
     */
    public FranLineAdjustReq buildReq(String billNo, String deptCode, BigDecimal amount, Integer type, OpInfo operatorInfo);


    /**
     * 调整加盟额度
     * FranLineTypeEnum
     */
    FranLineAdjustReq adjustFranLine(FranLineAdjustReq req);

    /**
     * 加盟回滚
     *
     * @param req
     */
    void franLineAdjustRollback(FranLineAdjustReq req);
}
