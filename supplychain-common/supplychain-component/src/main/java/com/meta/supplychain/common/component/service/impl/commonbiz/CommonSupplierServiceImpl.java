package com.meta.supplychain.common.component.service.impl.commonbiz;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonSupplierService;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierReqDTO;
import com.meta.supplychain.entity.dto.md.req.supplier.SelectItemReqDTO;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import com.meta.supplychain.entity.dto.md.resp.supplier.SelectItemDTO;
import com.meta.supplychain.infrastructure.feign.MstSupplierApiClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/10 15:36
 **/
@Service
public class CommonSupplierServiceImpl implements ICommonSupplierService {
    @Autowired
    private MstSupplierApiClient mstSupplierApiClient;

    @Override
    public MstSupplierVO queryByCode(String code) {
        return mstSupplierApiClient.queryByCode(code);
    }

    @Override
    public List<MstSupplierVO> querySupplierList(List<String> codeList) {
        SelectItemReqDTO<MstSupplierReqDTO> selectItemReq = new SelectItemReqDTO<>();
        MstSupplierReqDTO mstSupplierReqDTO = new MstSupplierReqDTO();
        mstSupplierReqDTO.setCodeList(codeList);

        selectItemReq.setQueryFilter(mstSupplierReqDTO);
        mstSupplierReqDTO.setCurrent(1L);
        mstSupplierReqDTO.setPageSize(1000L);

        selectItemReq.setValueFlag(1);

        PageResult<SelectItemDTO<MstSupplierVO>> selectItemDTOPageResult = mstSupplierApiClient.querySupplierList(selectItemReq);

        List<MstSupplierVO> list = new ArrayList<>();
        if(selectItemDTOPageResult.getTotal() > 0L){
            for(SelectItemDTO<MstSupplierVO> item : selectItemDTOPageResult.getRows()){
                list.add(item.getValue());
            }
        }

        return list;
    }

    @Override
    public Map<String, MstSupplierVO> querySupplierMap(List<String> codeList) {
        List<MstSupplierVO> mstSupplierVOList = querySupplierList(codeList);

        Map<String, MstSupplierVO> mstSupplierVOMap =
                mstSupplierVOList.stream().collect(Collectors.toMap(MstSupplierVO::getCode, Function.identity(), (o1, o2) -> o1));

        return mstSupplierVOMap;
    }
}
