package com.meta.supplychain.common.component.domain.md.intf;


import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.md.goodsstrategy.GoodsStrategyInfoDTO;
import com.meta.supplychain.entity.dto.md.goodsstrategy.ListGoodsStrategyDTO;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyLogResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsStrategyResp;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptOpLogPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptPO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyPO;
import com.metadata.idaas.client.model.LoginUserDTO;

import java.util.List;

public interface IMdGoodsStrategyDomainService {
    /**
     * 新增商品策略配置
     * @return
     */
    void addGoodsStrategy(List<GoodsStrategyReq> req);


    /**
     * 删除商品策略配置
     * @return
     */
    void deleteGoodsStrategy(List<Long> ids);

    List<MdGoodsStrategyDeptOpLogPO> generateStrategyLogs(List<MdGoodsStrategyDeptPO> strategyDeptList,
                                                                 MdGoodsStrategyPO mdGoodsStrategyPO,
                                                                 Integer operateType, LoginUserDTO loginUser, String batch);

    /**
     * 删除商品策略配置
     * @return
     */
    PageResult<GoodsStrategyLogResp> queryGoodsStrategyLogs(GoodsStrategyLogReq req);

    /**
     * 查询商品策略配置
     * @param req
     * @return
     */
    PageResult<GoodsStrategyInfoDTO> queryExistingStrategyList(QueryExistingStrategyReq req);

    /**
     * 查询商品策略配置
     * @param req
     * @return
     */
    PageResult<GoodsStrategyResp> queryGoodsStrategyList(QueryGoodsStrategyReq req);

    PageResult<GoodsStrategyResp> queryGoodsStrategyList4Role(QueryGoodsStrategy4RoleReq req);

    Result<GoodsStrategyResp> queryGoodsStrategyDetailList(Long id);

    /**
     * 组件查询商品订货策略
     * @return
     */
    List<MdGoodsStrategyDeptPO> listGoodsStrategy(ListGoodsStrategyDTO listGoodsStrategyDTO);
}