package com.meta.supplychain.common.component.service.impl;

import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.ISupplychainWdsBizRuleEngineService;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderAttrDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderAttrQueryDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderReturnAttrDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/21 15:56
 **/
@Service
public class SupplychainWdsBizRuleEngineServiceImpl implements ISupplychainWdsBizRuleEngineService {

    public ISupplychainControlEngineService getSupplychainControlEngineService(){
        ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
        return supplychainControlEngineService;
    }

    @Override
    public String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().getBillNo(mdBillNoBillTypeEnum,deptCode);
    }

    @Override
    public List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().listOrderAttr(orderAttrQueryDTO);
    }

    @Override
    public List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizBillRuleService().listOrderReturnAttr(orderAttrQueryDTO);
    }

    @Override
    public List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO) {
        return getSupplychainControlEngineService().getSupplychainBizGoodsRuleService().getGoodsManageAndCirculation(goodsQueryDTO);
    }


}
