package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingGroupDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMappingDTO;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyAutoMappingBatchDeleteReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyAutoMappingBatchReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMapping4DeptQueryReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMappingBatchReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeBatchCreateReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeBatchDeleteReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludePageQueryReq;
import com.meta.supplychain.entity.dto.md.resp.demandstrategyexclude.MdDemandStrategyExcludeResponseDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 需求策略领域服务接口
 * <AUTHOR>
 */
public interface IMdDemandStrategyDomainService {

    /**
     * 分页查询需求策略排除范围
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    PageResult<MdDemandStrategyExcludeResponseDTO> pageQueryExclude(MdDemandStrategyExcludePageQueryReq request);

    /**
     * 批量创建需求策略排除范围
     *
     * @param batchRequest 批量创建请求
     * @return 是否成功
     */
    Boolean createExclude(MdDemandStrategyExcludeBatchCreateReq batchRequest);

    /**
     * 批量删除需求策略排除范围
     *
     * @param batchRequest 批量删除请求
     * @return 是否成功
     */
    Boolean deleteExclude(MdDemandStrategyExcludeBatchDeleteReq batchRequest);

    Boolean createAutoMappingConditionGroup(List<MdDemandStrategyAutoMappingDTO> dtoList);

    @Transactional
    Boolean createAutoMapping(MdDemandStrategyAutoMappingBatchReq batchRequest);

    Boolean deleteAutoMapping(MdDemandStrategyAutoMappingBatchDeleteReq batchRequest);

    List<MdDemandStrategyAutoMappingGroupDTO> listAllStrategyGroups();

    List<MdDemandStrategyAutoMappingGroupDTO> listStrategyGroups();

    List<MdDemandStrategyAutoMappingGroupDTO> queryStrategyGroups(List<MdDemandStrategyAutoMappingGroupDTO> queryList);

    Boolean createStatusMapping(List<MdDemandStrategyStatusMappingDTO> dtoList);

    Boolean createOrUpdateStatusMapping(MdDemandStrategyStatusMappingBatchReq batchRequest);

    List<MdDemandStrategyStatusMapping4DeptDTO> listAllMdDemandStrategyStatusMappingData();

    List<MdDemandStrategyStatusMapping4DeptDTO> queryMdDemandStrategyStatusMappingData(MdDemandStrategyStatusMapping4DeptQueryReq query);
}