package com.meta.supplychain.common.component.domain.md.impl;


import cn.hutool.core.date.StopWatch;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdContractGoodsDefineDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.convert.md.MdContractDefGoodsDeptConvert;
import com.meta.supplychain.convert.md.MdContractGoodsDefineConvert;
import com.meta.supplychain.convert.md.MdContractGoodsDeptConvert;
import com.meta.supplychain.entity.dto.bds.req.QueryAllDeptDataReq;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.req.QueryGroupDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryAllDeptDataResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryGroupDeptListResp;
import com.meta.supplychain.entity.dto.fco.req.StockRebateGoodsReq;
import com.meta.supplychain.entity.dto.fco.req.StockRebateReq;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSimpleInfo;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractDefGoodsDTO;
import com.meta.supplychain.entity.dto.md.contractdef.MdContractGoodsDefineDTO;
import com.meta.supplychain.entity.dto.md.req.contractdef.*;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefineGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdContractGoodsDefinePO;
import com.meta.supplychain.entity.po.md.MdContractGoodsDeptPO;
import com.meta.supplychain.entity.po.md.MdContractMasPO;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.FcoFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractGoodsDefineGoodsDeptRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractGoodsDefineRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractGoodsDeptRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractMasRepositoryService;
import com.meta.supplychain.util.DateUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:46
 **/
@Service
public class MdContractGoodsDefineDomainServiceImpl implements IMdContractGoodsDefineDomainService {

    @Autowired
    private IMdContractGoodsDefineRepositoryService mdCgoodsDefinitionRepositoryService;

    @Autowired
    private IMdContractGoodsDefineGoodsDeptRepositoryService mdCgoodsDefinitionGoodsDeptRepositoryService;

    @Autowired
    private IMdContractGoodsDeptRepositoryService mdContractGoodsDeptRepositoryService;

    @Autowired
    private IMdContractMasRepositoryService mdContractMasRepositoryService;

    @Autowired
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Resource
    private ICommonGoodsService commonGoodsService;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Resource
    private FcoFeignClient fcoFeignClient;

    // 操作类型,1:暂存,2提交
    private static final Integer OP_TYPE = 1;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public MdContractGoodsDefineDTO save(MdContractDefSaveReq mdCgoodsDefinition) {
        Logs.info("保存合同商品定义入参:{}", Jsons.toJson(mdCgoodsDefinition));
        // 是否新增
        Boolean addFlag = mdCgoodsDefinition.getId() == null;

        if (addFlag) {
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.CONTRACT_DEFINE,"");
            mdCgoodsDefinition.setBillNo(billNo);
        } else {
            // 获取合同商品定义
            LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(null != mdCgoodsDefinition.getId(), MdContractGoodsDefinePO::getId, mdCgoodsDefinition.getId());
            queryWrapper.eq(StringUtils.isNotEmpty(mdCgoodsDefinition.getBillNo()), MdContractGoodsDefinePO::getBillNo, mdCgoodsDefinition.getBillNo());
            MdContractGoodsDefinePO mdCgoodsDefinitionPO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);
            if (null  == mdCgoodsDefinitionPO) {
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
            }
            if (!mdCgoodsDefinitionPO.getStatus().equals(MdContractGoodsDefineStatusEnum.STATUS_1.getCode())
                    && !mdCgoodsDefinitionPO.getStatus().equals(MdContractGoodsDefineStatusEnum.STATUS_3.getCode())) {
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
            }
        }

        //校验合同信息
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        mdContractMasPO.setContractNo(mdCgoodsDefinition.getContractNo());
        mdContractMasPO.setTenantId(Long.valueOf(TenantContext.get()));
        MdContractMasPO dbConcract = mdContractMasRepositoryService.getMdContractMasMapper().selectMaxContract(mdContractMasPO);
        if (null == dbConcract) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P008, new String[]{mdCgoodsDefinition.getContractNo()});
        }

        // 设置保存状态
        MdContractGoodsDefinePO savePO = MdContractGoodsDefineConvert.INSTANCE.convertReq2po(mdCgoodsDefinition);
        savePO.setExecTime(DateUtil.date2LocalDateTime(mdCgoodsDefinition.getExecTime()));
        savePO.setContractSerial(dbConcract.getContractSerial());
        savePO.setManualContractNo(dbConcract.getManualContractNo());
        savePO.setOperateMode(dbConcract.getOperateMode());
        savePO.setSupplierCode(dbConcract.getSupplierCode());
        savePO.setSupplierName(dbConcract.getSupplierName());
        if (OP_TYPE.equals(mdCgoodsDefinition.getOpType())) {
            savePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_1.getCode());
        } else {
            savePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_2.getCode());
        }

        // 保存合同商品定义部门商品
        List<MdContractGoodsDefineGoodsDeptPO> saveList = Lists.newArrayList();
        if (addFlag) {
            // 保存合同商品定义
            mdCgoodsDefinitionRepositoryService.save(savePO);
        } else {
            // 保存合同商品定义
            mdCgoodsDefinitionRepositoryService.updateById(savePO);
            // 删除历史定义
            LambdaQueryWrapper<MdContractGoodsDefineGoodsDeptPO> deleteQueryWrapper = new LambdaQueryWrapper<>();
            deleteQueryWrapper.eq(MdContractGoodsDefineGoodsDeptPO::getContractGoodsDefineId, savePO.getId());
            mdCgoodsDefinitionGoodsDeptRepositoryService.getBaseMapper().delete(deleteQueryWrapper);
        }

        for (int i = 0; i < mdCgoodsDefinition.getGoodsList().size(); i++) {
            MdContractDefGoodsReq mdContractDefGoodsReq = mdCgoodsDefinition.getGoodsList().get(i);
            for (int j = 0; j < mdContractDefGoodsReq.getGoodsDeptList().size(); j++) {
                MdContractDefGoodsDeptReq mdContractDefGoodsDeptReq = mdContractDefGoodsReq.getGoodsDeptList().get(j);
                MdContractGoodsDefineGoodsDeptPO saveDeptPO =
                        MdContractDefGoodsDeptConvert.INSTANCE.convertReq2po(mdContractDefGoodsDeptReq);
                saveDeptPO.setContractGoodsDefineId(savePO.getId());
                saveDeptPO.setContractNo(mdCgoodsDefinition.getContractNo());
                saveDeptPO.setSkuCode(mdContractDefGoodsReq.getSkuCode());
                saveDeptPO.setSkuName(mdContractDefGoodsReq.getSkuName());
                saveDeptPO.setBarcode(mdContractDefGoodsReq.getBarcode());
                saveDeptPO.setSkuNo(mdContractDefGoodsReq.getSkuNo());
                saveDeptPO.setSkuModel(mdContractDefGoodsReq.getSkuModel());
                saveDeptPO.setProducingArea(mdContractDefGoodsReq.getProducingArea());
                saveDeptPO.setPurchPrice(mdContractDefGoodsReq.getPurchPrice());
                saveDeptPO.setSalePrice(mdContractDefGoodsReq.getSalePrice());
                saveDeptPO.setCeilingPrice(mdContractDefGoodsReq.getCeilingPrice());
                saveDeptPO.setFloorPrice(mdContractDefGoodsReq.getFloorPrice());
                saveDeptPO.setInputTaxRate(mdContractDefGoodsReq.getInputTaxRate());
                saveDeptPO.setOutputTaxRate(mdContractDefGoodsReq.getOutputTaxRate());
                saveList.add(saveDeptPO);
            }
        }

        /**
         * a)非联营/联营管库存商品，或非租赁商品，相同店组群/具体部门+相同商品，允许多供应商。相同供应商，不允许多合同号对照（待生效或生效中的合同号）。
         * b)联营/联营管库存商品，租赁商品，相同店组群/具体部门+相同商品，不允许多供应商，且只可维护一份合同（待生效或生效中的合同号）。
         */
        // 获取商品信息
        List<String> skuCodeList = saveList.stream().map(MdContractGoodsDefineGoodsDeptPO::getSkuCode).distinct().collect(Collectors.toList());
        List<GoodsSimpleInfo> skuList = commonGoodsService.skuSimpleList(skuCodeList, "true");
        if (CollectionUtils.isEmpty(skuList)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P009);
        }
        Map<String, GoodsSimpleInfo> skuMap = skuList.stream()
                .collect(Collectors.toMap(GoodsSimpleInfo::getSkuCode, obj -> obj, (obj1, obj2) -> obj1));

        // 获取商品定义数据
        Set<String> deptSet = saveList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).collect(Collectors.toSet());
        LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MdContractGoodsDeptPO::getCode, deptSet);
        queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, skuCodeList);
        List<MdContractGoodsDeptPO> mdContractGoodsDeptPOS = mdContractGoodsDeptRepositoryService.getMdContractGoodsDeptMapper().selectList(queryWrapper);
        saveList.forEach(item -> {
            if (!skuMap.containsKey(item.getSkuCode())) {
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P010, new String[]{item.getSkuCode()});
            }
            GoodsSimpleInfo goodsSimpleInfo = skuMap.get(item.getSkuCode());
            item.setSpuCode(goodsSimpleInfo.getSpuCode());
            if (mdCgoodsDefinition.getType().equals(1)) {
                String operationModel = skuMap.get(item.getSkuCode()).getOperationModel();
                if (operationModel.equals("2") || operationModel.equals("7") || operationModel.equals("8")) {
                    Optional<MdContractGoodsDeptPO> first = mdContractGoodsDeptPOS.stream()
                            .filter(obj -> obj.getCode().equals(item.getCode())
                                    && obj.getSkuCode().equals(item.getSkuCode())
                                    && !obj.getContractNo().equals(item.getContractNo()))
                            .findFirst();
                    if (first.isPresent()) {
                        BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P011,
                                new String[]{item.getSkuCode(),
                                        item.getSkuName(),
                                        savePO.getSupplierCode(),
                                        savePO.getSupplierName(),
                                        savePO.getContractNo()});
                    }
                } else {
                    Optional<MdContractGoodsDeptPO> first = mdContractGoodsDeptPOS.stream()
                            .filter(obj -> obj.getCode().equals(item.getCode())
                                    && obj.getSkuCode().equals(item.getSkuCode())
                                    && obj.getSupplierCode().equals(savePO.getSupplierCode())
                                    && !obj.getContractNo().equals(item.getContractNo()))
                            .findFirst();
                    if (first.isPresent()) {
                        BizExceptions.throwWithCodeAndMsg(MdErrorCodeEnum.SCWDS012P011.getCode(),
                                MdErrorCodeEnum.SCWDS012P011.getErrorMsg(),
                                new String[]{item.getSkuCode(),
                                        item.getSkuName(),
                                        savePO.getSupplierCode(),
                                        savePO.getSupplierName(),
                                        savePO.getContractNo()});
                    }
                }
            }

            // 校验特供价
            if (item.getSpecialPriceMode().equals(-1)) {
                item.setSpecialTaxPrice(null);
                item.setSpecialRate(null);
                item.setSpecialStartTime(null);
                item.setSpecialEndTime(null);
            } else {
                if (null == item.getSpecialStartTime()) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P012, new String[]{item.getSkuCode()});
                }
                if (null == item.getSpecialEndTime()) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P013, new String[]{item.getSkuCode()});
                }
            }
            if (item.getSpecialPriceMode().equals(0) && null == item.getSpecialTaxPrice()) {
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P014, new String[]{item.getSkuCode()});
            }

            // 校验降价补偿
            if (new Integer(1).equals(item.getPriceCompensate())) {
                if (null == item.getPriceCompensateType()
                        || null == item.getCompensatePrice()) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P015, new String[]{item.getSkuCode()});
                }
                if (item.getPriceCompensateType().equals(3) && null == item.getCompensateStorePrice()) {
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P016, new String[]{item.getSkuCode()});
                }
            }
        });

        // 保存合同商品定义部门商品
        mdCgoodsDefinitionGoodsDeptRepositoryService.saveBatch(saveList);

        return MdContractGoodsDefineConvert.INSTANCE.convertPo2DTO(savePO);
    }

    @Override
    public PageResult<MdContractGoodsDefineDTO> pageList(MdContractDefPageListReq param) {
        Logs.info("分页查询合同商品定义入参:{}", Jsons.toJson(param));
        IPage<MdContractGoodsDefinePO> pageList = mdCgoodsDefinitionRepositoryService
                .getMdContractGoodsDefineMapper().pageList(new Page<>(param.getCurrent(), param.getPageSize()), param);

        if (CollectionUtils.isEmpty(pageList.getRecords())) {
            return PageResult.ofEmpty();
        }

        return PageResult.of(pageList.getTotal(),
                pageList.getRecords().stream().map(MdContractGoodsDefineConvert.INSTANCE::convertPo2DTO).collect(Collectors.toList()));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void toApprove(MdContractDefToApproveReq param) {
        Logs.info("合同商品定义列表提交审核入参:{}", Jsons.toJson(param));

        // 获取待审批合同商品定义
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDefinePO::getId, param.getId());
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(MdContractGoodsDefineStatusEnum.STATUS_1.getCode());
        statusList.add(MdContractGoodsDefineStatusEnum.STATUS_3.getCode());
        queryWrapper.in(MdContractGoodsDefinePO::getStatus, statusList);
        MdContractGoodsDefinePO mdCgoodsDefinitionPO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);

        if (Objects.isNull(mdCgoodsDefinitionPO)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
        }

        MdContractGoodsDefinePO updatePO = new MdContractGoodsDefinePO();
        updatePO.setId(mdCgoodsDefinitionPO.getId());
        updatePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_2.getCode());
        mdCgoodsDefinitionRepositoryService.updateById(updatePO);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void cancel(MdContractDefCancelReq param) {
        Logs.info("合同商品定义作废入参:{}", Jsons.toJson(param));
        // 获取待审批合同商品定义
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDefinePO::getId, param.getId());
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(MdContractGoodsDefineStatusEnum.STATUS_1.getCode());
        statusList.add(MdContractGoodsDefineStatusEnum.STATUS_3.getCode());
        statusList.add(MdContractGoodsDefineStatusEnum.STATUS_4.getCode());
        queryWrapper.in(MdContractGoodsDefinePO::getStatus, statusList);
        MdContractGoodsDefinePO mdCgoodsDefinitionPO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);

        if (Objects.isNull(mdCgoodsDefinitionPO)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
        }

        MdContractGoodsDefinePO updatePO = new MdContractGoodsDefinePO();
        updatePO.setId(mdCgoodsDefinitionPO.getId());
        updatePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_6.getCode());
        mdCgoodsDefinitionRepositoryService.updateById(updatePO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void approve(MdContractDefApproveReq param, LoginUserDTO loginUser) {
        Logs.info("合同商品定义审核入参:{}", Jsons.toJson(param));

        // 获取待审批合同商品定义
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDefinePO::getId, param.getId());
        queryWrapper.eq(MdContractGoodsDefinePO::getStatus, MdContractGoodsDefineStatusEnum.STATUS_2.getCode());
        MdContractGoodsDefinePO mdCgoodsDefinitionPO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);

        if (Objects.isNull(mdCgoodsDefinitionPO)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
        }

        MdContractGoodsDefinePO updatePO = new MdContractGoodsDefinePO();
        updatePO.setId(mdCgoodsDefinitionPO.getId());
        if (param.getAuditStatus() == 1) {
            updatePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_4.getCode());
        } else {
            updatePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_3.getCode());
        }
        updatePO.setAuditStatus(param.getAuditStatus());
        updatePO.setAuditRemark(param.getAuditRemark());
        updatePO.setAuditUid(loginUser.getUid());
        updatePO.setAuditCode(loginUser.getCode());
        updatePO.setAuditName(loginUser.getName());
        updatePO.setAuditTime(LocalDateTime.now());
        mdCgoodsDefinitionRepositoryService.updateById(updatePO);

        // 单据立即执行的
        if (mdCgoodsDefinitionPO.getExecTimeType().equals(1) && param.getAuditStatus().equals(1)) {
            executeBill(mdCgoodsDefinitionPO.getBillNo());
        }
    }

    @Override
    public MdContractGoodsDefineDTO getDetail(MdContractDefDetailReq param) {
        Logs.info("合同商品定义详情入参:{}", Jsons.toJson(param));
        // 获取合同商品定义
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != param.getId(), MdContractGoodsDefinePO::getId, param.getId());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getBillNo()), MdContractGoodsDefinePO::getBillNo, param.getBillNo());
        MdContractGoodsDefinePO definePO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);

        if (Objects.isNull(definePO)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
        }

        MdContractGoodsDefineDTO defineDto = MdContractGoodsDefineConvert.INSTANCE.convertPo2DTO(definePO);

        //获取合同定义部门商品
        LambdaQueryWrapper<MdContractGoodsDefineGoodsDeptPO> goodsDeptQueryWrapper = new LambdaQueryWrapper<>();
        goodsDeptQueryWrapper.eq(MdContractGoodsDefineGoodsDeptPO::getContractGoodsDefineId, param.getId());
        List<MdContractGoodsDefineGoodsDeptPO> mdContractGoodsDefineGoodsDeptPOS =
                mdCgoodsDefinitionGoodsDeptRepositoryService.getBaseMapper().selectList(goodsDeptQueryWrapper);

        List<MdContractDefGoodsDTO> goodsList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(mdContractGoodsDefineGoodsDeptPOS)) {
            Map<String, List<MdContractGoodsDefineGoodsDeptPO>> dbGoodsList = mdContractGoodsDefineGoodsDeptPOS
                    .stream().collect(Collectors.groupingBy(MdContractGoodsDefineGoodsDeptPO::getSkuCode));
            //组装商品数据
            dbGoodsList.forEach((key, list) -> {
                MdContractDefGoodsDTO goodsDTO = MdContractDefGoodsDeptConvert.INSTANCE.convertPo2GoodsDTO(list.get(0));

                //组装部门数据
                goodsDTO.setGoodsDeptList(MdContractDefGoodsDeptConvert.INSTANCE.convertPo2DTOList(list));
                goodsList.add(goodsDTO);
            });
        }
        defineDto.setGoodsList(goodsList);
        return defineDto;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void executeBill(String billNo) {
        Logs.info("执行合同商品定义入参:{}", Jsons.toJson(billNo));
        // 获取合同商品定义
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDefinePO::getBillNo, billNo);
        MdContractGoodsDefinePO definePO = mdCgoodsDefinitionRepositoryService.getOne(queryWrapper);

        if (Objects.isNull(definePO)) {
            Logs.error("合同商品定义不存在");
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P003);
        }

        if (!definePO.getStatus().equals(MdContractGoodsDefineStatusEnum.STATUS_4.getCode())) {
            Logs.error("合同商品定义状态不是待执行");
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P006);
        }

        // 获取合同定义部门商品
        LambdaQueryWrapper<MdContractGoodsDefineGoodsDeptPO> goodsDeptQueryWrapper = new LambdaQueryWrapper<>();
        goodsDeptQueryWrapper.eq(MdContractGoodsDefineGoodsDeptPO::getContractGoodsDefineId, definePO.getId());
        List<MdContractGoodsDefineGoodsDeptPO> defineList =
                mdCgoodsDefinitionGoodsDeptRepositoryService.getBaseMapper().selectList(goodsDeptQueryWrapper);

        // 修改定义状态
        definePO.setStatus(MdContractGoodsDefineStatusEnum.STATUS_5.getCode());
        definePO.setActualExecTime(LocalDateTime.now());
        mdCgoodsDefinitionRepositoryService.updateById(definePO);

        if (CollectionUtils.isNotEmpty(defineList)) {
            List<MdContractGoodsDeptPO> saveList = Lists.newArrayList();
            List<MdContractGoodsDeptPO> deleteList = Lists.newArrayList();
            List<MdContractGoodsDeptPO> canalMainList = Lists.newArrayList();

            // 执行合同商品定义
            if (definePO.getType().equals(MdContractGoodsDefineTypeEnum.DEFINE.getCode())) {
                // 收敛数据
                convergence(definePO, defineList, saveList, deleteList, canalMainList);
            }
            // 执行合同审商品取消
            if (definePO.getType().equals(MdContractGoodsDefineTypeEnum.CANCEL.getCode())) {
                // 拆单
                disassemble(definePO, defineList, saveList, deleteList);
            }

            // 保存合同商品定义
            List<Long> deleteIds = deleteList.stream().map(MdContractGoodsDeptPO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                mdContractGoodsDeptRepositoryService.getMdContractGoodsDeptMapper().deleteByIds(deleteIds);
            }
            if (CollectionUtils.isNotEmpty(saveList)) {
                mdContractGoodsDeptRepositoryService.saveBatch(saveList);
            }
            if (CollectionUtils.isNotEmpty(canalMainList)) {
                List<Long> ids = canalMainList.stream().map(MdContractGoodsDeptPO::getId).distinct().collect(Collectors.toList());
                mdContractGoodsDeptRepositoryService.getMdContractGoodsDeptMapper().updateMainSupplierMode(0, ids);
            }

            // 启动降价补偿需推送财务
            List<MdContractGoodsDefineGoodsDeptPO> sendList = defineList.stream()
                    .filter(item -> new Integer(1).equals(item.getPriceCompensate())).collect(Collectors.toList());
            sendStockRebate(definePO, sendList);
        }

    }

    @Override
    public List<MdContractGoodsDefineDTO> queryDefineList() {
        LambdaQueryWrapper<MdContractGoodsDefinePO> queryWrapper = new LambdaQueryWrapper<MdContractGoodsDefinePO>()
                .eq(MdContractGoodsDefinePO::getStatus, MdContractGoodsDefineStatusEnum.STATUS_4.getCode())
                .eq(MdContractGoodsDefinePO::getExecTimeType, 2)
                .le(MdContractGoodsDefinePO::getExecTime, LocalDateTime.now());
        List<MdContractGoodsDefinePO> list = mdCgoodsDefinitionRepositoryService.getMdContractGoodsDefineMapper().selectList(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().map(MdContractGoodsDefineConvert.INSTANCE::convertPo2DTO).collect(Collectors.toList());
    }

    /**
     * 收敛数据 若部门和店组群数据维护的一样，则收敛到店组群维度上
     * @param definePo
     * @param defineList
     * @param saveList
     * @param deleteList
     */
    private void convergence(MdContractGoodsDefinePO definePo,
                             List<MdContractGoodsDefineGoodsDeptPO> defineList,
                             List<MdContractGoodsDeptPO> saveList,
                             List<MdContractGoodsDeptPO> deleteList,
                             List<MdContractGoodsDeptPO> canalMainList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("group处理");
        List<MdContractGoodsDefineGoodsDeptPO> deptGroupList = defineList.stream().
                filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode())).collect(Collectors.toList());
        List<MdContractGoodsDefineGoodsDeptPO> deptList = defineList.stream().
                filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT.getCode())).collect(Collectors.toList());

        Logs.info("开始处理收敛数据,总条数：{},店组群条数:{},部门条数:{}", defineList.size(), deptGroupList.size(), deptList.size());
        // 合同商品数据先删后增
        Set<String> skuCodeSet = defineList.stream().map(MdContractGoodsDefineGoodsDeptPO::getSkuCode).collect(Collectors.toSet());
        Set<String> codeSet = defineList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).collect(Collectors.toSet());
        LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
        queryWrapper.in(MdContractGoodsDeptPO::getCode, codeSet);
        queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, skuCodeSet);
        queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
        List<MdContractGoodsDeptPO> dbPOList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);

        // 步骤一、如果数据库存在，直接删除数据库数据
        defineList.forEach(item -> {
            if (CollectionUtils.isNotEmpty(dbPOList)) {
                Optional<MdContractGoodsDeptPO> dbPO = dbPOList.stream()
                        .filter(obj -> obj.getCode().equals(item.getCode())
                                && obj.getSkuCode().equals(item.getSkuCode())
                                && obj.getDeptType().equals(item.getDeptType()))
                        .findFirst();
                if (dbPO.isPresent()) {
                    deleteList.add(dbPO.get());
                }
            }
        });


        // 步骤二、处理店组群数据
        if (CollectionUtils.isNotEmpty(deptGroupList)) {
            Set<String> groupCodeSet = deptGroupList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).collect(Collectors.toSet());
            // 获取店组群下所有直属部门
            List<QueryAllDeptDataResp.DeptInfo> deptInfoList = Lists.newArrayList();
            try {
                QueryAllDeptDataReq queryAllDeptDataReq = QueryAllDeptDataReq.builder()
                        .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                        .deepSearch(false)
                        .groupCodeList(groupCodeSet)
                        .build();
                deptInfoList.addAll(baseDataSystemFeignClient.queryAllDeptDataNotPage(queryAllDeptDataReq).getRows());
            } catch (Exception e) {
                Logs.error("查询店组群接口失败", e);
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
            }

            if (CollectionUtils.isNotEmpty(deptInfoList)) {
                List<String> queryDeptCodeList = deptInfoList.stream().map(QueryAllDeptDataResp.DeptInfo::getDeptCode).distinct().collect(Collectors.toList());
                queryWrapper.clear();
                queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
                queryWrapper.in(MdContractGoodsDeptPO::getCode, queryDeptCodeList);
                queryWrapper.eq(MdContractGoodsDeptPO::getDeptType, MdContractGoodsDeptTypeEnum.DEPT.getCode());
                queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
                List<MdContractGoodsDeptPO> dbList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(dbList)) {
                    // 找到店组群下的部门数据重复的数据
                    Map<String, List<QueryAllDeptDataResp.DeptInfo>> groupDeptMap = deptInfoList.stream()
                            .collect(Collectors.groupingBy(QueryAllDeptDataResp.DeptInfo::getGroupCode));
                    Map<String, List<MdContractGoodsDeptPO>> saveMap = dbList.stream().collect(Collectors.groupingBy(MdContractGoodsDeptPO::getCode));
                    for (int i = 0; i < deptGroupList.size(); i++) {

                        MdContractGoodsDefineGoodsDeptPO group = deptGroupList.get(i);
                        if (!groupDeptMap.containsKey(group.getCode())) {
                            continue;
                        }
                        groupDeptMap.get(group.getCode())
                                .stream().map(QueryAllDeptDataResp.DeptInfo::getDeptCode)
                                .distinct().collect(Collectors.toList()).forEach(deptCode -> {
                                    if (saveMap.containsKey(deptCode)) {
                                        saveMap.get(deptCode).forEach(item -> {
                                            if (checkConsistency(group, item)) {
                                                deleteList.add(item);
                                            }
                                        });
                                    }
                                });
                    }
                }

                deptGroupList.forEach(item -> {
                    MdContractGoodsDeptPO savePo = MdContractGoodsDeptConvert.INSTANCE.convertDefinePo2Po(item);
                    savePo.setSupplierCode(definePo.getSupplierCode());
                    savePo.setSupplierName(definePo.getSupplierName());
                    savePo.setDefineBillNo(definePo.getBillNo());
                    saveList.add(savePo);
                });
            }
        }
        stopWatch.stop();

        // 步骤三、处理部门数据
        stopWatch.start("dept处理");
        if (CollectionUtils.isNotEmpty(deptList)) {
            List<String> deptCodeList = deptList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).distinct().collect(Collectors.toList());
            // 获取部门上级店组群
            List<QueryBatchDeptListResp.Rows> groupList = Lists.newArrayList();
            try {
                QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                        .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                        .deptCodeList(deptCodeList)
                        .build();
                groupList.addAll(baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows());
            } catch (Exception e) {
                Logs.error("查询部门上级店组群接口失败", e);
                BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
            }

            List<MdContractGoodsDeptPO> dbList = Lists.newArrayList();
            Map<String, String> groupMap = Maps.newHashMap();
            // 获取店组群数据
            if (CollectionUtils.isNotEmpty(groupList)) {
                Set<String> groupCodeSet = Sets.newHashSet();
                groupList.forEach(item -> {
                    Optional<String> first = item.getDeptGroupList()
                            .stream().max(Comparator.comparing(QueryBatchDeptListResp.DeptGroup::getLevel))
                            .map(QueryBatchDeptListResp.DeptGroup::getCode);
                    if (first.isPresent()) {
                        groupMap.put(item.getCode(), first.get());
                        groupCodeSet.add(first.get());
                    }
                });

                if (CollectionUtils.isNotEmpty(groupCodeSet)) {
                    // 查询所有店组群的合同商品数据
                    queryWrapper.clear();
                    queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
                    queryWrapper.in(MdContractGoodsDeptPO::getCode, groupCodeSet);
                    queryWrapper.eq(MdContractGoodsDeptPO::getDeptType, MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode());
                    queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
                    List<MdContractGoodsDeptPO> dbPos = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);

                    // 步骤一添加的店组群数据也需要加入判断
                    dbList.addAll(saveList);
                    if (CollectionUtils.isNotEmpty(dbList)) {
                        if (CollectionUtils.isNotEmpty(dbPos)) {
                            dbPos.forEach(item -> {
                                Optional<MdContractGoodsDeptPO> first = dbList.stream().filter(obj -> obj.getContractNo().equals(item.getContractNo())
                                        && obj.getSupplierCode().equals(item.getSupplierCode())
                                        && obj.getCode().equals(item.getCode())
                                        && obj.getDeptType().equals(item.getDeptType())
                                        && obj.getSkuCode().equals(item.getSkuCode())).findFirst();
                                // 优先取当前传入合同商品
                                if (!first.isPresent()) {
                                    dbList.add(item);
                                }
                            });
                        }
                    } else {
                        dbList.addAll(dbPos);
                    }
                }
            }

            // 组装数据
            Map<String, List<MdContractGoodsDeptPO>> dbMap = dbList.stream().collect(Collectors.groupingBy(MdContractGoodsDeptPO::getCode));
            for (int i = 0; i < deptList.size(); i++) {
                MdContractGoodsDefineGoodsDeptPO dept = deptList.get(i);
                // 如果不存在店组群数据直接添加跳过
                if (!groupMap.containsKey(dept.getCode()) || !dbMap.containsKey(groupMap.get(dept.getCode()))) {
                    MdContractGoodsDeptPO savePo = MdContractGoodsDeptConvert.INSTANCE.convertDefinePo2Po(dept);
                    savePo.setSupplierCode(definePo.getSupplierCode());
                    savePo.setSupplierName(definePo.getSupplierName());
                    savePo.setDefineBillNo(definePo.getBillNo());
                    saveList.add(savePo);
                    continue;
                }
                // 存在则校验是否需要收敛
                Optional<MdContractGoodsDeptPO> first = dbMap.get(groupMap.get(dept.getCode()))
                        .stream().filter(item -> item.getSkuCode().equals(dept.getSkuCode())).findFirst();
                if (!first.isPresent() || !checkConsistency(dept, first.get())) {
                    MdContractGoodsDeptPO savePo = MdContractGoodsDeptConvert.INSTANCE.convertDefinePo2Po(dept);
                    savePo.setSupplierCode(definePo.getSupplierCode());
                    savePo.setSupplierName(definePo.getSupplierName());
                    savePo.setDefineBillNo(definePo.getBillNo());
                    saveList.add(savePo);
                }
            }
        }

        // 步骤四、保存数据如果存在主供应商 取消其他主供应商
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<MdContractGoodsDeptPO> mainList = saveList.stream().filter(item -> item.getMainSupplierMode().equals(1)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainList)) {
                Set<String> mainCodeSet = mainList.stream().map(MdContractGoodsDeptPO::getCode).collect(Collectors.toSet());
                Set<String> mainSkuCodeSet = mainList.stream().map(MdContractGoodsDeptPO::getSkuCode).collect(Collectors.toSet());
                queryWrapper.clear();
                queryWrapper.in(MdContractGoodsDeptPO::getCode, mainCodeSet);
                queryWrapper.in(MdContractGoodsDeptPO::getSkuCode, mainSkuCodeSet);
                List<MdContractGoodsDeptPO> dbMainList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
                mainList.forEach(item -> {
                    List<MdContractGoodsDeptPO> dbMainPOList = dbMainList.stream()
                            .filter(obj -> obj.getCode().equals(item.getCode())
                                    && obj.getSkuCode().equals(item.getSkuCode())
                                    && obj.getDeptType().equals(item.getDeptType())
                                    && !obj.getSupplierCode().equals(item.getSupplierCode()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dbMainPOList)) {
                        canalMainList.addAll(dbMainPOList);
                    }
                });
            }
        }

        Logs.info("处理结果saveList:{}, deleteList:{}, canalMainList:{}", Jsons.toJson(saveList), Jsons.toJson(deleteList), Jsons.toJson(canalMainList));
        Logs.info("收敛逻辑处理结束， 耗时:{}", stopWatch.prettyPrint());
    }

    /**
     * 拆单 若店组群下其中一个部门取消定义 则拆单
     * @param definePo
     * @param defineList
     * @param saveList
     * @param deleteList
     */
    private void disassemble(MdContractGoodsDefinePO definePo,
                             List<MdContractGoodsDefineGoodsDeptPO> defineList,
                             List<MdContractGoodsDeptPO> saveList,
                             List<MdContractGoodsDeptPO> deleteList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("group处理");

        List<MdContractGoodsDefineGoodsDeptPO> deptGroupList = defineList.stream().
                filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode())).collect(Collectors.toList());
        List<MdContractGoodsDefineGoodsDeptPO> deptList = defineList.stream().
                filter(item -> item.getDeptType().equals(MdContractGoodsDeptTypeEnum.DEPT.getCode())).collect(Collectors.toList());

        Logs.info("开始处理拆单数据,总条数：{},店组群条数:{},部门条数:{}", defineList.size(), deptGroupList.size(), deptList.size());
        // 第一步、处理店组群
        if (CollectionUtils.isNotEmpty(deptGroupList)) {
            // 查询所有店组群的合同商品数据
            Set<String> groupCodeSet = deptGroupList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).collect(Collectors.toSet());
            LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
            queryWrapper.in(MdContractGoodsDeptPO::getCode, groupCodeSet);
            queryWrapper.eq(MdContractGoodsDeptPO::getDeptType, MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode());
            queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
            List<MdContractGoodsDeptPO> dbList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
            deleteList.addAll(dbList);
        }

        stopWatch.stop();
        stopWatch.start("dept处理");
        // 第二步、处理部门
        if (CollectionUtils.isNotEmpty(deptList)) {
            // 存在则删除 不存在找上级店组群
            Set<String> deptCodeSet = deptList.stream().map(MdContractGoodsDefineGoodsDeptPO::getCode).collect(Collectors.toSet());
            LambdaQueryWrapper<MdContractGoodsDeptPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
            queryWrapper.in(MdContractGoodsDeptPO::getCode, deptCodeSet);
            queryWrapper.eq(MdContractGoodsDeptPO::getDeptType, MdContractGoodsDeptTypeEnum.DEPT.getCode());
            queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
            List<MdContractGoodsDeptPO> dbList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
            deleteList.addAll(dbList);

            Set<String> dbCodeSet = dbList.stream().map(MdContractGoodsDeptPO::getCode).collect(Collectors.toSet());
            List<String> notExistList = deptCodeSet.stream().filter(code -> !dbCodeSet.contains(code)).collect(Collectors.toList());
            List<QueryBatchDeptListResp.Rows> groupList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(notExistList)) {
                // 如果存在店组群定义过的数据 则拆分店组群
                try {
                    QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                            .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                            .deptCodeList(notExistList)
                            .build();
                    groupList.addAll(baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows());
                } catch (Exception e) {
                    Logs.error("查询部门上级店组群接口失败", e);
                    BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
                }
            }
            if (CollectionUtils.isNotEmpty(groupList)) {
                Set<String> groupCodeSet = Sets.newHashSet();
                groupList.forEach(item -> {
                    Optional<String> first = item.getDeptGroupList()
                            .stream().max(Comparator.comparing(QueryBatchDeptListResp.DeptGroup::getLevel))
                            .map(QueryBatchDeptListResp.DeptGroup::getCode);
                    if (first.isPresent()) {
                        groupCodeSet.add(first.get());
                    }
                });
                // 查询所有店组群的合同商品数据
                queryWrapper.clear();
                queryWrapper.eq(MdContractGoodsDeptPO::getContractNo, definePo.getContractNo());
                queryWrapper.in(MdContractGoodsDeptPO::getCode, groupCodeSet);
                queryWrapper.eq(MdContractGoodsDeptPO::getDeptType, MdContractGoodsDeptTypeEnum.DEPT_GROUP.getCode());
                queryWrapper.eq(MdContractGoodsDeptPO::getSupplierCode, definePo.getSupplierCode());
                List<MdContractGoodsDeptPO> dbGroupList = mdContractGoodsDeptRepositoryService.getBaseMapper().selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(dbGroupList)) {
                    deleteList.addAll(dbGroupList);
                    List<QueryGroupDeptListResp.Rows> theDeptList = Lists.newArrayList();
                    try {
                        QueryGroupDeptListReq queryGroupDeptListReq = QueryGroupDeptListReq.builder()
                                .classCode(GroupDeptEnum.CONTRACT_GROUP.getCode())
                                .codeList(new ArrayList<>(groupCodeSet))
                                .build();

                        theDeptList.addAll(baseDataSystemFeignClient.queryGroupDeptList(queryGroupDeptListReq).getRows());
                    } catch (Exception e) {
                        Logs.error("查询店组群门店列表接口失败", e);
                        BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P021);
                    }

                    Map<String, List<QueryGroupDeptListResp.Rows>> theDeptMap = theDeptList.stream()
                            .collect(Collectors.groupingBy(QueryGroupDeptListResp.Rows::getGroupCode));
                    dbGroupList.forEach(item -> {
                        if (theDeptMap.containsKey(item.getCode())) {
                            List<QueryGroupDeptListResp.Rows> depts = theDeptMap.get(item.getCode());
                            depts.forEach(dept -> {
                                MdContractGoodsDeptPO savePo = new MdContractGoodsDeptPO();
                                BeanUtils.copyProperties(item, savePo);
                                savePo.setId(null);
                                savePo.setDeptType(MdContractGoodsDeptTypeEnum.DEPT.getCode());
                                savePo.setCode(dept.getDeptCode());
                                savePo.setName(dept.getDeptName());
                                savePo.setCancelBillNo(definePo.getBillNo());
                                // 拆分店组群 且排除需要删除的部门
                                if (!deptCodeSet.contains(dept.getDeptCode())) {
                                    saveList.add(savePo);
                                }
                            });
                        }
                    });
                }
            }
        }
        Logs.info("处理结果saveList:{}, deleteList:{}", Jsons.toJson(saveList), Jsons.toJson(deleteList));
        Logs.info("收敛逻辑处理结束， 耗时:{}", stopWatch.prettyPrint());
    }

    /**
     * 校验两个合同商品是否一致
     * @param savePo
     * @param dbPo
     */
    private Boolean checkConsistency(MdContractGoodsDefineGoodsDeptPO savePo, MdContractGoodsDeptPO dbPo) {
        if (savePo == null || dbPo == null) {
            return false;
        }
        Boolean specialFlag = true;
        // 特供价过期的数据 按无特供价处理
        if (null != dbPo.getSpecialTaxPrice() && null != dbPo.getSpecialEndTime() && dbPo.getSpecialEndTime().isBefore(LocalDateTime.now())) {
            specialFlag = savePo.getSpecialTaxPrice() == null &&
                    Objects.equals(savePo.getSpecialPriceMode(), -1) &&
                    (savePo.getSpecialRate() == null ||  savePo.getSpecialRate().compareTo(BigDecimal.ZERO) == 0) &&
                    savePo.getSpecialStartTime() == null &&
                    savePo.getSpecialEndTime() == null;
            // 如果传入数据也过期 直接收敛
            if (null != savePo.getSpecialEndTime() && savePo.getSpecialEndTime().isBefore(LocalDateTime.now())) {
                specialFlag = true;
            }
        } else {
            specialFlag = Objects.equals(savePo.getSpecialTaxPrice(), dbPo.getSpecialTaxPrice()) &&
                    Objects.equals(savePo.getSpecialPriceMode(), dbPo.getSpecialPriceMode()) &&
                    Objects.equals(savePo.getSpecialRate(), dbPo.getSpecialRate()) &&
                    Objects.equals(savePo.getSpecialStartTime(), dbPo.getSpecialStartTime()) &&
                    Objects.equals(savePo.getSpecialEndTime(), dbPo.getSpecialEndTime());
        }

        return Objects.equals(savePo.getContractNo(), dbPo.getContractNo()) &&
                Objects.equals(savePo.getSkuCode(), dbPo.getSkuCode()) &&
                Objects.equals(savePo.getPurchPriceMethod(), dbPo.getPurchPriceMethod()) &&
                Objects.equals(savePo.getDeductionRate(), dbPo.getDeductionRate()) &&
                Objects.equals(savePo.getPurchTaxPrice(), dbPo.getPurchTaxPrice()) &&
                Objects.equals(savePo.getMaxPurchTaxPrice(), dbPo.getMaxPurchTaxPrice()) &&
                Objects.equals(savePo.getMainSupplierMode(), dbPo.getMainSupplierMode()) &&
                Objects.equals(savePo.getSupplyValidityTime(), dbPo.getSupplyValidityTime()) &&
                Objects.equals(savePo.getBanReturn(), dbPo.getBanReturn()) && specialFlag;

    }

    /**
     * 推送财务库存退补
     * @param sendPO
     * @param sendList
     */
    void sendStockRebate(MdContractGoodsDefinePO sendPO, List<MdContractGoodsDefineGoodsDeptPO> sendList) {
        if (CollectionUtils.isEmpty(sendList)) {
            return;
        }

        StockRebateReq req = new StockRebateReq();
        List<StockRebateGoodsReq> goodsParamsList = Lists.newArrayList();
        req.setBillNumber(sendPO.getBillNo());
        req.setOperateDate(sendPO.getActualExecTime());
        req.setTenantId(TenantContext.get());
        req.setGoodsParamsList(goodsParamsList);
        sendList.forEach(item -> {
            StockRebateGoodsReq goodsReq = new StockRebateGoodsReq();
            goodsReq.setSupplierCode(sendPO.getSupplierCode());
            goodsReq.setSupplierName(sendPO.getSupplierName());
            goodsReq.setDeptCode(item.getCode());
            goodsReq.setDeptName(item.getName());
            goodsReq.setContractNumber(sendPO.getContractNo());
            goodsReq.setGoodsCode(item.getSkuCode());
            goodsReq.setCompensateValue(item.getCompensatePrice());
            goodsReq.setCompensaMode(item.getPriceCompensateType());
            goodsParamsList.add(goodsReq);
        });
        try {
            fcoFeignClient.addStockRebate(req);
        } catch (Exception e) {
            Logs.error("推送财务库存退补接口失败", e);
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P023);
        }
    }

}
