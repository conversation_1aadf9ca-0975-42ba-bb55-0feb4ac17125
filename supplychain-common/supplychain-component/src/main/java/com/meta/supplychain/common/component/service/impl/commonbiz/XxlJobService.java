package com.meta.supplychain.common.component.service.impl.commonbiz;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.supplychain.entity.dto.xxjob.XxlJobReq;
import com.meta.supplychain.entity.dto.xxjob.XxlJobResp;
import com.meta.supplychain.util.CronGenerator;
import com.meta.supplychain.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.HttpCookie;
import java.util.*;

/**
 * @Description: xxljob工具类
 */

@Slf4j
@Configuration
public class XxlJobService {
    @Resource
    private RedisUtil redisUtil;

    private static final String XXL_JOB_KEY = "XXL_JOB_LOGIN_IDENTITY";

    //5小时
    private static Long REDIS_EXPIRE_TIME = 18000L;

    @NacosValue("${xxl.job.executor.pms.jobgroup:70}")
    private String jobGroup;

    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @NacosValue(autoRefreshed = true, value = "${xxl.job.admin.username:admin}")
    private String adminUserName;

    @NacosValue(autoRefreshed = true, value = "${xxl.job.admin.pwd:123456}")
    private String adminPwd;

    /**
     * 组装xxljob参数
     * @param xxlJob
     * @return
     */
    public Map<String, Object> createFormData(XxlJobReq xxlJob) {
        Map<String, Object> formData = new HashMap<>();
        formData.put("jobGroup", jobGroup); //供应链-订货采购管理
        formData.put("jobDesc", xxlJob.getJobDesc()); //任务名称
        formData.put("author", xxlJob.getAuthor());  //用户姓名
        formData.put("alarmEmail", "");
        formData.put("scheduleType", "CRON");
        formData.put("scheduleConf", CronGenerator.cronStr(xxlJob.getStartTime(),xxlJob.getMin()));   //cron表达式
        formData.put("schedule_conf_CRON", "");
        formData.put("schedule_conf_FIX_RATE", "");
        formData.put("schedule_conf_FIX_DELAY", "");
        formData.put("glueType", "BEAN");
        formData.put("executorHandler", xxlJob.getExecutorHandler());//JobHandler
        formData.put("executorParam", xxlJob.getExecutorParam()); //任务参数
        formData.put("executorRouteStrategy", "FIRST");
        formData.put("childJobId", "");
        formData.put("misfireStrategy", "DO_NOTHING");
        formData.put("executorBlockStrategy", "SERIAL_EXECUTION");
        formData.put("executorTimeout", "0");
        formData.put("executorFailRetryCount", "0");
        formData.put("glueRemark", "GLUE代码初始化");
        formData.put("glueSource", "");
        //构建好以后调用XxlJobClient的addJob方法并传递formData参数,这个类及方法的实现在下面
        return formData;
    }

    /**
     * 添加定时任务
     */
    public Integer addJob(XxlJobReq xxlJob) {
        Logs.info("XxlJobService.addJob.req:" + JSON.toJSONString(xxlJob));
        HttpRequest request = HttpRequest.post(adminAddresses + "/jobinfo/add")
                .header("Content-Type", "multipart/form-data")
                //每次请求都需要带上Cookie,getCookie方法在后面
                .header("Cookie", getCookie())
                .form(createFormData(xxlJob));
        try {
            // 执行 HTTP POST 请求创建定时任务
            HttpResponse response = request.execute();
            String result = response.body();
            if (StrUtil.isNotBlank(result) && 200 == response.getStatus()) {
                //定时任务创建成功后拿到任务id
                XxlJobResp obj = JSONObject.parseObject(result, XxlJobResp.class);
                Logs.info("定时任务创建成功，任务ID为：" + obj.toString());
                Integer jobId = obj.getContent();
                if(xxlJob.getIsStart()){
                    startJob(jobId);
                }
                return jobId;
            } else {
                Logs.error("XxlJobService.addJob.error:" + JSON.toJSONString(xxlJob) + ",resp:" + result);
            }
        } catch (Exception e) {
            Logs.error("XxlJobService.addJob.定时任务创建失败，发生异常：" ,e);
        }
        return null;
    }

    /**
     * 启动定时任务
     */
    public void startJob(Integer jobId) {
        //创建后的定时任务默认是STOP状态,所以我们还要通过定时任务id调度任务启动接口
        HttpRequest requests = HttpRequest.post(adminAddresses + "/jobinfo/start")
                .header("Content-Type", "multipart/form-data")
                .header("Cookie", getCookie())
                .form("id", jobId);
        //通过HTTP请求启动定时任务
        HttpResponse responses = requests.execute();
        String results = responses.body();
        if (StrUtil.isNotBlank(results) && 200 == responses.getStatus()) {
            log.info("定时任务" + jobId + "启动成功。");
        } else {
            log.error("定时任务" + jobId + "启动失败。");
        }
    }

    /**
     * 停止定时任务
     */
    public void stopJob(Integer jobId) {

        //通过定时任务id调度任务停止接口
        HttpRequest requests = HttpRequest.post(adminAddresses + "/jobinfo/stop")
                .header("Content-Type", "multipart/form-data")
                .header("Cookie", getCookie())
                .form("id", jobId);
        //通过HTTP请求停止定时任务
        HttpResponse responses = requests.execute();
        String results = responses.body();
        if (StrUtil.isNotBlank(results) && 200 == responses.getStatus()) {
            log.info("定时任务{}停止成功。", jobId);
        } else {
            log.error("定时任务{}停止失败。", jobId);
        }
    }

    /**
     * 2.查询定时任务:
     * 因为是动态实时创建定时任务,所以建议单独创建一个执行器去执行这些定时任务,方便后续批量进行查询出来进行删除。
     * 这里入参执行器ID,通过执行器ID来查询该执行器下所有Stop状态的定时任务,因为Xxl-job定时任务默认*在执行完最后一次任务后就会自动进入STOP状态,这样查询出来所有STOP状态任务后方便我们后续进行删除清理定时任务。
     */
    public List<Long> SelectJob(Integer jobGroup) {
        HttpRequest request = HttpRequest.post(adminAddresses + "/jobinfo/pageList")
                .header("Content-Type", "multipart/form-data")
                .header("Cookie", getCookie())
                .form("jobGroup", jobGroup)
                .form("triggerStatus", 0)
                .form("start", 0);
        //执行 HTTP POST 请求启动定时任务
        HttpResponse response = request.execute();
        // 解析响应体
        ObjectMapper mapper = new ObjectMapper();
        List<Long> idList = new LinkedList<>();
        try {
            JsonNode responseNode = mapper.readTree(response.body());
            JsonNode dataNode = responseNode.get("data");
            //遍历删除id对应的定时任务
            if (dataNode.isArray()) {
                for (JsonNode node : dataNode) {
                    Long id = node.get("id").asLong();
                    idList.add(id);
                }
            }
        } catch (IOException e) {
            System.out.println("解析响应体时发生异常：" + e.getMessage());
        }
        return idList;
    }

    /**
     * 3.删除定时任务:
     * Xxl-job目前没有没有直接批量进行删除定时任务的,所以我们使用遍历去挨个删除,如果考虑到性能问题,单独创建一个定时来调用该删除方法即可,每天凌晨去执行该删除清理的定时任务。
     */
    public void removalJob(List<Long> idList) {
        for (Long id : idList) {
            HttpRequest requests = HttpRequest.post(adminAddresses + "/jobinfo/remove")
                    .header("Content-Type", "multipart/form-data")
                    .header("Cookie", getCookie())
                    .form("id", id);
            //执行HTTP请求删除定时任务
            HttpResponse response = requests.execute();
            if (StrUtil.isNotBlank(response.body()) && 200 == response.getStatus()) {
                log.info("定时任务{}删除成功。", id);
            } else {
                log.error("定时任务{}删除失败。", id);
            }
        }
    }

    /**
     * 获取Cookie
     * @return
     */
    public String getCookie() {
        for (int i = 0; i < 10; i++) {
            String cookieStr = redisUtil.getRedis(XXL_JOB_KEY);
            if (cookieStr != null) {
                return "XXL_JOB_LOGIN_IDENTITY=" + cookieStr;
            }
            login();
        }
        throw new RuntimeException("获取 xxl-job cookie 失败!");
    }

    /**
     * 优先到MAP缓存中获取,如果没有获取到则会请求xxljob的登录来获取Cookie,这里提供三次失败可重试。
     */
    private void login() {
        String url = adminAddresses+"/login";
        HttpResponse response = HttpRequest.post(url)
                .form("userName", adminUserName)
                .form("password", adminPwd)
                .execute();
        List<HttpCookie> cookies = response.getCookies();
        Optional<HttpCookie> cookieOpt = cookies.stream()
                .filter(cookie -> cookie.getName().equals("XXL_JOB_LOGIN_IDENTITY")).findFirst();
        if (!cookieOpt.isPresent())
            throw new RuntimeException("获取 xxl-job cookie 失败!");

        String value = cookieOpt.get().getValue();
        redisUtil.set(XXL_JOB_KEY,value,REDIS_EXPIRE_TIME);
    }
}


