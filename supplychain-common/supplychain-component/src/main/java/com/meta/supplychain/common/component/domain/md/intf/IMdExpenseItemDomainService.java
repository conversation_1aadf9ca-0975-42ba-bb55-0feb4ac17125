package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.req.expense.*;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemCategoryResponseDTO;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemResponseDTO;

import java.util.Map;

public interface IMdExpenseItemDomainService {

    /**
     * 分页查询费用项目
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    PageResult<MdExpenseItemResponseDTO> pageQuery(MdExpenseItemPageQueryReq request);

    /**
     * 根据编码查询费用项目详情
     *
     * @param code 费用项目编码
     * @return 费用项目详情
     */
    MdExpenseItemResponseDTO getByCode(String code);

    /**
     * 创建费用项目
     *
     * @param request 创建请求
     * @return 是否成功
     */
    <PERSON>olean create(MdExpenseItemCreateReq request);

    /**
     * 更新费用项目
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean update(MdExpenseItemUpdateReq request);

    /**
     * 删除费用项目
     *
     * @param code 费用项目ID
     * @return 是否成功
     */
    Boolean delete(String code);
    
    /**
     * 启用费用项目
     *
     * @param code 费用项目ID
     * @return 是否成功
     */
    Boolean enable(String code);
    
    /**
     * 停用费用项目
     *
     * @param code 费用项目ID
     * @return 是否成功
     */
    Boolean disable(String code);

    /**
     * 统计各个费用项目分类下的费用项目数量
     *
     * @return 费用项目分类编码与其下费用项目数量的映射
     */
    Map<Integer, Long> countByCategoryCode();

    /**
     * 分页查询费用项目分类
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    PageResult<MdExpenseItemCategoryResponseDTO> categoryPageQuery(MdExpenseItemCategoryPageQueryReq request);

    /**
     * 根据编码查询费用项目分类详情
     *
     * @param code 费用项目分类编码
     * @return 费用项目分类详情
     */
    MdExpenseItemCategoryResponseDTO getCategoryByCode(Integer code);

    /**
     * 创建费用项目分类
     *
     * @param request 创建请求
     * @return 是否成功
     */
    Boolean createCategory(MdExpenseItemCategoryCreateReq request);

    /**
     * 更新费用项目分类
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean updateCategory(MdExpenseItemCategoryUpdateReq request);

    /**
     * 删除费用项目分类
     *
     * @param code 费用项目分类编码
     * @return 是否成功
     */
    Boolean deleteCategory(Integer code);
}