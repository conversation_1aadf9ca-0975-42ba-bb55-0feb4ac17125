package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.md.component.bizrule.*;
import com.meta.supplychain.entity.dto.md.component.deptrule.*;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsDeliverPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPriceInfoReq;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import com.meta.supplychain.entity.dto.replenishment.resp.BatchGenerateBillNoResp;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;

import java.util.List;

public interface ISupplychainPmsBizRuleEngineService {
    /**
     * 服务端获取单据号
     * @param mdBillNoBillTypeEnum
     * @return
     */
    String getBillNo(MdBillNoBillTypeEnum mdBillNoBillTypeEnum,String deptCode);

    /**
     * 查询订货属性
     * 订货申请/需求单
     */
    List<OrderAttrDTO> listOrderAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 查询退货属性
     * 退货申请/需求单
     */
    List<OrderReturnAttrDTO> listOrderReturnAttr(OrderAttrQueryDTO orderAttrQueryDTO);

    /**
     * 订货申请提交自动生成需求单校验 及 转订单状态
     * 订货申请
     */
    DemandProcessStrategyAutoDTO getAutoDemandProcessStrategy(DemandProcessStrategyAutoQueryDTO demandProcessStrategyAutoQueryDTO);

    /**
     * 手工生成需求单转订单状态
     * 需求单
     */
    DemandProcessStrategyManualDTO getManualDemandProcessStrategy(DemandProcessStrategyManualQueryDTO demandProcessStrategyManualQueryDTO);

    /**
     * 供应商预约策略：查询过滤对应部门的停靠点列表，配送部门
     * 供应商预约单
     */
//    List<DockDTO> listDock(DockQueryDTO dockQueryDTO);

    /**
     * 供应商预约策略 + 供应商预约单：返回停靠点使用信息
     * 供应商预约单
     */
//    List<DockUseInfoDTO> listDockUseInfo(DockUseQueryDTO dockUseQueryDTO);


    /**
     * 采购的商品判断，需聚合 checkApplyGoods()
     * 1.商品必须是合同品
     * 2.判断是否有第一供应商
     * 3.判断流转途径
     *
     * 订货/退货申请
     */
    List<ApplyGoodsDTO> checkApplyGoods(GoodsQueryDTO goodsQueryDTO);

    /**
     * 配送的商品判断，需聚合 checkApplyGoods()
     * 1.流转途径
     * 2.订货的情况下选择订货属性为追加或追减的时候
     * 按照商品+出货方+到店日期（到店日期）匹配存在的需求批次如果不存在需求批次，则不允许配送
     *
     * 订货/退货申请
     */
//    List<ApplyGoodsDTO> checkApplyGoods4Delivery(GoodsQueryDTO goodsQueryDTO);

    /**
     * 查询及校验商品经营状态(进货/退货、出货/退货)
     *
     * 经营状态公共逻辑 订/退货申请、需求单、采购计划单、采购/退订单、配送订单、门店调拨
     *
     * 查询及校验商品流转途径(允许门店调拨、销售、配送送货/退货、供应商送货/退货、直流、直流供应商)
     *
     * 流转途径公共逻辑 订/退货申请、需求单、配转采、采购计划单、采购/退订单、配送订单、门店调拨
     */
    List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO);


    /**
     * 需求批次策略-时段:生成采购批次，返回采购批次信息
     * 需求批次策略-日期，本次不做
     * 订货申请、需求单
     */
    void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO);

    /**
     * 判断商品是否允许追加追减
     *
     * 订货申请
     */
    List<GoodsAddReduceDTO> checkGoodsAddReduce(GoodsAddReduceQueryDTO goodsAddReduceQuery);


    /**
     * 供应商档案-是否直流合单
     *
     * 需求单、追加追减
     */
    List<MstSupplierVO> listSupplier(SupplierQueryDTO supplierQuery);

    /**
     * 查询合同、部门、商品
     *
     * 订货/退货申请、需求单、配送转采购、采购计划单、采购/退订单、采购验收/退货
     */
    List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO contractGoodsDeptQuery);

    /**
     * 查询商品列表
     * @param goodsQueryDTO
     * @return
     */
    List<GoodsQueryResp> listGoodsInfo(GoodsQueryDTO goodsQueryDTO);


    List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoList(QueryGoodsPriceInfoReq queryGoodsPriceInfoReq);

    List<BatchGenerateBillNoResp> getBatchBillNo(List<BatchGenerateBillNoDTO> list);

    GoodsStrategyResp getPurchGoodsStrategy(GoodsStrageReq req);

    ManageAndCirculationDTO getManageAndCirculation();

    List<ApplyGoodsDTO> checkGoodsBasic(List<GoodsQueryResp> goodsQueryRespList);

    ApplyGoodsDTO checkGoodsBasic(GoodsQueryResp goodsQueryResp);

    /**
     * 根据商品品类解析品类全路径
     *  1.查询商品品类等级系统参数
     *  2.解析商品品类全路径
     * @param categoryCodeList 商品品类结合
     * @return 品类全路径
     */
    List<CategoryCodeAll> getCategoryCodeAll(List<String> categoryCodeList);
}
