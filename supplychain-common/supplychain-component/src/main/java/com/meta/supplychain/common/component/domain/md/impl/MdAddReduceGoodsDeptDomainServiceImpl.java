package com.meta.supplychain.common.component.domain.md.impl;

import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdAddReduceGoodsDeptRepositoryService;
import com.meta.supplychain.common.component.domain.md.intf.IMdAddReduceGoodsDeptDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:43
 **/
@Service
public class MdAddReduceGoodsDeptDomainServiceImpl implements IMdAddReduceGoodsDeptDomainService {

    @Autowired
    private IMdAddReduceGoodsDeptRepositoryService mdAddReduceGoodsDeptRepositoryService;
}
