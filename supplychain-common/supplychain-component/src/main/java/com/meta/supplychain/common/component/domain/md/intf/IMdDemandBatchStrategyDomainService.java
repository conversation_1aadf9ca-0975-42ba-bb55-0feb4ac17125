package com.meta.supplychain.common.component.domain.md.intf;


import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.*;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.*;
import com.meta.supplychain.entity.dto.md.resp.demandbatchstraegy.MdDemandBatchRecordDeptSkuResp;
import com.meta.supplychain.entity.dto.md.resp.demandbatchstraegy.MdDemandBatchRecordSkuResp;
import com.meta.supplychain.entity.po.md.MdDemandBatchCateGoodsPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchTimeSegmentPO;
import com.meta.supplychain.exceptions.ScBizException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 需求批次策略领域服务接口
 * 
 * 该接口提供需求批次策略相关的业务操作，包括：
 * 1. 策略的增删改查
 * 2. 时间段的管理
 * 3. 品类商品的管理
 * 
 * 需求批次策略用于设置商品需求计划的生成规则，确定在何时为哪些商品生成需求批次。
 * 
 * <AUTHOR>
 * @date 2025/03/30
 */
public interface IMdDemandBatchStrategyDomainService {

    Result<Integer> generateTimeSegmentInsideId();

    /**
     * 分页查询需求批次策略
     * 
     * 根据查询条件筛选需求批次策略，并分页返回结果。
     * 支持按部门编码、仓库编码、生成方式等条件过滤。
     *
     * @param request 查询请求参数，包含分页信息和过滤条件
     * @return 分页查询结果，包含总记录数和当前页数据
     */
    PageResult<MdDemandBatchStrategyDTO> pageQuery(QueryMdDemandBatchStrategyReq request);

    /**
     * 根据单据号查询需求批次策略详情
     * 
     * 查询指定单据号的需求批次策略详细信息，包括基本信息、
     * 关联的时间段列表以及每个时间段关联的品类商品列表。
     *
     * @param billNo 需求批次策略单据号
     * @return 需求批次策略详情，包含时间段和品类商品信息；如果未找到则返回null
     */
    MdDemandBatchStrategyDTO detail(String billNo);

    /**
     * 删除需求批次策略
     * 
     * 根据单据号删除需求批次策略，同时级联删除关联的时间段和品类商品信息。
     * 该操作在一个事务中完成，确保数据一致性。
     *
     * @param billNo 需求批次策略单据号
     * @return 是否删除成功：true-删除成功；false-策略不存在或删除失败
     */
    @Transactional
    Boolean delete(String billNo);

    /**
     * 创建需求批次策略
     * 
     * 创建新的需求批次策略，包括基本信息、时间段和品类商品信息。
     * 会先进行参数校验，确保必填字段不为空且格式正确。
     * 创建成功后，会返回新创建的策略ID。
     *
     * @param createRequest 创建请求参数，包含策略基本信息、时间段和品类商品信息
     * @return 创建成功的需求批次策略ID
     * @throws ScBizException 参数校验失败时抛出业务异常
     */
    @Transactional
    Long create(CreateMdDemandBatchStrategyReq createRequest);

    /**
     * 修改需求批次策略
     * 
     * 全量修改需求批次策略，包括基本信息、时间段和品类商品。
     * 会保留原有单据号，但更新其他所有信息。
     *
     * @param request 修改请求参数，与创建请求结构相同，但需要包含单据号
     * @return 是否修改成功
     * @throws ScBizException 当单据号为空或策略不存在时抛出业务异常
     */
    @Transactional
    Boolean modify(CreateMdDemandBatchStrategyReq request);

    /**
     * 根据策略单据号查询时间段列表
     * 
     * 查询指定需求批次策略下的所有时间段列表，不包含品类商品信息。
     *
     * @param strategyBillNo 需求批次策略单据号
     * @return 时间段列表；如果策略不存在或没有时间段，则返回空列表
     */
    List<MdDemandBatchTimeSegmentDTO> queryTimeSegmentByStrategyNo(String strategyBillNo);

    /**
     * 根据ID删除时间段
     * 
     * 根据时间段ID列表批量删除时间段，同时级联删除关联的品类商品信息。
     * 该操作在一个事务中完成，确保数据一致性。
     *
     * @param idList 时间段ID列表
     * @return 是否删除成功
     */
    @Transactional(propagation = Propagation.REQUIRED)
    Boolean deleteTimeSegmentById(List<Long> idList);

    /**
     * 创建时间段
     * 
     * 创建单个时间段，会进行参数校验确保必填字段不为空且格式正确。
     * 创建成功后，会返回新创建的时间段ID。
     *
     * @param po 时间段PO对象，包含时间段基本信息
     * @return 创建成功的时间段ID
     * @throws ScBizException 参数校验失败时抛出业务异常
     */
    Long createTimeSegment(MdDemandBatchTimeSegmentPO po);

    /**
     * 批量创建时间段
     * 
     * 批量创建多个时间段，会对每个时间段进行参数校验。
     * 该方法会在一个批处理中执行，提高性能。
     *
     * @param timeSegmentPOList 时间段PO对象列表
     * @return 是否创建成功
     * @throws ScBizException 参数校验失败时抛出业务异常
     */
    Boolean createBatchTimeSegment(List<MdDemandBatchTimeSegmentPO> timeSegmentPOList);

    /**
     * 根据时间段ID查询品类商品列表
     * 
     * 查询指定时间段下关联的所有品类商品列表。
     *
     * @param timeSegmentId 时间段ID
     * @return 品类商品列表；如果时间段不存在或没有品类商品，则返回空列表
     */
    List<MdDemandBatchCateGoodsDTO> queryCateGoodsByTimeSegmentId(Long timeSegmentId);

    /**
     * 根据策略单据号查询品类商品列表
     * 
     * 查询指定需求批次策略下的所有品类商品列表，不区分时间段。
     *
     * @param strategyBillNo 策略单据号
     * @return 品类商品列表；如果策略不存在或没有品类商品，则返回空列表
     */
    List<MdDemandBatchCateGoodsDTO> queryCateGoodsByStrategyNo(String strategyBillNo);

    /**
     * 根据ID删除品类商品
     * 
     * 根据品类商品ID列表批量删除品类商品。
     *
     * @param id 品类商品ID列表
     * @return 是否删除成功
     */
    Boolean deleteCateGoodsById(List<Long> id);

    /**
     * 根据时间段ID删除品类商品
     * 
     * 删除指定时间段下关联的所有品类商品。
     *
     * @param timeSegmentIdList 时间段ID列表
     * @return 是否删除成功
     */
    Boolean deleteCateGoodsByTimeSegmentId(List<Long> timeSegmentIdList);

    /**
     * 创建品类商品
     * 
     * 创建单个品类商品，会进行参数校验确保必填字段不为空且格式正确。
     * 创建成功后，会返回新创建的品类商品ID。
     *
     * @param po 品类商品PO对象，包含品类商品基本信息
     * @return 创建成功的品类商品ID
     * @throws ScBizException 参数校验失败时抛出业务异常
     */
    Long createCateGoods(MdDemandBatchCateGoodsPO po);

    /**
     * 批量创建品类商品
     * 
     * 批量创建多个品类商品，会对每个品类商品进行参数校验。
     * 该方法会在一个批处理中执行，提高性能。
     *
     * @param cateGoodsPOList 品类商品PO对象列表
     * @return 是否创建成功
     * @throws ScBizException 参数校验失败时抛出业务异常
     */
    Boolean createBatchCateGoods(List<MdDemandBatchTimeSegmentPO> timeSegmentPOList, List<MdDemandBatchCateGoodsPO> cateGoodsPOList);

    /**
     * 查询需求记录列表
     * @param request
     * @return
     */
    PageResult<MdDemandBatchRecordDTO> pageMdDemandBatchRecord(MdDemandBatchRecordReq request);

    void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO);

    List<MdDemandBatchRecordDeptSkuResp> listMdDemandBatchRecordDeptSku(MdDemandBatchRecordByDeptSkuReq params);

    List<MdDemandBatchRecordSkuResp> listMdDemandBatchRecordSku(MdDemandBatchRecordBySkuReq params);
}