package com.meta.supplychain.common.component.service.intf;

import com.meta.supplychain.entity.dto.md.component.deptrule.*;
import com.meta.supplychain.entity.dto.pms.apply.CheckFranchiseDTO;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 业务部门规则控制器
 */
public interface ISupplychainBizDeptRuleService {

    /**
     * 供应商预约策略：查询过滤对应部门的停靠点列表，配送部门
     * 供应商预约单
     */
//    List<DockDTO> listDock(DockQueryDTO dockQueryDTO);

    /**
     * 供应商预约策略 + 供应商预约单：返回停靠点使用信息
     * 供应商预约单
     */
//    List<DockUseInfoDTO> listDockUseInfo(DockUseQueryDTO dockUseQueryDTO);


    /**
     *
     * 校验部门匹配的订单订货策略时间是否允许要货
     *  resp： 可以要货的部门
     */
    List<String> checkOrderStrategyTime(List<String> deptCodes, LocalTime localTime);

    /**
     * 校验加盟额度
     * @param checkFranchiseDTOS 部门金额列表
     * @return 通过校验的部门
     */
    List<String> checkFranchise(List<CheckFranchiseDTO> checkFranchiseDTOS);

}
