package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.meta.supplychain.common.component.domain.md.intf.IMdContractMasDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdDemandBatchStrategyDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdDistributionPriceDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdGoodsStrategyDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdOrderStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizGoodsRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizSysParamRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonSupplierService;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.req.QueryUpDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryUpDeptListResp;
import com.meta.supplychain.entity.dto.bipass.Sku30DaySalenumDTO;
import com.meta.supplychain.entity.dto.bipass.resp.QueryTbaseResult;
import com.meta.supplychain.entity.dto.goods.req.GoodsCategoryQueryReq;
import com.meta.supplychain.entity.dto.goods.req.GoodsQueryReq;
import com.meta.supplychain.entity.dto.goods.req.GoodsSearchQueryReq;
import com.meta.supplychain.entity.dto.goods.req.ManageAndCirculationReq;
import com.meta.supplychain.entity.dto.goods.resp.GoodsCategoryQueryResp;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSearchInfo;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSimpleInfo;
import com.meta.supplychain.entity.dto.goods.resp.ManageAndCirculationResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ApplyGoodsDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.CategoryCodeAll;
import com.meta.supplychain.entity.dto.md.component.goodsrule.CirculationModeDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoods4DeptResultDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptCheckDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptCheckResultDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractSkuInfo;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsDeliveryPriceDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsDeliveryPriceQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsPurchPriceDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsPurchPriceQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsAddReduceDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsAddReduceQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsBaseInfo;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsManageAndCirculationDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsPeriod;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsStockDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsStrageReq;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GoodsStrategyResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.MainSupplierInfoQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ManageAndCirculationDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.OrderStrategyResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.RequireGoodsInfo;
import com.meta.supplychain.entity.dto.md.component.goodsrule.RequireGoodsReq;
import com.meta.supplychain.entity.dto.md.component.goodsrule.RequireGoodsResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.StockQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.SupplierQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.WorkStatusDTO;
import com.meta.supplychain.entity.dto.md.contract.MdContractDetailsDTO;
import com.meta.supplychain.entity.dto.md.contract.MdContractScopeDTO;
import com.meta.supplychain.entity.dto.md.distprice.MdDistPriceDTO;
import com.meta.supplychain.entity.dto.md.goodsstrategy.ListGoodsStrategyDTO;
import com.meta.supplychain.entity.dto.md.orderstrategy.MdOrderStrategyQueryDTO;
import com.meta.supplychain.entity.dto.md.req.DemandGoodsStrategyAggreReq;
import com.meta.supplychain.entity.dto.md.req.contract.MdContractGetDetailsReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceComplexReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryValidDistPriceReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.ValidGoodsInfo;
import com.meta.supplychain.entity.dto.md.resp.DemandGoodsStrategyAggreResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsDeliverPriceInfoBatchResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsDeliverPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsPurchPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsSuppResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPriceInfoBatchReq;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPriceInfoReq;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPurchPriceInfoReq;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsSuppReq;
import com.meta.supplychain.entity.dto.md.resp.order.MdOrderStrategyDTO;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import com.meta.supplychain.entity.dto.promotion.req.QueryAcceptanceRefundReq;
import com.meta.supplychain.entity.dto.promotion.req.QueryDeliveryPriceReq;
import com.meta.supplychain.entity.dto.promotion.resp.PromotionPriceInfo;
import com.meta.supplychain.entity.dto.promotion.resp.QueryAcceptanceRefundResp;
import com.meta.supplychain.entity.dto.stock.resp.StkPeriod;
import com.meta.supplychain.entity.dto.stock.resp.StockVO;
import com.meta.supplychain.entity.po.md.MdGoodsStrategyDeptPO;
import com.meta.supplychain.enums.BiPassDsCodeEnum;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.PriceTypeEnum;
import com.meta.supplychain.enums.SpecialPriceModeEnum;
import com.meta.supplychain.enums.md.ControlPassEnum;
import com.meta.supplychain.enums.md.MdDeptTypeEnum;
import com.meta.supplychain.enums.md.MdDistPriceTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.enums.md.MdSystemParamEnum;
import com.meta.supplychain.enums.pms.ApplyCateEnum;
import com.meta.supplychain.enums.pms.DirectSignEnum;
import com.meta.supplychain.enums.pms.PMSSystemParamEnum;
import com.meta.supplychain.enums.pms.ShippingWayEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.BiPassClient;
import com.meta.supplychain.infrastructure.feign.PromotionFeignClient;
import com.meta.supplychain.util.Assert;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.util.PromotionHandleUtil;
import com.meta.supplychain.util.local.DeptTypeEnum;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.jetbrains.annotations.NotNull;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务商品及部门商品规则控制器
 *
 * <AUTHOR>
 * @date 2025/04/18 17:17
 **/
@Service
public class SupplychainBizGoodsRuleServiceImpl implements ISupplychainBizGoodsRuleService {

    @NacosValue(autoRefreshed = true, value = "${limiting.goods.replenishmentList:100}")
    private Integer replenishmentList;

    /**
     * 是否开启验收退补逻辑
     */
    @Value("${switch.useAcceptRefund: false}")
    private boolean useAcceptRefund;

    @Autowired
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Autowired
    private ICommonSupplierService mdSupplierDomainService;

    @Autowired
    private IMdDistributionPriceDomainService iMdDistributionPriceDomainService;

    @Autowired
    private PromotionHandleUtil promotionHandleUtil;

    @Autowired
    private PromotionFeignClient promotionFeignClient;

    @Resource
    private ICommonGoodsService commonGoodsService;

    @Autowired
    private ISupplychainBizSysParamRuleService iSupplychainBizSysParamRuleService;


    private ISupplychainBizSysParamRuleService getISupplychainBizSysParamRuleService() {
        ISupplychainBizSysParamRuleService supplychainBizSysParamRuleService = SpringContextUtil.getApplicationContext().getBean(ISupplychainBizSysParamRuleService.class);
        return supplychainBizSysParamRuleService;
    }

    private ICommonStockService getICommonStockService() {
        ICommonStockService commonStockService = SpringContextUtil.getApplicationContext().getBean(ICommonStockService.class);
        return commonStockService;
    }

    private ICommonGoodsService getICommonGoodsService() {
        ICommonGoodsService commonGoodsService = SpringContextUtil.getApplicationContext().getBean(ICommonGoodsService.class);
        return commonGoodsService;
    }

    private IMdContractMasDomainService getIMdContractMasDomainService() {
        IMdContractMasDomainService mdContractMasDomainService = SpringContextUtil.getApplicationContext().getBean(IMdContractMasDomainService.class);
        return mdContractMasDomainService;
    }

    private ISupplychainControlEngineService getISupplychainControlEngineService() {
        ISupplychainControlEngineService iSupplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
        return iSupplychainControlEngineService;
    }


    private BiPassClient getBiPassClient() {
        BiPassClient biPassClient = SpringContextUtil.getApplicationContext().getBean(BiPassClient.class);
        return biPassClient;
    }


    /**
     * 配送与采购场景商品公共判断
     * 1.商品在目录：商品字段inCatalog为1
     * 2.进项税率不能为空（可以为0）：商品字段isInputRate
     * 3.商品档案进价为空不允许录入：商品字段purchPrice
     * 4.不允许录入/导入经营方式=联营、租赁、AB生鲜、生鲜归集码
     * 商品字段：operationModel =》 [2, 8, 10, 12]存在这个数组集合不合规
     * 5.类别码商品不允许录入：商品字段categoryCodeFlag不为1
     */
    @Override
    public List<ApplyGoodsDTO> checkGoodsBasic(List<GoodsQueryResp> goodsQueryRespList) {
        List<ApplyGoodsDTO> applyGoodsList = new ArrayList<>();

        for (GoodsQueryResp goodsQueryResp : goodsQueryRespList) {
            ApplyGoodsDTO applyGoodsDTO = checkGoodsBasic(goodsQueryResp);
            applyGoodsList.add(applyGoodsDTO);
        }
        return applyGoodsList;
    }

    @Override
    public ApplyGoodsDTO checkGoodsBasic(GoodsQueryResp goodsQueryResp) {
        GoodsQueryResp.GoodsInfo goodsInfo = goodsQueryResp.getGoodsInfo();
        ApplyGoodsDTO applyGoodsDTO = new ApplyGoodsDTO();
        if (null != goodsQueryResp.getGoodsInfo()) {
            GoodsBaseInfo goodsBaseInfoBasic = CglibCopier.copy(goodsQueryResp.getGoodsInfo(), GoodsBaseInfo.class);
            applyGoodsDTO.setGoodsBaseInfo(goodsBaseInfoBasic);
        }

        if (null != goodsQueryResp.getRelationGoodsInfo()) {
            GoodsBaseInfo relationGoodsBaseInfo = CglibCopier.copy(goodsQueryResp.getRelationGoodsInfo(), GoodsBaseInfo.class);
            applyGoodsDTO.setRelationGoodsBaseInfo(relationGoodsBaseInfo);
        }


        GoodsBaseInfo childGoodsBaseInfo = CglibCopier.copy(goodsQueryResp.getChildGoodsInfo(), GoodsBaseInfo.class);

        applyGoodsDTO.setChildGoodsBaseInfo(childGoodsBaseInfo);

        applyGoodsDTO.setSkuCode(applyGoodsDTO.getSkuCode());
        applyGoodsDTO.setSkuName(applyGoodsDTO.getSkuName());
        //商品在目录
        if (ObjectUtils.notEqual(1, goodsInfo.getInCatalog())) {
            applyGoodsDTO.setCheckSign(false);
            applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B001.getErrorMsg());
            return applyGoodsDTO;
        }

        //进项税率不能为空（可以为0）
        if (null == goodsInfo.getInputTaxRate()) {
            applyGoodsDTO.setCheckSign(false);
            applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B002.getErrorMsg());
            return applyGoodsDTO;
        }

        //商品档案进价为空不允许录入
        if (null == goodsInfo.getPurchasePrice()) {
            applyGoodsDTO.setCheckSign(false);
            applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B003.getErrorMsg());
            return applyGoodsDTO;
        }

        //不允许录入/导入经营方式=联营、租赁、AB生鲜、生鲜归集码
        //1:自营 2:联营 7:联营管库存 8:租赁 9:生鲜A进A出 10:生鲜A进B出 12:生鲜归集码
        if (ObjectUtils.equals(2, goodsInfo.getOperationModel()) || ObjectUtils.equals(8, goodsInfo.getOperationModel())
                || ObjectUtils.equals(10, goodsInfo.getOperationModel())
                || ObjectUtils.equals(12, goodsInfo.getOperationModel())) {
            applyGoodsDTO.setCheckSign(false);
            applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B004.getErrorMsg());
            return applyGoodsDTO;
        }

        //类别码商品不允许录入
        if (ObjectUtils.equals(1, goodsInfo.getCategoryCodeFlag())) {
            applyGoodsDTO.setCheckSign(false);
            applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B005.getErrorMsg());
            return applyGoodsDTO;
        }
        applyGoodsDTO.setCheckSign(true);
        return applyGoodsDTO;
    }

    /**
     * 采购的商品判断，需聚合 checkApplyGoods()
     * 1.商品必须是合同品
     * 2.判断是否有第一供应商
     * 3.判断流转途径
     * 4.商品订货策略如果配置中央控制不允许录入
     * 5.经营状态：订货退货分场景
     * 订货/退货申请
     */
    @Override
    public List<ApplyGoodsDTO> checkApplyGoods(GoodsQueryDTO goodsQueryDTO) {
        List<GoodsQueryResp> goodsInfoList = listGoodsInfo(goodsQueryDTO);
        List<GoodsManageAndCirculationDTO> goodsManageAndCirculationList = getGoodsManageAndCirculation(goodsInfoList);
        Map<String, GoodsManageAndCirculationDTO> goodsManageAndCirculationMap = new HashMap<>();
        for (GoodsManageAndCirculationDTO goodsManageAndCirculationDTO : goodsManageAndCirculationList) {
            goodsManageAndCirculationMap.put(goodsManageAndCirculationDTO.getSkuCode(), goodsManageAndCirculationDTO);
        }
        List<ApplyGoodsDTO> applyGoodsDTOList = checkGoodsBasic(goodsInfoList);
        for (ApplyGoodsDTO applyGoodsDTO : applyGoodsDTOList) {
            if (applyGoodsDTO.getCheckSign()) {
                if (!goodsManageAndCirculationMap.containsKey(applyGoodsDTO.getSkuCode())) {
                    applyGoodsDTO.setCheckSign(false);
                    applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B006.getErrorMsg());
                    continue;
                }
                GoodsManageAndCirculationDTO goodsManageAndCirculationDTO = goodsManageAndCirculationMap.get(applyGoodsDTO.getSkuCode());
                if (null == goodsManageAndCirculationDTO.getCirculationModeDTO() || null == goodsManageAndCirculationDTO.getCirculationModeDTO()) {
                    applyGoodsDTO.setCheckSign(false);
                    applyGoodsDTO.setErrMsg(applyGoodsDTO.getSkuCode() + MdErrorCodeEnum.SCMD999B006.getErrorMsg());
                    continue;
                }
                WorkStatusDTO workStatusDTO = goodsManageAndCirculationDTO.getWorkStatusDTO();
                CirculationModeDTO circulationModeDTO = goodsManageAndCirculationDTO.getCirculationModeDTO();
                applyGoodsDTO.setWorkStatus(workStatusDTO);
                applyGoodsDTO.setCirculationMode(circulationModeDTO);
            }
        }

        return applyGoodsDTOList;
    }


//    @Override
//    public List<ApplyGoodsDTO> checkApplyGoods4Delivery(GoodsQueryDTO goodsQueryDTO) {
//        return null;
//    }

    @Override
    public List<GoodsQueryResp> listGoodsInfo(GoodsQueryDTO goodsQueryDTO) {
        List<String> goodsCodes = goodsQueryDTO.getSkuCodeList();
        if (CollectionUtils.isEmpty(goodsCodes)) {
            return Lists.newArrayList();
        }
        List<GoodsQueryResp> goodsInfos = new ArrayList<>();
        List<List<String>> partitionGoodsCodes = Lists.partition(goodsCodes, replenishmentList);
        for (List<String> partitionGoodsCode : partitionGoodsCodes) {
            GoodsQueryReq req = GoodsQueryReq.builder()
                    .storeCode(goodsQueryDTO.getDeptCode())
                    .goodsCodeList(partitionGoodsCode)
                    .attributeNameFlag(goodsQueryDTO.getAttributeNameFlag() != null && goodsQueryDTO.getAttributeNameFlag())
                    .build();
            goodsInfos.addAll(getICommonGoodsService().getGoodsInfo(req));
        }
        return goodsInfos;
    }

    @Override
    public Map<String, GoodsSimpleInfo> getSkuSimpleInfoMap(List<String> skuCodeList) {
        Map<String, GoodsSimpleInfo> skuMap = new HashMap<>();
        if (CollectionUtils.isEmpty(skuCodeList)) {
            return skuMap;
        }
        List<List<String>> splitList = Lists.partition(skuCodeList, 100);
        for (List<String> skuList : splitList) {
            String goodsString = String.join(",", skuList);
            List<GoodsSimpleInfo> goodsSimpleInfos = getICommonGoodsService().skuSimpleList(goodsString, "true");
            skuMap.putAll(goodsSimpleInfos.stream().collect(Collectors.toMap(GoodsSimpleInfo::getSkuCode, Function.identity())));
        }
        return skuMap;
    }


    /**
     * 查询经营状态
     *
     * @param goodsQueryDTO
     * @return
     */
    @Override
    public List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(GoodsQueryDTO goodsQueryDTO) {
        List<GoodsQueryResp> goodsInfoList = listGoodsInfo(goodsQueryDTO);
        return getGoodsManageAndCirculation(goodsInfoList);
    }

    @Override
    public ManageAndCirculationDTO getManageAndCirculation() {
        ManageAndCirculationDTO manageAndCirculationDTO = new ManageAndCirculationDTO();
        ManageAndCirculationReq circulationReq = ManageAndCirculationReq.builder()
                .build();
        ManageAndCirculationResp manageAndCircul = getICommonGoodsService().getManageAndCircul(circulationReq);
        Map<String, ManageAndCirculationResp.WorkState> manageStateMap = new HashMap();
        Map<String, ManageAndCirculationResp.CirculationMode> circulationMap = new HashMap();

        for (ManageAndCirculationResp.WorkState workState : manageAndCircul.getManageStateList()) {
            manageStateMap.put(workState.getManagementStateNo(), workState);
        }

        for (ManageAndCirculationResp.CirculationMode circulationMode : manageAndCircul.getCirculationList()) {
            circulationMap.put(circulationMode.getCirculationWayNo(), circulationMode);
        }

        manageAndCirculationDTO.setCirculationMap(circulationMap);
        manageAndCirculationDTO.setManageStateMap(manageStateMap);
        return manageAndCirculationDTO;
    }

    @Override
    public ManageAndCirculationResp getWorkAndCirculation() {
        ManageAndCirculationReq circulationReq = ManageAndCirculationReq.builder()
                .build();
        ManageAndCirculationResp manageAndCircul = getICommonGoodsService().getManageAndCircul(circulationReq);
        return manageAndCircul;
    }

    private List<GoodsManageAndCirculationDTO> getGoodsManageAndCirculation(List<GoodsQueryResp> goodsInfoList) {
        ManageAndCirculationReq circulationReq = ManageAndCirculationReq.builder()
                .build();
        ManageAndCirculationResp manageAndCircul = getICommonGoodsService().getManageAndCircul(circulationReq);
        Map<String, ManageAndCirculationResp.WorkState> manageStateMap = new HashMap();
        Map<String, ManageAndCirculationResp.CirculationMode> circulationMap = new HashMap();

        for (ManageAndCirculationResp.WorkState workState : manageAndCircul.getManageStateList()) {
            manageStateMap.put(workState.getManagementStateNo(), workState);
        }

        for (ManageAndCirculationResp.CirculationMode circulationMode : manageAndCircul.getCirculationList()) {
            circulationMap.put(circulationMode.getCirculationWayNo(), circulationMode);
        }

        List<GoodsManageAndCirculationDTO> goodsManageAndCirculationDTOList = new ArrayList<>();
        for (GoodsQueryResp goods : goodsInfoList) {
            GoodsManageAndCirculationDTO goodsManageAndCirculationDTO = new GoodsManageAndCirculationDTO();
            goodsManageAndCirculationDTO.setSkuCode(goods.getGoodsInfo().getSkuCode());
            if (manageStateMap.containsKey(goods.getGoodsInfo().getWorkStateCode())) {
                ManageAndCirculationResp.WorkState workState = manageStateMap.get(goods.getGoodsInfo().getWorkStateCode());
                WorkStatusDTO copy = CglibCopier.copy(workState, WorkStatusDTO.class);
                goodsManageAndCirculationDTO.setWorkStatusDTO(copy);
            }

            if (circulationMap.containsKey(goods.getGoodsInfo().getCirculationModeCode())) {
                ManageAndCirculationResp.CirculationMode circulationMode = circulationMap.get(goods.getGoodsInfo().getCirculationModeCode());
                CirculationModeDTO copy = CglibCopier.copy(circulationMode, CirculationModeDTO.class);
                goodsManageAndCirculationDTO.setCirculationModeDTO(copy);
            }

            goodsManageAndCirculationDTOList.add(goodsManageAndCirculationDTO);
        }

        return goodsManageAndCirculationDTOList;
    }

    @Override
    public void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO) {
        IMdDemandBatchStrategyDomainService mdDemandBatchStrategyDomainService = SpringContextUtil.getApplicationContext().getBean(IMdDemandBatchStrategyDomainService.class);
        mdDemandBatchStrategyDomainService.generateProcurementBatch(generatePurchBatchDTO);
        Logs.info("SupplychainBizGoodsRuleServiceImpl.generateProcurementBatch.generatePurchBatchDTO:" + JSON.toJSONString(generatePurchBatchDTO));
    }

    @Override
    public List<GoodsAddReduceDTO> checkGoodsAddReduce(GoodsAddReduceQueryDTO goodsAddReduceQuery) {
        return null;
    }

    @Override
    public List<MstSupplierVO> listSupplier(SupplierQueryDTO supplierQuery) {
        return mdSupplierDomainService.querySupplierList(supplierQuery.getSupplierCodeList());
    }

    @Override
    public List<DeptGoodsPurchPriceDTO> listDeptGoodsPurchPrice(DeptGoodsPurchPriceQueryDTO deptGoodsPriceQuery) {
        //        当申请类型=采购，则取促销特供价、合同特供价、合同进价三者取低；
        Map<String, List<String>> skuContractMap = new HashMap<>();
        for (ContractSkuInfo contractSkuInfo : deptGoodsPriceQuery.getContractSkuInfos()) {
            List<String> contractsOrDefault = skuContractMap.getOrDefault(contractSkuInfo.getSkuCode(), new ArrayList<>());
            if(StringUtils.isNotEmpty(contractSkuInfo.getContractNo())){
                contractsOrDefault.add(contractSkuInfo.getContractNo());
            }

            skuContractMap.put(contractSkuInfo.getSkuCode(), contractsOrDefault);
        }
        List<String> skuCodes = Lists.newArrayList(skuContractMap.keySet());
        List<DeptGoodsPurchPriceDTO> deptGoodsPurchPriceDTOS = new ArrayList<>();
        GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();
        goodsQueryDTO.setDeptCode(deptGoodsPriceQuery.getDeptCode());
        goodsQueryDTO.setSkuCodeList(skuCodes);
        goodsQueryDTO.setAttributeNameFlag(false);
        List<GoodsQueryResp> goodsQueryResps = listGoodsInfo(goodsQueryDTO);
        Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap = goodsQueryResps.stream().map(GoodsQueryResp::getGoodsInfo).collect(Collectors.toMap(GoodsQueryResp.GoodsInfo::getSkuCode, Function.identity()));

        //查询合同品
        List<ContractGoodsDeptDTO> contractGoodsDeptDTOS = getIMdContractMasDomainService().listContractGoodsDept(ContractGoodsDeptQueryDTO.builder()
                .deptCode(deptGoodsPriceQuery.getDeptCode())
                .skuCodeList(skuCodes)
                .applyCate(ApplyCateEnum.PURCH.getCode())
                .build());
        contractGoodsDeptDTOS = contractGoodsDeptDTOS.stream().filter(contractGoodsDeptDTO ->
                skuContractMap.get(contractGoodsDeptDTO.getSkuCode()) == null || CollectionUtils.isEmpty(skuContractMap.get(contractGoodsDeptDTO.getSkuCode()))
                        || skuContractMap.get(contractGoodsDeptDTO.getSkuCode()).contains(contractGoodsDeptDTO.getContractNo())
        ).collect(Collectors.toList());
        LocalDateTime curDate = LocalDateTime.now();

        QueryAcceptanceRefundReq acceptanceRefundReq = QueryAcceptanceRefundReq.builder()
                .storeCode(deptGoodsPriceQuery.getDeptCode())
                .skuList(skuCodes.stream().map(skuCode ->
                        QueryAcceptanceRefundReq.GoodsPriceInfo.builder().skuCode(skuCode).build()).collect(Collectors.toList()))
                .build();
        // 查询采购验收退补价格
        Map<String, PromotionPriceInfo> promotionMap = promotionHandleUtil.queryAcceptanceRefund(acceptanceRefundReq);

        // 根据合同特供价类型设置合同特供价
        for (ContractGoodsDeptDTO mdContractGoodsDeptPO : contractGoodsDeptDTOS) {
            if (mdContractGoodsDeptPO.getSpecialStartTime() != null && mdContractGoodsDeptPO.getSpecialEndTime() != null) {
                boolean validate = curDate.isAfter(mdContractGoodsDeptPO.getSpecialStartTime()) && curDate.isBefore(mdContractGoodsDeptPO.getSpecialEndTime());
                if (validate) {
                    switch (Objects.requireNonNull(SpecialPriceModeEnum.getEnumByCode(mdContractGoodsDeptPO.getSpecialPriceMode()))) {
                        case NONE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getPurchTaxPrice());
                            break;
                        case FIXED_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getSpecialTaxPrice());
                            break;
                        case ADD_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getPurchTaxPrice()
                                    .multiply(BigDecimal.ONE.add(mdContractGoodsDeptPO.getSpecialRate().divide(new BigDecimal(100)))));
                            break;
                        case REDUCE_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(MoneyUtil.milliToYuanB(goodsInfoMap.get(mdContractGoodsDeptPO.getSkuCode()).getReferPrice())
                                    .multiply(BigDecimal.ONE.subtract(mdContractGoodsDeptPO.getSpecialRate().divide(new BigDecimal(100)))));
                            break;
                    }
                } else {
                    mdContractGoodsDeptPO.setSpecialTaxPrice(BigDecimal.ZERO);
                }
            } else {
                mdContractGoodsDeptPO.setSpecialTaxPrice(BigDecimal.ZERO);
            }
        }
        // 补货场景
        if (ApplyCateEnum.REPLENISHMENT.getCode().equals(deptGoodsPriceQuery.getApplyCate())) {
            for (ContractGoodsDeptDTO contractGoodsDeptDTO : contractGoodsDeptDTOS) {
                String skuCode = contractGoodsDeptDTO.getSkuCode();
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(skuCode);
                BigDecimal minPrice = contractGoodsDeptDTO.getSpecialTaxPrice();
                DeptGoodsPurchPriceDTO deptGoodsPurchPriceDTO = new DeptGoodsPurchPriceDTO();
                deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_SPECIAL_PRICE.getCode());
                deptGoodsPurchPriceDTO.setSkuCode(skuCode);
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                //  合同特供价,合同进价时，两者取低(合同进价优先级高)
                if (0 == contractGoodsDeptDTO.getSpecialTaxPrice().compareTo(BigDecimal.ZERO)
                        || contractGoodsDeptDTO.getSpecialTaxPrice().compareTo(contractGoodsDeptDTO.getPurchTaxPrice()) >= 0) {
                    minPrice = contractGoodsDeptDTO.getPurchTaxPrice();
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_PURCH_PRICE.getCode());
                }
                deptGoodsPurchPriceDTO.setFinaPrice(minPrice);
                if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) == 0) {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(null);
                } else {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                }
                deptGoodsPurchPriceDTO.setPurchasePrice(MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice()));
                deptGoodsPurchPriceDTO.setContractNo(contractGoodsDeptDTO.getContractNo());
                deptGoodsPurchPriceDTO.setContractMaxPrice(contractGoodsDeptDTO.getMaxPurchTaxPrice());
                if (promotionMap.containsKey(skuCode) && minPrice.compareTo(promotionMap.get(skuCode).getPromotionPrice()) > 0) {
                    PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);
                    deptGoodsPurchPriceDTO.setFinaPrice(promotionPriceInfo.getPromotionPrice());
                    deptGoodsPurchPriceDTO.setPurchasePrice(MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice()));
                    deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
                    deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.PROMOTION_PURCH_PRICE.getCode());
                    deptGoodsPurchPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                }
                deptGoodsPurchPriceDTO.setSupplierCode(contractGoodsDeptDTO.getSupplierCode());
                deptGoodsPurchPriceDTO.setSupplierName(contractGoodsDeptDTO.getSupplierName());
                deptGoodsPurchPriceDTO.setMainSupplierMode(contractGoodsDeptDTO.getMainSupplierMode());
                if (ifPriceZero(goodsInfo)) {
                    deptGoodsPurchPriceDTO.setFinaPrice(BigDecimal.ZERO);
                }
                deptGoodsPurchPriceDTOS.add(deptGoodsPurchPriceDTO);
            }
            //采购订单或配转采
        } else if (ApplyCateEnum.PURCH.getCode().equals(deptGoodsPriceQuery.getApplyCate()) || ApplyCateEnum.DELIVERY_TO_PURCH.getCode().equals(deptGoodsPriceQuery.getApplyCate())) {
            //0，按优先级取价,按优先级取价为取促销进价、合同特供价、合同价的顺序取价；；1，按最低价取价;按最低价取价则取3者最低价
            String purchOrderPriceRetrieval = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.PURCHASE_ORDER_PRICE_RETRIEVAL);
            for (ContractGoodsDeptDTO contractGoodsDeptDTO : contractGoodsDeptDTOS) {
                String skuCode = contractGoodsDeptDTO.getSkuCode();
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(skuCode);
                if(null == goodsInfo){
                    Logs.info("SupplychainBizGoodsRuleServiceImpl.listDeptGoodsPurchPrice.purch.notexists" + skuCode);
                    continue;
                }
                BigDecimal minPrice = contractGoodsDeptDTO.getSpecialTaxPrice();
                DeptGoodsPurchPriceDTO deptGoodsPurchPriceDTO = new DeptGoodsPurchPriceDTO();
                deptGoodsPurchPriceDTO.setSkuCode(skuCode);
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                //  合同特供价,合同进价时，两者取低(合同进价优先级高)
                if (0 == contractGoodsDeptDTO.getSpecialTaxPrice().compareTo(BigDecimal.ZERO)
                        || contractGoodsDeptDTO.getSpecialTaxPrice().compareTo(contractGoodsDeptDTO.getPurchTaxPrice()) >= 0) {
                    minPrice = contractGoodsDeptDTO.getPurchTaxPrice();
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_PURCH_PRICE.getCode());
                } else {
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_SPECIAL_PRICE.getCode());

                }
                deptGoodsPurchPriceDTO.setFinaPrice(minPrice);
//                deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) == 0) {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(null);
                } else {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                }

                if(promotionMap.containsKey(skuCode) ){
                    PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);
                    deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
                    deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
                    deptGoodsPurchPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                }
                if ("1".equals(purchOrderPriceRetrieval)) {
                    if (promotionMap.containsKey(skuCode) && minPrice.compareTo(promotionMap.get(skuCode).getPromotionPrice()) > 0
                            && BigDecimal.ZERO.compareTo(promotionMap.get(skuCode).getPromotionPrice()) != 0) {
                        PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);
                        deptGoodsPurchPriceDTO.setFinaPrice(promotionPriceInfo.getPromotionPrice());
                        deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.PROMOTION_PURCH_PRICE.getCode());
//                        deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
//                        deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
//                        deptGoodsPurchPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                    }
                } else {
                    if (promotionMap.containsKey(skuCode) && BigDecimal.ZERO.compareTo(promotionMap.get(skuCode).getPromotionPrice()) != 0) {
                        PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);
                        deptGoodsPurchPriceDTO.setFinaPrice(promotionPriceInfo.getPromotionPrice());
                        deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.PROMOTION_PURCH_PRICE.getCode());
//                        deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
//                        deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
//                        deptGoodsPurchPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                    } else if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) != 0) {
                        deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                        deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_SPECIAL_PRICE.getCode());
                    } else {
                        deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                        deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_PURCH_PRICE.getCode());
                    }
                }

                deptGoodsPurchPriceDTO.setSkuCode(skuCode);
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) == 0) {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(null);
                } else {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                }

                deptGoodsPurchPriceDTO.setPurchasePrice(MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice()));
                deptGoodsPurchPriceDTO.setContractNo(contractGoodsDeptDTO.getContractNo());
                deptGoodsPurchPriceDTO.setContractMaxPrice(contractGoodsDeptDTO.getMaxPurchTaxPrice());
                deptGoodsPurchPriceDTO.setSupplierCode(contractGoodsDeptDTO.getSupplierCode());
                deptGoodsPurchPriceDTO.setSupplierName(contractGoodsDeptDTO.getSupplierName());
                deptGoodsPurchPriceDTO.setMainSupplierMode(contractGoodsDeptDTO.getMainSupplierMode());
                if (ifPriceZero(goodsInfo)) {
                    deptGoodsPurchPriceDTO.setFinaPrice(BigDecimal.ZERO);
                }
                deptGoodsPurchPriceDTOS.add(deptGoodsPurchPriceDTO);
            }
        }else if (ApplyCateEnum.PURCH_RETURNS.getCode().equals(deptGoodsPriceQuery.getApplyCate())) {
            //0，供应商进价；1，最后进价；判断取合同价还是部门商品最后进价
            String purchOrderPriceRetrieval = getISupplychainControlEngineService().getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_APPLY_PRICE_TYPE, deptGoodsPriceQuery.getDeptCode());//iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.PURCHASE_RETURN_ORDER_PRICE_RETRIEVAL);
            Logs.info("returnpricemode:" + TenantContext.get() + "purchOrderPriceRetrieval");
            for (ContractGoodsDeptDTO contractGoodsDeptDTO : contractGoodsDeptDTOS) {
                String skuCode = contractGoodsDeptDTO.getSkuCode();
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(skuCode);
                if(null == goodsInfo){
                    Logs.info("SupplychainBizGoodsRuleServiceImpl.listDeptGoodsPurchPrice.purchreturs.notexists" + skuCode);
                    continue;
                }
                DeptGoodsPurchPriceDTO deptGoodsPurchPriceDTO = new DeptGoodsPurchPriceDTO();
                deptGoodsPurchPriceDTO.setSkuCode(skuCode);
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                if("0".equals(purchOrderPriceRetrieval)){
                    deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_PURCH_PRICE.getCode());
                }
                else{
                    deptGoodsPurchPriceDTO.setFinaPrice(MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice()));
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.DEPT_PURCH_PRICE.getCode());
                }

                if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) == 0) {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(null);
                } else {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                }

                if(promotionMap.containsKey(skuCode) ){
                    PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);
                    deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
                    deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
                    deptGoodsPurchPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                }
                deptGoodsPurchPriceDTO.setSkuCode(skuCode);
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                deptGoodsPurchPriceDTO.setPurchasePrice(MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice()));
                deptGoodsPurchPriceDTO.setContractNo(contractGoodsDeptDTO.getContractNo());
                deptGoodsPurchPriceDTO.setContractMaxPrice(contractGoodsDeptDTO.getMaxPurchTaxPrice());
                deptGoodsPurchPriceDTO.setSupplierCode(contractGoodsDeptDTO.getSupplierCode());
                deptGoodsPurchPriceDTO.setSupplierName(contractGoodsDeptDTO.getSupplierName());
                deptGoodsPurchPriceDTO.setMainSupplierMode(contractGoodsDeptDTO.getMainSupplierMode());
                if (ifPriceZero(goodsInfo)) {
                    deptGoodsPurchPriceDTO.setFinaPrice(BigDecimal.ZERO);
                }
                deptGoodsPurchPriceDTOS.add(deptGoodsPurchPriceDTO);
            }
        }
        else {
            String refundApplyPriceType = getISupplychainControlEngineService().getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_APPLY_PRICE_TYPE, deptGoodsPriceQuery.getDeptCode());
            Logs.info("purchOrderPriceRetrieval:" + refundApplyPriceType);
            boolean isLastPrice = "1".equals(refundApplyPriceType);
            for (ContractGoodsDeptDTO contractGoodsDeptDTO : contractGoodsDeptDTOS) {
                String skuCode = contractGoodsDeptDTO.getSkuCode();
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(contractGoodsDeptDTO.getSkuCode());
                BigDecimal purchasePrice = MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice());
                PromotionPriceInfo promotionPriceInfo = promotionMap.get(skuCode);

                DeptGoodsPurchPriceDTO deptGoodsPurchPriceDTO = new DeptGoodsPurchPriceDTO();
                if(isLastPrice) {  //todo 部门最后进价
                    deptGoodsPurchPriceDTO.setFinaPrice(purchasePrice);
                    deptGoodsPurchPriceDTO.setPriceType( PriceTypeEnum.PURCHASE_PRICE.getCode());
                } else if(promotionMap.containsKey(skuCode) && BigDecimal.ZERO.compareTo(promotionPriceInfo.getPromotionPrice()) != 0) {
                    deptGoodsPurchPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
                    deptGoodsPurchPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
                    deptGoodsPurchPriceDTO.setFinaPrice(promotionPriceInfo.getPromotionPrice());
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.PROMOTION_PURCH_PRICE.getCode());
                } else if(contractGoodsDeptDTO.getSpecialTaxPrice() != null && BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) != 0) {
                    deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_SPECIAL_PRICE.getCode());
                } else {
                    deptGoodsPurchPriceDTO.setFinaPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                    deptGoodsPurchPriceDTO.setPriceType(PriceTypeEnum.CONTRACT_PURCH_PRICE.getCode());
                }
                deptGoodsPurchPriceDTO.setSkuCode(contractGoodsDeptDTO.getSkuCode());
                deptGoodsPurchPriceDTO.setContractPrice(contractGoodsDeptDTO.getPurchTaxPrice());
                if (BigDecimal.ZERO.compareTo(contractGoodsDeptDTO.getSpecialTaxPrice()) == 0) {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(null);
                } else {
                    deptGoodsPurchPriceDTO.setContractSpecialPrice(contractGoodsDeptDTO.getSpecialTaxPrice());
                }
                deptGoodsPurchPriceDTO.setPurchasePrice(purchasePrice);
                deptGoodsPurchPriceDTO.setContractNo(contractGoodsDeptDTO.getContractNo());
                deptGoodsPurchPriceDTO.setContractMaxPrice(contractGoodsDeptDTO.getMaxPurchTaxPrice());
                deptGoodsPurchPriceDTO.setSupplierCode(contractGoodsDeptDTO.getSupplierCode());
                deptGoodsPurchPriceDTO.setSupplierName(contractGoodsDeptDTO.getSupplierName());
                deptGoodsPurchPriceDTO.setMainSupplierMode(contractGoodsDeptDTO.getMainSupplierMode());
                if (ifPriceZero(goodsInfo)) {
                    deptGoodsPurchPriceDTO.setFinaPrice(BigDecimal.ZERO);
                }
                deptGoodsPurchPriceDTOS.add(deptGoodsPurchPriceDTO);
            }
        }
        return deptGoodsPurchPriceDTOS;
    }

    private Boolean ifPriceZero(GoodsQueryResp.GoodsInfo goodsInfo) {
        if (ObjectUtils.equals(7, goodsInfo.getOperationModel())) {
            return true;
        }
        return false;
    }

    public BigDecimal getValidPrice(BigDecimal price1, BigDecimal price2, BigDecimal price3) {
        // 按优先级顺序校验非空且非零值
        if (isValidPrice(price1)) {
            return price1;
        } else if (isValidPrice(price2)) {
            return price2;
        } else if (isValidPrice(price3)) {
            return price3;
        } else {
            return BigDecimal.ZERO;
        }
    }

    private boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) != 0;
    }


    @Override //todo 代码优化
    public List<DeptGoodsDeliveryPriceDTO> listDeptGoodsDeliveryPrice(DeptGoodsDeliveryPriceQueryDTO deptGoodsPriceQuery) {
        List<DeptGoodsDeliveryPriceDTO> deptGoodsDeliveryPriceDTOS = new ArrayList<>();
        List<ValidGoodsInfo> goodsInfos = deptGoodsPriceQuery.getGoodsInfos();
        Map<String, List<ValidGoodsInfo>> listMap = goodsInfos.stream().collect(Collectors.groupingBy(ValidGoodsInfo::getSkuCode));
        List<String> skuCodes = goodsInfos.stream().map(ValidGoodsInfo::getSkuCode).collect(Collectors.toList());
        GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();

        Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap = new HashMap<>();
        for (List<ValidGoodsInfo> validGoodsInfos : goodsInfos.stream().collect(Collectors.groupingBy(ValidGoodsInfo::getDistCode)).values()) {
            goodsQueryDTO.setDeptCode(validGoodsInfos.get(0).getDistCode());
            goodsQueryDTO.setSkuCodeList(validGoodsInfos.stream().map(ValidGoodsInfo::getSkuCode).distinct().collect(Collectors.toList()));
            goodsQueryDTO.setAttributeNameFlag(false);
            List<GoodsQueryResp> goodsQueryResps = listGoodsInfo(goodsQueryDTO);
            for (GoodsQueryResp goodsQueryResp : goodsQueryResps) {
                goodsInfoMap.put(goodsQueryResp.getGoodsInfo().getSkuCode() + "-" + validGoodsInfos.get(0).getDistCode(), goodsQueryResp.getGoodsInfo());
            }
        }

//        当申请类型=配送，则取配送价，不可改价
//        配送价取值优先级（为0或者空则往下一层取值）：
//                >配送促销价--促销提供接口
        QueryDeliveryPriceReq acceptanceRefundReq = QueryDeliveryPriceReq.builder()
                .storeCode(deptGoodsPriceQuery.getDeptCode())
                .skuCodeList(skuCodes).build();
        Map<String, PromotionPriceInfo> promotionMap = promotionHandleUtil.queryDeliveryPrice(acceptanceRefundReq);

//        List<ValidGoodsInfo> validGoodsInfos = goodsInfos.stream()
//                .filter(e -> !deptGoodsDeliveryPriceDTOS.stream().map(DeptGoodsDeliveryPriceDTO::getSkuCode)
//                        .collect(Collectors.toList()).contains(e.getSkuCode())).collect(Collectors.toList());
//        if(CollectionUtils.isEmpty(validGoodsInfos)) {
//            return deptGoodsDeliveryPriceDTOS;
//        }
//                >部门配送价 （固定价）-- 部门配送价提供接口
        QueryDistPriceReq distPriceReq = QueryDistPriceReq.builder()
                .skuList(goodsInfos.stream().map(e ->
                        QueryDistPriceComplexReq.SkuCodeAndWhCode.builder()
                                .whCode(e.getDistCode())
                                .skuClassCode(e.getCategoryCode())
                                .skuCode(e.getSkuCode())
                                .build()).collect(Collectors.toSet()))
                .deptCode(deptGoodsPriceQuery.getDeptCode())
                .build();
        List<MdDistPriceDTO> mdDistPriceDTOS = iMdDistributionPriceDomainService.queryValidDistPrice(distPriceReq);
        Map<String, MdDistPriceDTO> distPriceDTOMap = mdDistPriceDTOS.stream().collect(Collectors.toMap(MdDistPriceDTO::getSkuCode, Function.identity()));

        if (promotionMap != null && CollectionUtils.isNotEmpty(promotionMap.values())) {
            for (PromotionPriceInfo promotionPriceInfo : promotionMap.values()) {
                List<ValidGoodsInfo> validGoodsInfos = listMap.get(promotionPriceInfo.getSkuCode());
                for (ValidGoodsInfo validGoodsInfo : validGoodsInfos) {
                    GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(promotionPriceInfo.getSkuCode() + "-" + validGoodsInfo.getDistCode());
                    DeptGoodsDeliveryPriceDTO deptGoodsDeliveryPriceDTO = new DeptGoodsDeliveryPriceDTO();
                    deptGoodsDeliveryPriceDTO.setSkuCode(promotionPriceInfo.getSkuCode());
                    deptGoodsDeliveryPriceDTO.setFinaPrice(promotionPriceInfo.getPromotionPrice());
                    deptGoodsDeliveryPriceDTO.setDispatchPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getDispatchPrice()));
                    deptGoodsDeliveryPriceDTO.setPurchasePrice(MoneyUtil.longMilliToYuan4(goodsInfo.getPurchasePrice()));
                    deptGoodsDeliveryPriceDTO.setPromotionId(promotionPriceInfo.getPromotionId());
                    deptGoodsDeliveryPriceDTO.setPromotionName(promotionPriceInfo.getPromotionName());
                    deptGoodsDeliveryPriceDTO.setDistCode(validGoodsInfo.getDistCode());
                    deptGoodsDeliveryPriceDTO.setPromotionPrice(promotionPriceInfo.getPromotionPrice());
                    deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.ACCEPT_REFUND.getCode());
                    if (ifPriceZero(goodsInfo)) {
                        deptGoodsDeliveryPriceDTO.setFinaPrice(BigDecimal.ZERO);
                    }
                    if (distPriceDTOMap.containsKey(validGoodsInfo.getSkuCode())) {
                        MdDistPriceDTO mdDistPriceDTO = distPriceDTOMap.get(validGoodsInfo.getSkuCode());
                        deptGoodsDeliveryPriceDTO.setMarkupRate(mdDistPriceDTO.getMarkupRate());
                        deptGoodsDeliveryPriceDTO.setDistTaxPrice(mdDistPriceDTO.getDistTaxPrice());
                    }

                    deptGoodsDeliveryPriceDTOS.add(deptGoodsDeliveryPriceDTO);
                }
            }
        }

        mdDistPriceDTOS = mdDistPriceDTOS.stream()
                .filter(e -> !CollectionUtils.isNotEmpty(deptGoodsDeliveryPriceDTOS.stream().filter(dept -> dept.getSkuCode().equals(e.getSkuCode()) && dept.getDistCode().equals(e.getWhCode()))
                        .collect(Collectors.toList()))).collect(Collectors.toList());

        for (MdDistPriceDTO mdDistPriceDTO : mdDistPriceDTOS) {
            DeptGoodsDeliveryPriceDTO deptGoodsDeliveryPriceDTO = new DeptGoodsDeliveryPriceDTO();
            deptGoodsDeliveryPriceDTO.setSkuCode(mdDistPriceDTO.getSkuCode());
            deptGoodsDeliveryPriceDTO.setMarkupRate(mdDistPriceDTO.getMarkupRate());
            GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(mdDistPriceDTO.getSkuCode() + "-" + mdDistPriceDTO.getWhCode());
            if (MdDistPriceTypeEnum.FIXED_PRICE.getCode().equals(mdDistPriceDTO.getPriceType())) {
                BigDecimal distTaxPrice = mdDistPriceDTO.getDistTaxPrice();
                if (distTaxPrice == null || BigDecimal.ZERO.equals(distTaxPrice)) {
                    continue;
                }
                deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.DIST_FIXED_PRICE.getCode());
                deptGoodsDeliveryPriceDTO.setFinaPrice(distTaxPrice);
            } else if (MdDistPriceTypeEnum.MARKUP_RATE.getCode().equals(mdDistPriceDTO.getPriceType())) {
                BigDecimal markUpPrice = MoneyUtil.milliToYuanB(goodsInfo.getPurchasePrice())
                        .multiply(BigDecimal.ONE.add(mdDistPriceDTO.getMarkupRate().divide(new BigDecimal(100))));
                if (BigDecimal.ZERO.equals(markUpPrice)) {
                    continue;
                }
                deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.DIST_MARKUP_PRICE.getCode());
                deptGoodsDeliveryPriceDTO.setFinaPrice(markUpPrice);
            } else {
                BigDecimal deductPrice = MoneyUtil.milliToYuanB(goodsInfo.getReferPrice())
                        .multiply(BigDecimal.ONE.subtract(mdDistPriceDTO.getDeductionRate().divide(new BigDecimal(100))));
                deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.DIST_DEDUCTION_RATE_PRICE.getCode());
                deptGoodsDeliveryPriceDTO.setFinaPrice(deductPrice);
            }

            if (ifPriceZero(goodsInfo)) {
                deptGoodsDeliveryPriceDTO.setFinaPrice(BigDecimal.ZERO);
            }

            deptGoodsDeliveryPriceDTO.setDistCode(mdDistPriceDTO.getWhCode());
            deptGoodsDeliveryPriceDTO.setDeptDistPrice(deptGoodsDeliveryPriceDTO.getFinaPrice());
            deptGoodsDeliveryPriceDTO.setDispatchPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getDispatchPrice()));
            deptGoodsDeliveryPriceDTO.setPurchasePrice(MoneyUtil.longMilliToYuan4(goodsInfo.getPurchasePrice()));
            deptGoodsDeliveryPriceDTO.setDistTaxPrice(mdDistPriceDTO.getDistTaxPrice());
            deptGoodsDeliveryPriceDTO.setMarkupRate(mdDistPriceDTO.getMarkupRate());
            deptGoodsDeliveryPriceDTOS.add(deptGoodsDeliveryPriceDTO);
        }

//                >部门配送价 （加价率，基于出货方部门参考进价）--部门配送价提供接口
//                >部门配送价 （加价率，基于商品档案进价）--部门配送价提供接口

//                >部门配送价 （倒扣率，基于出货方部门售价）--部门配送价提供接口
//                >部门配送价 （倒扣率，基于商品档案售价）--部门配送价提供接口
//                >商品档案配送价--商品主档

        List<String> validSkuCodes = skuCodes.stream().filter(e ->
                !deptGoodsDeliveryPriceDTOS.stream().map(DeptGoodsDeliveryPriceDTO::getSkuCode).collect(Collectors.toList())
                        .contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validSkuCodes)) {
            return deptGoodsDeliveryPriceDTOS;
        }
        for (String remainSkuCode : validSkuCodes) {
            List<ValidGoodsInfo> remainGoodsInfos = listMap.get(remainSkuCode);
            for (ValidGoodsInfo validGoodsInfo : remainGoodsInfos) {
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(validGoodsInfo.getSkuCode() + "-" + validGoodsInfo.getDistCode());
                if (goodsInfo.getDispatchPrice() != null && goodsInfo.getDispatchPrice() > 0) {
                    DeptGoodsDeliveryPriceDTO deptGoodsDeliveryPriceDTO = new DeptGoodsDeliveryPriceDTO();
                    deptGoodsDeliveryPriceDTO.setSkuCode(remainSkuCode);
                    deptGoodsDeliveryPriceDTO.setDistCode(validGoodsInfo.getDistCode());
                    deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.DISPATCH_PRICE.getCode());
                    deptGoodsDeliveryPriceDTO.setFinaPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getDispatchPrice()));
                    deptGoodsDeliveryPriceDTO.setDispatchPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getDispatchPrice()));
                    deptGoodsDeliveryPriceDTO.setPurchasePrice(MoneyUtil.longMilliToYuan4(goodsInfo.getPurchasePrice()));

                    if (ifPriceZero(goodsInfo)) {
                        deptGoodsDeliveryPriceDTO.setFinaPrice(BigDecimal.ZERO);
                    }

                    deptGoodsDeliveryPriceDTOS.add(deptGoodsDeliveryPriceDTO);
                }
            }
        }
//                >部门参考进价（出货方）--部门商品
//                >商品档案进价--商品主档
        validSkuCodes = skuCodes.stream().filter(e ->
                !deptGoodsDeliveryPriceDTOS.stream().map(DeptGoodsDeliveryPriceDTO::getSkuCode).collect(Collectors.toList())
                        .contains(e)).collect(Collectors.toList());
        for (String remainSkuCode : validSkuCodes) {
            List<ValidGoodsInfo> remainGoodsInfos = listMap.get(remainSkuCode);
            for (ValidGoodsInfo validGoodsInfo : remainGoodsInfos) {
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(validGoodsInfo.getSkuCode() + "-" + validGoodsInfo.getDistCode());
                DeptGoodsDeliveryPriceDTO deptGoodsDeliveryPriceDTO = new DeptGoodsDeliveryPriceDTO();
                deptGoodsDeliveryPriceDTO.setSkuCode(remainSkuCode);
                deptGoodsDeliveryPriceDTO.setDistCode(validGoodsInfo.getDistCode());
                deptGoodsDeliveryPriceDTO.setPriceType(PriceTypeEnum.PURCHASE_PRICE.getCode());
                deptGoodsDeliveryPriceDTO.setFinaPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getPurchasePrice()));
                deptGoodsDeliveryPriceDTO.setDispatchPrice(MoneyUtil.longMilliToYuan4(goodsInfo.getDispatchPrice()));
                deptGoodsDeliveryPriceDTO.setPurchasePrice(MoneyUtil.longMilliToYuan4(goodsInfo.getPurchasePrice()));
                if (ifPriceZero(goodsInfo)) {
                    deptGoodsDeliveryPriceDTO.setFinaPrice(BigDecimal.ZERO);
                }
                deptGoodsDeliveryPriceDTOS.add(deptGoodsDeliveryPriceDTO);
            }
        }

        return deptGoodsDeliveryPriceDTOS;
    }

    @Override
    public List<ContractGoodsDeptDTO> listContractGoodsDept(ContractGoodsDeptQueryDTO contractGoodsDeptQuery) {
        return getIMdContractMasDomainService().listContractGoodsDept(contractGoodsDeptQuery);
    }

    @Override
    public List<ContractGoods4DeptResultDTO> queryContractGoods4Dept(ContractGoodsQueryDTO contractGoodsDeptQuery) {
        if (contractGoodsDeptQuery.getCancelFlag()) {
            return getIMdContractMasDomainService().queryContractGoods4DeptCancel(contractGoodsDeptQuery);
        }
        return getIMdContractMasDomainService().queryContractGoods4Dept(contractGoodsDeptQuery);
    }

    @Override
    public List<ContractGoodsDeptCheckResultDTO> checkContracGoods(ContractGoodsDeptCheckDTO checkDTO) {
        if (CollectionUtils.isEmpty(checkDTO.getSkuCodeList())) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P024);
        }

        if (checkDTO.getSkuCodeList().size() > 100) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P017);
        }

        // 获取合同信息
        MdContractGetDetailsReq param = new MdContractGetDetailsReq();
        param.setContractNo(checkDTO.getContractNo());
        Result<MdContractDetailsDTO> details = getIMdContractMasDomainService().getDetails(param);
        if (null == details.getData()) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P008, new String[]{checkDTO.getContractNo()});
        }
        MdContractDetailsDTO mdContractDTO = details.getData();

        // 获取商品信息
        List<String> skuCodeList = checkDTO.getSkuCodeList();
        GoodsSearchQueryReq goodsParam = new GoodsSearchQueryReq();
        goodsParam.setSkuCodes(skuCodeList);
        goodsParam.setSource("supplychain-component");
        goodsParam.setCurrent(1L);
        goodsParam.setPageSize(100L);
        List<GoodsSearchInfo> goodsSearchInfoList = commonGoodsService.simpleSearchQuery(goodsParam).getRows();
        if (CollectionUtils.isEmpty(goodsSearchInfoList)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCWDS012P009);
        }

        List<ContractGoodsDeptCheckResultDTO> resultList = Lists.newArrayList();
        Map<String, GoodsSearchInfo> goodsMap = goodsSearchInfoList.stream()
                .collect(Collectors.toMap(GoodsSearchInfo::getSkuCode, obj -> obj, (obj1, obj2) -> obj1));
        List<MdContractScopeDTO> contractScopeList = mdContractDTO.getContractScopeList();
        Set<String> categorySet = contractScopeList.stream()
                .filter(item -> item.getScopeType().equals(3)).map(MdContractScopeDTO::getScopeCode).collect(Collectors.toSet());
        Set<String> brandSet = contractScopeList.stream()
                .filter(item -> item.getScopeType().equals(4)).map(MdContractScopeDTO::getScopeCode).collect(Collectors.toSet());

        // 合同商品取消校验的时候判断是否存在对照数据
        ContractGoodsDeptQueryDTO contractGoodsDeptQueryDTO = new ContractGoodsDeptQueryDTO();
        contractGoodsDeptQueryDTO.setContractNo(checkDTO.getContractNo());
        contractGoodsDeptQueryDTO.setSkuCodeList(skuCodeList);
        contractGoodsDeptQueryDTO.setSupplierCode(checkDTO.getSupplierCode());
        List<ContractGoodsDeptDTO> dtoList = getIMdContractMasDomainService().listContractGoods(contractGoodsDeptQueryDTO);
        Map<String, List<ContractGoodsDeptDTO>> contractGoodsDeptMap = dtoList.stream()
                .collect(Collectors.groupingBy(ContractGoodsDeptDTO::getSkuCode));


        for (int i = 0; i < skuCodeList.size(); i++) {
            String item = skuCodeList.get(i);
            ContractGoodsDeptCheckResultDTO resultDTO = new ContractGoodsDeptCheckResultDTO();
            resultList.add(resultDTO);
            if (!goodsMap.containsKey(item)) {
                resultDTO.setCheckFlag(false);
                String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P010.getErrorMsg(), new String[]{item}).getMessage();
                resultDTO.setErrMsg(message);
                continue;
            }

            // 校验商品是否在合同范围内
            GoodsSearchInfo goodsSearchInfo = goodsMap.get(item);
            if (CollectionUtils.isNotEmpty(categorySet) && !categorySet.contains(goodsSearchInfo.getSkuCategoryCodeOffLine())) {
                resultDTO.setCheckFlag(false);
                String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P025.getErrorMsg(), new String[]{item}).getMessage();
                resultDTO.setErrMsg(message);
                continue;
            }
            if (CollectionUtils.isNotEmpty(brandSet) && !brandSet.contains(goodsSearchInfo.getSkuBrandCodeOffLine())) {
                resultDTO.setCheckFlag(false);
                String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P025.getErrorMsg(), new String[]{item}).getMessage();
                resultDTO.setErrMsg(message);
                continue;
            }
            if (checkDTO.getCancelFlag() && !contractGoodsDeptMap.containsKey(item)) {
                resultDTO.setCheckFlag(false);
                String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P031.getErrorMsg(), new String[]{item}).getMessage();
                resultDTO.setErrMsg(message);
                continue;
            }

            /**
             * 商品 -- 合同
             * ●（联营、联营管库存）<->联营，
             * ●租赁<->租赁，
             * ●（自营、生鲜A进A出）<->（经销、代销）
             */
            String operateMode = mdContractDTO.getOperateMode();
            switch (operateMode) {
                case "L":
                    if (!goodsSearchInfo.getSkuOperationModel().equals("2") && !goodsSearchInfo.getSkuOperationModel().equals("7")) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P026.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
                case "Z":
                    if (!goodsSearchInfo.getSkuOperationModel().equals("8")) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P027.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
                case "J":
                case "D":
                    if (!goodsSearchInfo.getSkuOperationModel().equals("1") && !goodsSearchInfo.getSkuOperationModel().equals("9")) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P027.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
                default:
                    resultDTO.setCheckFlag(false);
                    String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P027.getErrorMsg(), new String[]{item}).getMessage();
                    resultDTO.setErrMsg(message);
                    continue;
            }
            // 其余经营方式不允许
            if (goodsSearchInfo.getSkuOperationModel().equals("10") || goodsSearchInfo.getSkuOperationModel().equals("11")) {
                resultDTO.setCheckFlag(false);
                String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P027.getErrorMsg(), new String[]{item}).getMessage();
                resultDTO.setErrMsg(message);
                continue;
            }

            /**
             * ●单规格商品：只允许子商品，不允许多包装，散称标准份，散称主子码商品等
             * ●多规格商品：只允许主商品，不允许子码商品。
             * ●组合商品：只允许子商品（被组合商品），不允许组合商品，
             */
            Integer goodsType = goodsSearchInfo.getGoodsType();
            switch (goodsType) {
                case 1:
                    if (null != goodsSearchInfo.getCombType() && !goodsSearchInfo.getCombType().equals(1)) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P028.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
                case 2:
                    if (goodsSearchInfo.getMainSkuFlag().equals("0")) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P029.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
                case 3:
                    if (goodsSearchInfo.getMainSkuFlag().equals("1")) {
                        resultDTO.setCheckFlag(false);
                        String message = MessageFormatter.arrayFormat(MdErrorCodeEnum.SCWDS012P030.getErrorMsg(), new String[]{item}).getMessage();
                        resultDTO.setErrMsg(message);
                        continue;
                    }
                    break;
            }
            if (!resultDTO.getCheckFlag()) {
                ContractGoodsDeptCheckResultDTO.ContractGoodsCheckResultDTO goods =
                        new ContractGoodsDeptCheckResultDTO.ContractGoodsCheckResultDTO();
                resultDTO.setCheckFlag(true);
                resultDTO.setSkuDto(goods);
                goods.setSkuCode(goodsSearchInfo.getSkuCode());
                goods.setBarcode(goodsSearchInfo.getSkuBarCode());
                goods.setSkuNo(goodsSearchInfo.getGoodsNo());
                goods.setSkuName(goodsSearchInfo.getSkuName());
                goods.setSkuModel(goodsSearchInfo.getSkuSpecModel());
                goods.setProducingArea(goodsSearchInfo.getProducingArea());
                goods.setPurchPrice(StringUtils.isEmpty(goodsSearchInfo.getPurchasePrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getPurchasePrice()));
                goods.setSalePrice(StringUtils.isEmpty(goodsSearchInfo.getReferPrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getReferPrice()));
                goods.setCeilingPrice(StringUtils.isEmpty(goodsSearchInfo.getCeilingPrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getCeilingPrice()));
                goods.setFloorPrice(StringUtils.isEmpty(goodsSearchInfo.getFloorPrice()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getFloorPrice()));
                goods.setInputTaxRate(StringUtils.isEmpty(goodsSearchInfo.getInputTaxRate()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getInputTaxRate()));
                goods.setOutputTaxRate(StringUtils.isEmpty(goodsSearchInfo.getOutputTaxRate()) ? BigDecimal.valueOf(0) : new BigDecimal(goodsSearchInfo.getOutputTaxRate()));
            }
        }
        return resultList;
    }

    @Override
    public List<GoodsStockDTO> listGoodsStock(StockQueryDTO stockQuery) {
        //查询经营状态及流转途径
        ManageAndCirculationReq circulationReq = ManageAndCirculationReq.builder()
                .build();
        ManageAndCirculationResp manageAndCirculation = getICommonGoodsService().getManageAndCircul(circulationReq);

        //查询商品信息及包装率
        List<GoodsStockDTO> goodsStockList = queryGoodsInfo4Stock(stockQuery);

        //查询销量
        if (ObjectUtils.equals(1, stockQuery.getType())) {
            queryGoodsSaleInfo(goodsStockList, stockQuery);
        }

        //查询库存 key = 部门
        Map<String, List<StockVO>> stockInfo = getICommonStockService().getStockInfo(stockQuery);

        //组装库存
        handleStock(goodsStockList, stockInfo, manageAndCirculation);
        return goodsStockList;
    }

    /**
     * 填充销量数据
     *
     * @param goodsStockList
     */
    public void queryGoodsSaleInfo(List<GoodsStockDTO> goodsStockList, StockQueryDTO req) {
        try {
            //销量查询转tabse
            Map<String, String> queryParam = new HashMap<>();
            queryParam.put("tenant_id", TenantContext.get());
            if (CollectionUtils.isNotEmpty(req.getStoreCodeList())) {
                queryParam.put("dept_code_list", "'" + String.join("','", req.getStoreCodeList()) + "'");
            }
            if (CollectionUtils.isNotEmpty(req.getGoodInfoList())) {
                Map<String, String> goodsMap = req.getGoodInfoList().stream().collect(Collectors.toMap(StockQueryDTO.GoodsInfo::getGoodsCode, StockQueryDTO.GoodsInfo::getGoodsCode));
                List<String> goodCodeList = new ArrayList<>(goodsMap.keySet());
                queryParam.put("goods_code_list", "'" + String.join("','", goodCodeList) + "'");
            }

            QueryTbaseResult queryTbaseResult = getBiPassClient().queryTbase(BiPassDsCodeEnum.SKU_30DAY_SALENUM.getCode(), queryParam);

            if (null == queryTbaseResult || !"0".equals(queryTbaseResult.getCode())) {
                return;
            }

            List<Object> data = queryTbaseResult.getData();

            List<Sku30DaySalenumDTO> sku30DaySalenumDTOList = JSONArray.parseArray(Jsons.toJson(data), Sku30DaySalenumDTO.class);

            if (CollectionUtils.isNotEmpty(sku30DaySalenumDTOList)) {
                Map<String, Sku30DaySalenumDTO> goodsSaleMap = new HashMap<>();
                for (Sku30DaySalenumDTO sku30DaySalenumDTO : sku30DaySalenumDTOList) {
                    String key = sku30DaySalenumDTO.getDept_code() + "_" + sku30DaySalenumDTO.getGoods_code();
                    goodsSaleMap.put(key, sku30DaySalenumDTO);

                }

                for (GoodsStockDTO goodsStock : goodsStockList) {
                    String skuCode = goodsStock.getSkuCode();
                    for (GoodsStockDTO.GoodsStoreStockInfo goodsStoreStockInfo : goodsStock.getGoodsStoreInfo()) {
                        String storeCode = goodsStoreStockInfo.getStoreCode();

                        String key = storeCode + "_" + skuCode;
                        if (goodsSaleMap.containsKey(key)) {
                            Sku30DaySalenumDTO sku30DaySalenumDTO = goodsSaleMap.get(key);
                            goodsStoreStockInfo.setLast1DaysSaleNum(new BigDecimal(sku30DaySalenumDTO.getSale_amount_1d()));
                            goodsStoreStockInfo.setLast7DaysSaleNum(new BigDecimal(sku30DaySalenumDTO.getSale_amount_7d()));
                            goodsStoreStockInfo.setLast30DaysSaleNum(new BigDecimal(sku30DaySalenumDTO.getSale_amount_30d()));
                        }
                    }
                }
            }

        } catch (Exception e) {
            Logs.error("SupplychainBizGoodsRuleServiceImpl.queryGoodsSaleInfo", e);
        }
    }

    private void handleStock(List<GoodsStockDTO> goodsStockList, Map<String, List<StockVO>> stockInfo, ManageAndCirculationResp manageAndCirculation) {
        final Map<String, ManageAndCirculationResp.CirculationMode> circulationModeMap = new HashMap<>();
        final Map<String, ManageAndCirculationResp.WorkState> workStateMap = new HashMap<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(manageAndCirculation.getCirculationList())) {
            circulationModeMap.putAll(manageAndCirculation.getCirculationList().stream().collect(Collectors.toMap(ManageAndCirculationResp.CirculationMode::getCirculationWayNo, t -> t)));
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(manageAndCirculation.getManageStateList())) {
            workStateMap.putAll(manageAndCirculation.getManageStateList().stream().collect(Collectors.toMap(ManageAndCirculationResp.WorkState::getManagementStateNo, t -> t)));
        }

        goodsStockList.forEach(goodsStockDTO -> {
            for (GoodsStockDTO.GoodsStoreStockInfo goodsStoreInfo : goodsStockDTO.getGoodsStoreInfo()) {
                List<StockVO> skuStockDtoList = stockInfo.get(goodsStoreInfo.getStoreCode());

                // 经营状态
                ManageAndCirculationResp.WorkState workState = workStateMap.get(goodsStoreInfo.getWorkStateCode());
                ManageAndCirculationResp.CirculationMode circulationMode = circulationModeMap.get(goodsStoreInfo.getCirculationModeCode());
                List<GoodsStockDTO.ShippingWay> list = new ArrayList<>();
                if (Objects.nonNull(workState)) {
                    if (MoneyUtil.int2BigDecimal(workState.getAllowPurchaseFlag()).intValue() > 0) {
                        list.add(new GoodsStockDTO.ShippingWay(ShippingWayEnum.PURCHASE));
                    }
                }

                if (Objects.nonNull(circulationMode)) {
                    if (MoneyUtil.int2BigDecimal(circulationMode.getAllowPullFlag()).intValue() > 0) {
                        list.add(new GoodsStockDTO.ShippingWay(ShippingWayEnum.DELIVERY));
                        goodsStoreInfo.setTransferFlag(true);
                    }
                }

                goodsStoreInfo.setWorkStateInfo(workState);
                goodsStoreInfo.setCirculationModeInfo(circulationMode);
                goodsStoreInfo.setShippingWayList(list);

                if (CollectionUtils.isEmpty(skuStockDtoList)) {
                    continue;
                }
                Map<String, StockVO> stockVOMap = skuStockDtoList.stream().collect(Collectors.toMap(StockVO::getSkuCode, Function.identity(), (value1, value2) -> value2));
                StockVO skuStockDto = stockVOMap.get(goodsStockDTO.getSkuCode());

                if (null == skuStockDto) {
                    continue;
                }

                // 库存信息
                goodsStoreInfo.setStockNum(skuStockDto.getRealQty());

                //可用 实库 - 所有占用-所以预留
                goodsStoreInfo.setAtpQty(skuStockDto.getAtpQty());

                goodsStoreInfo.setToOutLockQty(skuStockDto.getToOutLockQty());
                goodsStoreInfo.setDrsLockQty(skuStockDto.getDrsLockQty());

                BigDecimal onWayStockNum = skuStockDto.getPoTransQty().add(skuStockDto.getDoTransQty()).add(skuStockDto.getToInTransQty());
                goodsStoreInfo.setOnWayStockNum(onWayStockNum);

                // 计量属性（0：普通 1：计量 2：称重）`
//                Integer measureProperty = goodsStoreInfo.getMeasureProperty();
//                if (MoneyUtil.int2BigDecimal(measureProperty).intValue() == MeasurePropertyEnum.WEIGH.getCode()) {
//                    goodsStoreInfo.setStockNum(skuStockDto.getRealQty().divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP));
//                    goodsStoreInfo.setOnWayStockNum(onWayStockNum.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP));
//
//                    goodsStoreInfo.setToOutLockQty(skuStockDto.getToOutLockQty().divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP));
//                    goodsStoreInfo.setDrsLockQty(skuStockDto.getDrsLockQty().divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP));
//                }
                BigDecimal onWay = skuStockDto.getDoTransQty().add(skuStockDto.getToInTransQty());
                goodsStoreInfo.setOnWayFlag(onWay.compareTo(BigDecimal.ZERO) > 0);


                // 效期
                if (CollectionUtils.isNotEmpty(skuStockDto.getStkPeriodList())) {
                    List<StkPeriod> periodStockList = skuStockDto.getStkPeriodList();
                    List<GoodsPeriod> collect = Optional.ofNullable(periodStockList).orElse(new ArrayList<>())
                            .stream().map(item -> {
                                GoodsPeriod copy = CglibCopier.copy(item, GoodsPeriod.class);
                                // 处理效期库存单位
//                                if (MoneyUtil.int2BigDecimal(measureProperty).intValue() == MeasurePropertyEnum.WEIGH.getCode()) {
//                                    copy.setNum(MoneyUtil.bigDecimalMilliToYuan4(item.getRealQty()));
//                                } else {
                                copy.setNum(item.getRealQty());
//                                }
                                copy.setMfg(DateUtil.localDateFormateYmd(item.getProductDate()));
                                copy.setExp(DateUtil.localDateFormateYmd(item.getExpiryDate()));
                                return copy;
                            })
                            .filter(item -> item.getNum().doubleValue() > 0)
                            .collect(Collectors.toList());
                    goodsStoreInfo.setPeriodList(collect);
                }
            }

        });
    }

    private List<GoodsStockDTO> queryGoodsInfo4Stock(StockQueryDTO stockQuery) {
        List<String> skuCodeList = new ArrayList<>();
        for (StockQueryDTO.GoodsInfo goodsInfo : stockQuery.getGoodInfoList()) {
            skuCodeList.add(goodsInfo.getGoodsCode());
        }

        List<GoodsStockDTO> goodsStockList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(skuCodeList)) {
            List<String> supplierCodeList = new ArrayList<>();

            Map<String, String> storeGroup = new HashMap<>();
            Map<String, List<GoodsQueryResp>> storeGoodsGroup = new HashMap<>();

            for (String deptCode : stockQuery.getStoreCodeList()) {
                GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();
                goodsQueryDTO.setDeptCode(deptCode);
                goodsQueryDTO.setSkuCodeList(skuCodeList);
                goodsQueryDTO.setAttributeNameFlag(true);
                storeGroup.put(deptCode, deptCode);

                List<GoodsQueryResp> goodsInfoList = listGoodsInfo(goodsQueryDTO);
                storeGoodsGroup.put(deptCode, goodsInfoList);
            }


            for (String skuCode : skuCodeList) {
                List<GoodsQueryResp> tempGodsList = storeGoodsGroup.get(stockQuery.getStoreCodeList().get(0));
                int packageNum = tempGodsList.stream().filter(goodsQueryResp -> goodsQueryResp.getGoodsInfo().getSkuCode().equals(skuCode))
                        .mapToInt(goodsQueryResp -> Objects.isNull(goodsQueryResp.getRelationGoodsInfo()) ?
                                goodsQueryResp.getGoodsInfo().getPackageNum() : goodsQueryResp.getRelationGoodsInfo().getPackageNum())
                        .max().orElse(0);

                GoodsStockDTO goodsStockDTO = new GoodsStockDTO();
                goodsStockDTO.setSkuCode(skuCode);
                goodsStockDTO.setPackageNum(packageNum);

                List<GoodsStockDTO.GoodsStoreStockInfo> goodsStoreInfo = new ArrayList<>();
                goodsStockDTO.setGoodsStoreInfo(goodsStoreInfo);

                goodsStockList.add(goodsStockDTO);

                Iterator<String> iterator = storeGoodsGroup.keySet().iterator();
                while (iterator.hasNext()) {
                    String storeCode = iterator.next();
                    List<GoodsQueryResp> goodsQueryResps = storeGoodsGroup.get(storeCode);
                    for (GoodsQueryResp goodsQueryResp : goodsQueryResps) {
                        if (skuCode.equals(goodsQueryResp.getGoodsInfo().getSkuCode())) {
                            GoodsStockDTO.GoodsStoreStockInfo goodsStoreStockInfo = new GoodsStockDTO.GoodsStoreStockInfo();
                            goodsStoreStockInfo.setStoreCode(storeCode);
                            GoodsQueryResp.GoodsInfo goodsInfo = goodsQueryResp.getGoodsInfo();
                            goodsStoreStockInfo.setMeasureProperty(goodsInfo.getMesureProperty());
                            goodsStoreStockInfo.setInCatalog(goodsInfo.getInCatalog());

                            goodsStoreStockInfo.setWorkStateCode(goodsInfo.getWorkStateCode());
                            goodsStoreStockInfo.setCirculationModeCode(goodsInfo.getCirculationModeCode());

                            //直流供应商标志与编码
                            goodsStoreStockInfo.setDirectFlag(goodsInfo.getDirectFlag());
                            //
                            goodsStoreStockInfo.setPackageNum(goodsInfo.getPackageNum());
                            goodsStoreStockInfo.setPackageFlag(goodsInfo.getPackageFlag());
                            goodsStoreStockInfo.setUnit(goodsInfo.getUnit());
                            goodsStoreStockInfo.setUnitName(goodsInfo.getUnitName());
                            goodsStoreInfo.add(goodsStoreStockInfo);

                            if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),goodsInfo.getDirectFlag())){
                                goodsStoreStockInfo.setDirectSupplierCode(goodsInfo.getDirectSupplierCode());
                                if (StringUtils.isNotEmpty(goodsInfo.getDirectSupplierCode())) {
                                    supplierCodeList.add(goodsInfo.getDirectSupplierCode());
                                }
                            }

                        }
                    }
                }
            }

            //直流供应商重查名称
            if (CollectionUtils.isNotEmpty(supplierCodeList)) {
                Map<String, MstSupplierVO> stringMstSupplierVOMap = mdSupplierDomainService.querySupplierMap(supplierCodeList);

                for (GoodsStockDTO goodsStockDTO : goodsStockList) {
                    for (GoodsStockDTO.GoodsStoreStockInfo goodsStoreStockInfo : goodsStockDTO.getGoodsStoreInfo()) {
                        if (StringUtils.isNotEmpty(goodsStoreStockInfo.getDirectSupplierCode()) && stringMstSupplierVOMap.containsKey(goodsStoreStockInfo.getDirectSupplierCode())) {
                            goodsStoreStockInfo.setDirectSupplierName(stringMstSupplierVOMap.get(goodsStoreStockInfo.getDirectSupplierCode()).getName());
                        }
                    }
                }
            }
        }

        return goodsStockList;

    }

    @Override
    public RequireGoodsResp getGoodsStrategy(RequireGoodsReq req) {
        RequireGoodsResp requireGoodsResp = getRequireGoodsStrategy(req);
        Map<String, RequireGoodsInfo> goodsInfoMap = req.getGoodsInfos().stream().collect(Collectors.toMap(RequireGoodsInfo::getGoodsCode, Function.identity(), (value1, value2) -> value2));
        if (ObjectUtils.equals(1, req.getType())) {
            MainSupplierInfoQueryDTO mainSupplierInfoQuery = new MainSupplierInfoQueryDTO();
            mainSupplierInfoQuery.setDeptCode(req.getDeptCode());
            mainSupplierInfoQuery.setGoodsInfoMap(goodsInfoMap);
            mainSupplierInfoQuery.setPriceSign(1);
            List<RequireGoodsResp.RequireSupplier> mainSupplierInfo = getMainSupplierInfo(mainSupplierInfoQuery);
            requireGoodsResp.setRequireSupplierList(mainSupplierInfo);
        }

        OrderStrategyResp orderStrategy = requireGoodsResp.getOrderStrategy();
        if (orderStrategy != null && CollectionUtils.isNotEmpty(requireGoodsResp.getRequireGoodsList())) {
            for (RequireGoodsResp.RequireGoods requireGoods : requireGoodsResp.getRequireGoodsList()) {
                if (StringUtils.isBlank(requireGoods.getWhCode())) {
                    requireGoods.setWhCode(orderStrategy.getWhCode());
                    requireGoods.setWhName(orderStrategy.getWhName());
                }
            }
        }
        requireGoodsResp.setDeptCode(req.getDeptCode());

        return requireGoodsResp;
    }

    @Override
    public List<GoodsStrategyResp> getBatchGoodsStrategy(List<GoodsStrageReq> goodsStrageReqList) {
        Set<String> deptCodeSet = new HashSet<>();
        for (GoodsStrageReq goodsStrageReq : goodsStrageReqList) {
            deptCodeSet.add(goodsStrageReq.getDeptCode());
        }
        // 获取部门上级店组群
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(new ArrayList<>(deptCodeSet))
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();

        Map<String, List<String>> deptGroupMap = new HashMap<>();

        for (QueryBatchDeptListResp.Rows row : groupList) {
            List<String> groupCodelist = new ArrayList<>();
            for (QueryBatchDeptListResp.DeptGroup deptGroup : row.getDeptGroupList()) {
                groupCodelist.add(deptGroup.getCode());
            }
            deptGroupMap.put(row.getCode(), groupCodelist);
        }

        List<GoodsStrategyResp> list = new ArrayList<>();

        //组装查询条件
        for (GoodsStrageReq goodsStrageReq : goodsStrageReqList) {
            GoodsStrategyResp goodsStrategyResp = getGoodsStrategyResp(goodsStrageReq, deptGroupMap.get(goodsStrageReq.getDeptCode()));
            list.add(goodsStrategyResp);
        }

        return list;
    }

    @Override
    public GoodsStrategyResp getPurchGoodsStrategy(GoodsStrageReq req) {

        QueryUpDeptListReq queryUpDeptListReq = new QueryUpDeptListReq();
        queryUpDeptListReq.setDeptCode(req.getDeptCode());
        queryUpDeptListReq.setClassCode(GroupDeptEnum.CONTROL_GROUP.getCode());
        queryUpDeptListReq.setOpenStatus(1);
        //查出当前部门下店组群 用于下面批量查询
        QueryUpDeptListResp resp = baseDataSystemFeignClient.queryUpDeptList(queryUpDeptListReq);
        List<String> codes = new ArrayList<>();
        if (resp != null && CollectionUtils.isNotEmpty(resp.getRows())) {
            codes = resp.getRows().stream().map(QueryUpDeptListResp.UpDeptInfo::getCode).collect(Collectors.toList());
        }

        GoodsStrategyResp goodsStrategyResp = getGoodsStrategyResp(req, codes);

        return goodsStrategyResp;
    }

    @NotNull
    private GoodsStrategyResp getGoodsStrategyResp(GoodsStrageReq req, List<String> codes) {
        GoodsStrategyResp goodsStrategyResp = new GoodsStrategyResp();
        Set<String> skuCodeSet = new HashSet<>();
        Set<String> categoryCodeSet = new HashSet<>();
        for (GoodsStrageReq.GoodsInfo goodsInfo : req.getGoodsInfos()) {
            skuCodeSet.add(goodsInfo.getSkuCode());
            String categoryCodeAll = goodsInfo.getCategoryCodeAll();

            if (StringUtils.isNotEmpty(categoryCodeAll)) {
                String[] split = categoryCodeAll.split(",");
                if (split.length > 0) {
                    List<String> list = Arrays.asList(split);
                    categoryCodeSet.addAll(list);
                }
            }
        }

        IMdGoodsStrategyDomainService mdGoodsStrategyDomainService = SpringContextUtil.getApplicationContext().getBean(IMdGoodsStrategyDomainService.class);

        IMdOrderStrategyDomainService mdOrderStrategyDomainService = SpringContextUtil.getApplicationContext().getBean(IMdOrderStrategyDomainService.class);

        if (ObjectUtils.equals(1, req.getOrderQueryType())) {
            MdOrderStrategyQueryDTO strategyReq = new MdOrderStrategyQueryDTO();
            List<String> deptCodeList = new ArrayList<>();
            deptCodeList.add(req.getDeptCode());
            strategyReq.setDeptCodeList(deptCodeList);
            strategyReq.setDeptType(MdDeptTypeEnum.DEPT.getCode());

            List<MdOrderStrategyDTO> mdOrderStrategyList = mdOrderStrategyDomainService.queryOrderStrategy(strategyReq);

            OrderStrategyResp orderStrategyResp = null;
            if (CollectionUtils.isNotEmpty(mdOrderStrategyList)) {
                orderStrategyResp = CglibCopier.copy(mdOrderStrategyList.get(0), OrderStrategyResp.class);
                orderStrategyResp.setStartTime(DateUtil.localTimeFormateHms(mdOrderStrategyList.get(0).getStartTime()));
                orderStrategyResp.setEndTime(DateUtil.localTimeFormateHms(mdOrderStrategyList.get(0).getEndTime()));
            }
            // 订单订货策略  兜底用
            else if (CollectionUtils.isEmpty(mdOrderStrategyList) && CollectionUtils.isNotEmpty(codes)) {
                deptCodeList = new ArrayList<>();
                deptCodeList.addAll(codes);
                strategyReq.setDeptCodeList(deptCodeList);
                strategyReq.setDeptType(MdDeptTypeEnum.DEPT_GROUP.getCode());
                mdOrderStrategyList = mdOrderStrategyDomainService.queryOrderStrategy(strategyReq);

                if (CollectionUtils.isNotEmpty(mdOrderStrategyList)) {
                    Map<String, MdOrderStrategyDTO> strategyMap = mdOrderStrategyList.stream().collect(Collectors.toMap(MdOrderStrategyDTO::getDeptCode, Function.identity()));
                    for (String code : codes) {
                        if (strategyMap.containsKey(code)) {
                            orderStrategyResp = CglibCopier.copy(strategyMap.get(code), OrderStrategyResp.class);
                            orderStrategyResp.setStartTime(DateUtil.localTimeFormateHms(strategyMap.get(code).getStartTime()));
                            orderStrategyResp.setEndTime(DateUtil.localTimeFormateHms(strategyMap.get(code).getEndTime()));
                        }
                    }
                }
            }
            goodsStrategyResp.setOrderStrategyResp(orderStrategyResp);
        }

        //第一次筛选 所有的具体商品+部门+店组群
        ListGoodsStrategyDTO listGoodsStrategyDTO = new ListGoodsStrategyDTO();
        listGoodsStrategyDTO.setDeptCode(req.getDeptCode());
        listGoodsStrategyDTO.setStoreGroupCodeList(codes);
        listGoodsStrategyDTO.setGoodsCodeList(new ArrayList<>(skuCodeSet));
        listGoodsStrategyDTO.setGoodsType(com.meta.supplychain.enums.GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode());

        //key 商品编码_1  1商品；2品类
        Map<String, MdGoodsStrategyDeptPO> goodsStrategyDeptMap = new HashMap<>();
        List<MdGoodsStrategyDeptPO> goodsStrategysSkuList = mdGoodsStrategyDomainService.listGoodsStrategy(listGoodsStrategyDTO);
        for (MdGoodsStrategyDeptPO mdGoodsStrategyDeptPO : goodsStrategysSkuList) {
            if (null != mdGoodsStrategyDeptPO.getPurchUnitRate() &&
                    (ObjectUtils.equals(ControlPassEnum.NONE.getCode(), mdGoodsStrategyDeptPO.getControlPass()) ||
                            ObjectUtils.equals(ControlPassEnum.EXPRESS.getCode(), mdGoodsStrategyDeptPO.getControlPass()))
            ) {
                String key = mdGoodsStrategyDeptPO.getGoodsCode() + "_1";
                goodsStrategyDeptMap.put(key, mdGoodsStrategyDeptPO);
            }


        }

        //第二次筛选 第一次没查到的商品品类编码+部门+店组群
        listGoodsStrategyDTO = new ListGoodsStrategyDTO();
        listGoodsStrategyDTO.setDeptCode(req.getDeptCode());
        listGoodsStrategyDTO.setStoreGroupCodeList(codes);
        listGoodsStrategyDTO.setGoodsCodeList(new ArrayList<>(categoryCodeSet));
        listGoodsStrategyDTO.setGoodsType(com.meta.supplychain.enums.GoodsTypeEnum.CATEGORY.getCode());

        List<MdGoodsStrategyDeptPO> goodsStrategysCategoryList = mdGoodsStrategyDomainService.listGoodsStrategy(listGoodsStrategyDTO);

        for (MdGoodsStrategyDeptPO mdGoodsStrategyDeptPO : goodsStrategysCategoryList) {
            if (null != mdGoodsStrategyDeptPO.getPurchUnitRate() &&
                    (ObjectUtils.equals(ControlPassEnum.NONE.getCode(), mdGoodsStrategyDeptPO.getControlPass()) ||
                            ObjectUtils.equals(ControlPassEnum.EXPRESS.getCode(), mdGoodsStrategyDeptPO.getControlPass()))
            ) {
                String key = mdGoodsStrategyDeptPO.getGoodsCode() + "_2";
                goodsStrategyDeptMap.put(key, mdGoodsStrategyDeptPO);
            }
        }

        List<GoodsStrategyResp.GoodsStrategy> goodsStrategyList = new ArrayList<>();

        for (GoodsStrageReq.GoodsInfo goodsInfo : req.getGoodsInfos()) {
            String key = goodsInfo.getSkuCode() + "_1";
            if (goodsStrategyDeptMap.containsKey(key)) {
                MdGoodsStrategyDeptPO mdGoodsStrategyDeptPO = goodsStrategyDeptMap.get(key);
                GoodsStrategyResp.GoodsStrategy goodsStrategy = convertGoodsStrategy(goodsInfo, mdGoodsStrategyDeptPO);
                goodsStrategyList.add(goodsStrategy);
                continue;
            }

            String categoryCodeAll = goodsInfo.getCategoryCodeAll();
            if (StringUtils.isNotEmpty(categoryCodeAll)) {
                String[] split = categoryCodeAll.split(",");

                if (split.length > 0) {
                    List<String> categoryList = Arrays.asList(split);
                    Collections.reverse(categoryList);
                    for (String s : categoryList) {
                        key = s + "_2";

                        if (goodsStrategyDeptMap.containsKey(key)) {
                            MdGoodsStrategyDeptPO mdGoodsStrategyDeptPO = goodsStrategyDeptMap.get(key);
                            GoodsStrategyResp.GoodsStrategy goodsStrategy = convertGoodsStrategy(goodsInfo, mdGoodsStrategyDeptPO);
                            goodsStrategyList.add(goodsStrategy);
                            continue;
                        }
                    }
                }
            }
        }
        goodsStrategyResp.setDeptCode(req.getDeptCode());
        goodsStrategyResp.setGoodsStrategyList(goodsStrategyList);
        return goodsStrategyResp;
    }

    private GoodsStrategyResp.GoodsStrategy convertGoodsStrategy(GoodsStrageReq.GoodsInfo goodsInfo, MdGoodsStrategyDeptPO mdGoodsStrategyDeptPO) {
        GoodsStrategyResp.GoodsStrategy goodsStrategy = GoodsStrategyResp.GoodsStrategy.builder()
                .whCode(mdGoodsStrategyDeptPO.getWhCode())
                .whName(mdGoodsStrategyDeptPO.getWhName())
                .goodsCode(goodsInfo.getSkuCode())
                .deptCode(mdGoodsStrategyDeptPO.getDeptCode())
                .deptType(mdGoodsStrategyDeptPO.getDeptType())
                .skuCode(goodsInfo.getSkuCode())
                .purchUnitRate(mdGoodsStrategyDeptPO.getPurchUnitRate())
                .deliveryUnitRate(mdGoodsStrategyDeptPO.getDeliveryUnitRate())
                .centralControl(mdGoodsStrategyDeptPO.getCentralControl())
                .controlPass(mdGoodsStrategyDeptPO.getControlPass())
                .build();

        return goodsStrategy;
    }

    private RequireGoodsResp getRequireGoodsStrategy(RequireGoodsReq req) {
        QueryUpDeptListReq queryUpDeptListReq = new QueryUpDeptListReq();
        queryUpDeptListReq.setDeptCode(req.getDeptCode());
        queryUpDeptListReq.setClassCode(GroupDeptEnum.CONTROL_GROUP.getCode());
        queryUpDeptListReq.setOpenStatus(1);
        //查出当前部门下店组群 用于下面批量查询
        QueryUpDeptListResp resp = baseDataSystemFeignClient.queryUpDeptList(queryUpDeptListReq);
        List<String> codes = new ArrayList<>();
        if (resp != null && CollectionUtils.isNotEmpty(resp.getRows())) {
            codes = resp.getRows().stream().map(QueryUpDeptListResp.UpDeptInfo::getCode).collect(Collectors.toList());
        }
        List<RequireGoodsInfo> goodsInfos = req.getGoodsInfos().stream().filter(e -> StringUtils.isNotEmpty(e.getCategoryCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(goodsInfos)) {
            BizExceptions.throwWithErrorCode(MdErrorCodeEnum.SCMD999B009);
        }

        List<String> specialProducts = goodsInfos.stream()
                .map(RequireGoodsInfo::getGoodsCode).collect(Collectors.toList());
        specialProducts.addAll(goodsInfos.stream()
                .map(RequireGoodsInfo::getSpuCode).collect(Collectors.toList()));

        MdOrderStrategyQueryDTO strategyReq = new MdOrderStrategyQueryDTO();
        List<String> deptCodeList = new ArrayList<>();
        deptCodeList.add(req.getDeptCode());
        strategyReq.setDeptCodeList(deptCodeList);
        strategyReq.setDeptType(MdDeptTypeEnum.DEPT.getCode());

        IMdOrderStrategyDomainService mdOrderStrategyDomainService = SpringContextUtil.getApplicationContext().getBean(IMdOrderStrategyDomainService.class);
        IMdGoodsStrategyDomainService mdGoodsStrategyDomainService = SpringContextUtil.getApplicationContext().getBean(IMdGoodsStrategyDomainService.class);

        List<MdOrderStrategyDTO> mdOrderStrategyList = mdOrderStrategyDomainService.queryOrderStrategy(strategyReq);

        OrderStrategyResp orderStrategyResp = null;
        if (CollectionUtils.isNotEmpty(mdOrderStrategyList)) {
            orderStrategyResp = CglibCopier.copy(mdOrderStrategyList.get(0), OrderStrategyResp.class);
            orderStrategyResp.setStartTime(DateUtil.localTimeFormateHms(mdOrderStrategyList.get(0).getStartTime()));
            orderStrategyResp.setEndTime(DateUtil.localTimeFormateHms(mdOrderStrategyList.get(0).getEndTime()));
        }
        // 订单订货策略  兜底用
        else if (CollectionUtils.isEmpty(mdOrderStrategyList) && CollectionUtils.isNotEmpty(codes)) {
            deptCodeList = new ArrayList<>();
            deptCodeList.addAll(codes);
            strategyReq.setDeptCodeList(deptCodeList);
            strategyReq.setDeptType(MdDeptTypeEnum.DEPT_GROUP.getCode());
            mdOrderStrategyList = mdOrderStrategyDomainService.queryOrderStrategy(strategyReq);

            if (CollectionUtils.isNotEmpty(mdOrderStrategyList)) {
                Map<String, MdOrderStrategyDTO> strategyMap = mdOrderStrategyList.stream().collect(Collectors.toMap(MdOrderStrategyDTO::getDeptCode, Function.identity()));
                for (String code : codes) {
                    if (strategyMap.containsKey(code)) {
                        orderStrategyResp = CglibCopier.copy(strategyMap.get(code), OrderStrategyResp.class);
                        orderStrategyResp.setStartTime(DateUtil.localTimeFormateHms(strategyMap.get(code).getStartTime()));
                        orderStrategyResp.setEndTime(DateUtil.localTimeFormateHms(strategyMap.get(code).getEndTime()));
                    }
                }
            }
        }

        RequireGoodsResp requireGoodsResp = RequireGoodsResp.builder()
                .orderStrategy(orderStrategyResp == null || StringUtils.isNotBlank(orderStrategyResp.getWhCode()) ? orderStrategyResp : null)
                .build();

        Map<String, List<RequireGoodsInfo>> categoryGoodsMap =
                goodsInfos.stream().collect(Collectors.groupingBy(RequireGoodsInfo::getCategoryCode));

        Map<String, MdGoodsStrategyDeptPO> strategyGoodsMap = new HashMap<>();

        //第一次筛选 所有的具体商品+部门+店组群
        List<String> finalCodes = codes;
        List<String> allDeptCodes = Lists.newArrayList(req.getDeptCode());
        allDeptCodes.addAll(finalCodes);

        ListGoodsStrategyDTO listGoodsStrategyDTO = new ListGoodsStrategyDTO();
        listGoodsStrategyDTO.setDeptCode(req.getDeptCode());
        listGoodsStrategyDTO.setStoreGroupCodeList(finalCodes);
        listGoodsStrategyDTO.setGoodsCodeList(specialProducts);
        listGoodsStrategyDTO.setGoodsType(com.meta.supplychain.enums.GoodsTypeEnum.SPECIFIC_PRODUCTS.getCode());

        List<MdGoodsStrategyDeptPO> stCommodityStrategys1 = mdGoodsStrategyDomainService.listGoodsStrategy(listGoodsStrategyDTO);

        Map<String, List<MdGoodsStrategyDeptPO>> strategyGroup = stCommodityStrategys1.stream().collect(Collectors.groupingBy(MdGoodsStrategyDeptPO::getGoodsCode));
        for (String goodsCode : strategyGroup.keySet()) {
            Map<String, MdGoodsStrategyDeptPO> strategyDeptMap
                    = strategyGroup.get(goodsCode).stream().filter(e -> DeptTypeEnum.SPECIFIC_DEPT.getCode().equals(e.getDeptType())).collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptCode, Function.identity()));
            if (strategyDeptMap.containsKey(req.getDeptCode())) {
                MdGoodsStrategyDeptPO stCommodityStrategyDept = strategyDeptMap.get(req.getDeptCode());
                Map<String, MdGoodsStrategyDeptPO> groupDeptMap
                        = strategyGroup.get(goodsCode).stream().filter(e -> DeptTypeEnum.STORE_GROUP.getCode().equals(e.getDeptType())).collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptCode, Function.identity()));
                for (String finalCode : finalCodes) {
                    if (groupDeptMap.containsKey(finalCode) && unCompleteStrategy(strategyGoodsMap.get(req.getDeptCode()))) {
                        coverageStrategy(stCommodityStrategyDept, groupDeptMap.get(finalCode));
                    }
                }
                strategyGoodsMap.put(goodsCode, stCommodityStrategyDept);
            } else {
                Map<String, MdGoodsStrategyDeptPO> groupDeptMap
                        = strategyGroup.get(goodsCode).stream().filter(e -> DeptTypeEnum.STORE_GROUP.getCode().equals(e.getDeptType())).collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptCode, Function.identity()));
                for (String finalCode : finalCodes) {
                    if (groupDeptMap.containsKey(finalCode)) {
                        MdGoodsStrategyDeptPO stCommodityStrategyDept = groupDeptMap.get(finalCode);
                        if (strategyGoodsMap.containsKey(goodsCode) && unCompleteStrategy(strategyGoodsMap.get(goodsCode))) {
                            coverageStrategy(strategyGoodsMap.get(goodsCode), stCommodityStrategyDept);
                        } else if (!strategyGoodsMap.containsKey(goodsCode)) {
                            strategyGoodsMap.put(goodsCode, stCommodityStrategyDept);
                        }
                    }
                }
            }
        }
        List<RequireGoodsInfo> notExistsGoodsInfos1 = goodsInfos.stream()
                .filter(e -> unCompleteStrategy(strategyGoodsMap.get(e.getGoodsCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notExistsGoodsInfos1)) {
            requireGoodsResp.setRequireGoodsList(convertStrategy2Require(strategyGoodsMap));
            return requireGoodsResp;
        }
        //第二次筛选 第一次没查到的商品最底层品类编码+部门+店组群 通过店组群层级筛选
        List<String> categoryGoods = notExistsGoodsInfos1.stream()
                .map(RequireGoodsInfo::getCategoryCode).collect(Collectors.toList());

        listGoodsStrategyDTO = new ListGoodsStrategyDTO();
        listGoodsStrategyDTO.setDeptCode(req.getDeptCode());
        listGoodsStrategyDTO.setStoreGroupCodeList(finalCodes);
        listGoodsStrategyDTO.setGoodsCodeList(categoryGoods);
        listGoodsStrategyDTO.setGoodsType(com.meta.supplychain.enums.GoodsTypeEnum.CATEGORY.getCode());

        List<MdGoodsStrategyDeptPO> stCommodityStrategys2 = mdGoodsStrategyDomainService.listGoodsStrategy(listGoodsStrategyDTO);

        Map<String, List<MdGoodsStrategyDeptPO>> strategyGroup2 = stCommodityStrategys2.stream().collect(Collectors.groupingBy(MdGoodsStrategyDeptPO::getGoodsCode));
        for (String goodsCode : strategyGroup2.keySet()) {
            Map<String, MdGoodsStrategyDeptPO> strategyDeptMap
                    = strategyGroup2.get(goodsCode).stream().collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptCode, Function.identity()));
            for (String finalCode : allDeptCodes) {
                if (strategyDeptMap.containsKey(finalCode)) {
                    MdGoodsStrategyDeptPO stCommodityStrategyDept = strategyDeptMap.get(finalCode);
                    if (categoryGoodsMap.containsKey(stCommodityStrategyDept.getGoodsCode())) {
                        for (RequireGoodsInfo goodsInfo : categoryGoodsMap.get(stCommodityStrategyDept.getGoodsCode())) {
                            if (strategyGoodsMap.containsKey(goodsInfo.getGoodsCode())) {
                                MdGoodsStrategyDeptPO strategyDeptTemp = strategyGoodsMap.get(goodsInfo.getGoodsCode());
                                coverageStrategy(strategyDeptTemp, stCommodityStrategyDept);
                                strategyGoodsMap.put(goodsInfo.getGoodsCode(), strategyDeptTemp);
                            } else {
                                strategyGoodsMap.put(goodsInfo.getGoodsCode(), stCommodityStrategyDept);
                            }
                        }
                    }
                }
            }
        }

        List<RequireGoodsInfo> notExistsGoodsInfos2 = goodsInfos.stream().filter(e -> unCompleteStrategy(strategyGoodsMap.get(e.getGoodsCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notExistsGoodsInfos2)) {
            requireGoodsResp.setRequireGoodsList(convertStrategy2Require(strategyGoodsMap));
            return requireGoodsResp;
        }
        //第三次筛选 前两次没查到的商品父品类编码+部门
        List<String> categoryGoods1 = notExistsGoodsInfos2.stream()
                .map(RequireGoodsInfo::getCategoryCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(categoryGoods1)) {
            GoodsCategoryQueryReq categoryQueryReq = GoodsCategoryQueryReq.builder()
                    .categoryCodes(categoryGoods1)
                    .build();
            List<GoodsCategoryQueryResp> goodsCategory = getICommonGoodsService().getGoodsCategory(categoryQueryReq);
            //构建父类品类-底层品类映射
            Map<String, List<String>> categoryParentMap = new HashMap<>();
            for (GoodsCategoryQueryResp goodsCategoryQueryResp : goodsCategory) {
                if (CollectionUtils.isNotEmpty(goodsCategoryQueryResp.getParentList())) {
                    for (GoodsCategoryQueryResp categoryInfo : goodsCategoryQueryResp.getParentList()) {
                        List<String> categoryList = categoryParentMap.getOrDefault(categoryInfo.getCategoryCode(), Lists.newArrayList());
                        categoryList.add(goodsCategoryQueryResp.getCategoryCode());
                        categoryParentMap.put(categoryInfo.getCategoryCode(), categoryList);
                    }
                }
            }
            //说明没有父品类编码可查 直接返回
            if (categoryParentMap.isEmpty()) {
                requireGoodsResp.setRequireGoodsList(convertStrategy2Require(strategyGoodsMap));
                return requireGoodsResp;
            }

            // 后面是否考虑limit多次查询
            listGoodsStrategyDTO = new ListGoodsStrategyDTO();
            listGoodsStrategyDTO.setDeptCode(req.getDeptCode());
            listGoodsStrategyDTO.setStoreGroupCodeList(finalCodes);
            listGoodsStrategyDTO.setGoodsCodeList(new ArrayList<>(categoryParentMap.keySet()));
            listGoodsStrategyDTO.setGoodsType(com.meta.supplychain.enums.GoodsTypeEnum.CATEGORY.getCode());

            List<MdGoodsStrategyDeptPO> stCommodityStrategys3 = mdGoodsStrategyDomainService.listGoodsStrategy(listGoodsStrategyDTO);

            Map<String, List<MdGoodsStrategyDeptPO>> strategyGroup3 = stCommodityStrategys3.stream().collect(Collectors.groupingBy(MdGoodsStrategyDeptPO::getGoodsCode));
            for (GoodsCategoryQueryResp goodsCategoryQueryResp : goodsCategory) {
                List<GoodsCategoryQueryResp> sortedList = goodsCategoryQueryResp.getParentList().stream()
                        .sorted(Comparator.comparing(GoodsCategoryQueryResp::getLevel, Comparator.reverseOrder()))
                        .collect(Collectors.toList());
                //层级筛选 从低向高取
                for (GoodsCategoryQueryResp categoryInfo : sortedList) {
                    List<MdGoodsStrategyDeptPO> stCommodityStrategyDepts = strategyGroup3.get(categoryInfo.getCategoryCode());
                    if (CollectionUtils.isEmpty(stCommodityStrategyDepts)) {
                        continue;
                    }
                    Map<String, MdGoodsStrategyDeptPO> deptMap = stCommodityStrategyDepts.stream().collect(Collectors.toMap(MdGoodsStrategyDeptPO::getDeptCode, Function.identity()));
                    for (String finalCode : allDeptCodes) {
                        if (deptMap.containsKey(finalCode)) {
                            MdGoodsStrategyDeptPO stCommodityStrategyDept = deptMap.get(finalCode);
                            if (categoryGoodsMap.containsKey(goodsCategoryQueryResp.getCategoryCode())) {
                                for (RequireGoodsInfo goodsInfo : categoryGoodsMap.get(goodsCategoryQueryResp.getCategoryCode())) {
                                    if (strategyGoodsMap.containsKey(goodsInfo.getGoodsCode())) {
                                        MdGoodsStrategyDeptPO strategyDeptTemp = strategyGoodsMap.get(goodsInfo.getGoodsCode());
                                        coverageStrategy(strategyDeptTemp, stCommodityStrategyDept);
                                        strategyGoodsMap.put(goodsInfo.getGoodsCode(), strategyDeptTemp);
                                    } else {
                                        strategyGoodsMap.put(goodsInfo.getGoodsCode(), stCommodityStrategyDept);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        requireGoodsResp.setRequireGoodsList(convertStrategy2Require(strategyGoodsMap));
        return requireGoodsResp;
    }

    private List<RequireGoodsResp.RequireGoods> convertStrategy2Require(Map<String, MdGoodsStrategyDeptPO> strategyGoodsMap) {
        List<RequireGoodsResp.RequireGoods> requireGoodsList = new ArrayList<>();
        for (Map.Entry<String, MdGoodsStrategyDeptPO> entry : strategyGoodsMap.entrySet()) {
            MdGoodsStrategyDeptPO stCommodityStrategyDept = entry.getValue();
            RequireGoodsResp.RequireGoods requireGoods = RequireGoodsResp.RequireGoods.builder()
                    .whCode(stCommodityStrategyDept.getWhCode())
                    .whName(stCommodityStrategyDept.getWhName())
//                    .directSign(stCommodityStrategyDept.getIsDirectCurrent())
                    .goodsCode(entry.getKey())
//                    .suppCode(stCommodityStrategyDept.getSuppCode())
//                    .suppName(stCommodityStrategyDept.getSuppName())
                    .deptCode(stCommodityStrategyDept.getDeptCode())
                    .deptType(stCommodityStrategyDept.getDeptType())
                    .skuCode(entry.getKey())
                    .purchUnitRate(stCommodityStrategyDept.getPurchUnitRate())
                    .deliveryUnitRate(stCommodityStrategyDept.getDeliveryUnitRate())
                    .centralControl(stCommodityStrategyDept.getCentralControl())
                    .controlPass(stCommodityStrategyDept.getControlPass())
                    .build();
            requireGoodsList.add(requireGoods);
        }
        return requireGoodsList;
    }

    /**
     * 策略不完整
     *
     * @param strategyDept
     * @return
     */
    private boolean unCompleteStrategy(MdGoodsStrategyDeptPO strategyDept) {
        if (strategyDept == null) {
            return true;
        }
        if (StringUtils.isNotEmpty(strategyDept.getWhCode())
                && (strategyDept.getControlPass() != null && strategyDept.getDeliveryUnitRate() != null && strategyDept.getPurchUnitRate() != null)
                && strategyDept.getCentralControl() != null) {
            return false;
        }
        return true;
    }

    private void coverageStrategy(MdGoodsStrategyDeptPO originStrategy, MdGoodsStrategyDeptPO newStrategy) {
        if (StringUtils.isEmpty(originStrategy.getWhCode())) { //配送中心配置
            originStrategy.setWhCode(newStrategy.getWhCode());
            originStrategy.setWhName(newStrategy.getWhName());
        }
        if (originStrategy.getControlPass() == null && originStrategy.getPurchUnitRate() == null
                && originStrategy.getDeliveryUnitRate() == null) { //包装控制
            originStrategy.setControlPass(newStrategy.getControlPass());
            originStrategy.setPurchUnitRate(newStrategy.getPurchUnitRate());
            originStrategy.setDeliveryUnitRate(newStrategy.getDeliveryUnitRate());
        }

        if (originStrategy.getCentralControl() == null) { //中央控制
            originStrategy.setCentralControl(newStrategy.getCentralControl());
        }
    }

    @Override
    public List<GoodsDeliverPriceInfoBatchResp> goodsDeliverPriceInfoListBatch(QueryGoodsPriceInfoBatchReq queryGoodsPriceInfoReq) {
        List<QueryGoodsPriceInfoBatchReq.DeptInfo> whDeptCodeMapList = queryGoodsPriceInfoReq.getWhDeptCodeMapList();
        List<GoodsDeliverPriceInfoBatchResp> respList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(whDeptCodeMapList)) {
            whDeptCodeMapList.forEach(whDeptCodeMap -> {
                String deptCode = whDeptCodeMap.getInDeptCode();
                String whCode = whDeptCodeMap.getWhCode();
                QueryGoodsPriceInfoReq req = QueryGoodsPriceInfoReq.builder()
                        .whCode(whCode)
                        .inDeptCode(deptCode)

                        .goodsInfoList(queryGoodsPriceInfoReq.getGoodsInfoList())
                        .build();
                List<GoodsDeliverPriceInfoResp> deliverPriceInfoRespList = goodsDeliverPriceInfoList(req);
                GoodsDeliverPriceInfoBatchResp resp = GoodsDeliverPriceInfoBatchResp.builder()
                        .whCode(whCode)
                        .inDeptCode(deptCode)
                        .deliverPriceInfoRespList(deliverPriceInfoRespList)
                        .build();
                respList.add(resp);
            });
        }
        return respList;
    }


    @Override
    public List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoList(QueryGoodsPriceInfoReq queryGoodsPriceInfoReq) {
        DeptGoodsDeliveryPriceQueryDTO build = DeptGoodsDeliveryPriceQueryDTO.builder()
                .deptCode(queryGoodsPriceInfoReq.getInDeptCode())
                .goodsInfos(
                        queryGoodsPriceInfoReq.getGoodsInfoList().stream().map(goodsInfo -> {
                            return ValidGoodsInfo.builder().skuCode(goodsInfo.getSkuCode())
                                    .distCode(queryGoodsPriceInfoReq.getWhCode())
                                    .categoryCode(goodsInfo.getCategoryCode())
                                    .build();
                        }).collect(Collectors.toList())
                )
                .build();
        List<DeptGoodsDeliveryPriceDTO> deptGoodsDeliveryPriceDTOS = listDeptGoodsDeliveryPrice(build);
        if (CollectionUtils.isEmpty(deptGoodsDeliveryPriceDTOS)) {
            return Collections.emptyList();
        }
        return deptGoodsDeliveryPriceDTOS.stream().map(item -> {
            return GoodsDeliverPriceInfoResp.builder()
                    .skuCode(item.getSkuCode())
                    .promoteActivityCode(item.getPromotionId() == null ? "" : item.getPromotionId().toString())
                    .promoteActivityName(item.getPromotionName())
                    .promotePrice(item.getPromotionPrice())
                    .skuDistPrice(item.getDispatchPrice())
                    .deptDistPrice(item.getDeptDistPrice())
                    .deptSkuPurchPrice(item.getPurchasePrice())
                    .deliveryPrice(item.getFinaPrice())
                    .markupRate(item.getMarkupRate())
                    .finaPrice(item.getFinaPrice())
                    .priceType(item.getPriceType())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<GoodsPurchPriceInfoResp> goodsPurchPriceInfoList(QueryGoodsPurchPriceInfoReq queryGoodsAttrReqParams) {

        int applyCate = ApplyCateEnum.PURCH.getCode();
        if (null != queryGoodsAttrReqParams.getApplyCate()) {
            applyCate = queryGoodsAttrReqParams.getApplyCate();
        }
        DeptGoodsPurchPriceQueryDTO build = DeptGoodsPurchPriceQueryDTO.builder()
                .deptCode(queryGoodsAttrReqParams.getDeptCode())
                .applyCate(applyCate)
                .supplierCode(queryGoodsAttrReqParams.getSupplierCode())
                .contractSkuInfos(queryGoodsAttrReqParams.getGoodsInfoList().stream()
                        .map(e -> ContractSkuInfo.builder().skuCode(e.getSkuCode()).build())
                        .collect(Collectors.toList()))
                .build();
        List<DeptGoodsPurchPriceDTO> deptGoodsPurchPriceDTOS = listDeptGoodsPurchPrice(build);
        if (CollectionUtils.isEmpty(deptGoodsPurchPriceDTOS)) {
            return Collections.emptyList();
        }

        return deptGoodsPurchPriceDTOS.stream().map(item -> {
            return GoodsPurchPriceInfoResp.builder()
                    .skuCode(item.getSkuCode())
                    .contractNo(item.getContractNo())
                    .finaPrice(null == item.getFinaPrice() ? item.getFinaPrice() : item.getFinaPrice().setScale(4, RoundingMode.HALF_UP))
                    .promotionId(null == item.getPromotionId() ? "" : item.getPromotionId() + "")
                    .promotionName(item.getPromotionName())
                    .promotionPrice(null == item.getPromotionPrice() ? item.getPromotionPrice() : item.getPromotionPrice().setScale(4, RoundingMode.HALF_UP))
                    .contractSpecialPrice(null == item.getContractSpecialPrice() ? item.getContractSpecialPrice() : item.getContractSpecialPrice().setScale(4, RoundingMode.HALF_UP))
                    .contractPrice(null == item.getContractPrice() ? item.getContractPrice() : item.getContractPrice().setScale(4, RoundingMode.HALF_UP))
                    .contractMaxPrice(null == item.getContractMaxPrice() ? item.getContractMaxPrice() : item.getContractMaxPrice().setScale(4, RoundingMode.HALF_UP))
                    .purchasePrice(null == item.getPurchasePrice() ? item.getPurchasePrice() : item.getPurchasePrice().setScale(4, RoundingMode.HALF_UP))
                    .priceType(item.getPriceType())
                    .supplierCode(item.getSupplierCode())
                    .supplierName(item.getSupplierName())
                    .mainSupplierMode(item.getMainSupplierMode())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public Result<List<GoodsSuppResp>> queryGoodsSupp(QueryGoodsSuppReq queryGoodsSuppReq) {
        List<GoodsSuppResp> goodsSuppResps = getIMdContractMasDomainService().queryGoodsSupp(queryGoodsSuppReq);
        return new Result<>(goodsSuppResps);
    }

    @Override
    public List<RequireGoodsResp.RequireSupplier> getMainSupplierInfo(MainSupplierInfoQueryDTO mainSupplierInfoQuery) {
        boolean enable = getISupplychainBizSysParamRuleService().isEnable(MdSystemParamEnum.ENABLE_CONTRACT);
        //查主供应商
        if (enable) {
            String deptCode = mainSupplierInfoQuery.getDeptCode();
            Map<String, RequireGoodsInfo> goodsInfoMap = mainSupplierInfoQuery.getGoodsInfoMap();
            List<RequireGoodsResp.RequireSupplier> requireSupplierList = new ArrayList<>();

            List<String> goodsCodes = goodsInfoMap.values().stream().map(RequireGoodsInfo::getGoodsCode).collect(Collectors.toList());

            ContractGoodsDeptQueryDTO contractGoodsDeptQuery = new ContractGoodsDeptQueryDTO();
            contractGoodsDeptQuery.setDeptCode(deptCode);
//            contractGoodsDeptQuery.setMainSupplierMode();
            contractGoodsDeptQuery.setSkuCodeList(goodsCodes);
            List<ContractGoodsDeptDTO> contGoodsDeptList = listContractGoodsDept(contractGoodsDeptQuery);
            if (CollectionUtils.isEmpty(contGoodsDeptList)) {
                return requireSupplierList;
            }

            Map<String, QueryAcceptanceRefundResp.CompensationInfo> promotionMap = new HashMap<>();
            if (ObjectUtils.equals(1, mainSupplierInfoQuery.getPriceSign())) {
                LocalDateTime curDate = LocalDateTime.now();

                handleSpecialPrice(curDate, contGoodsDeptList, goodsInfoMap);

                QueryAcceptanceRefundReq acceptanceRefundReq = QueryAcceptanceRefundReq.builder()
                        .storeCode(deptCode)
                        .skuList(goodsCodes.stream().map(skuCode ->
                                QueryAcceptanceRefundReq.GoodsPriceInfo.builder().skuCode(skuCode).build()).collect(Collectors.toList()))
                        .build();
                QueryAcceptanceRefundResp queryAcceptanceRefundResp = promotionFeignClient.queryAcceptanceRefund(acceptanceRefundReq);

                if (queryAcceptanceRefundResp != null && CollectionUtils.isNotEmpty(queryAcceptanceRefundResp.getCompensationInfoList())) {
                    List<QueryAcceptanceRefundResp.CompensationInfo> compensationInfoList = queryAcceptanceRefundResp.getCompensationInfoList();
                    promotionMap = compensationInfoList.stream().collect(Collectors.toMap(QueryAcceptanceRefundResp.CompensationInfo::getSkuCode, Function.identity()));
                }
            }

            Map<String, List<ContractGoodsDeptDTO>> contGoodsDeptMap = contGoodsDeptList.stream().collect(Collectors.groupingBy(ContractGoodsDeptDTO::getSkuCode));
            for (Map.Entry<String, List<ContractGoodsDeptDTO>> entry : contGoodsDeptMap.entrySet()) {
                RequireGoodsResp.RequireSupplier requireSupplier = new RequireGoodsResp.RequireSupplier();
                requireSupplier.setDeptCode(deptCode);
                requireSupplier.setGoodsCode(entry.getKey());
                requireSupplier.setSkuCode(entry.getKey());


                QueryAcceptanceRefundResp.CompensationInfo compensationInfo = promotionMap.get(entry.getKey());

                //按合同号的倒序
                List<ContractGoodsDeptDTO> contractGoodsDeptDTOList = entry.getValue().stream()
                        .sorted(Comparator.comparing(ContractGoodsDeptDTO::getContractNo)).collect(Collectors.toList());

                Set<RequireGoodsResp.Supplier> supplierContractList = Sets.newLinkedHashSet();
                Set<RequireGoodsResp.Supplier> supplierList = Sets.newLinkedHashSet();
                for (ContractGoodsDeptDTO contractGoodsDept : contractGoodsDeptDTOList) {
                    RequireGoodsResp.Supplier supplier = new RequireGoodsResp.Supplier();
                    supplier.setSupplierCode(contractGoodsDept.getSupplierCode());
                    supplier.setSupplierName(contractGoodsDept.getSupplierName());
                    supplier.setMainSupplierMode(contractGoodsDept.getMainSupplierMode());
                    supplier.setContractNo(contractGoodsDept.getContractNo());
                    supplier.setPurchPrice(contractGoodsDept.getPurchTaxPrice());

                    //取采购价逻辑 TODO
                    if (ObjectUtils.equals(1, mainSupplierInfoQuery.getPriceSign())) {
                        BigDecimal acceptRefundPrice = null;
                        if (null != compensationInfo) {
                            if (useAcceptRefund) {
                                if (compensationInfo != null && (StringUtils.isEmpty(compensationInfo.getSupplierCode()) || compensationInfo.getSupplierCode().equals(contractGoodsDept.getSupplierCode()))) {
                                    if (compensationInfo.getCompensateValue() != null && compensationInfo.getCompensateValue() > 0) {
                                        acceptRefundPrice = BigDecimal.valueOf(compensationInfo.getCompensateValue() / 100d);
                                    }
                                }
                            }
                        }
                        supplier.setSpecialPrice(contractGoodsDept.getPurchTaxPrice());
                        BigDecimal minPrice = getMinPrice(acceptRefundPrice, contractGoodsDept.getPurchTaxPrice(), contractGoodsDept.getPurchTaxPrice());
                        supplier.setPrice(minPrice);

                    }
                    supplierContractList.add(supplier);

                }

                supplierList.addAll(supplierContractList);
                requireSupplier.setSupplierList(Lists.newArrayList(supplierList));
                requireSupplierList.add(requireSupplier);
            }

            return requireSupplierList;
        }
        return null;
    }

    private void handleSpecialPrice(LocalDateTime curDate, List<ContractGoodsDeptDTO> contractGoodsDeptDTOS, Map<String, RequireGoodsInfo> goodsInfoMap) {
        for (ContractGoodsDeptDTO mdContractGoodsDeptPO : contractGoodsDeptDTOS) {
            if (mdContractGoodsDeptPO.getSpecialStartTime() != null && mdContractGoodsDeptPO.getSpecialEndTime() != null) {
                boolean validate = curDate.isAfter(mdContractGoodsDeptPO.getSpecialStartTime()) && curDate.isBefore(mdContractGoodsDeptPO.getSpecialEndTime());
                if (validate) {
                    switch (Objects.requireNonNull(SpecialPriceModeEnum.getEnumByCode(mdContractGoodsDeptPO.getSpecialPriceMode()))) {
                        case NONE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getPurchTaxPrice());
                            break;
                        case FIXED_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getPurchTaxPrice());
                            break;
                        case ADD_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(mdContractGoodsDeptPO.getPurchTaxPrice()
                                    .multiply(BigDecimal.ONE.add(mdContractGoodsDeptPO.getSpecialRate().divide(new BigDecimal(100)))));
                            break;
                        case REDUCE_PRICE:
                            mdContractGoodsDeptPO.setSpecialTaxPrice(goodsInfoMap.get(mdContractGoodsDeptPO.getSkuCode()).getReferPrice()
                                    .multiply(BigDecimal.ONE.subtract(mdContractGoodsDeptPO.getSpecialRate().divide(new BigDecimal(100)))));
                            break;
                    }
                }
            } else {
                mdContractGoodsDeptPO.setSpecialTaxPrice(BigDecimal.ZERO);
            }
        }
    }

    //acceptRefundPrice, purchPrice, specialPrice
    private BigDecimal getMinPrice(BigDecimal acceptRefundPrice, BigDecimal purchPrice, BigDecimal specialPrice) {
        BigDecimal zeroBigDecimal = new BigDecimal("0");
        if (acceptRefundPrice != null && purchPrice != null && specialPrice != null && acceptRefundPrice.equals(zeroBigDecimal) && purchPrice.equals(zeroBigDecimal) && specialPrice.equals(zeroBigDecimal)) {
            //return new SpecialPriceInfo(2, 0L);
            return zeroBigDecimal;
        }
        BigDecimal minPrice = new BigDecimal("0");
//        Integer priceType;
        //if(price1 != null && price1 > 0 && price2 != null && price1 < price2) {
        if (acceptRefundPrice != null && acceptRefundPrice.compareTo(zeroBigDecimal) > 0 && purchPrice != null && purchPrice.compareTo(acceptRefundPrice) > 0) {
//            priceType= 1;
            minPrice = acceptRefundPrice;
        } else {
            minPrice = purchPrice;
            return purchPrice;
        }
//        if(minPrice != null && price3 != null && price3 > 0 && minPrice > price3)  {
        if (minPrice != null && specialPrice != null && specialPrice.compareTo(zeroBigDecimal) > 0 && minPrice.compareTo(specialPrice) > 0) {
            minPrice = specialPrice;
//            priceType= 3;
        }
        return minPrice;
//        return new SpecialPriceInfo(priceType, minPrice);
    }

    /**
     * 根据商品品类解析品类全路径
     * 1.查询商品品类等级系统参数
     * 2.解析商品品类全路径
     *
     * @param categoryCodeList 商品品类结合
     * @return 品类全路径
     */
    @Override
    public List<CategoryCodeAll> getCategoryCodeAll(List<String> categoryCodeList) {
        //获取系统参数  3,3,3
        String cateCodeLen = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.CATE_CODE_LEN);
        Assert.isNotBlank(cateCodeLen, "-1", "商品分类编码长度");
        List<CategoryCodeAll> categoryCodeAllList = new ArrayList<>();
        categoryCodeList.forEach(item -> {
            List<String> categoryPath = parseCategoryPath(cateCodeLen, item);
            categoryCodeAllList.add(CategoryCodeAll.builder()
                    .categoryCode(item)
                    .categoryCodeAll(String.join(",", categoryPath))
                    .build());
        });
        return categoryCodeAllList;
    }

    @Override
    public DemandGoodsStrategyAggreResp getDemandGoodsStrategyAggre(DemandGoodsStrategyAggreReq demandGoodsStrategyAggreReq) {
        DemandGoodsStrategyAggreResp resp = new DemandGoodsStrategyAggreResp();
        //查询采购价
        QueryGoodsPurchPriceInfoReq queryGoodsPurchPriceInfoReq = new QueryGoodsPurchPriceInfoReq();
        queryGoodsPurchPriceInfoReq.setDeptCode(demandGoodsStrategyAggreReq.getDeptCode());//部门编码
        queryGoodsPurchPriceInfoReq.setSupplierCode(demandGoodsStrategyAggreReq.getSupplierCode());//供应商编码
        queryGoodsPurchPriceInfoReq.setApplyCate(demandGoodsStrategyAggreReq.getApplyCate());//价格类别:3采购订单/需求单(周从铭),4配转采
        List<QueryGoodsPurchPriceInfoReq.GoodsInfo> goodsInfoList = new ArrayList<>();
        queryGoodsPurchPriceInfoReq.setGoodsInfoList(goodsInfoList);

        List<QueryGoodsPriceInfoReq.GoodsInfo> deliveryGoodsInfoList = new ArrayList<>();

        List<GoodsStrageReq.GoodsInfo> goodsStrageGoodsInfoList = new ArrayList<>();

        for (DemandGoodsStrategyAggreReq.GoodsInfo goodsInfo : demandGoodsStrategyAggreReq.getGoodsInfoList()) {
            //采购价
            QueryGoodsPurchPriceInfoReq.GoodsInfo purchGoodInfo = new QueryGoodsPurchPriceInfoReq.GoodsInfo();
            purchGoodInfo.setSkuCode(goodsInfo.getSkuCode());

            goodsInfoList.add(purchGoodInfo);

            //配送价
            QueryGoodsPriceInfoReq.GoodsInfo deliveryGoodInfo = new QueryGoodsPriceInfoReq.GoodsInfo();
            deliveryGoodInfo.setSkuCode(goodsInfo.getSkuCode());
            deliveryGoodsInfoList.add(deliveryGoodInfo);

            //商品订货策略
            GoodsStrageReq.GoodsInfo goodsStrageGoodsInfo = new GoodsStrageReq.GoodsInfo();
            goodsStrageGoodsInfo.setSkuCode(goodsInfo.getSkuCode());

            String categoryCodeAll = goodsInfo.getCategoryCodeAll();

            if (StringUtils.isEmpty(categoryCodeAll)) {
                List<String> categoryCodeList = new ArrayList<>();
                categoryCodeList.add(goodsInfo.getCategoryCode());
                List<CategoryCodeAll> categoryCodeAll1 = getCategoryCodeAll(categoryCodeList);
                if (CollectionUtils.isNotEmpty(categoryCodeAll1)) {
                    categoryCodeAll = categoryCodeAll1.get(0).getCategoryCodeAll();
                    goodsInfo.setCategoryCodeAll(categoryCodeAll);
                }
            }
            goodsStrageGoodsInfo.setCategoryCodeAll(goodsInfo.getCategoryCodeAll());
            goodsStrageGoodsInfoList.add(goodsStrageGoodsInfo);
        }

        //采购价格
        List<GoodsPurchPriceInfoResp> goodsPurchPriceInfoResps = goodsPurchPriceInfoList(queryGoodsPurchPriceInfoReq);
        resp.setGoodsPurchPriceInfoRespList(goodsPurchPriceInfoResps);

        if(StringUtils.isNotEmpty(demandGoodsStrategyAggreReq.getWhCode())){
            QueryGoodsPriceInfoReq queryGoodsPriceInfoReq = new QueryGoodsPriceInfoReq();
            queryGoodsPriceInfoReq.setWhCode(demandGoodsStrategyAggreReq.getWhCode());//仓库编码
            queryGoodsPriceInfoReq.setInDeptCode(demandGoodsStrategyAggreReq.getDeptCode());//入货部门编码
            queryGoodsPriceInfoReq.setGoodsInfoList(deliveryGoodsInfoList);

            //配送价格
            List<GoodsDeliverPriceInfoResp> goodsDeliverPriceInfoResps = goodsDeliverPriceInfoList(queryGoodsPriceInfoReq);
            resp.setGoodsDeliverPriceInfoRespList(goodsDeliverPriceInfoResps);
        }



        GoodsStrageReq goodsStrageReq = new GoodsStrageReq();
        goodsStrageReq.setDeptCode(demandGoodsStrategyAggreReq.getDeptCode());
        goodsStrageReq.setOrderQueryType(demandGoodsStrategyAggreReq.getOrderQueryType());
        goodsStrageReq.setGoodsInfos(goodsStrageGoodsInfoList);

        GoodsStrategyResp goodsStrategyResp = getPurchGoodsStrategy(goodsStrageReq);
        resp.setGoodsStrategyResp(goodsStrategyResp);
        return resp;
    }

    /**
     * 根据系统参数和品类编码生成路径
     *
     * @param paramStr 系统参数（逗号分隔的层级长度）
     * @param category 品类编码
     * @return 从一级到该层级的全路径
     * @throws IllegalArgumentException 输入不合法时抛出异常
     */
    public static List<String> parseCategoryPath(String paramStr, String category) {
        // 1. 解析参数为整数数组
        String[] parts = paramStr.split(",");
        int[] levels = new int[parts.length];
        for (int i = 0; i < parts.length; i++) {
            levels[i] = Integer.parseInt(parts[i]);
        }

        // 2. 计算累计长度（用于路径截取）
        List<Integer> cumulativeLens = new ArrayList<>();
        int sum = 0;
        for (int len : levels) {
            sum += len;
            cumulativeLens.add(sum);
        }

        // 3. 检查输入长度是否匹配任意层级
        boolean isValid = false;
        for (int length : cumulativeLens) {
            if (category.length() == length) {
                isValid = true;
                break;
            }
        }
        Assert.isTrue(isValid, "-1", "品类编码<" + category + ">长度不符合任何层级规则");

        // 4. 生成路径（只到当前层级）
        List<String> result = new ArrayList<>();
        for (int end : cumulativeLens) {
            if (end > category.length()) break;
            result.add(category.substring(0, end));
        }

        return result;
    }

    public static void main(String[] args) {
        String param = "3,3,3";
        String category = "4010010011";

        try {
            List<String> result = parseCategoryPath(param, category);
            System.out.println(result);  // 输出: [004, 004001, 004001001]
        } catch (IllegalArgumentException e) {
            System.err.println("解析失败: " + e.getMessage());
        }
    }
}
