package com.meta.supplychain.common.component.domain.md.impl;

import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierCategoryDomainService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 供应商分类相关业务逻辑
 */
@Service
public class MdSupplierCategoryDomainServiceImpl implements IMdSupplierCategoryDomainService {

    @Resource
    private IMstSupplierCategoryRepositoryService repositoryService;

    
}
