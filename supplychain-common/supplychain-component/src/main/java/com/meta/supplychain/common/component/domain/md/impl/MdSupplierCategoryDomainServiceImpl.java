package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierCategoryDomainService;
import com.meta.supplychain.convert.md.MstSupplierCategoryConvert;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierCategoryPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierCategoryDTO;
import com.meta.supplychain.entity.po.md.MstSupplierCategoryPO;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商分类相关业务逻辑
 */
@Service
public class MdSupplierCategoryDomainServiceImpl implements IMdSupplierCategoryDomainService {

    @Resource
    private IMstSupplierCategoryRepositoryService repositoryService;

    @Override
    public Long createOrUpdateSupplierCategory(MstSupplierCategoryDTO dto) {
        MstSupplierCategoryPO po = MstSupplierCategoryConvert.INSTANCE.dto2Po(dto);

        // 父级校验
        if (StringUtils.isNotBlank(po.getParentCode())
                && repositoryService.lambdaQuery().eq(MstSupplierCategoryPO::getCateCode, po.getParentCode()).one() == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD007B001, new Object[]{po.getParentCode()});
        }

        if (dto.getId() == null) {
            repositoryService.save(po);
        } else {
            repositoryService.updateById(po);
        }
        return po.getId();
    }

    @Override
    public void deleteSupplierCategoryByCode(String cateCode) {
        LambdaUpdateWrapper<MstSupplierCategoryPO> updateWrapper
                = Wrappers.lambdaUpdate(MstSupplierCategoryPO.class).eq(MstSupplierCategoryPO::getCateCode, cateCode);

        repositoryService.remove(updateWrapper);
    }

    @Override
    public PageResult<MstSupplierCategoryDTO> pageQuerySupplierCategory(MstSupplierCategoryPageQueryReq request) {
        Page<MstSupplierCategoryPO> pageResult = repositoryService.pageQuerySupplierCategory(new Page<>(request.getCurrent(), request.getPageSize()), request);

        List<MstSupplierCategoryDTO> result = pageResult.getRecords().stream()
                .map(MstSupplierCategoryConvert.INSTANCE::po2Dto)
                .collect(Collectors.toList());

        return PageResult.of(pageResult.getTotal(), result);
    }
}
